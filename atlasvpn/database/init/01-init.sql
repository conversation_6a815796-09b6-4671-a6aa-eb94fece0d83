-- PostgreSQL Initialization Script for AtlasVPN
-- This script runs automatically when the PostgreSQL container starts for the first time

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "citext";

-- Create database if not exists (already created by POSTGRES_DB)
-- Set proper timezone
SET timezone = 'UTC';

-- Create indexes for common queries (will be applied after tables are created by SQLAlchemy)
-- These will be ignored if tables don't exist yet, but will be available for manual execution

-- Performance optimization settings
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET track_activity_query_size = 2048;
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 1000;

-- Reload configuration
SELECT pg_reload_conf();

-- Log successful initialization
\echo 'AtlasVPN PostgreSQL database initialized successfully' 