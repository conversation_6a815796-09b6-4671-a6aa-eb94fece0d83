/**
 * Task system utilities and helpers
 */

import type { Task, TaskType, TaskStatus, TaskDifficulty } from '~/types';

/**
 * Task type configurations
 */
export const TASK_TYPES: Record<TaskType, {
  name: string;
  icon: string;
  color: string;
  description: string;
}> = {
  telegram: {
    name: 'Telegram',
    icon: 'i-simple-icons-telegram',
    color: 'blue',
    description: 'Join Telegram channels and groups',
  },
  social: {
    name: 'Social Media',
    icon: 'i-heroicons-share',
    color: 'purple',
    description: 'Follow social media accounts',
  },
  referral: {
    name: 'Referral',
    icon: 'i-heroicons-user-group',
    color: 'green',
    description: 'Invite friends to earn rewards',
  },
  daily: {
    name: 'Daily Task',
    icon: 'i-heroicons-calendar-days',
    color: 'yellow',
    description: 'Complete daily check-ins and activities',
  },
  special: {
    name: 'Special Event',
    icon: 'i-heroicons-star',
    color: 'pink',
    description: 'Limited-time special events',
  },
  youtube: {
    name: 'YouTube',
    icon: 'i-simple-icons-youtube',
    color: 'red',
    description: 'Subscribe to YouTube channels',
  },
  instagram: {
    name: 'Instagram',
    icon: 'i-simple-icons-instagram',
    color: 'pink',
    description: 'Follow Instagram accounts',
  },
  twitter: {
    name: 'Twitter',
    icon: 'i-simple-icons-twitter',
    color: 'blue',
    description: 'Follow Twitter accounts',
  },
};

/**
 * Task difficulty configurations
 */
export const TASK_DIFFICULTIES: Record<TaskDifficulty, {
  name: string;
  multiplier: number;
  color: string;
  description: string;
}> = {
  easy: {
    name: 'Easy',
    multiplier: 1.0,
    color: 'green',
    description: 'Simple tasks that take 1-2 minutes',
  },
  medium: {
    name: 'Medium',
    multiplier: 1.5,
    color: 'yellow',
    description: 'Moderate tasks that take 3-5 minutes',
  },
  hard: {
    name: 'Hard',
    multiplier: 2.0,
    color: 'red',
    description: 'Complex tasks that take 5-10 minutes',
  },
};

/**
 * Task status configurations
 */
export const TASK_STATUSES: Record<TaskStatus, {
  name: string;
  color: string;
  description: string;
}> = {
  available: {
    name: 'Available',
    color: 'blue',
    description: 'Task is ready to be started',
  },
  pending: {
    name: 'Pending',
    color: 'yellow',
    description: 'Task is being verified',
  },
  completed: {
    name: 'Completed',
    color: 'green',
    description: 'Task has been completed successfully',
  },
  failed: {
    name: 'Failed',
    color: 'red',
    description: 'Task verification failed',
  },
};

/**
 * Get task type configuration
 * @param type - Task type
 * @returns Task type configuration
 */
export function getTaskTypeConfig(type: TaskType) {
  return TASK_TYPES[type] || TASK_TYPES.daily;
}

/**
 * Get task difficulty configuration
 * @param difficulty - Task difficulty
 * @returns Task difficulty configuration
 */
export function getTaskDifficultyConfig(difficulty: TaskDifficulty) {
  return TASK_DIFFICULTIES[difficulty] || TASK_DIFFICULTIES.easy;
}

/**
 * Get task status configuration
 * @param status - Task status
 * @returns Task status configuration
 */
export function getTaskStatusConfig(status: TaskStatus) {
  return TASK_STATUSES[status] || TASK_STATUSES.available;
}

/**
 * Calculate task reward based on base amount and difficulty
 * @param baseAmount - Base reward amount
 * @param difficulty - Task difficulty
 * @returns Calculated reward amount
 */
export function calculateTaskReward(baseAmount: number, difficulty: TaskDifficulty): number {
  const config = getTaskDifficultyConfig(difficulty);
  return Math.round(baseAmount * config.multiplier * 100) / 100;
}

/**
 * Check if task can be started
 * @param task - Task to check
 * @returns True if task can be started
 */
export function canStartTask(task: Task): boolean {
  return task.status === 'available' && !task.is_claimed;
}

/**
 * Check if task can be claimed
 * @param task - Task to check
 * @returns True if task can be claimed
 */
export function canClaimTask(task: Task): boolean {
  return task.status === 'completed' && !task.is_claimed;
}

/**
 * Check if task is completed
 * @param task - Task to check
 * @returns True if task is completed
 */
export function isTaskCompleted(task: Task): boolean {
  return task.status === 'completed' || task.is_claimed;
}

/**
 * Get task progress percentage
 * @param task - Task to check
 * @returns Progress percentage (0-100)
 */
export function getTaskProgress(task: Task): number {
  if (task.is_claimed) return 100;
  if (task.status === 'completed') return 90;
  if (task.status === 'pending') return 60;
  if (task.status === 'failed') return 0;
  return 0; // available
}

/**
 * Format task completion time
 * @param seconds - Time in seconds
 * @returns Formatted time string
 */
export function formatTaskTime(seconds: number): string {
  if (seconds < 60) return `${seconds}s`;
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  if (remainingSeconds === 0) return `${minutes}m`;
  return `${minutes}m ${remainingSeconds}s`;
}

/**
 * Get task category from type
 * @param type - Task type
 * @returns Task category
 */
export function getTaskCategory(type: TaskType): string {
  const categories: Record<TaskType, string> = {
    telegram: 'social',
    social: 'social',
    youtube: 'social',
    instagram: 'social',
    twitter: 'social',
    referral: 'earning',
    daily: 'daily',
    special: 'event',
  };
  return categories[type] || 'other';
}

/**
 * Filter tasks by category
 * @param tasks - Array of tasks
 * @param category - Category to filter by
 * @returns Filtered tasks
 */
export function filterTasksByCategory(tasks: Task[], category: string): Task[] {
  if (category === 'all') return tasks;
  return tasks.filter(task => getTaskCategory(task.type) === category);
}

/**
 * Sort tasks by priority
 * @param tasks - Array of tasks
 * @returns Sorted tasks
 */
export function sortTasksByPriority(tasks: Task[]): Task[] {
  return [...tasks].sort((a, b) => {
    // Priority order: available > pending > completed > failed
    const statusPriority: Record<TaskStatus, number> = {
      available: 4,
      pending: 3,
      completed: 2,
      failed: 1,
    };
    
    // Claimed tasks go to the end
    if (a.is_claimed && !b.is_claimed) return 1;
    if (!a.is_claimed && b.is_claimed) return -1;
    
    // Sort by status priority
    const statusDiff = statusPriority[b.status] - statusPriority[a.status];
    if (statusDiff !== 0) return statusDiff;
    
    // Sort by reward amount (higher first)
    const rewardDiff = b.reward_amount - a.reward_amount;
    if (rewardDiff !== 0) return rewardDiff;
    
    // Sort by creation date (newer first)
    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
  });
}

/**
 * Get task statistics
 * @param tasks - Array of tasks
 * @returns Task statistics
 */
export function getTaskStatistics(tasks: Task[]) {
  const stats = {
    total: tasks.length,
    available: 0,
    pending: 0,
    completed: 0,
    failed: 0,
    claimed: 0,
    totalRewards: 0,
    claimedRewards: 0,
    pendingRewards: 0,
  };

  tasks.forEach(task => {
    stats[task.status]++;
    
    if (task.is_claimed) {
      stats.claimed++;
      stats.claimedRewards += task.reward_amount;
    } else if (task.status === 'completed') {
      stats.pendingRewards += task.reward_amount;
    }
    
    stats.totalRewards += task.reward_amount;
  });

  return stats;
}

/**
 * Generate task completion message
 * @param task - Completed task
 * @returns Completion message
 */
export function getTaskCompletionMessage(task: Task): string {
  const typeConfig = getTaskTypeConfig(task.type);
  const messages = {
    telegram: `Great! You've joined the ${task.title}. Welcome to the community!`,
    social: `Awesome! You're now following ${task.title}. Stay updated with the latest news!`,
    referral: `Excellent! You've successfully invited a friend. Keep sharing to earn more!`,
    daily: `Perfect! Daily check-in completed. Come back tomorrow for more rewards!`,
    special: `Amazing! You've completed the special event task. Well done!`,
    youtube: `Fantastic! You've subscribed to ${task.title}. Enjoy the content!`,
    instagram: `Nice! You're now following ${task.title} on Instagram!`,
    twitter: `Cool! You're now following ${task.title} on Twitter!`,
  };
  
  return messages[task.type] || `Task "${task.title}" completed successfully!`;
}

/**
 * Get next available task
 * @param tasks - Array of tasks
 * @param currentTask - Current task (optional)
 * @returns Next available task or null
 */
export function getNextAvailableTask(tasks: Task[], currentTask?: Task): Task | null {
  const availableTasks = tasks.filter(task => canStartTask(task));
  
  if (availableTasks.length === 0) return null;
  
  // If current task is provided, try to find a task of the same type
  if (currentTask) {
    const sameTypeTask = availableTasks.find(task => 
      task.type === currentTask.type && task.id !== currentTask.id
    );
    if (sameTypeTask) return sameTypeTask;
  }
  
  // Return the highest priority available task
  return sortTasksByPriority(availableTasks)[0];
}

/**
 * Calculate daily task streak bonus
 * @param streakDays - Number of consecutive days
 * @param baseReward - Base reward amount
 * @returns Bonus amount
 */
export function calculateStreakBonus(streakDays: number, baseReward: number): number {
  const maxBonus = 2.0; // 100% bonus at maximum
  const maxStreakForBonus = 30; // Maximum streak days for bonus calculation
  
  const bonusMultiplier = Math.min(
    1 + (streakDays / maxStreakForBonus),
    maxBonus
  );
  
  return Math.round((baseReward * bonusMultiplier - baseReward) * 100) / 100;
}

/**
 * Get task verification requirements
 * @param task - Task to check
 * @returns Verification requirements
 */
export function getTaskVerificationRequirements(task: Task): string[] {
  const requirements: Record<TaskType, string[]> = {
    telegram: [
      'Join the Telegram channel/group',
      'Stay joined for at least 24 hours',
      'Do not leave immediately after joining',
    ],
    social: [
      'Follow the social media account',
      'Keep following for at least 7 days',
      'Engage with at least one post',
    ],
    youtube: [
      'Subscribe to the YouTube channel',
      'Watch at least one full video',
      'Keep subscription active',
    ],
    instagram: [
      'Follow the Instagram account',
      'Like at least one recent post',
      'Keep following for verification',
    ],
    twitter: [
      'Follow the Twitter account',
      'Retweet at least one post',
      'Keep following for verification',
    ],
    referral: [
      'Share your referral link',
      'Friend must sign up using your link',
      'Friend must complete first task',
    ],
    daily: [
      'Complete daily check-in',
      'Maintain login streak',
      'Complete within 24 hours',
    ],
    special: [
      'Complete all required actions',
      'Submit within event timeframe',
      'Meet all event criteria',
    ],
  };
  
  return requirements[task.type] || ['Complete the required action'];
}
