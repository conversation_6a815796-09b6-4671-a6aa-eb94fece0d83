/**
 * @file api/hooks/index.ts
 * @description Centralized exports for all React Query hooks
 * Re-exports all hooks from modular files for backward compatibility
 */

// Export all auth hooks
export * from './auth';

// Export all user hooks
export * from './user';

// Export all card hooks
export * from './cards';

// Export all task hooks
export * from './tasks';

// Export all referral hooks
export * from './referrals';

// Export all VPN hooks
export * from './vpn';

// Export all chat hooks
export * from './chat';

// Export all system hooks
export * from './system';

// Export utility functions
export * from './utils';

// Consolidated query keys for backward compatibility
export const QUERY_KEYS = {
  // Auth
  CURRENT_USER: ['auth', 'currentUser'],
  VERIFY_SESSION: ['auth', 'verifySession'],
  
  // User
  USER_STATS: ['user', 'stats'],
  USER_TRANSACTIONS: ['user', 'transactions'],
  
  // Cards
  ALL_CARDS: ['cards', 'all'],
  
  // Tasks
  AVAILABLE_TASKS: ['tasks', 'available'],
  DAILY_STREAK: ['tasks', 'dailyStreak'],
  TASK_ANALYTICS: ['tasks', 'analytics'],
  
  // Referrals
  REFERRAL_INFO: ['referrals', 'info'],
  REFERRED_USERS: ['referrals', 'users'],
  
  // VPN
  VPN_PACKAGES: ['vpn', 'packages'],
  VPN_SUBSCRIPTIONS: ['vpn', 'subscriptions'],
  VPN_PANELS: ['vpn', 'panels'],
  
  // Chat
  CHAT_CONVERSATIONS: ['chat', 'conversations'],
  CHAT_MESSAGES: (id: number) => ['chat', 'messages', id],
  
  // System
  HEALTH_CHECK: ['system', 'health'],
  SYSTEM_STATUS: ['system', 'status']
} as const;