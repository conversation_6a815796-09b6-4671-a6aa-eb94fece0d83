/// <reference types="vitest" />
import { defineConfig } from 'vite';
import { resolve } from 'path';

export default defineConfig({
  test: {
    environment: 'jsdom',
    globals: true,
    setupFiles: ['./src/tests/setup.ts'],
    include: ['src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}'],
    exclude: ['node_modules', 'dist', '.astro'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/tests/',
        '**/*.d.ts',
        '**/*.config.*',
        'dist/',
        '.astro/',
      ],
    },
    // Mock browser APIs
    deps: {
      inline: ['@testing-library/jest-dom'],
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@tests': resolve(__dirname, './src/tests'),
    },
  },
  define: {
    // Mock environment variables for tests
    'import.meta.env.VITE_API_URL': JSON.stringify('http://localhost:8000/api'),
    'import.meta.env.VITE_WS_BASE_URL': JSON.stringify('ws://localhost:8000/ws'),
    'import.meta.env.DEV': true,
    'import.meta.env.PROD': false,
  },
}); 