/**
 * @file api/hooks/chat.ts
 * @description Chat-related React Query hooks
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type {
  ChatConversation,
  ChatMessage,
  QueryOptions,
  MutationOptions
} from '../types';
import { chatService } from '../services';

// Query Keys
export const CHAT_QUERY_KEYS = {
  CHAT_CONVERSATIONS: ['chat', 'conversations'],
  CHAT_MESSAGES: (id: number) => ['chat', 'messages', id]
} as const;

// Default query options
const DEFAULT_QUERY_OPTIONS: QueryOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  retry: false
};

// Chat Hooks
export function useChatConversationsQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: CHAT_QUERY_KEYS.CHAT_CONVERSATIONS,
    queryFn: () => chatService.getConversations(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function useChatMessagesQuery(conversationId: number, options?: QueryOptions) {
  return useQuery({
    queryKey: CHAT_QUERY_KEYS.CHAT_MESSAGES(conversationId),
    queryFn: () => chatService.getConversationMessages(conversationId),
    enabled: !!conversationId,
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function useSendMessageMutation(options?: MutationOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ conversationId, content }: { conversationId: number; content: string }) => 
      chatService.sendMessage(conversationId, content),
    onSuccess: (_, { conversationId }) => {
      queryClient.invalidateQueries({ queryKey: CHAT_QUERY_KEYS.CHAT_MESSAGES(conversationId) });
      queryClient.invalidateQueries({ queryKey: CHAT_QUERY_KEYS.CHAT_CONVERSATIONS });
    },
    ...options
  });
}