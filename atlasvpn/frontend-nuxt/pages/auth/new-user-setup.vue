<template>
  <div class="new-user-setup min-h-screen bg-[#030303] text-white">
    <!-- Background Effects -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
      <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse"></div>
      <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
    </div>

    <div class="relative z-10 container mx-auto px-4 py-8 min-h-screen flex flex-col justify-center">
      <div class="max-w-md mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
          <h1 class="text-3xl font-bold mb-2 gradient-text">Welcome to AtlasVPN!</h1>
          <p class="text-gray-300">Let's set up your account</p>
        </div>

        <!-- Setup Form -->
        <div class="glass-card p-6">
          <form @submit.prevent="handleSubmit" class="space-y-6">
            <!-- Username Field -->
            <div>
              <label for="username" class="block text-sm font-medium text-gray-300 mb-2">
                Choose a username
              </label>
              <UInput
                id="username"
                v-model="form.username"
                placeholder="Enter your username"
                :disabled="isLoading"
                :error="!!errors.username"
                class="w-full"
                size="lg"
              />
              <p v-if="errors.username" class="text-red-400 text-sm mt-1">
                {{ errors.username }}
              </p>
              <p class="text-gray-400 text-xs mt-1">
                This will be your unique identifier in the app
              </p>
            </div>

            <!-- Terms and Conditions -->
            <div class="flex items-start space-x-3">
              <UCheckbox
                v-model="form.acceptTerms"
                :disabled="isLoading"
                class="mt-1"
              />
              <div class="text-sm">
                <p class="text-gray-300">
                  I agree to the 
                  <a href="#" class="text-purple-400 hover:text-purple-300 underline">
                    Terms of Service
                  </a>
                  and 
                  <a href="#" class="text-purple-400 hover:text-purple-300 underline">
                    Privacy Policy
                  </a>
                </p>
              </div>
            </div>

            <!-- Submit Button -->
            <UButton
              type="submit"
              :loading="isLoading"
              :disabled="!canSubmit"
              size="lg"
              color="primary"
              class="w-full"
            >
              {{ isLoading ? 'Creating Account...' : 'Create Account' }}
            </UButton>
          </form>
        </div>

        <!-- Back Button -->
        <div class="text-center mt-6">
          <UButton
            @click="goBack"
            variant="ghost"
            size="sm"
            :disabled="isLoading"
          >
            <template #leading>
              <Icon name="i-heroicons-arrow-left" class="w-4 h-4" />
            </template>
            Back to Login
          </UButton>
        </div>
      </div>
    </div>

    <!-- Error Toast -->
    <div v-if="error" class="fixed bottom-4 left-4 right-4 z-50">
      <UAlert
        :title="error"
        color="red"
        variant="solid"
        :close-button="{ icon: 'i-heroicons-x-mark-20-solid', color: 'white', variant: 'link' }"
        @close="error = null"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: false
})

// SEO
useHead({
  title: 'Setup Account - AtlasVPN',
  meta: [
    { name: 'description', content: 'Complete your AtlasVPN account setup' }
  ]
})

const { register } = useAuth()
const { initData } = useTelegram()

const form = reactive({
  username: '',
  acceptTerms: false
})

const errors = reactive({
  username: ''
})

const isLoading = ref(false)
const error = ref<string | null>(null)

const canSubmit = computed(() => {
  return form.username.trim().length >= 3 && 
         form.acceptTerms && 
         !isLoading.value
})

const validateUsername = () => {
  errors.username = ''
  
  if (!form.username.trim()) {
    errors.username = 'Username is required'
    return false
  }
  
  if (form.username.trim().length < 3) {
    errors.username = 'Username must be at least 3 characters'
    return false
  }
  
  if (form.username.trim().length > 20) {
    errors.username = 'Username must be less than 20 characters'
    return false
  }
  
  if (!/^[a-zA-Z0-9_]+$/.test(form.username.trim())) {
    errors.username = 'Username can only contain letters, numbers, and underscores'
    return false
  }
  
  return true
}

const handleSubmit = async () => {
  error.value = null
  
  if (!validateUsername()) {
    return
  }
  
  if (!form.acceptTerms) {
    error.value = 'Please accept the terms and conditions'
    return
  }
  
  if (!initData.value) {
    error.value = 'Telegram data not available. Please try again.'
    return
  }
  
  isLoading.value = true
  
  try {
    await register(initData.value, form.username.trim())
    // Registration successful, user will be redirected to dashboard
  } catch (err: any) {
    console.error('Registration failed:', err)
    error.value = err.message || 'Registration failed. Please try again.'
  } finally {
    isLoading.value = false
  }
}

const goBack = () => {
  navigateTo('/')
}

// Watch username for real-time validation
watch(() => form.username, () => {
  if (errors.username) {
    validateUsername()
  }
})
</script>

<style scoped>
.gradient-text {
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 1rem;
}
</style>
