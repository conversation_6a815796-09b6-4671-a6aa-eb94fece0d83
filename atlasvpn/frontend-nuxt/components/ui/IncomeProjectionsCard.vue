<template>
  <div class="income-projections-card relative overflow-hidden rounded-xl border border-purple-500/20 bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm shadow-lg shadow-purple-500/15">
    <!-- Glow Effect -->
    <div class="absolute bottom-0 left-0 w-32 h-32 bg-purple-500/10 rounded-full blur-3xl translate-y-16 -translate-x-16"></div>
    
    <!-- Corner Accents -->
    <div class="absolute top-0 right-0 w-8 h-8 opacity-70 rotate-90">
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
        <path d="M 0 12 V 0 H 12" stroke="#a855f7" stroke-width="1" />
        <path d="M 0 8 V 0 H 8" stroke="#a855f7" stroke-width="2" />
      </svg>
    </div>

    <div class="relative p-4">
      <!-- Header -->
      <div class="mb-3">
        <div class="flex items-center gap-2">
          <Icon name="i-heroicons-chart-bar-square" class="text-purple-400 text-sm" />
          <div class="text-sm uppercase tracking-wider text-purple-300 font-bold">
            Income Projections
          </div>
        </div>
        <div class="mt-1 h-px bg-gradient-to-r from-purple-500/50 via-purple-400/20 to-transparent" />
      </div>

      <!-- Projections Grid -->
      <div class="grid grid-cols-2 gap-2 overflow-hidden flex-1">
        <StatProjectionItem
          label="Daily"
          :value="formatCurrency(dailyEarnings)"
          period="24h"
          :highlight="false"
        />
        <StatProjectionItem
          label="Weekly"
          :value="formatCurrency(weeklyEarnings)"
          period="7d"
          :highlight="false"
        />
        <StatProjectionItem
          label="Monthly"
          :value="formatCurrency(monthlyEarnings)"
          period="30d"
          :highlight="true"
        />
        <StatProjectionItem
          label="Services"
          :value="subscriptions.toString()"
          period="active"
          :highlight="false"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  subscriptions: number
  balance: number
  hourlyRate: number
}

const props = defineProps<Props>()

const dailyEarnings = computed(() => (props.hourlyRate ?? 0) * 24)
const weeklyEarnings = computed(() => dailyEarnings.value * 7)
const monthlyEarnings = computed(() => dailyEarnings.value * 30)

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}
</script>

<style scoped>
.income-projections-card {
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.05) 0%, transparent 50%);
}
</style>
