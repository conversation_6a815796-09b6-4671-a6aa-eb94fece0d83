import { useRef, useEffect, useState, useCallback } from 'react';

type UseAudioParams = {
  url: string;
  loop?: boolean;
  autoPlay?: boolean;
  audioRef?: React.MutableRefObject<HTMLAudioElement | null>;
};

type UseAudioReturn = {
  play: () => void;
  pause: () => void;
  mute: () => void;
  unmute: () => void;
  isMuted: boolean;
  setAudioRef: (audio: HTMLAudioElement) => void;
};

/**
 * Custom hook to play audio in React components.
 * Plays the specified audio URL with optional looping and autoplay.
 */
export function useAudio({
  url,
  loop = false,
  autoPlay = true,
  audioRef: externalAudioRef,
}: UseAudioParams): UseAudioReturn {
  const internalAudioRef = useRef<HTMLAudioElement | null>(null);
  const audioRef = externalAudioRef || internalAudioRef;
  const [isMuted, setIsMuted] = useState(false);

  // Add function to set the audio reference externally
  const setAudioRef = useCallback(
    (audio: HTMLAudioElement) => {
      if (audioRef) {
        audioRef.current = audio;
        audio.loop = loop;
        audio.muted = isMuted;
      }
    },
    [audioRef, loop, isMuted]
  );

  useEffect(() => {
    // Skip audio creation if no URL provided
    if (!url) {
      console.warn('No audio URL provided to useAudio hook');
      return;
    }

    // Skip audio creation if external ref is provided (handled in parent component)
    if (externalAudioRef) {
      return;
    }

    // Check if the audio file exists by making a HEAD request
    const checkAudioExists = async () => {
      try {
        const response = await fetch(url, { method: 'HEAD' });
        if (!response.ok) {
          console.warn(`Audio file at ${url} not found or inaccessible`);
          return false;
        }
        return true;
      } catch (err) {
        console.warn(`Error checking audio file at ${url}:`, err);
        return false;
      }
    };

    // Create and configure audio element
    const createAudio = async () => {
      // Check if audio exists first
      const exists = await checkAudioExists();
      if (!exists) return;

      const audio = new Audio(url);
      audio.loop = loop;
      audio.muted = isMuted;
      audio.preload = 'auto'; // Ensure audio is preloaded

      // Set up error handling
      audio.onerror = e => {
        console.warn(`Audio error (${url}):`, e);
      };

      audioRef.current = audio;

      // Attempt to play immediately if autoPlay is enabled
      if (autoPlay) {
        playAudio();
      }
    };

    // Function to attempt playing the audio
    const playAudio = () => {
      if (audioRef.current && !audioRef.current.muted) {
        audioRef.current.play().catch(err => {
          if (err.name === 'NotSupportedError' || err.name === 'NotAllowedError') {
            console.warn(`Audio playback blocked (${err.name}): ${err.message}`);
          } else {
            console.error('Audio play failed:', err);
          }
        });
      }
    };

    // Set up retry logic for user interactions
    const setupAutoplayRetry = () => {
      // Events that can trigger audio playback after user interaction
      const events: Array<keyof WindowEventMap> = [
        'click',
        'touchstart',
        'pointerdown',
        'wheel',
        'keydown',
      ];

      const retryPlay = () => {
        playAudio();
        // Remove all event listeners after the first interaction
        events.forEach(evt => window.removeEventListener(evt, retryPlay));
      };

      // Add event listeners for each interaction type
      events.forEach(evt => window.addEventListener(evt, retryPlay, { once: true }));

      // Return cleanup function
      return () => {
        events.forEach(evt => window.removeEventListener(evt, retryPlay));
      };
    };

    // Initialize audio
    createAudio();

    // Set up retry mechanism if autoplay is enabled
    let removeRetryListeners: (() => void) | undefined;
    if (autoPlay) {
      removeRetryListeners = setupAutoplayRetry();
    }

    // Cleanup function
    return () => {
      if (removeRetryListeners) {
        removeRetryListeners();
      }

      // Only clean up if we created the audio element (not external ref)
      if (!externalAudioRef && audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = ''; // Clear the source
        audioRef.current = null;
      }
    };
  }, [url, loop, autoPlay, isMuted, externalAudioRef, audioRef]);

  const play = useCallback(() => {
    if (audioRef.current) {
      // Store actual muted state and restore after play attempt
      const wasMuted = audioRef.current.muted;
      if (wasMuted) {
        audioRef.current.muted = false;
      }

      audioRef.current
        .play()
        .catch(err => {
          console.warn('Audio play failed in play() method:', err);
        })
        .finally(() => {
          // Restore muted state if it was previously muted
          if (wasMuted && audioRef.current) {
            audioRef.current.muted = true;
          }
        });
    }
  }, [audioRef]);

  const pause = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.pause();
    }
  }, [audioRef]);

  const mute = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.muted = true;
      audioRef.current.volume = 0; // Ensure volume is 0 as well
      setIsMuted(true);
    }
  }, [audioRef]);

  const unmute = useCallback(() => {
    if (audioRef.current) {
      audioRef.current.muted = false;
      audioRef.current.volume = 1; // Ensure volume is restored
      setIsMuted(false);

      // Attempt to play immediately if it wasn't playing due to being muted
      if (audioRef.current.paused) {
        audioRef.current.play().catch(err => {
          console.warn('Audio play failed after unmute:', err);
        });
      }
    }
  }, [audioRef]);

  return { play, pause, mute, unmute, isMuted, setAudioRef };
}
