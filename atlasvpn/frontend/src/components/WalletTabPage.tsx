import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User, Transaction, TransactionType } from '../types';
import { useDashboardStatsQuery } from '../hooks/dashboardHooks';
import { formatCurrency } from '../utils/format';
import {
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ArrowUpCircleIcon,
  ArrowDownCircleIcon,
  BanknotesIcon,
} from '@heroicons/react/24/outline';
import { FaPlus } from 'react-icons/fa';
import type { ComponentType } from 'react';
import LoadingSpinner from './LoadingSpinner';
import { api } from '../utils/apiClient';
import Section from './layout/Section';

interface WalletTabPageProps {
  transactions?: Transaction[];
  isLoadingTransactions: boolean;
  user?: User;
  onTabChange?: (tabId: string) => void;
}

const DEPOSIT_AMOUNTS = [10, 20, 50, 100];

const getTransactionDetails = (type: TransactionType | string) => {
  switch (type) {
    case TransactionType.wallet_topup:
      return { icon: ArrowUpCircleIcon, color: 'text-green-400', label: 'Wallet Top-up' };
    case TransactionType.subscription_purchase:
      return { icon: ArrowDownCircleIcon, color: 'text-red-400', label: 'VPN Purchase' };
    case TransactionType.subscription_renewal:
      return { icon: ArrowDownCircleIcon, color: 'text-red-400', label: 'VPN Renewal' };
    case TransactionType.referral_commission:
      return { icon: ArrowUpCircleIcon, color: 'text-green-400', label: 'Referral Bonus' };
    case TransactionType.card_profit_claim:
      return { icon: ArrowUpCircleIcon, color: 'text-yellow-400', label: 'Card Profit Claim' };
    case TransactionType.card_level_up:
      return { icon: ArrowDownCircleIcon, color: 'text-red-400', label: 'Card Level Up' };
    case TransactionType.card_purchase:
      return { icon: ArrowDownCircleIcon, color: 'text-red-400', label: 'Card Purchase' };
    case TransactionType.admin_adjustment:
      return { icon: ArrowUpCircleIcon, color: 'text-blue-400', label: 'Admin Adjustment' };
    default:
      // Fallback for unknown types
      const typeString = String(type);
      const label = typeString.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase());
      return { icon: BanknotesIcon, color: 'text-gray-400', label: label };
  }
};

const WalletTabPage: React.FC<WalletTabPageProps> = ({
  transactions = [],
  isLoadingTransactions,
  user,
  onTabChange,
}) => {
  const { data: dashboardStats } = useDashboardStatsQuery();
  const [loadingDeposit, setLoadingDeposit] = useState(false);
  const [selectedAmount, setSelectedAmount] = useState<number | null>(DEPOSIT_AMOUNTS[0]);
  const [customAmount, setCustomAmount] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentUrl, setPaymentUrl] = useState('');

  const validTransactions: Transaction[] = Array.isArray(transactions) ? transactions : [];
  const showInitialLoadingSpinner = isLoadingTransactions && validTransactions.length === 0;
  const showNoTransactionsMessage = !isLoadingTransactions && validTransactions.length === 0;

  const handleDeposit = async () => {
    const amount = selectedAmount !== null ? selectedAmount : parseFloat(customAmount);
    if (!amount || isNaN(amount) || amount < 1) {
      return;
    }

    setLoadingDeposit(true);
    try {
      const response = await api.post<{ payment_url: string }>('/payments/create', {
        amount: amount,
        currency: 'USD',
        description: `Wallet top-up: $${amount}`,
      });
      setPaymentUrl(response.data.payment_url);
      setShowPaymentModal(true);
    } catch (err: any) {
      console.error('Error creating payment:', err);
    } finally {
      setLoadingDeposit(false);
    }
  };

  const renderStatus = (status?: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="w-4 h-4 text-green-400" />;
      case 'pending':
        return <ClockIcon className="w-4 h-4 text-yellow-400 animate-pulse" />;
      case 'failed':
        return <XCircleIcon className="w-4 h-4 text-red-400" />;
      default:
        return <div className="w-4 h-4 bg-gray-600 rounded-full opacity-50" />;
    }
  };

  return (
    <div className="">
      <Section>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="relative bg-gradient-to-br from-black/70 via-base-900/60 to-base-900/80 rounded-xl border border-purple-500/20 overflow-hidden p-5 backdrop-blur-sm shadow-lg shadow-purple-900/30"
        >
          <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-radial from-purple-600/30 to-transparent rounded-full blur-2xl opacity-70" />
          <div className="relative flex items-center justify-between">
            <div>
              <h2 className="text-sm font-medium text-purple-300/80 uppercase tracking-wider mb-1 font-display">
                Balance
              </h2>
              <div className="text-3xl font-bold text-white">
                ${formatCurrency(dashboardStats?.wallet_balance || 0)}
              </div>
            </div>
            <div className="w-12 h-12 bg-gradient-to-br from-purple-600/50 to-blue-600/50 rounded-full flex items-center justify-center border border-purple-500/30 shadow-inner">
              <BanknotesIcon className="w-6 h-6 text-purple-300" />
            </div>
          </div>
        </motion.div>
      </Section>

      <Section title="Add Funds">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gradient-to-br from-base-900/60 to-base-800/50 rounded-xl p-5 backdrop-blur-sm border border-accent-500/10 space-y-4"
        >
          <div className="grid grid-cols-4 gap-2">
            {DEPOSIT_AMOUNTS.map(amount => (
              <button
                key={amount}
                onClick={() => {
                  setSelectedAmount(amount);
                  setCustomAmount('');
                }}
                className={`py-3 px-1 rounded-lg text-center transition-all duration-200 text-sm font-medium border ${
                  selectedAmount === amount
                    ? 'bg-purple-600/80 border-purple-500 text-white shadow-md shadow-purple-500/20'
                    : 'bg-black/30 border-white/10 text-white/70 hover:bg-black/50 hover:border-white/20'
                }`}
              >
                ${amount}
              </button>
            ))}
          </div>
          <div className="relative">
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-purple-400 font-medium">
              $
            </span>
            <input
              type="number"
              min="1"
              value={customAmount}
              onChange={e => {
                setCustomAmount(e.target.value);
                setSelectedAmount(null);
              }}
              placeholder="Enter custom amount"
              className="w-full bg-black/40 border border-white/10 rounded-lg pl-7 pr-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:border-purple-500 focus:ring-1 focus:ring-purple-500 transition-colors"
            />
          </div>
          <button
            onClick={handleDeposit}
            disabled={loadingDeposit || (selectedAmount === null && !customAmount)}
            className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white py-3 rounded-lg
                       font-semibold transition-all duration-300 hover:from-purple-700 hover:to-blue-700
                       disabled:opacity-60 disabled:cursor-not-allowed shadow-md hover:shadow-lg shadow-purple-500/20 hover:shadow-purple-500/30 border border-transparent hover:border-purple-400/50"
          >
            {loadingDeposit ? (
              <LoadingSpinner size="sm" />
            ) : (
              <>
                <FaPlus className="w-3 h-3" /> Deposit Now
              </>
            )}
          </button>
        </motion.div>
      </Section>

      <Section title="History">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gradient-to-br from-base-900/60 to-base-800/50 rounded-xl p-5 backdrop-blur-sm border border-accent-500/10 space-y-3"
        >
          {showInitialLoadingSpinner && (
            <div className="flex justify-center items-center py-6">
              <LoadingSpinner size="md" />
              <span className="ml-2 text-white/70">Loading history...</span>
            </div>
          )}
          {showNoTransactionsMessage && (
            <div className="text-center py-6 text-sm text-white/50 font-display">
              No transactions found.
            </div>
          )}
          <div className="space-y-2 max-h-96 overflow-y-auto pr-1 styled-scrollbar">
            {validTransactions.map((transaction: Transaction) => {
              const details = getTransactionDetails(transaction.type);
              const Icon = details.icon;
              const isPositive = [
                TransactionType.wallet_topup,
                TransactionType.referral_commission,
                TransactionType.admin_adjustment,
                TransactionType.card_profit_claim,
              ].includes(transaction.type as TransactionType);
              return (
                <motion.div
                  key={transaction.id}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex items-center justify-between p-3 bg-black/20 rounded-lg border border-white/5"
                >
                  <div className="flex items-center gap-3">
                    <Icon className={`w-5 h-5 ${details.color} flex-shrink-0`} />
                    <div>
                      <div className="text-sm text-white font-medium">{details.label}</div>
                      <div className="text-xs text-white/60">
                        {new Date(transaction.created_at).toLocaleString(undefined, {
                          dateStyle: 'short',
                          timeStyle: 'short',
                        })}
                      </div>
                    </div>
                  </div>
                  <div
                    className={`text-sm font-semibold text-right ${isPositive ? 'text-green-400' : 'text-red-400'}`}
                  >
                    {isPositive ? '+' : '-'}${formatCurrency(Math.abs(transaction.amount))}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </motion.div>
      </Section>

      <AnimatePresence>
        {showPaymentModal && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-md flex items-center justify-center p-4 z-[200]"
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-gradient-to-b from-base-800 to-base-900 rounded-2xl p-6 
                            backdrop-blur-xl border border-purple-500/20 max-w-sm w-full shadow-xl shadow-purple-900/30"
            >
              <h3 className="text-lg font-semibold text-center text-white mb-3 font-display">
                Complete Deposit
              </h3>
              <p className="text-white/70 text-sm text-center mb-5 font-display">
                You will be redirected to Cryptomus to securely complete your payment of $
                {formatCurrency(
                  selectedAmount !== null ? selectedAmount : parseFloat(customAmount) || 0
                )}
                .
              </p>
              <div className="flex flex-col gap-3">
                <button
                  onClick={() => {
                    window.open(paymentUrl, '_blank');
                    setShowPaymentModal(false);
                  }}
                  className="w-full py-2.5 rounded-lg font-semibold flex items-center justify-center gap-2 text-sm bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg shadow-purple-500/20 transition-all duration-300"
                >
                  Proceed to Payment
                </button>
                <button
                  onClick={() => setShowPaymentModal(false)}
                  className="w-full py-2.5 rounded-lg font-medium text-sm bg-white/5 hover:bg-white/10 text-white/80 hover:text-white transition-colors"
                >
                  Cancel
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default WalletTabPage;
