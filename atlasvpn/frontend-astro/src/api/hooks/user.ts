/**
 * @file api/hooks/user.ts
 * @description User-related React Query hooks
 */

import { useQuery } from '@tanstack/react-query';
import type {
  UserStats,
  Transaction,
  QueryOptions
} from '../types';
import { userService } from '../services';

// Query Keys
export const USER_QUERY_KEYS = {
  USER_STATS: ['user', 'stats'],
  USER_TRANSACTIONS: ['user', 'transactions']
} as const;

// Default query options
const DEFAULT_QUERY_OPTIONS: QueryOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  retry: false
};

// User Hooks
export function useUserStatsQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: USER_QUERY_KEYS.USER_STATS,
    queryFn: () => userService.getUserStats(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function useUserTransactionsQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: USER_QUERY_KEYS.USER_TRANSACTIONS,
    queryFn: () => userService.getTransactions(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}