/**
 * @file api/services.ts
 * @description Centralized API services for AtlasVPN
 * Consolidates all API endpoints into organized service modules
 */

import { api, apiClient } from './client';
import type {
  User,
  UserStats,
  UserProfile,
  Card,
  Task,
  TaskStats,
  ReferralInfo,
  ReferredUser,
  VpnPackage,
  VpnSubscription,
  VpnPanel,
  Transaction,
  ChatMessage,
  ChatConversation,
  SuccessResponse,
  PurchaseResponse,
  RewardResponse,
  HealthCheck,
  TelegramLoginParams,
  LoginParams,
  VerifySessionData
} from './types';

// Authentication Services
export const authService = {
  // Verify current session
  async verifySession(): Promise<VerifySessionData> {
    const response = await api.get('/auth/verify-session');
    return response.data;
  },

  // Login with username/password
  async login(credentials: LoginParams): Promise<any> {
    const response = await api.post('/auth/token', credentials);
    return response.data;
  },

  // Telegram login
  async telegramLogin(params: TelegramLoginParams): Promise<any> {
    const response = await api.post('/auth/telegram/login', params);
    return response.data;
  },

  // Logout
  async logout(): Promise<SuccessResponse> {
    const response = await api.post('/auth/logout');
    return response.data;
  },

  // Refresh token
  async refreshToken(): Promise<any> {
    const response = await api.post('/auth/refresh');
    return response.data;
  },

  // Register new user
  async register(userData: any): Promise<User> {
    const response = await api.post('/auth/telegram/register', userData);
    return response.data;
  }
};

// User Services
export const userService = {
  // Get current user
  async getCurrentUser(): Promise<User> {
    return apiClient.request<User>('/user/me');
  },

  // Get user dashboard stats
  async getUserStats(): Promise<UserStats> {
    return apiClient.request<UserStats>('/user/dashboard/stats');
  },

  // Update user profile
  async updateProfile(data: Partial<UserProfile>): Promise<User> {
    return apiClient.request<User>('/user/profile/update', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // Get user transactions
  async getTransactions(): Promise<Transaction[]> {
    return apiClient.request<Transaction[]>('/user/transactions');
  }
};

// Card Services
export const cardService = {
  // Get all available cards
  async getAllCards(): Promise<Card[]> {
    return apiClient.request<Card[]>('/cards/all');
  },

  // Buy a card
  async buyCard(cardCatalogId: number): Promise<PurchaseResponse> {
    return apiClient.request<PurchaseResponse>(`/cards/buy/${cardCatalogId}`, {
      method: 'POST',
    });
  },

  // Upgrade a card
  async upgradeCard(userCardId: number): Promise<PurchaseResponse> {
    return apiClient.request<PurchaseResponse>(`/cards/${userCardId}/upgrade`, {
      method: 'POST',
    });
  },

  // Claim all card profits
  async claimAllProfits(): Promise<RewardResponse> {
    return apiClient.request<RewardResponse>('/cards/claim-all', {
      method: 'POST',
    });
  }
};

// Task Services
export const taskService = {
  // Get available tasks
  async getAvailableTasks(): Promise<Task[]> {
    return apiClient.request<Task[]>('/tasks/available');
  },

  // Start a task
  async startTask(taskId: number): Promise<SuccessResponse> {
    return apiClient.request<SuccessResponse>(`/tasks/${taskId}/start`, {
      method: 'POST',
    });
  },

  // Claim task reward
  async claimTaskReward(taskId: number): Promise<RewardResponse> {
    return apiClient.request<RewardResponse>(`/tasks/${taskId}/claim`, {
      method: 'POST',
    });
  },

  // Verify task completion
  async verifyTask(taskId: number, data?: any): Promise<SuccessResponse> {
    return apiClient.request<SuccessResponse>(`/tasks/${taskId}/verify`, {
      method: 'POST',
      body: JSON.stringify(data || {}),
    });
  },

  // Get task analytics (admin)
  async getTaskAnalytics(): Promise<TaskStats> {
    return apiClient.request<TaskStats>('/admin/tasks/analytics');
  },

  // Get daily streak
  async getDailyStreak(): Promise<any> {
    return apiClient.request('/tasks/daily-streak');
  }
};

// Referral Services
export const referralService = {
  // Get referral info
  async getReferralInfo(): Promise<ReferralInfo> {
    return apiClient.request<ReferralInfo>('/referrals/info');
  },

  // Get referred users
  async getReferredUsers(): Promise<ReferredUser[]> {
    return apiClient.request<ReferredUser[]>('/referrals/users');
  },

  // Check referral code validity
  async checkReferralCode(code: string): Promise<{ valid: boolean; user?: any }> {
    return apiClient.request<{ valid: boolean; user?: any }>(`/referrals/check/${code}`);
  }
};

// VPN Services
export const vpnService = {
  // Get available VPN packages
  async getAvailablePackages(): Promise<VpnPackage[]> {
    return apiClient.request<VpnPackage[]>('/user/available-packages');
  },

  // Get user VPN subscriptions
  async getUserSubscriptions(): Promise<VpnSubscription[]> {
    return apiClient.request<VpnSubscription[]>('/user/subscriptions');
  },

  // Purchase VPN package
  async purchasePackage(packageId: number): Promise<PurchaseResponse> {
    return apiClient.request<PurchaseResponse>(`/user/purchase/${packageId}`, {
      method: 'POST',
    });
  },

  // Get VPN panels/servers
  async getVpnPanels(): Promise<VpnPanel[]> {
    return apiClient.request<VpnPanel[]>('/vpn/panels');
  }
};

// Chat Services
export const chatService = {
  // Get chat conversations
  async getConversations(): Promise<ChatConversation[]> {
    return apiClient.request<ChatConversation[]>('/chat/conversations');
  },

  // Get conversation messages
  async getConversationMessages(conversationId: number): Promise<ChatMessage[]> {
    return apiClient.request<ChatMessage[]>(`/chat/conversations/${conversationId}/messages`);
  },

  // Send message
  async sendMessage(conversationId: number, content: string): Promise<ChatMessage> {
    return apiClient.request<ChatMessage>(`/chat/conversations/${conversationId}/messages`, {
      method: 'POST',
      body: JSON.stringify({ content }),
    });
  },

  // Create new conversation
  async createConversation(title: string): Promise<ChatConversation> {
    return apiClient.request<ChatConversation>('/chat/conversations', {
      method: 'POST',
      body: JSON.stringify({ title }),
    });
  }
};

// System Services
export const systemService = {
  // Health check
  async healthCheck(): Promise<HealthCheck> {
    return apiClient.request<HealthCheck>('/health');
  },

  // Get system status
  async getSystemStatus(): Promise<any> {
    return apiClient.request('/system/status');
  }
};

// Consolidated API object for backward compatibility
export const api_services = {
  // User operations
  getCurrentUser: () => userService.getCurrentUser(),
  getUserStats: () => userService.getUserStats(),
  updateProfile: (data: Partial<UserProfile>) => userService.updateProfile(data),
  getTransactions: () => userService.getTransactions(),

  // Authentication operations
  verifySession: () => authService.verifySession(),
  login: (credentials: LoginParams) => authService.login(credentials),
  telegramLogin: (params: TelegramLoginParams) => authService.telegramLogin(params),
  logout: () => authService.logout(),
  refreshToken: () => authService.refreshToken(),

  // Cards operations
  getAllCards: () => cardService.getAllCards(),
  buyCard: (cardId: number) => cardService.buyCard(cardId),
  upgradeCard: (cardId: number) => cardService.upgradeCard(cardId),
  claimAllProfits: () => cardService.claimAllProfits(),

  // Tasks operations
  getTasks: () => taskService.getAvailableTasks(),
  startTask: (taskId: number) => taskService.startTask(taskId),
  claimTask: (taskId: number) => taskService.claimTaskReward(taskId),
  verifyTask: (taskId: number, data?: any) => taskService.verifyTask(taskId, data),
  getDailyStreak: () => taskService.getDailyStreak(),

  // Referrals operations
  getReferralInfo: () => referralService.getReferralInfo(),
  getReferredUsers: () => referralService.getReferredUsers(),
  checkReferralCode: (code: string) => referralService.checkReferralCode(code),

  // VPN operations
  getPackages: () => vpnService.getAvailablePackages(),
  getSubscriptions: () => vpnService.getUserSubscriptions(),
  purchasePackage: (packageId: number) => vpnService.purchasePackage(packageId),
  getVpnPanels: () => vpnService.getVpnPanels(),

  // Chat operations
  getConversations: () => chatService.getConversations(),
  getConversationMessages: (id: number) => chatService.getConversationMessages(id),
  sendMessage: (id: number, content: string) => chatService.sendMessage(id, content),
  createConversation: (title: string) => chatService.createConversation(title),

  // System operations
  healthCheck: () => systemService.healthCheck(),
  getSystemStatus: () => systemService.getSystemStatus(),
};

// Export all services
export {
  authService as auth,
  userService as user,
  cardService as card,
  taskService as task,
  referralService as referral,
  vpnService as vpn,
  chatService as chat,
  systemService as system
};