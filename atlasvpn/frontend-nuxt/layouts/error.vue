<template>
  <div class="error-layout">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="bg-orb bg-orb-error-1"></div>
      <div class="bg-orb bg-orb-error-2"></div>
    </div>

    <!-- Error Content -->
    <main class="error-content">
      <div class="container-responsive">
        <div class="error-card">
          <!-- Error Icon -->
          <div class="error-icon">
            <Icon name="i-heroicons-exclamation-triangle" class="icon" />
          </div>

          <!-- Error Details -->
          <div class="error-details">
            <h1 class="error-title">{{ error.statusCode || 'Error' }}</h1>
            <p class="error-message">{{ error.statusMessage || 'Something went wrong' }}</p>
            
            <!-- Error Description -->
            <div class="error-description">
              <p v-if="error.statusCode === 404">
                The page you're looking for doesn't exist or has been moved.
              </p>
              <p v-else-if="error.statusCode === 500">
                We're experiencing technical difficulties. Please try again later.
              </p>
              <p v-else>
                An unexpected error occurred. Please try refreshing the page.
              </p>
            </div>

            <!-- Action Buttons -->
            <div class="error-actions">
              <UButton
                @click="handleError"
                size="lg"
                class="btn-primary-enhanced"
              >
                <Icon name="i-heroicons-arrow-path" class="w-5 h-5 mr-2" />
                Try Again
              </UButton>
              
              <UButton
                @click="goHome"
                variant="outline"
                size="lg"
                class="glass-card-subtle border-atlas-purple-500 text-atlas-purple-300"
              >
                <Icon name="i-heroicons-home" class="w-5 h-5 mr-2" />
                Go Home
              </UButton>
            </div>

            <!-- Debug Info (Development Only) -->
            <div v-if="isDevelopment && error.stack" class="debug-info">
              <details class="debug-details">
                <summary class="debug-summary">Debug Information</summary>
                <pre class="debug-stack">{{ error.stack }}</pre>
              </details>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
interface ErrorProps {
  error: {
    statusCode?: number
    statusMessage?: string
    message?: string
    stack?: string
  }
}

const props = defineProps<ErrorProps>()

const isDevelopment = process.env.NODE_ENV === 'development'

const handleError = () => {
  clearError({ redirect: '/' })
}

const goHome = () => {
  navigateTo('/')
}

// Set page meta
useHead({
  title: `Error ${props.error.statusCode || ''} - AtlasVPN`,
  meta: [
    { name: 'robots', content: 'noindex' }
  ]
})
</script>

<style scoped>
.error-layout {
  min-height: 100vh;
  background-color: var(--color-atlas-black);
  color: var(--ui-text);
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.bg-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.3;
}

.bg-orb-error-1 {
  top: 20%;
  left: 20%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, #ef4444 0%, transparent 70%);
  animation: pulse 4s ease-in-out infinite;
}

.bg-orb-error-2 {
  bottom: 20%;
  right: 20%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, var(--color-atlas-orange) 0%, transparent 70%);
  animation: pulse 6s ease-in-out infinite;
  animation-delay: -2s;
}

/* Error Content */
.error-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-8) 0;
}

.error-card {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 1.5rem;
  padding: var(--spacing-12);
  text-align: center;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

.error-icon {
  margin-bottom: var(--spacing-8);
}

.error-icon .icon {
  width: 80px;
  height: 80px;
  color: #ef4444;
  filter: drop-shadow(0 0 20px rgba(239, 68, 68, 0.3));
}

.error-details {
  space-y: var(--spacing-6);
}

.error-title {
  font-size: var(--font-size-6xl);
  font-weight: 800;
  color: #ef4444;
  margin-bottom: var(--spacing-4);
  text-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.error-message {
  font-size: var(--font-size-2xl);
  font-weight: 600;
  color: var(--ui-text);
  margin-bottom: var(--spacing-6);
}

.error-description {
  margin-bottom: var(--spacing-8);
}

.error-description p {
  font-size: var(--font-size-lg);
  color: var(--ui-text-muted);
  line-height: var(--line-height-relaxed);
}

.error-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  align-items: center;
  margin-bottom: var(--spacing-8);
}

.debug-info {
  margin-top: var(--spacing-8);
  text-align: left;
}

.debug-details {
  background: var(--color-atlas-darker);
  border: 1px solid var(--color-atlas-dark);
  border-radius: 0.5rem;
  padding: var(--spacing-4);
}

.debug-summary {
  font-weight: 600;
  color: var(--color-atlas-orange);
  cursor: pointer;
  margin-bottom: var(--spacing-2);
}

.debug-stack {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: var(--font-size-xs);
  color: var(--ui-text-muted);
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
  padding: var(--spacing-2);
  background: var(--color-atlas-deep);
  border-radius: 0.25rem;
  overflow-x: auto;
}

/* Responsive Design */
@media (min-width: 640px) {
  .error-actions {
    flex-direction: row;
    justify-content: center;
  }
}

@media (max-width: 639px) {
  .error-card {
    padding: var(--spacing-8);
    margin: var(--spacing-4);
  }

  .error-icon .icon {
    width: 60px;
    height: 60px;
  }

  .error-title {
    font-size: var(--font-size-4xl);
  }

  .error-message {
    font-size: var(--font-size-xl);
  }

  .error-description p {
    font-size: var(--font-size-base);
  }

  .bg-orb-error-1,
  .bg-orb-error-2 {
    width: 200px;
    height: 200px;
    filter: blur(40px);
  }
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .bg-orb-error-1,
  .bg-orb-error-2 {
    animation: none;
  }
}
</style>
