import React from 'react';
import QueryProvider from './QueryProvider';
import ToastProvider from './ToastProvider';
import NewUserSetupInteractive from './NewUserSetupInteractive';

interface NewUserSetupWrapperProps {
  onNavigate: (path: string) => void;
}

const NewUserSetupWrapper: React.FC<NewUserSetupWrapperProps> = ({ onNavigate }) => {
  return (
    <QueryProvider>
      <ToastProvider>
        <NewUserSetupInteractive onNavigate={onNavigate} />
      </ToastProvider>
    </QueryProvider>
  );
};

export default NewUserSetupWrapper;
