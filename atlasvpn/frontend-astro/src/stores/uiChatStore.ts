/**
 * UI Chat Store - Lightweight Zustand store for chat UI state management
 * Migrated from original frontend
 */

import { create } from 'zustand';

export type ChatViewMode = 'list' | 'conversation' | 'settings';

interface UIChatState {
  // Selected chat
  selectedChatId: string | null;
  setSelectedChat: (chatId: string | null) => void;
  
  // View mode
  viewMode: ChatViewMode;
  setViewMode: (mode: ChatViewMode) => void;
  
  // UI states
  isSidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  
  isTyping: boolean;
  setTyping: (typing: boolean) => void;
  
  // Message input
  messageInput: string;
  setMessageInput: (input: string) => void;
  
  // Scroll position
  scrollPosition: number;
  setScrollPosition: (position: number) => void;
  
  // Notifications
  unreadCount: number;
  setUnreadCount: (count: number) => void;
  incrementUnreadCount: () => void;
  resetUnreadCount: () => void;
  
  // Modal states
  isEmojiPickerOpen: boolean;
  setEmojiPickerOpen: (open: boolean) => void;
  
  isAttachmentMenuOpen: boolean;
  setAttachmentMenuOpen: (open: boolean) => void;
  
  // Search
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  
  isSearchMode: boolean;
  setSearchMode: (searchMode: boolean) => void;
  
  // Reset function
  resetUIChat: () => void;
}

const initialState = {
  selectedChatId: null,
  viewMode: 'list' as ChatViewMode,
  isSidebarOpen: true,
  isTyping: false,
  messageInput: '',
  scrollPosition: 0,
  unreadCount: 0,
  isEmojiPickerOpen: false,
  isAttachmentMenuOpen: false,
  searchQuery: '',
  isSearchMode: false,
};

export const useUIChatStore = create<UIChatState>()((set, get) => ({
  ...initialState,
  
  setSelectedChat: (chatId: string | null) => set({ selectedChatId: chatId }),
  
  setViewMode: (mode: ChatViewMode) => set({ viewMode: mode }),
  
  setSidebarOpen: (open: boolean) => set({ isSidebarOpen: open }),
  
  setTyping: (typing: boolean) => set({ isTyping: typing }),
  
  setMessageInput: (input: string) => set({ messageInput: input }),
  
  setScrollPosition: (position: number) => set({ scrollPosition: position }),
  
  setUnreadCount: (count: number) => set({ unreadCount: count }),
  
  incrementUnreadCount: () => set((state) => ({ 
    unreadCount: state.unreadCount + 1 
  })),
  
  resetUnreadCount: () => set({ unreadCount: 0 }),
  
  setEmojiPickerOpen: (open: boolean) => set({ isEmojiPickerOpen: open }),
  
  setAttachmentMenuOpen: (open: boolean) => set({ isAttachmentMenuOpen: open }),
  
  setSearchQuery: (query: string) => set({ searchQuery: query }),
  
  setSearchMode: (searchMode: boolean) => set({ isSearchMode: searchMode }),
  
  resetUIChat: () => set(initialState),
}));

// Export store instance for direct access if needed
export default useUIChatStore;