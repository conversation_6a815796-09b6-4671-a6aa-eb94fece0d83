"""
Admin Tasks Router

Handles all task management functionality including:
- Task CRUD operations
- Task completion management  
- Task verification and approval
- Task statistics and analytics
- Task pack management
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.orm import selectinload, joinedload
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import logging

from auth import get_current_admin
from database import get_async_db
from models import (
    Task, TaskCompletion, TaskPack, TaskPackTask, 
    User, Transaction, TransactionType, Role,
    TaskVerification, TaskType
)
from schemas import (
    TaskOut, TaskCreate, TaskUpdate,
    TaskCompletionOut, TaskPackOut, TaskPackCreate,
    UserOut
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/tasks",
    tags=["Admin - Tasks"]
)

@router.get("/", response_model=List[TaskOut])
async def get_all_tasks(
    skip: int = 0,
    limit: int = 100,
    task_type: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Get all tasks with filtering options"""
    try:
        query = select(Task).options(selectinload(Task.completions))
        
        # Apply filters
        filters = []
        if task_type:
            filters.append(Task.task_type == task_type)
        if is_active is not None:
            filters.append(Task.is_active == is_active)
            
        if filters:
            query = query.where(and_(*filters))
            
        query = query.offset(skip).limit(limit).order_by(desc(Task.created_at))
        
        result = await db.execute(query)
        tasks = result.scalars().all()
        
        # Calculate completion stats for each task
        enriched_tasks = []
        for task in tasks:
            task_dict = {
                "id": task.id,
                "title": task.title,
                "description": task.description,
                "task_type": task.task_type,
                "reward_amount": float(task.reward_amount),
                "is_active": task.is_active,
                "created_at": task.created_at.isoformat(),
                "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                "verification_method": task.verification_method,
                "completion_count": len(task.completions),
                "unique_users": len(set(c.user_id for c in task.completions)),
                "completion_rate": 0.0
            }
            
            # Calculate completion rate if we have user count
            total_users_query = select(func.count(User.id)).where(User.role == Role.user)
            total_users_result = await db.execute(total_users_query)
            total_users = total_users_result.scalar()
            
            if total_users > 0:
                task_dict["completion_rate"] = round((task_dict["unique_users"] / total_users) * 100, 2)
                
            enriched_tasks.append(task_dict)
        
        return enriched_tasks
        
    except Exception as e:
        logger.error(f"Error fetching tasks: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/", response_model=TaskOut)
async def create_task(
    task: TaskCreate,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Create a new task"""
    try:
        db_task = Task(
            title=task.title,
            description=task.description,
            task_type=task.task_type,
            reward_amount=task.reward_amount,
            is_active=task.is_active if task.is_active is not None else True,
            verification_method=task.verification_method,
            created_at=datetime.utcnow()
        )
        
        db.add(db_task)
        await db.commit()
        await db.refresh(db_task)
        
        logger.info(f"Admin {current_admin.username} created task: {db_task.title}")
        
        return {
            "id": db_task.id,
            "title": db_task.title,
            "description": db_task.description,
            "task_type": db_task.task_type,
            "reward_amount": float(db_task.reward_amount),
            "is_active": db_task.is_active,
            "created_at": db_task.created_at.isoformat(),
            "updated_at": db_task.updated_at.isoformat() if db_task.updated_at else None,
            "verification_method": db_task.verification_method,
            "completion_count": 0,
            "unique_users": 0,
            "completion_rate": 0.0
        }
        
    except Exception as e:
        logger.error(f"Error creating task: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{task_id}", response_model=TaskOut)
async def get_task(
    task_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Get a specific task by ID"""
    try:
        query = select(Task).options(
            selectinload(Task.completions).selectinload(TaskCompletion.user)
        ).where(Task.id == task_id)
        
        result = await db.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        return {
            "id": task.id,
            "title": task.title,
            "description": task.description,
            "task_type": task.task_type,
            "reward_amount": float(task.reward_amount),
            "is_active": task.is_active,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat() if task.updated_at else None,
            "verification_method": task.verification_method,
            "completion_count": len(task.completions),
            "unique_users": len(set(c.user_id for c in task.completions)),
            "completion_rate": 0.0
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching task {task_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{task_id}", response_model=TaskOut)
async def update_task(
    task_id: int,
    task_update: TaskUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Update a task"""
    try:
        query = select(Task).where(Task.id == task_id)
        result = await db.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Update fields
        update_data = task_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(task, field, value)
        
        task.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(task)
        
        logger.info(f"Admin {current_admin.username} updated task {task_id}")
        
        return {
            "id": task.id,
            "title": task.title,
            "description": task.description,
            "task_type": task.task_type,
            "reward_amount": float(task.reward_amount),
            "is_active": task.is_active,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat() if task.updated_at else None,
            "verification_method": task.verification_method,
            "completion_count": 0,
            "unique_users": 0,
            "completion_rate": 0.0
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating task {task_id}: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{task_id}")
async def delete_task(
    task_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Delete a task (soft delete by setting is_active to False)"""
    try:
        query = select(Task).where(Task.id == task_id)
        result = await db.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Soft delete by setting is_active to False
        task.is_active = False
        task.updated_at = datetime.utcnow()
        
        await db.commit()
        
        logger.info(f"Admin {current_admin.username} deleted task {task_id}")
        
        return {"message": "Task deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting task {task_id}: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{task_id}/toggle")
async def toggle_task(
    task_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Toggle task active status"""
    try:
        query = select(Task).where(Task.id == task_id)
        result = await db.execute(query)
        task = result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(status_code=404, detail="Task not found")
        
        # Toggle active status
        task.is_active = not task.is_active
        task.updated_at = datetime.utcnow()
        
        await db.commit()
        
        status_text = "activated" if task.is_active else "deactivated"
        logger.info(f"Admin {current_admin.username} {status_text} task {task_id}")
        
        return {
            "message": f"Task {status_text} successfully",
            "is_active": task.is_active
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error toggling task {task_id}: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/completions", response_model=List[TaskCompletionOut])
async def get_task_completions(
    skip: int = 0,
    limit: int = 100,
    task_id: Optional[int] = None,
    user_id: Optional[int] = None,
    is_approved: Optional[bool] = None,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Get task completions with filtering"""
    try:
        query = select(TaskCompletion).options(
            selectinload(TaskCompletion.task),
            selectinload(TaskCompletion.user)
        )
        
        # Apply filters
        filters = []
        if task_id:
            filters.append(TaskCompletion.task_id == task_id)
        if user_id:
            filters.append(TaskCompletion.user_id == user_id)
        if is_approved is not None:
            filters.append(TaskCompletion.is_approved == is_approved)
            
        if filters:
            query = query.where(and_(*filters))
            
        query = query.offset(skip).limit(limit).order_by(desc(TaskCompletion.completed_at))
        
        result = await db.execute(query)
        completions = result.scalars().all()
        
        return completions
        
    except Exception as e:
        logger.error(f"Error fetching task completions: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/completions/{completion_id}/approve")
async def approve_completion(
    completion_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Approve a task completion and grant reward"""
    try:
        query = select(TaskCompletion).options(
            selectinload(TaskCompletion.task),
            selectinload(TaskCompletion.user)
        ).where(TaskCompletion.id == completion_id)
        
        result = await db.execute(query)
        completion = result.scalar_one_or_none()
        
        if not completion:
            raise HTTPException(status_code=404, detail="Task completion not found")
        
        if completion.is_approved:
            raise HTTPException(status_code=400, detail="Task completion already approved")
        
        # Approve completion
        completion.is_approved = True
        completion.approved_at = datetime.utcnow()
        completion.approved_by = current_admin.id
        
        # Grant reward to user
        user = completion.user
        user.wallet_balance += completion.task.reward_amount
        
        # Create transaction record
        transaction = Transaction(
            user_id=user.id,
            type=TransactionType.task_reward,
            amount=completion.task.reward_amount,
            description=f"Task completion reward: {completion.task.title}",
            created_at=datetime.utcnow()
        )
        db.add(transaction)
        
        await db.commit()
        
        logger.info(f"Admin {current_admin.username} approved task completion {completion_id}")
        
        return {
            "message": "Task completion approved and reward granted",
            "reward_amount": float(completion.task.reward_amount),
            "user_new_balance": float(user.wallet_balance)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error approving task completion {completion_id}: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/completions/{completion_id}/reject")
async def reject_completion(
    completion_id: int,
    reason: str,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Reject a task completion"""
    try:
        query = select(TaskCompletion).where(TaskCompletion.id == completion_id)
        result = await db.execute(query)
        completion = result.scalar_one_or_none()
        
        if not completion:
            raise HTTPException(status_code=404, detail="Task completion not found")
        
        if completion.is_approved:
            raise HTTPException(status_code=400, detail="Cannot reject an already approved completion")
        
        # Reject completion
        completion.is_approved = False
        completion.rejection_reason = reason
        completion.rejected_at = datetime.utcnow()
        completion.rejected_by = current_admin.id
        
        await db.commit()
        
        logger.info(f"Admin {current_admin.username} rejected task completion {completion_id}")
        
        return {"message": "Task completion rejected"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error rejecting task completion {completion_id}: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats", response_model=Dict[str, Any])
async def get_task_stats(
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Get comprehensive task statistics"""
    try:
        # Total tasks
        total_tasks_query = select(func.count(Task.id))
        total_tasks_result = await db.execute(total_tasks_query)
        total_tasks = total_tasks_result.scalar()
        
        # Active tasks
        active_tasks_query = select(func.count(Task.id)).where(Task.is_active == True)
        active_tasks_result = await db.execute(active_tasks_query)
        active_tasks = active_tasks_result.scalar()
        
        # Total completions
        total_completions_query = select(func.count(TaskCompletion.id))
        total_completions_result = await db.execute(total_completions_query)
        total_completions = total_completions_result.scalar()
        
        # Approved completions
        approved_completions_query = select(func.count(TaskCompletion.id)).where(
            TaskCompletion.is_approved == True
        )
        approved_completions_result = await db.execute(approved_completions_query)
        approved_completions = approved_completions_result.scalar()
        
        # Pending completions
        pending_completions_query = select(func.count(TaskCompletion.id)).where(
            TaskCompletion.is_approved.is_(None)
        )
        pending_completions_result = await db.execute(pending_completions_query)
        pending_completions = pending_completions_result.scalar()
        
        # Total rewards distributed
        total_rewards_query = select(func.sum(Task.reward_amount)).select_from(
            Task.__table__.join(TaskCompletion.__table__)
        ).where(TaskCompletion.is_approved == True)
        total_rewards_result = await db.execute(total_rewards_query)
        total_rewards = total_rewards_result.scalar() or 0
        
        # Unique users who completed tasks
        unique_users_query = select(func.count(func.distinct(TaskCompletion.user_id)))
        unique_users_result = await db.execute(unique_users_query)
        unique_users = unique_users_result.scalar()
        
        # Average completion time (for approved tasks)
        avg_completion_time = None
        try:
            avg_time_query = select(func.avg(
                func.julianday(TaskCompletion.approved_at) - func.julianday(TaskCompletion.completed_at)
            )).where(
                and_(
                    TaskCompletion.is_approved == True,
                    TaskCompletion.approved_at.is_not(None)
                )
            )
            avg_time_result = await db.execute(avg_time_query)
            avg_days = avg_time_result.scalar()
            if avg_days:
                avg_completion_time = round(avg_days * 24, 2)  # Convert to hours
        except Exception as e:
            logger.warning(f"Could not calculate average completion time: {str(e)}")
        
        # Task type distribution
        task_types_query = select(
            Task.task_type,
            func.count(Task.id).label('count')
        ).group_by(Task.task_type)
        task_types_result = await db.execute(task_types_query)
        task_types = {row.task_type: row.count for row in task_types_result}
        
        # Recent activity (last 7 days)
        seven_days_ago = datetime.utcnow() - timedelta(days=7)
        recent_completions_query = select(func.count(TaskCompletion.id)).where(
            TaskCompletion.completed_at >= seven_days_ago
        )
        recent_completions_result = await db.execute(recent_completions_query)
        recent_completions = recent_completions_result.scalar()
        
        return {
            "total_tasks": total_tasks,
            "active_tasks": active_tasks,
            "total_completions": total_completions,
            "approved_completions": approved_completions,
            "pending_completions": pending_completions,
            "rejected_completions": total_completions - approved_completions - pending_completions,
            "approval_rate": round((approved_completions / total_completions * 100), 2) if total_completions > 0 else 0,
            "total_rewards_distributed": float(total_rewards),
            "unique_users_participated": unique_users,
            "average_completion_time_hours": avg_completion_time,
            "task_types_distribution": task_types,
            "recent_completions_7_days": recent_completions,
            "engagement_rate": round((unique_users / total_tasks * 100), 2) if total_tasks > 0 else 0
        }
        
    except Exception as e:
        logger.error(f"Error fetching task statistics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Task Pack Management Routes

@router.get("/packs", response_model=List[TaskPackOut])
async def get_task_packs(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Get all task packs"""
    try:
        query = select(TaskPack).options(selectinload(TaskPack.tasks))
        
        if is_active is not None:
            query = query.where(TaskPack.is_active == is_active)
            
        query = query.offset(skip).limit(limit).order_by(desc(TaskPack.created_at))
        
        result = await db.execute(query)
        packs = result.scalars().all()
        
        return packs
        
    except Exception as e:
        logger.error(f"Error fetching task packs: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/packs", response_model=TaskPackOut)
async def create_task_pack(
    pack: TaskPackCreate,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Create a new task pack"""
    try:
        db_pack = TaskPack(
            name=pack.name,
            description=pack.description,
            vpn_package_id=pack.vpn_package_id,
            is_lifetime=pack.is_lifetime,
            is_active=pack.is_active if pack.is_active is not None else True,
            created_at=datetime.utcnow()
        )
        
        db.add(db_pack)
        await db.commit()
        await db.refresh(db_pack)
        
        logger.info(f"Admin {current_admin.username} created task pack: {db_pack.name}")
        
        return db_pack
        
    except Exception as e:
        logger.error(f"Error creating task pack: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/packs/{pack_id}", response_model=TaskPackOut)
async def update_task_pack(
    pack_id: int,
    pack_update: TaskPackCreate,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Update a task pack"""
    try:
        query = select(TaskPack).where(TaskPack.id == pack_id)
        result = await db.execute(query)
        pack = result.scalar_one_or_none()
        
        if not pack:
            raise HTTPException(status_code=404, detail="Task pack not found")
        
        # Update fields
        update_data = pack_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(pack, field, value)
        
        pack.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(pack)
        
        logger.info(f"Admin {current_admin.username} updated task pack {pack_id}")
        
        return pack
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating task pack {pack_id}: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/packs/{pack_id}")
async def delete_task_pack(
    pack_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_admin: User = Depends(get_current_admin)
):
    """Delete a task pack (soft delete)"""
    try:
        query = select(TaskPack).where(TaskPack.id == pack_id)
        result = await db.execute(query)
        pack = result.scalar_one_or_none()
        
        if not pack:
            raise HTTPException(status_code=404, detail="Task pack not found")
        
        # Soft delete
        pack.is_active = False
        pack.updated_at = datetime.utcnow()
        
        await db.commit()
        
        logger.info(f"Admin {current_admin.username} deleted task pack {pack_id}")
        
        return {"message": "Task pack deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting task pack {pack_id}: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e)) 