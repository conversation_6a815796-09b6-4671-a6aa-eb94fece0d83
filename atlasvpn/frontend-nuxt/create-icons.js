const fs = require('fs');
const path = require('path');

// Create a simple SVG icon for AtlasVPN
const createSVGIcon = (size) => `
<svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#7c3aed;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="${size}" height="${size}" rx="${size * 0.2}" fill="#000000"/>
  <circle cx="${size/2}" cy="${size/2}" r="${size * 0.3}" fill="url(#grad)"/>
  <path d="M${size*0.35} ${size*0.4} L${size*0.5} ${size*0.25} L${size*0.65} ${size*0.4} L${size*0.65} ${size*0.6} L${size*0.5} ${size*0.75} L${size*0.35} ${size*0.6} Z" fill="white"/>
</svg>`;

// Convert SVG to PNG using a simple approach (for demo purposes)
// In production, you'd use a proper image conversion library
const createIconFile = (size, filename) => {
  const svg = createSVGIcon(size);
  const publicDir = path.join(__dirname, 'public');

  // For now, we'll create SVG files and rename them to PNG
  // In a real scenario, you'd convert SVG to PNG
  fs.writeFileSync(path.join(publicDir, filename), svg);
  console.log(`Created ${filename}`);
};

// Create all required icon sizes
const iconSizes = [
  { size: 72, filename: 'icon-72x72.png' },
  { size: 96, filename: 'icon-96x96.png' },
  { size: 128, filename: 'icon-128x128.png' },
  { size: 144, filename: 'icon-144x144.png' },
  { size: 152, filename: 'icon-152x152.png' },
  { size: 192, filename: 'icon-192x192.png' },
  { size: 384, filename: 'icon-384x384.png' },
  { size: 512, filename: 'icon-512x512.png' }
];

// Create favicon.ico (simplified)
const createFavicon = () => {
  const svg = createSVGIcon(32);
  fs.writeFileSync(path.join(__dirname, 'public', 'favicon.ico'), svg);
  console.log('Created favicon.ico');
};

// Create all icons
iconSizes.forEach(({ size, filename }) => {
  createIconFile(size, filename);
});

createFavicon();

console.log('All PWA icons created successfully!');