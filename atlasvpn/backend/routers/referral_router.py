from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, select
from typing import List
from database import get_async_db
from models import User, Transaction, TransactionType
from schemas import ReferralInfo, ReferredUser
from auth import get_current_user
from services.referral_service import ReferralService
import os

router = APIRouter(prefix="/api/referrals", tags=["referrals"])

@router.get("/info", response_model=ReferralInfo)
async def get_referral_info(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get user's referral information"""
    referral_service = ReferralService(db)
    
    try:
        referral_info = await referral_service.get_referral_info(current_user.id)

        # Generate referral link using web app URL - remove /app
        bot_username = os.getenv("BOT_USERNAME", "your_bot_username")
        referral_link = f"https://t.me/{bot_username}?startapp=ref_{current_user.referral_code}"

        # Add referral link to the response (Ensure referral_info is a dict or handles item assignment)
        # Assuming referral_service.get_referral_info returns a dict-like object or Pydantic model
        if isinstance(referral_info, dict):
            referral_info["referral_link"] = referral_link
        elif hasattr(referral_info, 'referral_link'): # Handle Pydantic model case
             # If it's a Pydantic model, we might need to return a new object or modify it
             # For simplicity, let's assume it returns a dict for now or the model has the field.
             # If ReferralInfo schema doesn't have referral_link, this will need adjustment.
             # Check frontend/src/types/referral.ts - it DOES expect referral_link
             setattr(referral_info, 'referral_link', referral_link)
        else:
             # Fallback: create a new dict if the type is unexpected
             referral_info = {
                 **referral_info.__dict__, # Attempt to convert if it's an object
                 "referral_link": referral_link
             }

        return referral_info

    except Exception as e:
        # logger.error(f"Error getting referral info for user {current_user.id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get referral info: {str(e)}"
        )

@router.get("/users", response_model=List[ReferredUser])
async def get_referred_users(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_async_db)
):
    """Get list of users referred by current user"""
    referral_service = ReferralService(db)
    
    try:
        return await referral_service.get_referred_users(current_user.id)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get referred users: {str(e)}"
        )

@router.get("/check/{code}")
async def check_referral_code(
    code: str,
    db: AsyncSession = Depends(get_async_db)
):
    """Check if a referral code is valid"""
    referral_service = ReferralService(db)
    
    try:
        user = await referral_service.check_referral_code(code)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Invalid referral code"
            )

        return {
            "valid": True,
            "referrer_username": user.username
        }

    except HTTPException:
        raise
    except Exception as e:
        # logger.error(f"Error checking referral code '{code}': {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check referral code: {str(e)}"
        )

# This endpoint seems problematic: It's processing commission based on arbitrary user_id and amount,
# which looks like a security risk or something that should be triggered internally by other events (like payment webhook)
# Commenting out for now - needs review and likely redesign.
# @router.post("/process-commission")
# async def process_referral_commission(
#     user_id: int,
#     amount: float,
#     db: AsyncSession = Depends(get_async_db)
# ):
#     """Process referral commission when referred user makes a purchase"""
#     user = await db.get(User, user_id)
#     if not user or not user.referred_by:
#         return {"success": False, "message": "User not found or not referred"}

#     referrer = await db.get(User, user.referred_by)
#     if not referrer or not referrer.is_active:
#         return {"success": False, "message": "Referrer not found or inactive"}

#     # Calculate commission (e.g., 10% of purchase)
#     commission = amount * 0.10

#     # Create commission transaction
#     transaction = Transaction(
#         user_id=referrer.id,
#         type=TransactionType.referral_commission,
#         amount=commission,
#         description=f"Referral commission from {user.username}'s purchase",
#         balance_after=referrer.wallet_balance + commission,
#         status="completed"
#     )

#     # Update referrer's wallet balance
#     referrer.wallet_balance += commission

#     db.add(transaction)
#     await db.commit()

#     return {
#         "success": True,
#         "commission_amount": commission,
#         "referrer_id": referrer.id
#     } 