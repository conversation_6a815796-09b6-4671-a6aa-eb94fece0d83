/**
 * @file setup.ts
 * @description Test setup and global mocks for Vitest
 */

import '@testing-library/jest-dom';
import { vi, beforeEach } from 'vitest';

// Mock browser APIs
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock Telegram WebApp
const mockTelegramWebApp = {
  initData: '',
  initDataUnsafe: {},
  version: '6.7',
  colorScheme: 'dark',
  themeParams: {
    accent_text_color: '#6ab7ff',
    bg_color: '#17212b',
    button_color: '#5288c1',
    button_text_color: '#ffffff',
    destructive_text_color: '#ec3942',
    header_bg_color: '#17212b',
    hint_color: '#708499',
    link_color: '#6ab7ff',
    secondary_bg_color: '#232e3c',
    section_bg_color: '#17212b',
    section_header_text_color: '#6ab7ff',
    subtitle_text_color: '#708499',
    text_color: '#f5f5f5',
  },
  isExpanded: true,
  viewportHeight: 675,
  viewportStableHeight: 675,
  headerColor: '#17212b',
  backgroundColor: '#17212b',
  isClosingConfirmationEnabled: false,
  isVerticalSwipesEnabled: true,
  isActive: true,
  isFullscreen: false,
  orientation: 'portrait',
  safeAreaInset: {
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  contentSafeAreaInset: {
    top: 0,
    bottom: 0,
    left: 0,
    right: 0,
  },
  ready: vi.fn(),
  expand: vi.fn(),
  close: vi.fn(),
  setHeaderColor: vi.fn(),
  setBackgroundColor: vi.fn(),
  enableClosingConfirmation: vi.fn(),
  disableClosingConfirmation: vi.fn(),
  enableVerticalSwipes: vi.fn(),
  disableVerticalSwipes: vi.fn(),
  requestWriteAccess: vi.fn(),
  requestContact: vi.fn(),
  HapticFeedback: {
    impactOccurred: vi.fn(),
    notificationOccurred: vi.fn(),
    selectionChanged: vi.fn(),
  },
  CloudStorage: {
    setItem: vi.fn(),
    getItem: vi.fn(),
    getItems: vi.fn(),
    removeItem: vi.fn(),
    removeItems: vi.fn(),
    getKeys: vi.fn(),
  },
  MainButton: {
    text: '',
    color: '#2481cc',
    textColor: '#ffffff',
    isVisible: false,
    isActive: true,
    isProgressVisible: false,
    setText: vi.fn(),
    onClick: vi.fn(),
    onEvent: vi.fn(),
    offEvent: vi.fn(),
    show: vi.fn(),
    hide: vi.fn(),
    enable: vi.fn(),
    disable: vi.fn(),
    showProgress: vi.fn(),
    hideProgress: vi.fn(),
    setParams: vi.fn(),
  },
  BackButton: {
    isVisible: false,
    onClick: vi.fn(),
    onEvent: vi.fn(),
    offEvent: vi.fn(),
    show: vi.fn(),
    hide: vi.fn(),
  },
  onEvent: vi.fn(),
  offEvent: vi.fn(),
  sendData: vi.fn(),
  switchInlineQuery: vi.fn(),
  openLink: vi.fn(),
  openTelegramLink: vi.fn(),
  openInvoice: vi.fn(),
  showPopup: vi.fn(),
  showAlert: vi.fn(),
  showConfirm: vi.fn(),
  showScanQrPopup: vi.fn(),
  closeScanQrPopup: vi.fn(),
  readTextFromClipboard: vi.fn(),
  requestFullscreen: vi.fn(),
  exitFullscreen: vi.fn(),
};

Object.defineProperty(window, 'Telegram', {
  value: {
    WebApp: mockTelegramWebApp,
  },
});

// Mock performance API
Object.defineProperty(window, 'performance', {
  value: {
    now: vi.fn(() => Date.now()),
    mark: vi.fn(),
    measure: vi.fn(),
    getEntriesByType: vi.fn(() => []),
    getEntriesByName: vi.fn(() => []),
  },
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
  root: null,
  rootMargin: '',
  thresholds: [],
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock fetch
global.fetch = vi.fn();

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
};

// Reset all mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
  localStorageMock.clear();
  sessionStorageMock.clear();
}); 