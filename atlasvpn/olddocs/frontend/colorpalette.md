# Color Palette

This document outlines the color palette used in the application, following a dark theme with cyberpunk-inspired purple and yellow accents.

## Base Colors

-   **Background Primary:** `#0D0D1A` (Very Dark Desaturated Blue/Purple - main background)
-   **Background Secondary:** `#1A1A2E` (Dark Desaturated Blue - card backgrounds, overlays)
-   **Background Tertiary:** `#1F1F2E` (Slightly lighter dark blue - secondary elements)
-   **Text Primary:** `#FFFFFF` (White)
-   **Text Secondary:** `#A0AEC0` (Gray - subtitles, less important text)
-   **Text Tertiary:** `#718096` (Darker Gray - disabled states, hints)

## Accent Colors

-   **Accent Purple (Primary):** `#A855F7` (Bright Purple - key actions, highlights, gradients)
    -   *Usage:* Buttons, borders, icons, highlights, gradients.
    -   *Tailwind Alias:* `accent-500`, `purple-500` (adjust if using custom names)
-   **Accent Yellow (Secondary):** `#FCD34D` (Bright Yellow - earnings, important stats, accents)
    -   *Usage:* Profit display, highlights, icons, gradients.
    -   *Tailwind Alias:* `yellow-300` (adjust if using custom names)
-   **Accent Blue:** `#3B82F6` (Bright Blue - complementary accent, potentially for specific states)
    -   *Usage:* Secondary highlights, informational icons, gradients.
    -   *Tailwind Alias:* `blue-500` (adjust if using custom names)

## Semantic Colors

-   **Success:** `#34D399` (Green - confirmation messages)
-   **Warning:** `#FBBF24` (Amber - non-critical alerts)
-   **Error:** `#F87171` (Red - errors, critical alerts)

## Gradients

-   **Purple-Blue:** `from-purple-500 to-blue-500`
-   **Yellow-Amber:** `from-yellow-400 to-amber-600` or `from-yellow-300 to-yellow-500`
-   **Dark Background:** `from-black/70 via-base-900/60 to-base-900/80` (Example)

## Implementation Notes

-   Utilize Tailwind CSS utility classes wherever possible.
-   Define custom colors or extend the theme in `tailwind.config.js` if necessary.
-   Ensure sufficient color contrast for accessibility (WCAG AA minimum).
-   Use gradients sparingly to highlight key areas or create visual depth. 