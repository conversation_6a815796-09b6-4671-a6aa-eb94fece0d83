#!/bin/bash
# Monitor and restart Atlas VPN service if needed
# This script should be run as a cron job or systemd timer

# Configuration
MAX_FD_COUNT=5000           # Maximum allowed file descriptors
MAX_CONNECTIONS=2000        # Maximum allowed connections
RESTART_THRESHOLD=90        # CPU/Memory percentage to trigger restart
SERVICE_NAME="atlas"        # systemd service name
NOTIFICATION_EMAIL=""       # Email for notifications (optional)
LOG_FILE="/opt/atlasvpn/backend/logs/monitor_restarts.log"
MONITOR_PID_FILE="/opt/atlasvpn/backend/resource_monitor.pid"

# Create log directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"

# Log function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Send notification (if email is configured)
notify() {
    if [ -n "$NOTIFICATION_EMAIL" ]; then
        echo "$1" | mail -s "Atlas VPN Service Alert" "$NOTIFICATION_EMAIL"
    fi
    log "$1"
}

# Find atlas service PID
get_service_pid() {
    systemctl show -p MainPID "$SERVICE_NAME" | cut -d= -f2
}

# Check if resource monitor is running, start if not
check_resource_monitor() {
    if [ -f "$MONITOR_PID_FILE" ]; then
        PID=$(cat "$MONITOR_PID_FILE")
        if ps -p "$PID" > /dev/null; then
            log "Resource monitor is already running with PID $PID"
            return 0
        else
            log "Resource monitor PID file exists but process is not running"
            rm -f "$MONITOR_PID_FILE"
        fi
    fi
    
    log "Starting resource monitor..."
    cd /opt/atlasvpn/backend || exit 1
    nohup python3 resource_monitor.py > /dev/null 2>&1 &
    echo $! > "$MONITOR_PID_FILE"
    log "Resource monitor started with PID $(cat "$MONITOR_PID_FILE")"
}

# Check if bc is installed
if ! command -v bc &> /dev/null; then
    log "Warning: 'bc' command not found, installing it for proper threshold comparisons"
    apt-get update && apt-get install -y bc
    
    # If we still can't find bc after installation attempt, use a fallback comparison method
    if ! command -v bc &> /dev/null; then
        log "Could not install 'bc', using integer-only comparisons"
        
        # Define a fallback comparison function
        compare_values() {
            local value=$1
            local threshold=$2
            # Extract integer part only
            value=${value%.*}
            # Simple integer comparison
            [ "$value" -gt "$threshold" ]
            return $?
        }
    else
        # Define comparison function using bc
        compare_values() {
            local value=$1
            local threshold=$2
            (( $(echo "$value > $threshold" | bc -l) ))
            return $?
        }
    fi
else
    # Define comparison function using bc
    compare_values() {
        local value=$1
        local threshold=$2
        (( $(echo "$value > $threshold" | bc -l) ))
        return $?
    }
fi

# Check and restart service if needed
check_service() {
    # Check if service is running
    if ! systemctl is-active --quiet "$SERVICE_NAME"; then
        notify "ALERT: $SERVICE_NAME service is not running. Attempting to start..."
        systemctl start "$SERVICE_NAME"
        sleep 5
        if systemctl is-active --quiet "$SERVICE_NAME"; then
            notify "$SERVICE_NAME service started successfully"
        else
            notify "ERROR: Failed to start $SERVICE_NAME service"
            return 1
        fi
    fi
    
    # Get service PID
    SERVICE_PID=$(get_service_pid)
    if [ -z "$SERVICE_PID" ] || [ "$SERVICE_PID" -eq 0 ]; then
        notify "ERROR: Could not determine PID for $SERVICE_NAME service"
        return 1
    fi
    
    log "Checking $SERVICE_NAME service (PID: $SERVICE_PID)"
    
    # Check file descriptor count
    FD_COUNT=$(lsof -p "$SERVICE_PID" | wc -l)
    log "File descriptor count: $FD_COUNT"
    
    # Check connection count
    CONN_COUNT=$(lsof -p "$SERVICE_PID" | grep -c 'IPv4|IPv6|TCP|UDP')
    log "Connection count: $CONN_COUNT"
    
    # Check memory usage
    MEM_PERCENT=$(ps -p "$SERVICE_PID" -o %mem | tail -1 | tr -d ' ')
    log "Memory usage: $MEM_PERCENT%"
    
    # Check CPU usage over 1 minute
    CPU_PERCENT=$(ps -p "$SERVICE_PID" -o %cpu | tail -1 | tr -d ' ')
    log "CPU usage: $CPU_PERCENT%"
    
    # Check database file size
    DB_SIZE=$(du -m /opt/atlasvpn/backend/users.db 2>/dev/null | cut -f1)
    log "Database size: ${DB_SIZE:-unknown} MB"
    
    # Check if restart is needed
    if [ "$FD_COUNT" -gt "$MAX_FD_COUNT" ]; then
        notify "ALERT: High file descriptor count ($FD_COUNT > $MAX_FD_COUNT). Restarting service..."
        restart_service
        return 0
    fi
    
    if [ "$CONN_COUNT" -gt "$MAX_CONNECTIONS" ]; then
        notify "ALERT: High connection count ($CONN_COUNT > $MAX_CONNECTIONS). Restarting service..."
        restart_service
        return 0
    fi
    
    if compare_values "$MEM_PERCENT" "$RESTART_THRESHOLD"; then
        notify "ALERT: High memory usage ($MEM_PERCENT% > $RESTART_THRESHOLD%). Restarting service..."
        restart_service
        return 0
    fi
    
    if compare_values "$CPU_PERCENT" "$RESTART_THRESHOLD"; then
        notify "ALERT: High CPU usage ($CPU_PERCENT% > $RESTART_THRESHOLD%). Restarting service..."
        restart_service
        return 0
    fi
    
    log "Service is running normally"
    return 0
}

# Restart service with cleanup
restart_service() {
    log "Restarting $SERVICE_NAME service..."
    
    # Checkpoint database before restart
    if [ -f "/opt/atlasvpn/backend/database.py" ]; then
        log "Running database checkpoint before restart..."
        cd /opt/atlasvpn/backend || exit 1
        python3 -c "from database import checkpoint_database; checkpoint_database()" || log "Failed to checkpoint database"
    fi
    
    # Restart service
    systemctl restart "$SERVICE_NAME"
    
    # Check if restart was successful
    sleep 5
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        notify "$SERVICE_NAME service restarted successfully"
        
        # Get new PID and file descriptor count
        NEW_PID=$(get_service_pid)
        NEW_FD_COUNT=$(lsof -p "$NEW_PID" | wc -l)
        log "New PID: $NEW_PID, New file descriptor count: $NEW_FD_COUNT"
    else
        notify "ERROR: Failed to restart $SERVICE_NAME service"
    fi
}

# Main execution
log "===== Starting service monitor ====="
check_resource_monitor
check_service
log "===== Monitor completed ====="

exit 0 