/**
 * Performance utilities for monitoring and optimizing application performance
 */

/**
 * Performance metrics interface
 */
interface PerformanceMetrics {
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  firstInputDelay: number;
  cumulativeLayoutShift: number;
  memoryUsage?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

/**
 * Performance observer for monitoring web vitals
 */
class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
  }

  /**
   * Initialize performance observers
   */
  private initializeObservers(): void {
    if (typeof window === 'undefined') return;

    // Observe navigation timing
    this.observeNavigationTiming();

    // Observe paint timing
    this.observePaintTiming();

    // Observe layout shift
    this.observeLayoutShift();

    // Observe first input delay
    this.observeFirstInputDelay();

    // Observe largest contentful paint
    this.observeLargestContentfulPaint();
  }

  /**
   * Observe navigation timing
   */
  private observeNavigationTiming(): void {
    if (typeof window === 'undefined' || !window.performance) return;

    const navigation = window.performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigation) {
      this.metrics.loadTime = navigation.loadEventEnd - navigation.loadEventStart;
      this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
    }
  }

  /**
   * Observe paint timing
   */
  private observePaintTiming(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime;
          }
        }
      });

      observer.observe({ entryTypes: ['paint'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Paint timing observer not supported:', error);
    }
  }

  /**
   * Observe layout shift
   */
  private observeLayoutShift(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    try {
      let clsValue = 0;
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
            this.metrics.cumulativeLayoutShift = clsValue;
          }
        }
      });

      observer.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Layout shift observer not supported:', error);
    }
  }

  /**
   * Observe first input delay
   */
  private observeFirstInputDelay(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.metrics.firstInputDelay = (entry as any).processingStart - entry.startTime;
        }
      });

      observer.observe({ entryTypes: ['first-input'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('First input delay observer not supported:', error);
    }
  }

  /**
   * Observe largest contentful paint
   */
  private observeLargestContentfulPaint(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) return;

    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.largestContentfulPaint = lastEntry.startTime;
      });

      observer.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(observer);
    } catch (error) {
      console.warn('Largest contentful paint observer not supported:', error);
    }
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): Partial<PerformanceMetrics> {
    // Add memory usage if available
    if (typeof window !== 'undefined' && (window.performance as any).memory) {
      const memory = (window.performance as any).memory;
      this.metrics.memoryUsage = {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit,
      };
    }

    return { ...this.metrics };
  }

  /**
   * Disconnect all observers
   */
  disconnect(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

/**
 * Measure function execution time
 * @param fn - Function to measure
 * @param name - Name for the measurement
 * @returns Function result and execution time
 */
export async function measureExecutionTime<T>(
  fn: () => T | Promise<T>,
  name: string = 'function'
): Promise<{ result: T; executionTime: number }> {
  const startTime = performance.now();
  const result = await fn();
  const endTime = performance.now();
  const executionTime = endTime - startTime;

  console.log(`${name} execution time: ${executionTime.toFixed(2)}ms`);

  return { result, executionTime };
}

/**
 * Create a performance mark
 * @param name - Mark name
 */
export function mark(name: string): void {
  if (typeof window !== 'undefined' && window.performance?.mark) {
    window.performance.mark(name);
  }
}

/**
 * Measure time between two marks
 * @param name - Measure name
 * @param startMark - Start mark name
 * @param endMark - End mark name
 */
export function measure(name: string, startMark: string, endMark: string): number | null {
  if (typeof window === 'undefined' || !window.performance?.measure) return null;

  try {
    window.performance.measure(name, startMark, endMark);
    const measure = window.performance.getEntriesByName(name, 'measure')[0];
    return measure?.duration || null;
  } catch (error) {
    console.warn('Performance measure failed:', error);
    return null;
  }
}

/**
 * Get current performance metrics
 */
export function getPerformanceMetrics(): Partial<PerformanceMetrics> {
  return performanceMonitor.getMetrics();
}

/**
 * Log performance metrics to console
 */
export function logPerformanceMetrics(): void {
  const metrics = getPerformanceMetrics();
  console.group('Performance Metrics');
  
  Object.entries(metrics).forEach(([key, value]) => {
    if (typeof value === 'object') {
      console.group(key);
      Object.entries(value).forEach(([subKey, subValue]) => {
        console.log(`${subKey}: ${subValue}`);
      });
      console.groupEnd();
    } else {
      console.log(`${key}: ${value}ms`);
    }
  });
  
  console.groupEnd();
}

/**
 * Check if performance is good based on web vitals thresholds
 */
export function isPerformanceGood(): boolean {
  const metrics = getPerformanceMetrics();
  
  // Web Vitals thresholds
  const thresholds = {
    firstContentfulPaint: 1800, // 1.8s
    largestContentfulPaint: 2500, // 2.5s
    firstInputDelay: 100, // 100ms
    cumulativeLayoutShift: 0.1, // 0.1
  };

  const checks = [
    !metrics.firstContentfulPaint || metrics.firstContentfulPaint <= thresholds.firstContentfulPaint,
    !metrics.largestContentfulPaint || metrics.largestContentfulPaint <= thresholds.largestContentfulPaint,
    !metrics.firstInputDelay || metrics.firstInputDelay <= thresholds.firstInputDelay,
    !metrics.cumulativeLayoutShift || metrics.cumulativeLayoutShift <= thresholds.cumulativeLayoutShift,
  ];

  return checks.every(check => check);
}

/**
 * Get performance grade based on metrics
 */
export function getPerformanceGrade(): 'A' | 'B' | 'C' | 'D' | 'F' {
  const metrics = getPerformanceMetrics();
  let score = 100;

  // Deduct points based on metrics
  if (metrics.firstContentfulPaint && metrics.firstContentfulPaint > 1800) {
    score -= 20;
  }
  if (metrics.largestContentfulPaint && metrics.largestContentfulPaint > 2500) {
    score -= 20;
  }
  if (metrics.firstInputDelay && metrics.firstInputDelay > 100) {
    score -= 20;
  }
  if (metrics.cumulativeLayoutShift && metrics.cumulativeLayoutShift > 0.1) {
    score -= 20;
  }
  if (metrics.loadTime && metrics.loadTime > 3000) {
    score -= 20;
  }

  if (score >= 90) return 'A';
  if (score >= 80) return 'B';
  if (score >= 70) return 'C';
  if (score >= 60) return 'D';
  return 'F';
}

/**
 * Optimize images by lazy loading
 */
export function optimizeImages(): void {
  if (typeof window === 'undefined') return;

  const images = document.querySelectorAll('img[data-src]');
  
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          img.src = img.dataset.src || '';
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });

    images.forEach(img => imageObserver.observe(img));
  } else {
    // Fallback for browsers without IntersectionObserver
    images.forEach(img => {
      const image = img as HTMLImageElement;
      image.src = image.dataset.src || '';
    });
  }
}

/**
 * Preload critical resources
 */
export function preloadCriticalResources(resources: string[]): void {
  if (typeof window === 'undefined') return;

  resources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource;
    
    // Determine resource type
    if (resource.endsWith('.css')) {
      link.as = 'style';
    } else if (resource.endsWith('.js')) {
      link.as = 'script';
    } else if (resource.match(/\.(jpg|jpeg|png|webp|svg)$/)) {
      link.as = 'image';
    } else if (resource.match(/\.(woff|woff2|ttf|otf)$/)) {
      link.as = 'font';
      link.crossOrigin = 'anonymous';
    }
    
    document.head.appendChild(link);
  });
}

/**
 * Clean up performance monitoring
 */
export function cleanupPerformanceMonitoring(): void {
  performanceMonitor.disconnect();
}

// Export the monitor instance
export { performanceMonitor };
