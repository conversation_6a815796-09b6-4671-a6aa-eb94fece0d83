/* 
 * fonts.css - Optimized font loading for better performance
 * Using preload strategy and fallback fonts to prevent blocking
 * Google Fonts are now loaded asynchronously via JavaScript
 */

/* Remove the complex system font stack */
/* @font-face { ... removed ... } */

/* Use system fonts as fallback to prevent blocking */
.font-sans {
  font-family: 'Oxanium', 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased; /* Better text rendering on macOS/iOS */
  -moz-osx-font-smoothing: grayscale; /* Better text rendering on macOS */
  text-rendering: optimizeLegibility; /* Optimize legibility */
  font-feature-settings:
    'kern' 1,
    'liga' 1,
    'calt' 1; /* Enable kerning, ligatures, and contextual alternates */
}

/* Custom font-display class using system fonts as fallback */
.font-display {
  font-family: 'Tektur', 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
  /* Apply the specified width when Tektur loads */
  font-variation-settings: 'wdth' 100;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Progressive enhancement: Load Google Fonts only after page is ready */
.fonts-loaded .font-sans {
  font-family: 'Oxanium', 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.fonts-loaded .font-display {
  font-family: 'Tektur', 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Add custom font size utility classes for better readability */
.text-readable {
  font-size: clamp(14px, 4vw, 16px); /* Responsive font sizing with min/max bounds */
  line-height: 1.5;
  letter-spacing: -0.01em; /* Slightly tighter spacing for modern look */
}

/* Performance optimizations for text rendering */
* {
  text-size-adjust: 100%; /* Prevent text size inflation on mobile devices */
}

/* Optimize GPU rendering for animations */
.will-change-transform {
  will-change: transform;
}

.will-change-scroll {
  will-change: scroll-position;
  overscroll-behavior: contain; /* Prevent scroll chaining */
}

/* Font smoothing to avoid jagged text edges on mobile */
html,
body {
  -webkit-text-size-adjust: 100%; /* Prevent font-size inflation on rotation */
  text-size-adjust: 100%;
}

/* High-contrast mode helpers */
@media (prefers-contrast: high) {
  .font-sans {
    letter-spacing: 0.01em; /* Increase spacing for better readability in high contrast */
  }
}

/* Async font loading script - add to head */
/* 
<script>
  // Progressive font loading
  if ('fonts' in document) {
    const oxanium = new FontFace('Oxanium', 'url(https://fonts.gstatic.com/s/oxanium/v4/RrQOaoIzBLdcVSDzR8YhKr9fOCm_8lGy.woff2)');
    const tektur = new FontFace('Tektur', 'url(https://fonts.gstatic.com/s/tektur/v14/cUqVOXV1kgMKmgSY7LqXpg.woff2)');
    
    Promise.all([oxanium.load(), tektur.load()]).then(() => {
      document.fonts.add(oxanium);
      document.fonts.add(tektur);
      document.documentElement.classList.add('fonts-loaded');
    }).catch(console.warn);
  }
</script>
*/
