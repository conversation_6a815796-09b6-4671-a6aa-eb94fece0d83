#!/bin/bash

# Task API Test Runner
# Runs tests for the task API system with schema validation

# Set default values
API_BASE_URL=${API_BASE_URL:-"http://localhost:8000"}
TEST_MODE=${TEST_MODE:-"true"}
TEST_SCRIPT=""
SCRIPT_DIR=$(dirname "$0")
LOGS_DIR="$SCRIPT_DIR/logs"
RUN_ALL=false
VALIDATE_SCHEMAS=true

# Display help message
usage() {
    echo "Usage: $0 [options] [test_script]"
    echo
    echo "Options:"
    echo "  --all                Run all task test scripts"
    echo "  --core               Run core task API tests"
    echo "  --daily              Run daily task tests"
    echo "  --security           Run security task tests"
    echo "  --admin              Run admin task tests"
    echo "  --url <url>          Set API base URL (default: http://localhost:8000)"
    echo "  --no-schema          Disable schema validation"
    echo "  --test-mode <bool>   Set test mode (true/false, default: true)"
    echo "  --help               Display this help message"
    echo
    echo "Examples:"
    echo "  $0 --all             Run all task tests"
    echo "  $0 --core            Run core task tests"
    echo "  $0 --daily           Run daily task tests"
    echo "  $0 --security        Run security task tests"
    echo "  $0 --admin           Run admin task tests"
    echo "  $0 --test-mode false --security  Run security tests with test mode disabled"
    echo "  $0 --url http://api.example.com --core"
    echo
}

# Create logs directory if it doesn't exist
create_logs_dir() {
    mkdir -p "$LOGS_DIR"
    echo "Logs will be saved to $LOGS_DIR"
}

# Check if virtual environment exists and activate it
activate_venv() {
    if [ -d "$SCRIPT_DIR/../venv" ]; then
        echo "Activating virtual environment..."
        source "$SCRIPT_DIR/../venv/bin/activate"
    else
        echo "Virtual environment not found at $SCRIPT_DIR/../venv"
        echo "Using system Python..."
    fi
}

# Run a specific test script
run_test() {
    local script=$1
    local test_type=$2
    
    echo "----------------------------------------------"
    echo "Running $test_type tests: $script"
    echo "----------------------------------------------"
    echo "API Base URL: $API_BASE_URL"
    echo "Test Mode: $TEST_MODE"
    echo "Schema Validation: $([ "$VALIDATE_SCHEMAS" = true ] && echo 'Enabled' || echo 'Disabled')"
    
    # Set environment variables
    export API_BASE_URL=$API_BASE_URL
    export TEST_MODE=$TEST_MODE
    export VALIDATE_SCHEMAS=$VALIDATE_SCHEMAS
    
    # Run the test script - ensure we're in the script directory
    timestamp=$(date +"%Y%m%d_%H%M%S")
    log_file="$LOGS_DIR/${test_type}_test_${timestamp}.log"
    
    # Set PYTHONPATH to include the parent directory
    export PYTHONPATH="$SCRIPT_DIR/..:$PYTHONPATH"
    
    # Run from the script directory
    cd "$SCRIPT_DIR"
    python "$script" 2>&1 | tee "$log_file"
    
    # Check exit status
    status=${PIPESTATUS[0]}
    if [ $status -eq 0 ]; then
        echo "✅ $test_type tests completed successfully"
    else
        echo "❌ $test_type tests failed with status $status"
    fi
    
    echo "Log saved to: $log_file"
    echo "----------------------------------------------"
    return $status
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --all)
                RUN_ALL=true
                shift
                ;;
            --core)
                TEST_SCRIPT="task_api_test.py"
                shift
                ;;
            --daily)
                TEST_SCRIPT="daily_task_test.py"
                shift
                ;;
            --security)
                TEST_SCRIPT="security_task_test.py"
                shift
                ;;
            --admin)
                TEST_SCRIPT="admin_task_test.py"
                shift
                ;;
            --url)
                API_BASE_URL="$2"
                shift 2
                ;;
            --no-schema)
                VALIDATE_SCHEMAS=false
                shift
                ;;
            --test-mode)
                TEST_MODE="$2"
                shift 2
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                # Assume it's a test script
                TEST_SCRIPT="$1"
                shift
                ;;
        esac
    done
}

# Check for required Python packages
check_dependencies() {
    echo "Checking dependencies..."
    
    # List of required packages
    required_packages=("httpx" "rich" "pydantic" "fastapi")
    missing_packages=()
    
    for package in "${required_packages[@]}"; do
        if ! python -c "import $package" &>/dev/null; then
            missing_packages+=("$package")
        fi
    done
    
    if [ ${#missing_packages[@]} -gt 0 ]; then
        echo "Missing required packages: ${missing_packages[*]}"
        echo "Installing missing packages..."
        pip install "${missing_packages[@]}"
    else
        echo "All required packages are installed."
    fi
}

# Main function
main() {
    # Parse arguments
    parse_args "$@"
    
    # Create logs directory
    create_logs_dir
    
    # Activate virtual environment
    activate_venv
    
    # Check dependencies
    check_dependencies
    
    # If no test script specified and not running all, show help
    if [ -z "$TEST_SCRIPT" ] && [ "$RUN_ALL" = false ]; then
        echo "Error: No test script specified"
        usage
        exit 1
    fi
    
    # Tracking overall status
    overall_status=0
    
    # Run specified test or all tests
    if [ "$RUN_ALL" = true ]; then
        echo "Running all task tests..."
        
        # Run core task tests
        run_test "task_api_test.py" "core"
        core_status=$?
        [ $core_status -ne 0 ] && overall_status=1
        
        # Run daily task tests
        run_test "daily_task_test.py" "daily"
        daily_status=$?
        [ $daily_status -ne 0 ] && overall_status=1
        
        # Run security task tests
        run_test "security_task_test.py" "security"
        security_status=$?
        [ $security_status -ne 0 ] && overall_status=1
        
        # Run admin task tests
        run_test "admin_task_test.py" "admin"
        admin_status=$?
        [ $admin_status -ne 0 ] && overall_status=1
        
        # Display summary
        echo "----------------------------------------------"
        echo "Task Test Summary:"
        echo "----------------------------------------------"
        echo "Core Tests:     $([ $core_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
        echo "Daily Tests:    $([ $daily_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
        echo "Security Tests: $([ $security_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
        echo "Admin Tests:    $([ $admin_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
        echo "----------------------------------------------"
        echo "Overall Status: $([ $overall_status -eq 0 ] && echo '✅ PASSED' || echo '❌ FAILED')"
        echo "----------------------------------------------"
    else
        # Extract test type from filename
        test_type=$(basename $TEST_SCRIPT .py | sed 's/_task_test//' | sed 's/task_api/core/' | sed 's/admin_task/admin/')
        
        # Run the specified test
        run_test "$TEST_SCRIPT" "$test_type"
        overall_status=$?
    fi
    
    return $overall_status
}

# Run main function
main "$@"
exit $? 