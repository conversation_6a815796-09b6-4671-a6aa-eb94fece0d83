// API Configuration
export const API_CONFIG = {
  BASE_URL: {
    DEVELOPMENT: 'http://localhost:8000/api',
    PRODUCTION: '/api',
  },
  WEBSOCKET_URL: {
    DEVELOPMENT: 'ws://localhost:8000/ws',
    PRODUCTION: 'wss://app.atlasvip.cloud/ws',
  },
  TIMEOUT: 15000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// Application Routes
export const ROUTES = {
  HOME: '/',
  NEW_USER_SETUP: '/new-user-setup',
  DASHBOARD: '/dashboard',
  DASHBOARD_HOME: '/dashboard/home',
  DASHBOARD_EARN: '/dashboard/earn',
  DASHBOARD_WALLET: '/dashboard/wallet',
  DASHBOARD_FRIENDS: '/dashboard/friends',
  DASHBOARD_PREMIUM: '/dashboard/premium',
  VERSE: '/verse',
} as const;

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'atlas_auth_token',
  USER_DATA: 'atlas_user_data',
  THEME: 'atlas_theme',
  LANGUAGE: 'atlas_language',
  SETTINGS: 'atlas_settings',
  TELEGRAM_DATA: 'atlas_telegram_data',
  PERFORMANCE_MODE: 'atlas_performance_mode',
  LAST_SYNC: 'atlas_last_sync',
} as const;

// Query Keys for TanStack Query
export const QUERY_KEYS = {
  USER: ['user'],
  CURRENT_USER: ['user', 'current'],
  USER_PROFILE: (id: number) => ['user', 'profile', id],
  TASKS: ['tasks'],
  TASK_ANALYTICS: ['tasks', 'analytics'],
  DAILY_STREAK: ['tasks', 'daily-streak'],
  VPN_PACKAGES: ['vpn', 'packages'],
  VPN_SUBSCRIPTIONS: ['vpn', 'subscriptions'],
  VPN_PANELS: ['vpn', 'panels'],
  TRANSACTIONS: ['transactions'],
  USER_TRANSACTIONS: (userId: number) => ['transactions', 'user', userId],
  DASHBOARD_STATS: ['dashboard', 'stats'],
  REFERRALS: ['referrals'],
  CARDS: ['cards'],
  ALL_CARDS: ['cards', 'all'],
  USER_CARDS: (userId: number) => ['cards', 'user', userId],
} as const;

// Game Configuration
export const GAME_CONFIG = {
  CARD_SYSTEM: {
    MAX_LEVEL: 10,
    UPGRADE_COST_MULTIPLIER: 1.5,
    PROFIT_MULTIPLIER: 1.2,
    COOLDOWN_HOURS: 1,
  },
  TASK_SYSTEM: {
    DAILY_RESET_HOUR: 0, // UTC
    MAX_DAILY_TASKS: 10,
    STREAK_BONUS_MULTIPLIER: 1.1,
    MAX_STREAK_BONUS: 2.0,
  },
  REFERRAL_SYSTEM: {
    COMMISSION_RATE: 0.1, // 10%
    MIN_PAYOUT: 1.0,
    MAX_LEVELS: 3,
  },
} as const;

// UI Configuration
export const UI_CONFIG = {
  // Animation durations (in milliseconds)
  ANIMATION_DURATION: {
    FAST: 150,
    NORMAL: 300,
    SLOW: 500,
  },
  
  // Breakpoints (in pixels)
  BREAKPOINTS: {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
  },
  
  // Z-index layers
  Z_INDEX: {
    DROPDOWN: 1000,
    STICKY: 1020,
    FIXED: 1030,
    MODAL_BACKDROP: 1040,
    MODAL: 1050,
    POPOVER: 1060,
    TOOLTIP: 1070,
    TOAST: 1080,
  },
  
  // Toast configuration
  TOAST: {
    DURATION: {
      SHORT: 3000,
      NORMAL: 5000,
      LONG: 8000,
    },
    POSITION: 'top-right',
    MAX_TOASTS: 5,
  },
  
  // Loading states
  LOADING: {
    SKELETON_ANIMATION_DURATION: 1500,
    SPINNER_SIZE: {
      SM: 16,
      MD: 24,
      LG: 32,
    },
  },
} as const;

// Telegram WebApp Configuration
export const TELEGRAM_CONFIG = {
  BOT_USERNAME: 'AtlasVPNBot',
  WEBAPP_URL: 'https://t.me/AtlasVPNBot/app',
  SHARE_URL_TEMPLATE: 'https://t.me/share/url?url={url}&text={text}',
  
  // Theme colors
  THEME_COLORS: {
    PRIMARY: '#8B5CF6', // purple-500
    SECONDARY: '#3B82F6', // blue-500
    SUCCESS: '#10B981', // emerald-500
    WARNING: '#F59E0B', // amber-500
    ERROR: '#EF4444', // red-500
    BACKGROUND: '#000000',
    SURFACE: '#1F2937', // gray-800
  },
  
  // Haptic feedback patterns
  HAPTIC: {
    LIGHT: 'light',
    MEDIUM: 'medium',
    HEAVY: 'heavy',
    SUCCESS: 'success',
    WARNING: 'warning',
    ERROR: 'error',
  },
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  AUTH_FAILED: 'Authentication failed. Please try logging in again.',
  INVALID_CREDENTIALS: 'Invalid username or password.',
  SESSION_EXPIRED: 'Your session has expired. Please log in again.',
  INSUFFICIENT_BALANCE: 'Insufficient wallet balance for this transaction.',
  TASK_ALREADY_COMPLETED: 'This task has already been completed.',
  INVALID_REFERRAL_CODE: 'Invalid referral code.',
  SERVER_ERROR: 'Server error occurred. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  RATE_LIMIT_EXCEEDED: 'Too many requests. Please wait before trying again.',
} as const;

// Success Messages
export const SUCCESS_MESSAGES = {
  TASK_COMPLETED: 'Task completed successfully!',
  CARD_UPGRADED: 'Card upgraded successfully!',
  CARD_PURCHASED: 'Card purchased successfully!',
  REWARD_CLAIMED: 'Reward claimed successfully!',
  PROFILE_UPDATED: 'Profile updated successfully!',
  SETTINGS_SAVED: 'Settings saved successfully!',
  REFERRAL_SENT: 'Referral invitation sent!',
  VPN_SUBSCRIBED: 'VPN subscription activated!',
} as const;

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_3D_WORLD: true,
  ENABLE_CHAT: true,
  ENABLE_VPN: true,
  ENABLE_BIOMETRIC_AUTH: false,
  ENABLE_CLOUD_STORAGE: true,
  ENABLE_HAPTIC_FEEDBACK: true,
  ENABLE_PUSH_NOTIFICATIONS: false,
  ENABLE_ANALYTICS: true,
  ENABLE_DEBUG_MODE: false,
} as const;

// Development Configuration
export const DEV_CONFIG = {
  ENABLE_CONSOLE_LOGS: true,
  MOCK_TELEGRAM_DATA: {
    initData: 'mock_init_data',
    initDataUnsafe: {
      user: {
        id: 123456789,
        first_name: 'Test',
        last_name: 'User',
        username: 'testuser',
        language_code: 'en',
      },
      auth_date: Date.now(),
      hash: 'mock_hash',
    },
    isReady: true,
  },
} as const;

// Export all constants as a single object for easier importing
export const CONSTANTS = {
  API_CONFIG,
  ROUTES,
  STORAGE_KEYS,
  QUERY_KEYS,
  GAME_CONFIG,
  UI_CONFIG,
  TELEGRAM_CONFIG,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  FEATURE_FLAGS,
  DEV_CONFIG,
} as const;
