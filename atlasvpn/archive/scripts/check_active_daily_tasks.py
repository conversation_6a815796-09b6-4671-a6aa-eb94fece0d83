import httpx
import os
import json
import asyncio
import logging
import traceback
from typing import Optional, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def get_admin_token(base_url: str, credentials: Dict[str, str]) -> Optional[str]:
    """Logs in as admin and returns the access token, mimicking daily_task_test CSRF flow."""
    admin_token = None
    csrf_token = None
    auth_url = f'{base_url}/auth/token'
    
    try:
        async with httpx.AsyncClient(timeout=30.0, verify=False) as client:
            # 1. Attempt GET request to get CSR<PERSON> token (mimicking test script)
            try:
                get_resp = await client.get(auth_url) 
                # Don't raise for status, check manually
                if get_resp.status_code == 200:
                     csrf_token = get_resp.cookies.get('csrf_token') # Look for this specific cookie name
                     if csrf_token:
                         logger.info(f"Retrieved 'csrf_token' cookie via GET: {csrf_token[:10]}...")
                     else:
                         logger.warning("GET /auth/token succeeded but no 'csrf_token' cookie found.")
                else:
                    logger.warning(f"GET /auth/token failed with status {get_resp.status_code}. CSRF token might be set differently.")
                    # Still check if *any* csrf cookie was potentially set despite error
                    csrf_token = get_resp.cookies.get('csrf_token') or get_resp.cookies.get('fastapi-csrf-token')
                    if csrf_token:
                        logger.info(f"Found a CSRF token ({csrf_token[:10]}...) in cookies despite GET error.")
                    
            except httpx.RequestError as e:
                logger.warning(f"GET /auth/token request failed: {e}")
            
            # If no token after GET, try hitting protected endpoint (fallback)
            if not csrf_token:
                logger.info("No CSRF token from GET /auth/token, trying initial protected request...")
                protected_url = f'{base_url}/api/tasks/admin/tasks/list' 
                try:
                    initial_resp = await client.get(protected_url)
                    csrf_token = initial_resp.cookies.get('csrf_token') or initial_resp.cookies.get('fastapi-csrf-token')
                    if csrf_token:
                        logger.info(f"Retrieved CSRF token via protected endpoint: {csrf_token[:10]}...")
                    else:
                         logger.warning("No CSRF token found even after hitting protected endpoint.")
                except httpx.RequestError as e:
                    logger.warning(f"Initial protected request failed: {e}")

            # Proceed to login attempt
            if not csrf_token:
                logger.warning("Proceeding with login attempt without any CSRF token.")

            # 2. POST for login token
            post_headers = {
                'Content-Type': 'application/json',
                'X-Captcha-Token': 'bypass_captcha_in_test_mode' 
            }
            post_cookies = None # Prepare cookie dict
            if csrf_token:
                post_headers['X-CSRF-Token'] = csrf_token
                post_cookies = {'csrf_token': csrf_token} # Mimic test: send cookie back

            try:
                login_resp = await client.post(
                    auth_url,
                    json=credentials,
                    headers=post_headers,
                    cookies=post_cookies # Explicitly pass cookies back
                )
                login_resp.raise_for_status() 

                data = login_resp.json()
                admin_token = data.get('access_token')
                if admin_token:
                    logger.info("Admin login successful.")
                else:
                    logger.error("Admin login response missing access_token.")

            except httpx.RequestError as e:
                 logger.error(f"Admin login request failed: {e}")
            except httpx.HTTPStatusError as e:
                if e.response.status_code == 403 and "CSRF" in e.response.text:
                     logger.error(f"Admin login failed: Status 403 - CSRF token missing or invalid. Token used in header: {post_headers.get('X-CSRF-Token')}, Token sent in cookie: {post_cookies.get('csrf_token') if post_cookies else None}")
                else:
                    logger.error(f"Admin login failed: Status {e.response.status_code} - {e.response.text}")
            except json.JSONDecodeError:
                 logger.error(f"Failed to decode admin login JSON response: {login_resp.text}")

    except Exception as e:
        logger.error(f"An unexpected error occurred during admin login: {e}")
        logger.error(traceback.format_exc())
        
    return admin_token

async def check_active_daily_tasks():
    """Connects to the API, fetches all tasks as admin, and checks for active daily tasks."""
    base_url = os.environ.get('API_BASE_URL', 'http://localhost:8000')
    admin_credentials = {
        "username": os.environ.get("ADMIN_USERNAME", "admin_test"),
        "password": os.environ.get("ADMIN_PASSWORD", "admin123")
    }

    logger.info(f"Checking for active daily tasks on {base_url}...")
    
    admin_token = await get_admin_token(base_url, admin_credentials)
    
    if not admin_token:
        logger.error("Could not obtain admin token. Aborting task check.")
        return

    admin_headers = {
        'Authorization': f'Bearer {admin_token}',
        'Content-Type': 'application/json'
    }
    tasks_url = f'{base_url}/api/tasks/admin/tasks/list'
    
    try:
        async with httpx.AsyncClient(timeout=30.0, verify=False) as client:
            logger.info(f"Fetching all tasks from {tasks_url}...")
            tasks_resp = await client.get(tasks_url, headers=admin_headers)
            tasks_resp.raise_for_status() # Check for HTTP errors

            all_tasks = tasks_resp.json()
            
            active_daily_tasks = [
                task for task in all_tasks
                if task.get('type') == 'DAILY_CHECKIN' and task.get('is_active') is True
            ]

            if active_daily_tasks:
                logger.info(f"Found {len(active_daily_tasks)} active DAILY_CHECKIN task(s):")
                print(json.dumps(active_daily_tasks, indent=2))
            else:
                logger.info("No active DAILY_CHECKIN tasks found.")

    except httpx.RequestError as e:
        logger.error(f"Failed to connect to API or fetch tasks: {e}")
    except httpx.HTTPStatusError as e:
        logger.error(f"API error fetching tasks: Status {e.response.status_code} - {e.response.text}")
    except json.JSONDecodeError:
        logger.error(f"Failed to decode API response from {tasks_url}: {tasks_resp.text}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during task check: {e}")
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    asyncio.run(check_active_daily_tasks()) 