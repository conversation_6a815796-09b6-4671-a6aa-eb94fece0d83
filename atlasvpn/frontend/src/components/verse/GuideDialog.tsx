import React from 'react';
import { motion } from 'framer-motion';
import { FaTimes, FaInfoCircle, FaHandPointer, FaSearch, FaPaintBrush } from 'react-icons/fa';

interface GuideDialogProps {
  onClose: () => void;
}

const GuideDialog: React.FC<GuideDialogProps> = ({ onClose }) => {
  const hintText =
    'Land size and price vary! Some lands are rare bargains, others are premium. Drag to pan, Scroll/Pinch to zoom, Click plots for details.';

  return (
    <motion.div
      className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose} // Close on backdrop click
    >
      <motion.div
        className="bg-gray-800/80 backdrop-blur-lg border border-purple-500/30 rounded-xl shadow-xl w-full max-w-md text-white overflow-hidden"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: 'spring', damping: 20, stiffness: 250 }}
        onClick={(e?: React.MouseEvent) => e?.stopPropagation()} // Prevent closing when clicking inside the dialog
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-white/15">
          <div className="flex items-center gap-2">
            <FaInfoCircle className="h-5 w-5 text-purple-400" />
            <h3 className="text-lg font-semibold">Verse Guide</h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors p-1 rounded-full hover:bg-white/10"
            aria-label="Close Guide"
          >
            <FaTimes className="h-4 w-4" />
          </button>
        </div>

        {/* Content */}
        <div className="p-5 space-y-4 text-sm">
          <p className="bg-black/20 p-3 rounded-md border border-white/10">{hintText}</p>

          <div className="space-y-2">
            <h4 className="font-semibold text-purple-300">Key Interactions:</h4>
            <ul className="list-none space-y-1.5 pl-2">
              <li className="flex items-start gap-2">
                <FaHandPointer className="h-4 w-4 mt-0.5 text-gray-400 flex-shrink-0" />
                <span>
                  <strong className="font-medium text-gray-100">Click a Land Plot:</strong> Opens
                  the detail card with info like rarity, price, and actions (Buy/Claim/Paint).
                </span>
              </li>
              <li className="flex items-start gap-2">
                <FaSearch className="h-4 w-4 mt-0.5 text-gray-400 flex-shrink-0" />
                <span>
                  <strong className="font-medium text-gray-100">Explore:</strong> Drag to rotate the
                  globe. Scroll or pinch to zoom in and out.
                </span>
              </li>
              <li className="flex items-start gap-2">
                <FaPaintBrush className="h-4 w-4 mt-0.5 text-gray-400 flex-shrink-0" />
                <span>
                  <strong className="font-medium text-gray-100">Paint Owned Land:</strong> If you
                  own a plot, click 'Paint Land' in its detail card to customize it.
                </span>
              </li>
            </ul>
          </div>

          <p className="text-xs text-gray-400 text-center pt-2">Happy exploring the VPNVerse!</p>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default GuideDialog;
