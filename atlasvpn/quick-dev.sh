#!/bin/bash

# Quick Development Script - Run Nuxt directly without <PERSON><PERSON>
# This is the fastest way to develop and test changes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[QUICK-DEV]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "frontend-nuxt" ]; then
    print_error "Please run this script from the atlasvpn root directory"
    exit 1
fi

# Check if pnpm is installed
if ! command -v pnpm &> /dev/null; then
    print_error "pnpm is not installed. Installing..."
    npm install -g pnpm
fi

# Navigate to frontend directory
cd frontend-nuxt

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    pnpm install
fi

# Set environment variables for development
export NODE_ENV=development
export PORT=3005
export NITRO_PORT=3005
export NUXT_PUBLIC_API_URL=https://app.atlasvip.cloud/api
export NUXT_PUBLIC_WS_BASE_URL=wss://app.atlasvip.cloud/ws
export NUXT_PUBLIC_TELEGRAM_WEBAPP_URL=https://app.atlasvip.cloud
export NUXT_DEVTOOLS=true
export NUXT_DEV_SSR_LOGS=true

print_success "Starting Nuxt development server..."
print_status "Frontend will be available at: http://localhost:3005"
print_status "Note: For Telegram testing, you'll need to use the proxied URL: https://app.atlasvip.cloud"
print_status "Press Ctrl+C to stop the server"

# Start the development server
pnpm dev
