---
description: foolow this rules while working on data fetching between app and tabs
globs: 
alwaysApply: false
---
# Data Fetching and Refactoring Strategy

This document outlines the rules and strategy for handling data fetching within the Telegram mini-game application to ensure consistency, maintainability, and performance.

## Core Principles

1.  **Centralization:** All API data fetching logic MUST reside exclusively within the relevant Zustand stores (`authStore`, `taskStore`, `vpnStore`, `cardStore`, `dashboardStore`, etc.). Stores are the single source of truth for their respective data slices.
2.  **Store Responsibility:** Each Zustand store is responsible for:
    *   Defining the state shape for its data slice.
    *   Implementing actions to fetch and update its state from the API.
    *   Managing its own loading and error states related to its specific fetch actions.
    *   Implementing caching/throttling logic for its fetch actions.
3.  **Component Simplicity:** React components (Tabs, individual display components) SHOULD NOT contain direct API fetching logic. Their responsibilities are:
    *   Selecting data from Zustand stores using hooks.
    *   Displaying the selected data and loading/error states provided by the stores.
    *   Triggering store actions (e.g., `fetchData()`, `claimReward()`) based on user interaction or lifecycle events (like mounting).

## Fetching Strategy

1.  **Primary Fetch Trigger:**
    *   Use `useEffect` hooks within each specific *Tab component* (e.g., `EarnTab`, `PremiumTab`, `WalletTabPage`).
    *   On mount, the `useEffect` hook SHOULD call the necessary fetch action(s) from its corresponding store(s).
    *   The store's fetch action itself MUST implement caching logic (`_needsRefresh` or similar) to prevent unnecessary API calls if the data is considered fresh.
2.  **Initialization Fetch:**
    *   Continue using `initialize...` functions (e.g., `initializeAuth`, `initializeDashboardStore`) for essential data required immediately on application startup (typically authentication status and core user profile data).
3.  **Manual Refresh:**
    *   Refresh buttons within components SHOULD directly call the relevant store's fetch action (e.g., `useTaskStore.getState().loadTasks()`).
4.  **Background Refresh (App Visibility):**
    *   Implement a single `visibilitychange` event listener, preferably in a top-level layout component like `DashboardLayout.tsx`.
    *   When the document becomes visible, this listener SHOULD iterate through relevant stores and trigger their respective fetch actions *silently* (using a flag within the action, e.g., `fetchStats(true)`) if the store's caching logic indicates the data is stale. This prevents showing loading indicators for background updates.
5.  **Caching & Throttling (Inside Stores):**
    *   **Mandatory:** Every fetch action within a store MUST implement a standard caching pattern.
    *   Check an internal `loading` flag (specific to that action) to prevent concurrent requests for the same data.
    *   Check a `lastUpdated` timestamp against a `CACHE_TTL` (Time To Live) constant defined within the store.
    *   Only proceed with the API call if `!loading && (forceRefresh || needsRefresh(CACHE_TTL))`.
    *   Update `lastUpdated` timestamp ONLY on successful API response.
    *   Ensure `loading` flags are reset correctly in `finally` blocks.

## Refactoring Plan

1.  **Eliminate `UserDashboard.tsx` Fetching:**
    *   **Remove:** All data fetching logic (`fetchData`, `fetchTabData`), the `useReducer` for local fetch state, and associated state variables (`loading`, `error`, `fetchInProgressRef`, `lastFetchTimeRef`) from `UserDashboard.tsx`.
2.  **Migrate Modals/Slides:**
    *   Move the modal/slide components currently rendered in `UserDashboard.tsx` (`WelcomeBackSplash`, `ClaimAllBanner`, `TaskDetailSlide`, `NewServiceForm`) to the components/tabs where they are functionally relevant:
        *   `ClaimAllBanner`: Likely belongs in `CardTab` or `EarnTab`.
        *   `WelcomeBackSplash`: Could be triggered from `DashboardLayout` or `HomeTab` based on store state.
        *   `TaskDetailSlide`: Belongs in `EarnTab` or wherever the task list is primarily displayed.
        *   `NewServiceForm`: Belongs in `PremiumTab`.
    *   Alternatively, implement a global modal state management system (using Zustand or Context) if modals need to be triggered from multiple locations.
3.  **Delete `UserDashboard.tsx`:**
    *   Once all fetching logic and modal rendering have been migrated, the `UserDashboard.tsx` component SHOULD be deleted. Routing should directly render the `DashboardLayout` which uses `<Outlet />` to render the appropriate tab component based on the URL.
4.  **Simplify Tab Components:**
    *   Refactor tabs (especially `EarnTab`) to primarily select data from stores.
    *   `EarnTab` SHOULD render the `CardTab` and `TaskTabComponent` components conditionally based on its internal state (`activeInnerSection`), passing data selected from `useCardStore` and `useTaskStore`.
    *   Ensure all tabs trigger their necessary data fetches via `useEffect` on mount, relying on the store's caching logic.

## Error Handling

1.  **Store-Level:** Stores handle API errors during fetch actions using the `handleStoreError` utility. They update their specific `error` state property.
2.  **Component-Level:** Components read the `error` state from the relevant store and display appropriate UI feedback (e.g., an error message, a retry button). Components SHOULD NOT implement complex error handling logic themselves.
3.  **Rate Limits (429):** The `api.ts` interceptor handles 429 errors globally, setting a `rateLimitWaitUntil` state in `authStore`. UI components can react to this state (e.g., disabling buttons, showing a global banner).

## Future Considerations

1.  **WebSockets:** For truly real-time updates (balance, passive income, task completion), investigate implementing WebSockets. This would push updates from the server, reducing the need for client-side polling or frequent background fetches.
2.  **Server State Libraries:** Evaluate libraries like TanStack Query (`react-query`) or SWR. They can manage server state (fetching, caching, background updates, mutations) automatically, potentially simplifying Zustand store logic, which could then focus purely on client/UI state.
[storeUtils.ts](mdc:frontend/src/utils/storeUtils.ts)

[UserDashboard.tsx](mdc:frontend/src/pages/UserDashboard.tsx)[vpnStore.ts](mdc:frontend/src/stores/vpnStore.ts) [authStore.ts](mdc:frontend/src/stores/authStore.ts)[taskStore.ts](mdc:frontend/src/stores/taskStore.ts) [cardStore.ts](mdc:frontend/src/stores/cardStore.ts) [dashboardStore.ts](mdc:frontend/src/stores/dashboardStore.ts) [DashboardLayout.tsx](mdc:frontend/src/components/DashboardLayout.tsx) [LandingPage.tsx](mdc:frontend/src/pages/LandingPage.tsx) [EarnTab.tsx](mdc:frontend/src/components/EarnTab.tsx) [CardTab.tsx](mdc:frontend/src/components/CardTab.tsx)[TaskTab.tsx](mdc:frontend/src/components/TaskTab.tsx) [WalletTabPage.tsx](mdc:frontend/src/components/WalletTabPage.tsx) [HomeTabPage.tsx](mdc:frontend/src/components/HomeTabPage.tsx) [api.ts](mdc:frontend/src/utils/api.ts)[card.ts](mdc:frontend/src/api/card.ts) 



