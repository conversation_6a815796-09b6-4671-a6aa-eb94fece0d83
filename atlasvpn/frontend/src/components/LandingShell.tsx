import React from 'react';

const LandingShell: React.FC = () => {
  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-900 to-gray-800">
      <div className="text-center">
        <div className="animate-pulse">
          <div className="w-32 h-32 mx-auto mb-6 bg-blue-600 rounded-full animate-bounce" />
          <h1 className="text-3xl font-bold text-white">VIPVerse</h1>
          <p className="mt-2 text-gray-300">Loading your experience...</p>
        </div>
      </div>
    </div>
  );
};

export default React.memo(LandingShell);
