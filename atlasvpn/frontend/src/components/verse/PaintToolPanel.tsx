/**
 * @file PaintToolPanel.tsx
 * @description Modern, Photoshop-like UI for smooth painting on land plots with Fabric.js
 */

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import encryptedStorage from '../../utils/encryptedStorage';
import { LandPlot } from '../../utils/mockVerseData';
import {
  FaPalette,
  FaTimes,
  FaSave,
  FaTrash,
  FaUndo,
  FaRedo,
  FaEraser,
  FaPencilAlt,
  FaFill,
  FaQuestion,
  FaRuler,
  FaEye,
  FaMagic,
  FaFont,
  FaExpand,
  FaCompress,
  FaChevronLeft,
  FaChevronRight,
  FaTools,
  FaAdjust,
  FaPaintBrush,
  FaSprayCan,
  FaWater,
  FaDrawPolygon,
  FaRegCircle,
  FaCircle,
  FaChevronDown,
} from 'react-icons/fa';
import {
  BsBrush,
  BsPaintBucket,
  BsCircle,
  BsCircleFill,
  BsPencil,
  BsSquare,
  BsSquareFill,
} from 'react-icons/bs';
import { GiSpray, GiPerspectiveDiceSixFacesRandom } from 'react-icons/gi';
import { TbZoomInArea, TbZoomOutArea } from 'react-icons/tb';
import { HexColorPicker } from 'react-colorful';

// @ts-ignore
import * as fabricModule from 'fabric'; // Import the module
const fabric = fabricModule; // Use the imported module directly as the fabric namespace

// Helper function to convert hex and opacity to rgba
const hexToRgba = (hex: string, opacityPercent: number): string => {
  let r = 0,
    g = 0,
    b = 0;
  if (hex.length === 4) {
    // #RGB
    r = parseInt(hex[1] + hex[1], 16);
    g = parseInt(hex[2] + hex[2], 16);
    b = parseInt(hex[3] + hex[3], 16);
  } else if (hex.length === 7) {
    // #RRGGBB
    r = parseInt(hex.slice(1, 3), 16);
    g = parseInt(hex.slice(3, 5), 16);
    b = parseInt(hex.slice(5, 7), 16);
  }
  const alpha = Math.min(1, Math.max(0, opacityPercent / 100));
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
};

interface PaintToolPanelProps {
  land: LandPlot;
  initialPaintData?: string | null;
  onSave: (landId: string, paintData: string | null) => void;
  onClose: () => void;
}

// Calculate high-resolution canvas size based on land size (larger lands get higher resolution)
const calculateCanvasSize = (angularSize: number): number => {
  // Map from angularSize range (typically 4-14) to canvas sizes (400-1200px)
  const minSize = 4,
    maxSize = 14;
  const minCanvas = 400,
    maxCanvas = 1200;

  // Linear mapping
  const normalized = Math.min(Math.max((angularSize - minSize) / (maxSize - minSize), 0), 1);
  return Math.round(minCanvas + normalized * (maxCanvas - minCanvas));
};

// Storage key for local storage
const STORAGE_PREFIX = 'vpnverse_land_paint_';

// Brush types
type BrushType =
  | 'brush'
  | 'pencil'
  | 'eraser'
  | 'fill'
  | 'spray'
  | 'smoothPencil'
  | 'watercolor'
  | 'rectangle'
  | 'rectangleFill'
  | 'circle'
  | 'circleFill'
  | 'line';

// Shape drawing state
interface ShapeDrawingState {
  startX: number;
  startY: number;
  active: boolean;
  shape?: fabricModule.Object;
}

export function PaintToolPanel({ land, initialPaintData, onSave, onClose }: PaintToolPanelProps) {
  // Canvas size based on land size
  const CANVAS_SIZE = calculateCanvasSize(land.angularSize);

  // Main refs and state
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const canvasContainerRef = useRef<HTMLDivElement>(null);
  const fabricCanvasRef = useRef<fabricModule.Canvas | null>(null);
  const [currentColor, setCurrentColor] = useState('#000000'); // Start with black
  const [brushRadius, setBrushRadius] = useState(8); // Starting brush size
  const [brushType, setBrushType] = useState<BrushType>('brush'); // Current brush type
  const [isSaving, setIsSaving] = useState(false);
  const [isFullScreen, setIsFullScreen] = useState(false);
  const [isToolPanelOpen, setIsToolPanelOpen] = useState(true);
  const [isColorPanelOpen, setIsColorPanelOpen] = useState(false);
  const [isToolSettingsOpen, setIsToolSettingsOpen] = useState(true);
  const [opacity, setOpacity] = useState(100); // Brush opacity percentage
  const [isDrawing, setIsDrawing] = useState(false);
  const shapeDrawingRef = useRef<ShapeDrawingState>({
    startX: 0,
    startY: 0,
    active: false,
  });

  // Extended color palette with more professional colors
  const colors = [
    '#000000',
    '#FFFFFF',
    '#F44336',
    '#E91E63',
    '#9C27B0',
    '#673AB7',
    '#3F51B5',
    '#2196F3',
    '#03A9F4',
    '#00BCD4',
    '#009688',
    '#4CAF50',
    '#8BC34A',
    '#CDDC39',
    '#FFEB3B',
    '#FFC107',
    '#FF9800',
    '#FF5722',
    '#795548',
    '#607D8B',
    '#9E9E9E',
    '#BDBDBD',
  ];

  // Initialize Fabric.js canvas
  useEffect(() => {
    if (!canvasRef.current) return;

    // Create Fabric.js canvas and store in ref
    const fabricCanvas = new fabric.Canvas(canvasRef.current, {
      width: CANVAS_SIZE,
      height: CANVAS_SIZE,
      backgroundColor: '#FFFFFF',
      selection: false, // Disable group selection
      preserveObjectStacking: true,
    });

    fabricCanvasRef.current = fabricCanvas;

    // Add object tracking (for debugging)
    fabricCanvas.on('object:added', e => {
      console.log('[PaintTool] object added to canvas', e.target);
      // Force render and persist after object is added
      fabricCanvas.renderAll();
    });

    fabricCanvas.on('object:removed', e => {
      console.log('[PaintTool] object removed from canvas', e.target);
    });

    fabricCanvas.on('after:render', () => {
      console.log('[PaintTool] canvas rendered');
    });

    // Clean up function
    return () => {
      fabricCanvas.dispose();
      fabricCanvasRef.current = null;
    };
  }, [CANVAS_SIZE]);

  // Load initial canvas state
  useEffect(() => {
    if (!fabricCanvasRef.current) return;

    // Try to load from encrypted storage first
    (async () => {
      try {
        const storageKey = `${STORAGE_PREFIX}${land.id}`;
        const savedData = await encryptedStorage.localStorage.getItem<string>(storageKey);
        console.log('[PaintTool] Attempting to load from encrypted storage', {
          storageKey,
          hasSavedData: !!savedData,
        }); // Log

        if (savedData) {
          try {
            // Check if valid JSON
            const parsedData = JSON.parse(savedData);
            if (fabricCanvasRef.current) {
              fabricCanvasRef.current.loadFromJSON(parsedData, () => {
                fabricCanvasRef.current?.renderAll();
                console.log(`[PaintTool] Loaded paint data from encrypted storage for ${land.id}`);
              });
            }
            return;
          } catch (parseError) {
            console.error('[PaintTool] Invalid JSON in encrypted storage, clearing:', parseError);
            await encryptedStorage.localStorage.removeItem(storageKey);
          }
        }
      } catch (error) {
        console.error('[PaintTool] Error accessing encrypted storage:', error);
      }

      // If no encrypted storage data (or it was invalid), try initialPaintData from props
      if (initialPaintData && fabricCanvasRef.current) {
        try {
          if (typeof initialPaintData === 'string' && initialPaintData.trim().startsWith('{')) {
            console.log('[PaintTool] Attempting to load from initialPaintData prop'); // Log
            const parsedData = JSON.parse(initialPaintData);
            if (fabricCanvasRef.current) {
              fabricCanvasRef.current.loadFromJSON(parsedData, () => {
                fabricCanvasRef.current?.renderAll();
                console.log(`[PaintTool] Loaded initial paint data for ${land.id}`);
              });
            }
          } else {
            console.warn('[PaintTool] initialPaintData is not a valid JSON string, skipping load.');
          }
        } catch (error) {
          console.error(
            '[PaintTool] Error loading initialPaintData (invalid JSON or other issue):',
            error
          );
        }
      }
    })();
  }, [initialPaintData, land.id]);

  // Save canvas to encrypted storage
  const saveCanvasToStorage = useCallback(() => {
    if (!fabricCanvasRef.current) return;

    (async () => {
      try {
        const storageKey = `${STORAGE_PREFIX}${land.id}`;
        if (!fabricCanvasRef.current) return;
        const saveData = JSON.stringify(fabricCanvasRef.current.toJSON());
        await encryptedStorage.localStorage.setItem(storageKey, saveData);
        console.log(`[PaintTool] Saved paint data for ${land.id} to encrypted storage`);
      } catch (error) {
        console.error('[PaintTool] Error saving to encrypted storage:', error);
      }
    })();
  }, [land.id]);

  const handleShapeStart = useCallback((o: any) => {
    if (!fabricCanvasRef.current || !o.pointer) return;
    const canvas = fabricCanvasRef.current;
    console.log('[PaintTool] handleShapeStart triggered', { pointer: o.pointer, brushType }); // Log
    shapeDrawingRef.current = {
      startX: o.pointer.x,
      startY: o.pointer.y,
      active: true,
    };
    setIsDrawing(true);
  }, []);

  const handleShapeMove = useCallback(
    (o: any) => {
      if (!fabricCanvasRef.current || !o.pointer || !shapeDrawingRef.current.active) return;
      const canvas = fabricCanvasRef.current;
      const { startX, startY } = shapeDrawingRef.current;
      const currentX = o.pointer.x;
      const currentY = o.pointer.y;
      console.log('[PaintTool] handleShapeMove triggered', {
        startX,
        startY,
        currentX,
        currentY,
        brushType,
      }); // Log

      if (shapeDrawingRef.current.shape) {
        canvas.remove(shapeDrawingRef.current.shape);
      }

      let shape: fabricModule.Object;
      const effectiveColor = hexToRgba(currentColor, opacity);

      switch (brushType) {
        case 'line':
          shape = new fabricModule.Line([startX, startY, currentX, currentY], {
            stroke: effectiveColor,
            strokeWidth: Math.max(1, brushRadius * 0.5),
            selectable: false,
            evented: false, // Min strokeWidth
          });
          break;
        case 'rectangle':
          const width = Math.abs(currentX - startX);
          const height = Math.abs(currentY - startY);
          const left = Math.min(currentX, startX);
          const top = Math.min(currentY, startY);
          shape = new fabricModule.Rect({
            left,
            top,
            width,
            height,
            stroke: effectiveColor,
            strokeWidth: Math.max(1, brushRadius * 0.5),
            fill: 'transparent',
            selectable: false,
            evented: false,
          }); // Min strokeWidth
          break;
        case 'rectangleFill':
          const fillWidth = Math.abs(currentX - startX);
          const fillHeight = Math.abs(currentY - startY);
          const fillLeft = Math.min(currentX, startX);
          const fillTop = Math.min(currentY, startY);
          shape = new fabricModule.Rect({
            left: fillLeft,
            top: fillTop,
            width: fillWidth,
            height: fillHeight,
            fill: effectiveColor,
            selectable: false,
            evented: false,
          });
          break;
        case 'circle':
          const radius = Math.sqrt(Math.pow(currentX - startX, 2) + Math.pow(currentY - startY, 2));
          shape = new fabricModule.Circle({
            left: startX - radius,
            top: startY - radius,
            radius,
            stroke: effectiveColor,
            strokeWidth: Math.max(1, brushRadius * 0.5),
            fill: 'transparent',
            selectable: false,
            evented: false,
          }); // Min strokeWidth
          break;
        case 'circleFill':
          const fillRadius = Math.sqrt(
            Math.pow(currentX - startX, 2) + Math.pow(currentY - startY, 2)
          );
          shape = new fabricModule.Circle({
            left: startX - fillRadius,
            top: startY - fillRadius,
            radius: fillRadius,
            fill: effectiveColor,
            selectable: false,
            evented: false,
          });
          break;
        default:
          return;
      }

      canvas.add(shape);
      shapeDrawingRef.current.shape = shape;
      canvas.requestRenderAll(); // Request render for live preview
    },
    [brushType, currentColor, opacity, brushRadius]
  );

  const handleShapeEnd = useCallback(() => {
    if (!fabricCanvasRef.current || !shapeDrawingRef.current.active) return;
    // Finalize shape: The last shape added by handleShapeMove is the one we keep.
    // We just need to reset the drawing state.
    console.log('[PaintTool] handleShapeEnd triggered', {
      activeShape: shapeDrawingRef.current.shape,
    }); // Log
    shapeDrawingRef.current.active = false;
    // Optional: if you want the finalized shape to be selectable after drawing:
    if (shapeDrawingRef.current.shape) {
      shapeDrawingRef.current.shape.set({ selectable: true, evented: true });
      fabricCanvasRef.current.requestRenderAll();
    }
    shapeDrawingRef.current.shape = undefined; // Clear the reference to the temporary/last drawn shape
    setIsDrawing(false);
    // DO NOT detach listeners here. configureBrush will manage them.
  }, []); // Removed brushType, let configureBrush manage listeners

  // Fill bucket tool implementation
  const handleFillBucket = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      if (!fabricCanvasRef.current || !canvasContainerRef.current) return;
      const canvas = fabricCanvasRef.current;
      const container = canvasContainerRef.current;
      const containerRect = container.getBoundingClientRect();
      const x = e.clientX - containerRect.left;
      const y = e.clientY - containerRect.top;
      const scaleX = CANVAS_SIZE / containerRect.width;
      const scaleY = CANVAS_SIZE / containerRect.height;
      const canvasX = x * scaleX;
      const canvasY = y * scaleY;
      console.log('[PaintTool] handleFillBucket triggered', {
        canvasX,
        canvasY,
        currentColor,
        opacity,
      }); // Log

      const fillRect = new fabricModule.Rect({
        left: 0,
        top: 0,
        width: CANVAS_SIZE,
        height: CANVAS_SIZE,
        fill: hexToRgba(currentColor, opacity),
        selectable: false,
        evented: false,
      });

      // Log types for debugging (MOVED AFTER DECLARATION)
      console.log(
        '[PaintTool] FillDebug: canvas type:',
        typeof canvas,
        'fillRect type:',
        typeof fillRect
      );
      console.log(
        '[PaintTool] FillDebug: canvas instance of fabric.Canvas:',
        canvas instanceof fabric.Canvas
      );
      if (fillRect) {
        // Check if fillRect is defined before logging its properties
        console.log(
          '[PaintTool] FillDebug: fillRect instance of fabric.Object:',
          fillRect instanceof fabric.Object
        );
      }

      canvas.add(fillRect);

      if (canvas && typeof (canvas as any)['sendToBack'] === 'function') {
        // Cast canvas to any for typeof check
        (canvas as any).sendToBack(fillRect);
      } else {
        console.error('[PaintTool] canvas.sendToBack is not a function. Canvas object:', canvas);
      }
      canvas.renderAll();
      // Ensure renderAll is called if not already
      if (fabricCanvasRef.current) fabricCanvasRef.current.renderAll();
    },
    [CANVAS_SIZE, currentColor, opacity]
  );

  // Configure brush based on selected tool type
  const configureBrush = useCallback(() => {
    if (!fabricCanvasRef.current) return;
    console.log('[PaintTool] configureBrush called', {
      brushType,
      currentColor,
      opacity,
      brushRadius,
    }); // Log when called
    const canvas = fabricCanvasRef.current;
    const effectiveColor = hexToRgba(currentColor, opacity);

    // 1. Always disable drawing mode and remove ALL specific listeners for shapes first
    canvas.isDrawingMode = false;
    canvas.off('mouse:down', handleShapeStart);
    canvas.off('mouse:move', handleShapeMove);
    canvas.off('mouse:up', handleShapeEnd);
    // Potentially other listeners if any were added for other tools

    canvas.discardActiveObject();
    // canvas.renderAll(); // Not strictly needed here, will render after brush setup if isDrawingMode is true

    const isShapeTool = ['rectangle', 'rectangleFill', 'circle', 'circleFill', 'line'].includes(
      brushType
    );

    if (isShapeTool) {
      // 2. If it's a shape tool, re-add the shape listeners
      canvas.isDrawingMode = false; // Ensure it stays false
      canvas.on('mouse:down', handleShapeStart);
      canvas.on('mouse:move', handleShapeMove);
      canvas.on('mouse:up', handleShapeEnd);
    } else {
      // 3. If it's a brush tool (or fill, or other)
      switch (brushType) {
        case 'brush':
        case 'pencil':
        case 'smoothPencil':
        case 'watercolor':
          canvas.isDrawingMode = true;
          canvas.freeDrawingBrush = new fabricModule.PencilBrush(canvas);
          if (canvas.freeDrawingBrush) {
            canvas.freeDrawingBrush.color =
              brushType === 'watercolor' ? hexToRgba(currentColor, opacity * 0.5) : effectiveColor;
            canvas.freeDrawingBrush.width =
              brushType === 'pencil'
                ? Math.max(1, brushRadius * 0.5)
                : brushType === 'watercolor'
                  ? brushRadius * 1.3
                  : brushRadius;
            (canvas.freeDrawingBrush as any).globalCompositeOperation = 'source-over';
            if (brushType === 'smoothPencil')
              (canvas.freeDrawingBrush as fabricModule.PencilBrush).decimate = 8;
            if (brushType === 'watercolor')
              (canvas.freeDrawingBrush as any).shadow = new fabricModule.Shadow({
                color: hexToRgba(currentColor, opacity * 0.3),
                blur: 10,
              });

            // Track brush creation and modification
            console.log('[PaintTool] Created/modified freeDrawingBrush', canvas.freeDrawingBrush);
          }
          break;
        case 'eraser':
          canvas.isDrawingMode = true;
          const eraserInstance = new fabricModule.PencilBrush(canvas);
          if (eraserInstance) {
            eraserInstance.color = '#FFFFFF'; // Changed to white for eraser
            eraserInstance.width = brushRadius * 1.5;

            // Explicitly set the globalCompositeOperation directly on the fabric object
            canvas.freeDrawingBrush = eraserInstance;
            (canvas.freeDrawingBrush as any).globalCompositeOperation = 'destination-out';

            // Double-check that it was set correctly
            console.log(
              '[PaintTool] Eraser composite operation:',
              (canvas.freeDrawingBrush as any).globalCompositeOperation
            );
          } else {
            canvas.freeDrawingBrush = undefined;
          }
          break;
        case 'spray':
          canvas.isDrawingMode = true;
          canvas.freeDrawingBrush = new fabricModule.SprayBrush(canvas);
          if (canvas.freeDrawingBrush) {
            canvas.freeDrawingBrush.color = effectiveColor;
            canvas.freeDrawingBrush.width = brushRadius * 2;
            if (canvas.freeDrawingBrush instanceof fabricModule.SprayBrush) {
              canvas.freeDrawingBrush.randomOpacity = true;
              canvas.freeDrawingBrush.density = 35;
            }
            (canvas.freeDrawingBrush as any).globalCompositeOperation = 'source-over';
          }
          break;
        case 'fill':
          // Fill is handled by direct click on container, no brush config needed here
          canvas.isDrawingMode = false;
          break;
        default:
          canvas.isDrawingMode = false;
          break;
      }
    }
    if (canvas.isDrawingMode && !canvas.freeDrawingBrush) {
      // Safety: if isDrawingMode is true, ensure freeDrawingBrush is set (e.g. to a default pencil)
      // This case should ideally not be hit if logic is correct
      canvas.freeDrawingBrush = new fabricModule.PencilBrush(canvas);
    }
    canvas.requestRenderAll(); // Ensure canvas updates visually after configuration
  }, [
    brushType,
    currentColor,
    opacity,
    brushRadius,
    handleShapeStart,
    handleShapeMove,
    handleShapeEnd,
  ]);

  // Create a critical fix for the Fabric.js PencilBrush to ensure path is permanent
  useEffect(() => {
    if (!fabricCanvasRef.current) return;

    const canvas = fabricCanvasRef.current;

    // Store originalOnMouseUp in a ref to make it available in cleanup
    const originalOnMouseUpRef = useRef<Function | null>(null);

    // Override the Fabric.js PencilBrush's onMouseUp method to ensure path visibility
    if (fabricModule.PencilBrush && fabricModule.PencilBrush.prototype) {
      // Use type assertion to access internal method
      originalOnMouseUpRef.current = (fabricModule.PencilBrush.prototype as any).onMouseUp;

      // Override with type assertion
      (fabricModule.PencilBrush.prototype as any).onMouseUp = function () {
        // Call the original method
        const result = originalOnMouseUpRef.current?.call(this);

        // Get the last created path
        if (this.canvas && this.canvas._objects.length > 0) {
          const lastPath = this.canvas._objects[this.canvas._objects.length - 1];
          if (lastPath) {
            console.log('[PaintTool] Overridden onMouseUp - fixing path:', lastPath);
            // Explicitly set properties to ensure visibility
            lastPath.opacity = 1;
            lastPath.visible = true;
            // Use type assertion for custom property
            (lastPath as any).permanent = true;
            lastPath.globalCompositeOperation = 'source-over';
            // Force canvas to re-render
            this.canvas.renderAll();
          }
        }

        return result;
      };
    }

    // Add a special handler after mouse up
    const handlePathCreated = (e: any) => {
      console.log('[PaintTool] Path created event', e.path);

      // Check if a valid path was created
      if (e.path) {
        // Manually mark the path as permanent and not erasable
        (e.path as any).permanent = true;
        e.path.opacity = 1;
        e.path.visible = true;
        e.path.stroke = e.path.stroke || currentColor; // Ensure stroke color is set
        e.path.fill = brushType.includes('Fill') ? hexToRgba(currentColor, opacity) : '';
        e.path.strokeWidth = e.path.strokeWidth || Math.max(1, brushRadius);
        e.path.globalCompositeOperation = 'source-over';

        console.log('[PaintTool] Fixed path visibility properties:', {
          opacity: e.path.opacity,
          visible: e.path.visible,
          stroke: e.path.stroke,
          strokeWidth: e.path.strokeWidth,
          globalCompositeOperation: e.path.globalCompositeOperation,
        });

        // Force canvas to re-render after path creation
        setTimeout(() => {
          if (fabricCanvasRef.current) {
            fabricCanvasRef.current.renderAll();
            console.log('[PaintTool] Force rendered after path creation');

            // Also try to manually save canvas state to encrypted storage
            (async () => {
              try {
                if (!fabricCanvasRef.current) return;
                const storageKey = `${STORAGE_PREFIX}${land.id}`;
                const saveData = JSON.stringify(fabricCanvasRef.current.toJSON());
                await encryptedStorage.localStorage.setItem(storageKey, saveData);
                console.log('[PaintTool] Saved canvas to encrypted storage after path creation');
              } catch (e) {
                console.error('[PaintTool] Error saving to encrypted storage:', e);
              }
            })();
          }
        }, 50);
      }
    };

    // Add event listener
    canvas.on('path:created', handlePathCreated);

    // Also add a more generic object creation handler
    canvas.on('object:added', (e: any) => {
      if (e.target && !(e.target as any).isTemporary) {
        e.target.visible = true;
        e.target.opacity = 1;
        (e.target as any).permanent = true;
        console.log('[PaintTool] Fixed visibility for added object:', e.target);
        canvas.renderAll();
      }
    });

    // Monitor free drawing brush changes
    const monitorFreeDrawingBrush = () => {
      if (canvas.isDrawingMode && canvas.freeDrawingBrush) {
        console.log('[PaintTool] FreeDrawingBrush monitoring:', {
          color: canvas.freeDrawingBrush.color,
          width: canvas.freeDrawingBrush.width,
          compositeOperation: (canvas.freeDrawingBrush as any).globalCompositeOperation,
        });
      }
    };

    // Call periodically while active
    const brushMonitorInterval = setInterval(monitorFreeDrawingBrush, 2000);

    // Cleanup
    return () => {
      if (canvas) {
        canvas.off('path:created', handlePathCreated);
        canvas.off('object:added');
      }
      clearInterval(brushMonitorInterval);

      // Restore original methods
      if (
        fabricModule.PencilBrush &&
        fabricModule.PencilBrush.prototype &&
        originalOnMouseUpRef.current
      ) {
        (fabricModule.PencilBrush.prototype as any).onMouseUp = originalOnMouseUpRef.current;
      }
    };
  }, [currentColor, opacity, brushType, brushRadius, land.id]);

  // Update brush configuration when tools or settings change (MOVED HERE, AFTER configureBrush DEF)
  useEffect(() => {
    configureBrush();
  }, [configureBrush]);

  // Action handlers
  const handleUndo = useCallback(() => {
    if (!fabricCanvasRef.current) return;

    if (fabricCanvasRef.current._objects.length > 0) {
      const objects = fabricCanvasRef.current._objects;
      fabricCanvasRef.current.remove(objects[objects.length - 1]);
      fabricCanvasRef.current.renderAll();
    }
  }, []);

  const handleClear = useCallback(() => {
    if (!fabricCanvasRef.current) return;
    console.log('[PaintTool] handleClear called'); // Log
    fabricCanvasRef.current.clear();
    // Set backgroundColor as a property and then render
    fabricCanvasRef.current.backgroundColor = '#FFFFFF';
    fabricCanvasRef.current.renderAll();
  }, []);

  const handleSave = useCallback(() => {
    if (!fabricCanvasRef.current) {
      console.error('[PaintTool] Canvas ref is null when trying to save');
      return;
    }

    setIsSaving(true);

    try {
      // Get save data from canvas as JSON
      const storageKey = `${STORAGE_PREFIX}${land.id}`;
      if (!fabricCanvasRef.current) return;
      const saveData = JSON.stringify(fabricCanvasRef.current.toJSON());
      console.log('Fabric.js canvas data to be saved:', JSON.stringify(saveData)); // Log the data

      // Check if canvas is empty (only background or no objects)
      const isEmpty = fabricCanvasRef.current._objects.length === 0;

      // Use async function inside to handle promise-based storage
      (async () => {
        try {
          if (isEmpty) {
            // If empty, remove from encrypted storage
            await encryptedStorage.localStorage.removeItem(storageKey);
            // Call parent's onSave with null
            onSave(land.id, null);
          } else {
            // Save to encrypted storage
            await encryptedStorage.localStorage.setItem(storageKey, saveData);
            // Call parent's onSave with saveData
            onSave(land.id, saveData);
          }

          // Short delay to show saving feedback
          setTimeout(() => {
            setIsSaving(false);
            onClose();
          }, 300);
        } catch (storageError) {
          console.error('[PaintTool] Error saving to encrypted storage:', storageError);
          setIsSaving(false);
          alert('Error saving artwork. Please try again.');
        }
      })();
    } catch (error) {
      console.error('[PaintTool] Error during save operation:', error);
      setIsSaving(false);
      alert('Error saving artwork. Please try again.');
    }
  }, [land.id, onSave, onClose]);

  // Toggle UI states
  const toggleFullScreen = useCallback(() => {
    setIsFullScreen(prev => !prev);
  }, []);

  const toggleToolPanel = useCallback(() => {
    setIsToolPanelOpen(prev => !prev);
  }, []);

  const toggleColorPanel = useCallback(() => {
    setIsColorPanelOpen(prev => !prev);
  }, []);

  const toggleToolSettings = useCallback(() => {
    setIsToolSettingsOpen(prev => !prev);
  }, []);

  // Add an effect to disable body scrolling when modal is open
  useEffect(() => {
    // Disable body scrolling while paint modal is open
    document.body.style.overflow = 'hidden';
    document.body.style.touchAction = 'none';

    // Re-enable scrolling when component unmounts
    return () => {
      document.body.style.overflow = '';
      document.body.style.touchAction = '';
    };
  }, []);

  // Get the display name for current brush type
  const getBrushTypeName = () => {
    switch (brushType) {
      case 'brush':
        return 'Brush';
      case 'pencil':
        return 'Pencil';
      case 'eraser':
        return 'Eraser';
      case 'fill':
        return 'Fill';
      case 'spray':
        return 'Spray';
      case 'smoothPencil':
        return 'Smooth Pencil';
      case 'watercolor':
        return 'Watercolor';
      case 'rectangle':
        return 'Rectangle';
      case 'rectangleFill':
        return 'Filled Rectangle';
      case 'circle':
        return 'Circle';
      case 'circleFill':
        return 'Filled Circle';
      case 'line':
        return 'Line';
      default:
        return 'Brush';
    }
  };

  // Get icon for brush type
  const getBrushIcon = (type: BrushType) => {
    switch (type) {
      case 'brush':
        return <BsBrush />;
      case 'pencil':
        return <BsPencil />;
      case 'eraser':
        return <FaEraser />;
      case 'fill':
        return <BsPaintBucket />;
      case 'spray':
        return <GiSpray />;
      case 'smoothPencil':
        return <FaPencilAlt />;
      case 'watercolor':
        return <FaWater />;
      case 'rectangle':
        return <BsSquare />;
      case 'rectangleFill':
        return <BsSquareFill />;
      case 'circle':
        return <BsCircle />;
      case 'circleFill':
        return <BsCircleFill />;
      case 'line':
        return <FaDrawPolygon />;
      default:
        return <BsBrush />;
    }
  };

  // Handle browser resizes for canvas scaling
  useEffect(() => {
    const handleResize = () => {
      // Force canvas to redraw at new size - most libraries need this
      if (canvasRef.current && fabricCanvasRef.current) {
        // Ensure fabricCanvasRef.current exists
        console.log('[PaintTool] handleResize called'); // Log
        const saveData = JSON.stringify(fabricCanvasRef.current?.toJSON());
        // Brief timeout to ensure resize completes
        setTimeout(() => {
          if (fabricCanvasRef.current) {
            console.log(
              '[PaintTool] handleResize: Attempting to reload canvas from saveData (TEMPORARILY DISABLED)'
            ); // Log
            // fabricCanvasRef.current.loadFromJSON(saveData); // Temporarily disabled
          }
        }, 100);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <motion.div
      className={`fixed inset-0 bg-black/90 backdrop-blur-lg flex items-center justify-center z-50 overflow-hidden ${isFullScreen ? '' : 'p-0 md:p-4'}`}
      style={{ overscrollBehavior: 'none', touchAction: 'none' }}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      {/* Use a fixed height container with internal scrolling only if needed */}
      <motion.div
        className={`bg-gray-900/80 backdrop-blur-xl flex flex-col text-white pointer-events-auto overflow-hidden
                    ${isFullScreen ? 'w-full h-full rounded-none border-none' : 'w-full max-w-6xl rounded-xl border border-purple-500/30'}`}
        style={
          !isFullScreen
            ? {
                height: 'min(95vh, 900px)',
                maxHeight: '95vh',
              }
            : {
                height: '100vh',
                width: '100vw',
              }
        }
        initial={{ scale: 0.97, y: 10 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.97, y: 10 }}
        transition={{ type: 'spring', damping: 30, stiffness: 300 }}
      >
        {/* Fixed-position header */}
        <div
          className={`flex items-center justify-between p-2 md:p-3 border-b border-white/15 bg-gray-900/90 backdrop-blur-sm shrink-0
                        ${isFullScreen ? '' : 'rounded-t-xl'}`}
        >
          <div className="flex items-center gap-2">
            <button
              onClick={toggleToolPanel}
              className="p-2 rounded-full hover:bg-white/10 transition-colors text-gray-300 hover:text-white"
              aria-label={isToolPanelOpen ? 'Close Tools' : 'Open Tools'}
            >
              {isToolPanelOpen ? <FaChevronLeft /> : <FaTools />}
            </button>
            <FaPalette className="text-purple-400 hidden sm:block" />
            <div>
              <h3
                className="text-sm md:text-base font-bold truncate max-w-[150px] sm:max-w-xs"
                title={`Paint Land: ${land.name}`}
              >
                <span className="hidden sm:inline">Paint:</span> {land.name}
              </h3>
              <p className="text-xs text-gray-300 hidden md:block">
                <span className="inline-flex items-center bg-purple-500/20 px-2 py-0.5 rounded mr-2">
                  {land.angularSize.toFixed(1)}° Size
                </span>
                <span className="inline-flex items-center bg-gray-500/20 px-2 py-0.5 rounded mr-2">
                  {CANVAS_SIZE}×{CANVAS_SIZE} px
                </span>
                {land.price ? (
                  <span className="inline-flex items-center bg-green-500/20 px-2 py-0.5 rounded">
                    ${land.price} Value
                  </span>
                ) : null}
              </p>
            </div>
          </div>

          <div className="flex items-center gap-1 md:gap-2">
            <div className="flex items-center gap-1 bg-black/30 rounded-lg px-2 py-1">
              <button
                onClick={handleUndo}
                className="text-gray-300 hover:text-white transition-colors p-1.5 rounded-md hover:bg-white/10"
                aria-label="Undo"
                title="Undo"
              >
                <FaUndo className="w-3.5 h-3.5" />
              </button>
              <button
                onClick={handleClear}
                className="text-gray-300 hover:text-white transition-colors p-1.5 rounded-md hover:bg-white/10"
                aria-label="Clear Canvas"
                title="Clear Canvas"
              >
                <FaTrash className="w-3.5 h-3.5" />
              </button>
            </div>

            <button
              onClick={toggleFullScreen}
              className="text-gray-300 hover:text-white transition-colors p-2 rounded-full hover:bg-white/10"
              aria-label={isFullScreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
              title={isFullScreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}
            >
              {isFullScreen ? <FaCompress /> : <FaExpand />}
            </button>
            <button
              onClick={onClose}
              className="text-gray-300 hover:text-white transition-colors p-2 rounded-full hover:bg-white/10"
              aria-label="Close"
              title="Close"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        {/* Main content area with fixed height and flex properties */}
        <div className="flex flex-1 min-h-0 overflow-hidden">
          {/* Left Tools panel - vertical, collapsible */}
          <AnimatePresence>
            {isToolPanelOpen && (
              <motion.div
                initial={{ x: '-100%', opacity: 0 }}
                animate={{ x: '0%', opacity: 1 }}
                exit={{ x: '-100%', opacity: 0 }}
                transition={{ type: 'tween', duration: 0.2, ease: 'easeOut' }}
                className="w-14 md:w-16 bg-gray-900/80 border-r border-white/10 flex flex-col py-2 gap-1 shrink-0 overflow-y-auto"
              >
                {/* Brush tools grid */}
                <div className="px-2">
                  <p className="text-[10px] uppercase tracking-wider text-gray-400 mb-1 text-center">
                    Tools
                  </p>
                  <div className="grid grid-cols-1 gap-1">
                    {/* Paint Brush */}
                    <button
                      onClick={() => setBrushType('brush')}
                      className={`p-2 aspect-square rounded-md transition-all ${brushType === 'brush' ? 'bg-purple-600 shadow-lg' : 'bg-black/30 hover:bg-gray-700/50'}`}
                      aria-label="Paint Brush"
                      title="Paint Brush"
                    >
                      <BsBrush className="h-full w-full" />
                    </button>

                    {/* Pencil */}
                    <button
                      onClick={() => setBrushType('pencil')}
                      className={`p-2 aspect-square rounded-md transition-all ${brushType === 'pencil' ? 'bg-purple-600 shadow-lg' : 'bg-black/30 hover:bg-gray-700/50'}`}
                      aria-label="Pencil"
                      title="Pencil"
                    >
                      <BsPencil className="h-full w-full" />
                    </button>

                    {/* Eraser */}
                    <button
                      onClick={() => setBrushType('eraser')}
                      className={`p-2 aspect-square rounded-md transition-all ${brushType === 'eraser' ? 'bg-purple-600 shadow-lg' : 'bg-black/30 hover:bg-gray-700/50'}`}
                      aria-label="Eraser"
                      title="Eraser"
                    >
                      <FaEraser className="h-full w-full" />
                    </button>

                    {/* Fill Bucket */}
                    <button
                      onClick={() => setBrushType('fill')}
                      className={`p-2 aspect-square rounded-md transition-all ${brushType === 'fill' ? 'bg-purple-600 shadow-lg' : 'bg-black/30 hover:bg-gray-700/50'}`}
                      aria-label="Fill Bucket"
                      title="Fill Bucket"
                    >
                      <BsPaintBucket className="h-full w-full" />
                    </button>

                    {/* Spray */}
                    <button
                      onClick={() => setBrushType('spray')}
                      className={`p-2 aspect-square rounded-md transition-all ${brushType === 'spray' ? 'bg-purple-600 shadow-lg' : 'bg-black/30 hover:bg-gray-700/50'}`}
                      aria-label="Spray Paint"
                      title="Spray Paint"
                    >
                      <GiSpray className="h-full w-full" />
                    </button>

                    {/* Line */}
                    <button
                      onClick={() => setBrushType('line')}
                      className={`p-2 aspect-square rounded-md transition-all ${brushType === 'line' ? 'bg-purple-600 shadow-lg' : 'bg-black/30 hover:bg-gray-700/50'}`}
                      aria-label="Line Tool"
                      title="Line Tool"
                    >
                      <FaDrawPolygon className="h-full w-full" />
                    </button>

                    {/* Rectangle */}
                    <button
                      onClick={() => setBrushType('rectangle')}
                      className={`p-2 aspect-square rounded-md transition-all ${brushType === 'rectangle' ? 'bg-purple-600 shadow-lg' : 'bg-black/30 hover:bg-gray-700/50'}`}
                      aria-label="Rectangle"
                      title="Rectangle"
                    >
                      <BsSquare className="h-full w-full" />
                    </button>

                    {/* Filled Rectangle */}
                    <button
                      onClick={() => setBrushType('rectangleFill')}
                      className={`p-2 aspect-square rounded-md transition-all ${brushType === 'rectangleFill' ? 'bg-purple-600 shadow-lg' : 'bg-black/30 hover:bg-gray-700/50'}`}
                      aria-label="Filled Rectangle"
                      title="Filled Rectangle"
                    >
                      <BsSquareFill className="h-full w-full" />
                    </button>

                    {/* Circle */}
                    <button
                      onClick={() => setBrushType('circle')}
                      className={`p-2 aspect-square rounded-md transition-all ${brushType === 'circle' ? 'bg-purple-600 shadow-lg' : 'bg-black/30 hover:bg-gray-700/50'}`}
                      aria-label="Circle"
                      title="Circle"
                    >
                      <BsCircle className="h-full w-full" />
                    </button>

                    {/* Filled Circle */}
                    <button
                      onClick={() => setBrushType('circleFill')}
                      className={`p-2 aspect-square rounded-md transition-all ${brushType === 'circleFill' ? 'bg-purple-600 shadow-lg' : 'bg-black/30 hover:bg-gray-700/50'}`}
                      aria-label="Filled Circle"
                      title="Filled Circle"
                    >
                      <BsCircleFill className="h-full w-full" />
                    </button>
                  </div>
                </div>

                {/* Color preview */}
                <div className="px-2 mt-auto">
                  <button
                    onClick={toggleColorPanel}
                    className="mt-2 w-full aspect-square rounded-md border-2 border-white/30 shadow-inner overflow-hidden"
                    style={{ backgroundColor: currentColor }}
                    aria-label="Change Color"
                    title="Change Color"
                  />
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main canvas area */}
          <div className="flex-1 flex flex-col min-w-0 min-h-0 overflow-hidden">
            {/* Canvas container */}
            <div
              ref={canvasContainerRef}
              className="flex-1 flex items-center justify-center bg-neutral-800 min-h-0 overflow-hidden"
              onClick={brushType === 'fill' ? handleFillBucket : undefined}
            >
              <div className="relative h-full w-full flex items-center justify-center">
                {/* Fabric.js canvas */}
                <canvas
                  ref={canvasRef}
                  className="touch-none select-none bg-white shadow-xl border border-gray-300"
                  style={{
                    width: '100%',
                    height: '100%',
                    maxWidth: '100%',
                    maxHeight: '100%',
                    touchAction: 'none',
                  }}
                  width={CANVAS_SIZE}
                  height={CANVAS_SIZE}
                />

                {/* Current tool indicator */}
                <div className="absolute top-2 left-2 bg-black/50 rounded-lg px-3 py-1 text-xs font-medium flex items-center gap-2">
                  <span className="flex items-center justify-center w-5 h-5">
                    {getBrushIcon(brushType)}
                  </span>
                  <span>{getBrushTypeName()}</span>
                </div>

                {/* Tool instructions overlay */}
                <div className="absolute top-2 right-2 bg-black/50 rounded-lg px-3 py-1 text-xs font-medium max-w-xs text-gray-200">
                  {brushType === 'brush' && 'Click and drag to paint with a smooth brush'}
                  {brushType === 'pencil' && 'Click and drag to draw with thinner lines'}
                  {brushType === 'eraser' && 'Click and drag over existing paint to erase'}
                  {brushType === 'fill' && 'Click on the canvas to fill it with the selected color'}
                  {brushType === 'spray' && 'Click and drag to spray paint with a diffused effect'}
                  {brushType === 'watercolor' && 'Paint with a transparent watercolor effect'}
                  {brushType === 'line' && 'Click and drag to draw a straight line'}
                  {brushType === 'rectangle' && 'Click and drag to draw a rectangle outline'}
                  {brushType === 'rectangleFill' && 'Click and drag to draw a filled rectangle'}
                  {brushType === 'circle' && 'Click and drag outward to draw a circle outline'}
                  {brushType === 'circleFill' && 'Click and drag outward to draw a filled circle'}
                </div>
              </div>
            </div>

            {/* Tool settings panel - conditionally shown below canvas */}
            <AnimatePresence>
              {isToolSettingsOpen && (
                <motion.div
                  initial={{ y: '100%' }}
                  animate={{ y: '0%' }}
                  exit={{ y: '100%' }}
                  transition={{ type: 'tween', duration: 0.2 }}
                  className="bg-gray-900/90 border-t border-white/10 p-3"
                >
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="text-sm font-medium">{getBrushTypeName()} Settings</h4>
                    <button
                      onClick={toggleToolSettings}
                      className="text-gray-400 hover:text-white p-1 rounded-full"
                    >
                      <FaChevronDown />
                    </button>
                  </div>

                  <div className="flex flex-wrap gap-x-4 gap-y-2">
                    {/* Brush size control */}
                    <div className="flex flex-col gap-1 min-w-[120px]">
                      <div className="flex justify-between items-center">
                        <label className="text-xs text-gray-300">Size</label>
                        <span className="text-xs text-gray-400">{brushRadius}px</span>
                      </div>
                      <input
                        type="range"
                        min="1"
                        max="50"
                        value={brushRadius}
                        onChange={e => setBrushRadius(parseInt(e.target.value))}
                        className="w-full accent-purple-500"
                      />
                    </div>

                    {/* Opacity control */}
                    <div className="flex flex-col gap-1 min-w-[120px]">
                      <div className="flex justify-between items-center">
                        <label className="text-xs text-gray-300">Opacity</label>
                        <span className="text-xs text-gray-400">{opacity}%</span>
                      </div>
                      <input
                        type="range"
                        min="1"
                        max="100"
                        value={opacity}
                        onChange={e => setOpacity(parseInt(e.target.value))}
                        className="w-full accent-purple-500"
                      />
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Color panel - show when triggered */}
            <AnimatePresence>
              {isColorPanelOpen && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 20 }}
                  className="absolute left-0 right-0 bottom-20 mx-auto w-64 bg-gray-900/95 backdrop-blur-md rounded-lg shadow-xl p-4 border border-white/10"
                >
                  <div className="flex justify-between items-center mb-3">
                    <h4 className="text-sm font-medium">Color Picker</h4>
                    <button
                      onClick={toggleColorPanel}
                      className="text-gray-400 hover:text-white p-1 rounded-full"
                    >
                      <FaTimes className="w-3 h-3" />
                    </button>
                  </div>

                  <HexColorPicker
                    color={currentColor}
                    onChange={setCurrentColor}
                    className="w-full mb-3"
                  />

                  <div className="flex flex-wrap gap-1 mt-2">
                    {colors.map(color => (
                      <button
                        key={color}
                        onClick={() => setCurrentColor(color)}
                        className={`w-6 h-6 rounded-sm border transition-all ${
                          currentColor === color
                            ? 'border-white scale-110 shadow-lg'
                            : 'border-gray-700 hover:scale-105'
                        }`}
                        style={{ backgroundColor: color }}
                        aria-label={`Select color ${color}`}
                      />
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Fixed action bar at the bottom */}
            <div className="flex justify-between items-center p-3 border-t border-white/15 bg-gray-900/90 backdrop-blur-sm shrink-0">
              <button
                onClick={toggleToolSettings}
                className="text-xs px-3 py-1.5 rounded-md bg-black/30 hover:bg-white/10 transition-colors"
              >
                {isToolSettingsOpen ? 'Hide Settings' : 'Show Settings'}
              </button>

              <div className="flex gap-2">
                <button
                  onClick={onClose}
                  className="px-3 py-1.5 text-sm font-medium rounded-md border border-gray-600 text-gray-300 hover:bg-gray-700 transition-colors"
                  disabled={isSaving}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className={`flex items-center gap-1 px-3 py-1.5 text-sm font-medium rounded-md ${isSaving ? 'bg-green-600 animate-pulse' : 'bg-purple-600 hover:bg-purple-700'} text-white transition-colors`}
                >
                  {isSaving ? (
                    'Saving...'
                  ) : (
                    <>
                      <FaSave className="w-3.5 h-3.5" /> Save
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
