#!/bin/bash
# Startup script for Atlas VPN backend with monitoring
# This script activates the virtual environment and starts all necessary services

# Configuration
PROJECT_DIR="/opt/atlasvpn"
BACKEND_DIR="$PROJECT_DIR/backend"
VENV_DIR="$BACKEND_DIR/venv"
LOG_DIR="$BACKEND_DIR/logs"
START_MONITOR=true

# Create log directory if it doesn't exist
mkdir -p "$LOG_DIR"

# Function to log messages
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_DIR/startup.log"
}

# Ensure we're in the right directory
cd "$BACKEND_DIR" || {
    log "ERROR: Failed to change to backend directory at $BACKEND_DIR";
    exit 1;
}

# Activate virtual environment
if [ -d "$VENV_DIR" ]; then
    log "Activating Python virtual environment at $VENV_DIR";
    source "$VENV_DIR/bin/activate" || {
        log "ERROR: Failed to activate virtual environment";
        exit 1;
    }
else
    log "ERROR: Virtual environment not found at $VENV_DIR";
    exit 1;
fi

# Verify Python version
PYTHON_VERSION=$(python3 --version)
log "Using $PYTHON_VERSION from $(which python3)"

# Verify installed packages
log "Checking installed packages..."
pip freeze | grep -E 'fastapi|uvicorn|sqlalchemy|psutil|aiohttp' | tee -a "$LOG_DIR/startup.log"

# Check if any missing packages need to be installed
if ! pip freeze | grep -q "psutil"; then
    log "Installing missing psutil package..."
    pip install psutil
fi

# Start resource monitor if enabled
if [ "$START_MONITOR" = true ]; then
    log "Starting resource monitor..."
    
    # Kill any existing monitor process
    if [ -f "$BACKEND_DIR/resource_monitor.pid" ]; then
        OLD_PID=$(cat "$BACKEND_DIR/resource_monitor.pid")
        if ps -p "$OLD_PID" > /dev/null; then
            log "Killing existing resource monitor process (PID: $OLD_PID)"
            kill "$OLD_PID" 2>/dev/null
        fi
        rm -f "$BACKEND_DIR/resource_monitor.pid"
    fi
    
    # Start new monitor process
    nohup python3 "$BACKEND_DIR/resource_monitor.py" --interval 30 > "$LOG_DIR/resource_monitor.log" 2>&1 &
    MONITOR_PID=$!
    echo "$MONITOR_PID" > "$BACKEND_DIR/resource_monitor.pid"
    log "Resource monitor started with PID $MONITOR_PID"
fi

# Make monitor script executable
chmod +x "$BACKEND_DIR/monitor_and_restart.sh"

# Set up cron job for periodic monitoring
log "Setting up cron job for periodic monitoring..."
(crontab -l 2>/dev/null | grep -v "monitor_and_restart.sh"; echo "*/15 * * * * $BACKEND_DIR/monitor_and_restart.sh") | crontab -

# Start the main application
log "Starting Atlas VPN backend service..."

# Use uvicorn with optimal settings for production
exec uvicorn main:app \
    --host 0.0.0.0 \
    --port 8000 \
    --workers 4 \
    --log-level info \
    --log-config "$BACKEND_DIR/log_config.json" \
    --reload \
    --reload-dir "$BACKEND_DIR" \
    --reload-exclude "$BACKEND_DIR/venv" \
    --reload-exclude "$BACKEND_DIR/logs" \
    >> "$LOG_DIR/uvicorn.log" 2>&1 