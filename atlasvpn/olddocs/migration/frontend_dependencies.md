# Frontend Environment
Node.js: v22.13.1
PNPM: 9.15.4

# Package.json vs Installed Versions
```
Legend: production dependency, optional only, dev only

vpn-dashboard-frontend@0.1.0 /root/project/frontend (PRIVATE)

dependencies:
@headlessui/react 2.2.0
@heroicons/react 2.2.0
@react-three/drei 9.120.3
@react-three/fiber 8.17.10
@sentry/browser 8.42.0
@sentry/react 8.42.0
@sentry/tracing 7.114.0
@telegram-apps/sdk-react 2.0.22
@types/crypto-js 4.2.2
@types/js-cookie 3.0.6
@types/lodash.debounce 4.0.9
@types/qrcode.react 1.0.5
@types/react 18.3.14
@types/react-dom 18.3.2
@types/three 0.170.0
axios 1.7.9
clsx 2.1.1
crypto-js 4.2.0
date-fns 4.1.0
dompurify 3.2.3
framer-motion 11.13.1
isomorphic-dompurify 2.19.0
js-cookie 3.0.5
lodash.debounce 4.0.8
qrcode.react 4.1.0
react 18.3.1
react-dom 18.3.1
react-hook-form 7.54.2
react-hot-toast 2.4.1
react-icons 5.4.0
react-router-dom 7.0.2
react-select 5.8.3
react-type-animation 3.2.0
three 0.171.0
zod 3.24.1

devDependencies:
@types/node 22.10.1
@typescript-eslint/eslint-plugin 8.18.2
@typescript-eslint/parser 8.18.2
@vitejs/plugin-react-swc 3.7.2
autoprefixer 10.4.20
eslint 9.17.0
eslint-plugin-react 7.37.3
eslint-plugin-react-hooks 5.1.0
postcss 8.4.49
tailwindcss 3.4.16
typescript 5.7.2
vite 6.0.3
```
