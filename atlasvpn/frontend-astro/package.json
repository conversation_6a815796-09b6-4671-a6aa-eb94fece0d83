{"name": "frontend-astro", "version": "0.0.1", "private": true, "scripts": {"dev": "astro dev --host 0.0.0.0 --port 3005", "start": "astro dev", "build": "NODE_OPTIONS='--max-old-space-size=4096' astro check && NODE_OPTIONS='--max-old-space-size=4096' astro build", "preview": "astro preview --host 0.0.0.0 --port 3005", "astro": "astro", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/react": "^4.3.0", "@astrojs/tailwind": "^6.0.2", "@heroicons/react": "^2.2.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@telegram-apps/sdk-react": "^3.3.6", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "astro": "^5.10.1", "axios": "^1.10.0", "crypto-js": "^4.2.0", "framer-motion": "^12.20.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "tailwindcss": "^3.4.17", "three": "^0.177.0", "typescript": "^5.8.3", "zustand": "^5.0.6"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@vitest/coverage-v8": "^3.2.4", "jsdom": "^26.1.0", "vite": "^7.0.6", "vitest": "^3.2.4"}}