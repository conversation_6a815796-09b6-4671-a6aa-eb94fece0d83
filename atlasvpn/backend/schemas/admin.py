"""
Admin-related Pydantic schemas for validation and serialization.
Contains schemas for admin authentication, task statistics, security logs, and admin-specific operations.
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, ConfigDict
import ipaddress
import json

from .base import BaseModelConfig
from .task import TaskOut, RewardType, TaskStatus


# Admin Authentication schemas
class AdminLoginRequest(BaseModelConfig):
    """Schema for admin login request"""
    username: str
    password: str
    csrf_token: Optional[str] = None  # Allow csrf_token from frontend

    model_config = ConfigDict(from_attributes=True)


# Task Statistics and Admin Task schemas
class TaskStatistics(BaseModelConfig):
    """Task statistics schema for admin analytics"""
    total_attempts: int = Field(..., ge=0)
    successful_completions: int = Field(..., ge=0)
    claimed_rewards: int = Field(..., ge=0)
    success_rate: float = Field(..., ge=0, le=100)
    active_users: int = Field(..., ge=0)
    
    model_config = ConfigDict(from_attributes=True)


class AdminTaskOut(TaskOut):
    """Extended task schema with admin statistics"""
    statistics: Optional[TaskStatistics] = None
    total_verifications: Optional[int] = Field(None, ge=0)
    successful_verifications: Optional[int] = Field(None, ge=0)
    verification_success_rate: Optional[float] = Field(None, ge=0, le=100)
    average_completion_time: Optional[int] = Field(None, ge=0)  # In minutes
    
    model_config = ConfigDict(from_attributes=True)


class TaskStatusUpdate(BaseModelConfig):
    """Schema for updating task status"""
    status: TaskStatus
    current_progress: Optional[int] = None
    verification_data: Optional[dict] = None
    
    model_config = ConfigDict(from_attributes=True)


class UpdateTaskData(BaseModelConfig):
    """Schema for updating task data"""
    name: Optional[str] = Field(None, min_length=3, max_length=100)
    description: Optional[str] = Field(None, min_length=10)
    reward_type: Optional[RewardType] = None
    reward_value: Optional[float] = Field(None, ge=0, le=1000)
    target_value: Optional[int] = Field(None, ge=1)
    is_active: Optional[bool] = None
    platform_url: Optional[str] = None
    platform_id: Optional[str] = None
    verify_key: Optional[str] = None
    max_verification_attempts: Optional[int] = Field(None, ge=1, le=10)
    verification_cooldown: Optional[int] = Field(None, ge=30, le=3600)

    @field_validator('platform_url')
    @classmethod
    def validate_platform_url(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            if not v.startswith(('http://', 'https://')):
                raise ValueError('Platform URL must start with http:// or https://')
            if len(v) > 500:
                raise ValueError('Platform URL cannot exceed 500 characters')
        return v

    @field_validator('max_verification_attempts')
    @classmethod
    def validate_max_attempts(cls, v: Optional[int]) -> Optional[int]:
        if v is not None:
            if v < 1:
                raise ValueError('Maximum verification attempts must be at least 1')
            if v > 10:
                raise ValueError('Maximum verification attempts cannot exceed 10')
        return v

    @field_validator('verification_cooldown')
    @classmethod
    def validate_verification_cooldown(cls, v: Optional[int]) -> Optional[int]:
        if v is not None:
            if v < 30:
                raise ValueError('Verification cooldown must be at least 30 seconds')
            if v > 3600:
                raise ValueError('Verification cooldown cannot exceed 3600 seconds (1 hour)')
        return v


# Security and Logging schemas
class SecurityLogCreate(BaseModelConfig):
    """Schema for creating security logs"""
    action_type: str = Field(..., max_length=50)
    user_id: Optional[int] = Field(None, gt=0)
    ip_address: str = Field(..., max_length=45)
    user_agent: Optional[str] = Field(None, max_length=255)
    status: str = Field(..., max_length=20)
    details: Optional[Dict[str, Any]] = None
    risk_level: str = Field(..., pattern=r'^(low|medium|high|critical)$')

    model_config = ConfigDict(from_attributes=True)

    @field_validator('action_type')
    @classmethod
    def validate_action_type(cls, v: str) -> str:
        valid_actions = {
            'login_attempt', 'password_reset', 'profile_update',
            'task_verification', 'token_blacklist', 'rate_limit',
            'suspicious_activity'
        }
        if v not in valid_actions:
            raise ValueError(f'Invalid action type. Must be one of: {", ".join(sorted(valid_actions))}')
        return v

    @field_validator('ip_address')
    @classmethod
    def validate_ip_address(cls, v: str) -> str:
        try:
            ipaddress.ip_address(v)
        except ValueError:
            raise ValueError('Invalid IP address format')
        return v

    @field_validator('user_agent')
    @classmethod
    def validate_user_agent(cls, v: Optional[str]) -> Optional[str]:
        if v is not None:
            # Remove any non-printable characters
            v = ''.join(char for char in v if char.isprintable())
            # Truncate if too long
            if len(v) > 255:
                v = v[:255]
        return v

    @field_validator('status')
    @classmethod
    def validate_status(cls, v: str) -> str:
        valid_statuses = {'success', 'failure', 'blocked', 'warning', 'error'}
        if v not in valid_statuses:
            raise ValueError(f'Invalid status. Must be one of: {", ".join(sorted(valid_statuses))}')
        return v

    @field_validator('details')
    @classmethod
    def validate_details(cls, v: Optional[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        if v is not None:
            # Convert to JSON and back to ensure serializability
            try:
                json_str = json.dumps(v)
                if len(json_str) > 1000:
                    raise ValueError('Details JSON cannot exceed 1000 characters')
                return json.loads(json_str)
            except (TypeError, json.JSONDecodeError):
                raise ValueError('Details must be JSON serializable')
        return v

    @field_validator('risk_level')
    @classmethod
    def validate_risk_level(cls, v: str) -> str:
        valid_levels = {'low', 'medium', 'high', 'critical'}
        if v not in valid_levels:
            raise ValueError(f'Invalid risk level. Must be one of: {", ".join(sorted(valid_levels))}')
        return v.lower()


# Token Blacklist schemas
class TokenBlacklistSchema(BaseModelConfig):
    """Schema for token blacklist management"""
    token: str = Field(..., max_length=500)
    user_id: int = Field(..., gt=0)
    expires_at: datetime
    revoked_at: datetime = Field(default_factory=datetime.utcnow)
    revocation_reason: str = Field(..., max_length=200)
    revoked_by_ip: str = Field(..., max_length=45)

    model_config = ConfigDict(from_attributes=True)

    @field_validator('token')
    @classmethod
    def validate_token(cls, v: str) -> str:
        if not v:
            raise ValueError('Token is required')
        if len(v) > 500:
            raise ValueError('Token cannot exceed 500 characters')
        return v

    @field_validator('revocation_reason')
    @classmethod
    def validate_reason(cls, v: str) -> str:
        if not v:
            raise ValueError('Revocation reason is required')
        if len(v) > 200:
            raise ValueError('Revocation reason cannot exceed 200 characters')
        return v.strip()

    @field_validator('revoked_by_ip')
    @classmethod
    def validate_ip(cls, v: str) -> str:
        try:
            ipaddress.ip_address(v)
            return v
        except ValueError:
            raise ValueError('Invalid IP address format')

    @field_validator('expires_at')
    @classmethod
    def validate_expiry(cls, v: datetime) -> datetime:
        if v < datetime.utcnow():
            raise ValueError('Expiry time cannot be in the past')
        return v

    @field_validator('revoked_at')
    @classmethod
    def validate_revocation_time(cls, v: datetime, info) -> datetime:
        expires_at = info.data.get('expires_at')
        if expires_at and v > expires_at:
            raise ValueError('Revocation time cannot be after expiry time')
        return v


# Additional task-related schemas for admin operations
class TaskRewardOut(BaseModelConfig):
    """Schema for task reward information"""
    task_id: int
    task_name: str
    reward_type: RewardType
    reward_value: float
    claimed_at: datetime
    transaction_id: Optional[int]
    
    model_config = ConfigDict(from_attributes=True)


class TaskStatusOut(BaseModelConfig):
    """Schema for task status information"""
    status: TaskStatus
    verification_attempts: int = Field(..., ge=0, le=10)
    verification_time_left: int = Field(..., ge=0, le=3600)  # Seconds until next verification
    current_progress: int = Field(..., ge=0, le=1000)
    is_claimed: bool
    last_verified_at: Optional[datetime] = None
    next_verification_time: Optional[datetime] = None
    verification_method: Optional[str] = Field(None, pattern=r'^(api|code|manual|auto)$')
    
    model_config = ConfigDict(from_attributes=True)


# Platform-specific verification data schemas
class YouTubeVerificationData(BaseModelConfig):
    """Schema for YouTube verification data"""
    verification_code: str


class SocialFollowVerificationData(BaseModelConfig):
    """Schema for social media follow verification data"""
    platform: str
    platform_id: str
    platform_url: str 