import React, { useState, useEffect, useCallback, memo } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../utils/apiClient';
import { Role, User } from '../types';
import Avatar3DViewer from '../components/Avatar3DViewer';
import AvatarSelector from '../components/AvatarSelector';
import {
  PencilIcon,
  ShieldCheckIcon,
  KeyIcon,
  UserCircleIcon,
  BellIcon,
  CreditCardIcon,
} from '@heroicons/react/24/outline';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';

const MemoizedAvatar3DViewer = memo(Avatar3DViewer);

interface ProfilePageProps {
  isTab?: boolean;
}

interface ProfileFormData {
  email: string;
  first_name: string;
  last_name: string;
  telegram_photo_url: string;
}

const ProfilePage: React.FC<ProfilePageProps> = ({ isTab = false }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [showAvatarSelector, setShowAvatarSelector] = useState(false);
  const [activeTab, setActiveTab] = useState<'profile' | 'privacy' | 'notifications'>('profile');
  const [formData, setFormData] = useState<ProfileFormData>({
    email: '',
    first_name: '',
    last_name: '',
    telegram_photo_url: '',
  });

  useEffect(() => {
    // Placeholder for user data fetching
  }, []);

  const showNotification = useCallback((message: string, isError = false) => {
    if (isError) {
      setError(message);
    } else {
      setSuccess(true);
    }
    setTimeout(() => {
      setError(null);
      setSuccess(false);
    }, 3000);
  }, []);

  const handleProfileUpdate = async (data: Partial<ProfileFormData>) => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.put('/api/user/profile/update', data);

      if (response.status === 200 && response.data) {
        setFormData(prev => ({
          ...prev,
          ...data,
        }));
        showNotification('Profile updated successfully');
      }
    } catch (error) {
      showNotification(error instanceof Error ? error.message : 'Failed to update profile', true);
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarSelect = useCallback(
    async (avatarName: string) => {
      try {
        setLoading(true);
        setError(null);

        const cleanAvatarName = avatarName.replace('.webp', '').trim();

        await handleProfileUpdate({
          telegram_photo_url: cleanAvatarName,
        });

        setShowAvatarSelector(false);
      } catch (error) {
        showNotification(error instanceof Error ? error.message : 'Failed to update avatar', true);
      } finally {
        setLoading(false);
      }
    },
    [handleProfileUpdate, showNotification]
  );

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      await handleProfileUpdate({
        email: formData.email,
        first_name: formData.first_name,
        last_name: formData.last_name,
      });
    },
    [formData, handleProfileUpdate]
  );

  const content = (
    <div className="min-h-screen bg-gray-900 p-4">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-6">Profile</h1>
        {/* Profile Card */}
        <motion.div
          className="relative mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-accent-500/20 via-purple-500/20 to-accent-500/20 blur-3xl -z-10 animate-pulse" />

          {/* Horizontal Card Layout */}
          <div className="flex flex-col md:flex-row items-center md:items-start gap-8 p-6 rounded-2xl bg-base-900/50 backdrop-blur-md border border-accent-500/20">
            {/* Avatar Section with Animated Frame */}
            <div className="relative">
              {/* Rotating outer ring */}
              <motion.div
                className="absolute -inset-4 rounded-full border-2 border-dashed border-accent-500/30"
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: 'linear' }}
              />

              {/* Pulsing glow rings */}
              <motion.div
                className="absolute -inset-2 rounded-full bg-accent-500/10"
                animate={{ scale: [1, 1.1, 1], opacity: [0.5, 0.2, 0.5] }}
                transition={{ duration: 2, repeat: Infinity }}
              />

              {/* Avatar Container */}
              <motion.div
                className="relative cursor-pointer"
                onClick={() => setShowAvatarSelector(true)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="rounded-full overflow-hidden bg-black/50 backdrop-blur-sm ring-2 ring-accent-500/40 hover:ring-accent-500/60 transition-all">
                  <Avatar3DViewer
                    filename={`${formData.telegram_photo_url || 'Lovely Angel'}.webp`}
                    size={{ width: 140, height: 140 }}
                    className="transform hover:scale-105 transition-all"
                  />
                  <div className="absolute inset-0 bg-black/50 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                    <PencilIcon className="w-6 h-6 text-white" />
                  </div>
                </div>
              </motion.div>
            </div>

            {/* User Details Section */}
            <div className="flex-1 text-center md:text-left">
              <motion.div
                className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-accent-400 via-purple-400 to-accent-300 bg-clip-text text-transparent uppercase tracking-wider"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                {/* Placeholder for username */}
              </motion.div>

              <motion.div
                className="mt-2 text-lg font-medium text-white/80"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2 }}
              >
                Privacy Guardian #0000
              </motion.div>

              {/* Stats Grid */}
              <motion.div
                className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <div className="flex flex-col items-center md:items-start">
                  <span className="text-xs text-white/60">Member Since</span>
                  <span className="text-sm text-accent-300">
                    {/* Placeholder for created_at */}
                  </span>
                </div>

                {/* Placeholder for wallet_balance */}
                <div className="flex flex-col items-center md:items-start">
                  <span className="text-xs text-white/60">Balance</span>
                  <div className="flex items-center space-x-1">
                    <CreditCardIcon className="w-4 h-4 text-accent-400" />
                    <span className="text-sm text-accent-300">$0.00</span>
                  </div>
                </div>

                <div className="flex flex-col items-center md:items-start">
                  <span className="text-xs text-white/60">Status</span>
                  <span className="text-sm text-green-400">Active</span>
                </div>
              </motion.div>
            </div>
          </div>
        </motion.div>

        {/* Navigation Pills */}
        <motion.nav
          className="flex justify-center space-x-1 bg-base-900/30 backdrop-blur-md p-1 rounded-lg border border-accent-500/10 mb-6"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          {[
            { id: 'profile', icon: UserCircleIcon, label: 'Profile' },
            { id: 'privacy', icon: ShieldCheckIcon, label: 'Privacy' },
            { id: 'notifications', icon: BellIcon, label: 'Notifications' },
          ].map(({ id, icon: Icon, label }) => (
            <motion.button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`px-4 py-2 rounded-md flex items-center space-x-2 text-sm font-medium transition-all ${
                activeTab === id
                  ? 'bg-accent-500/20 text-accent-300'
                  : 'text-white/60 hover:text-white hover:bg-white/5'
              }`}
              whileTap={{ scale: 0.98 }}
            >
              <Icon className="w-4 h-4" />
              <span>{label}</span>
            </motion.button>
          ))}
        </motion.nav>

        {/* Content Cards */}
        <AnimatePresence mode="wait">
          {activeTab === 'profile' && (
            <motion.div
              key="profile"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className="card space-y-6"
            >
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-white/70 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="input"
                    placeholder="Enter your email"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-white/70 mb-2">
                      First Name
                    </label>
                    <input
                      type="text"
                      name="first_name"
                      value={formData.first_name}
                      onChange={handleInputChange}
                      className="input"
                      placeholder="Optional"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-white/70 mb-2">
                      Last Name
                    </label>
                    <input
                      type="text"
                      name="last_name"
                      value={formData.last_name}
                      onChange={handleInputChange}
                      className="input"
                      placeholder="Optional"
                    />
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4">
                  <div className="text-sm text-white/60">
                    All fields are optional but help us provide better service
                  </div>
                  <motion.button
                    type="submit"
                    disabled={loading}
                    className="button-primary"
                    whileHover={{ scale: 1.01 }}
                    whileTap={{ scale: 0.99 }}
                  >
                    {loading ? 'Updating...' : 'Update Profile'}
                  </motion.button>
                </div>
              </form>
            </motion.div>
          )}

          {activeTab === 'privacy' && (
            <motion.div
              key="privacy"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-4"
            >
              <div className="card">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-white">Zero Logs Policy</h3>
                    <p className="text-sm text-white/60 mt-1">
                      We never track or store your activities
                    </p>
                  </div>
                  <ShieldCheckIcon className="w-6 h-6 text-green-400" />
                </div>
              </div>

              <div className="card">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-white">End-to-End Encryption</h3>
                    <p className="text-sm text-white/60 mt-1">
                      Your data remains private and secure
                    </p>
                  </div>
                  <KeyIcon className="w-6 h-6 text-green-400" />
                </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'notifications' && (
            <motion.div
              key="notifications"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="card space-y-6"
            >
              <div className="flex items-center justify-between py-2">
                <div>
                  <h3 className="text-lg font-medium text-white">Service Updates</h3>
                  <p className="text-sm text-white/60 mt-1">
                    Important updates about your VPN service
                  </p>
                </div>
                <div className="relative">
                  <input type="checkbox" className="toggle toggle-accent" defaultChecked />
                </div>
              </div>

              <div className="flex items-center justify-between py-2">
                <div>
                  <h3 className="text-lg font-medium text-white">Security Alerts</h3>
                  <p className="text-sm text-white/60 mt-1">
                    Get notified about security-related events
                  </p>
                </div>
                <div className="relative">
                  <input type="checkbox" className="toggle toggle-accent" defaultChecked />
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Avatar Selector Modal */}
        <AnimatePresence>
          {showAvatarSelector && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4"
            >
              <motion.div
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.95, opacity: 0 }}
                className="relative w-full max-w-lg"
              >
                <AvatarSelector
                  onSelect={handleAvatarSelect}
                  onClose={() => setShowAvatarSelector(false)}
                  currentAvatar={formData.telegram_photo_url}
                />
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Notifications */}
        <AnimatePresence>
          {(error || success) && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className={`fixed bottom-20 left-1/2 transform -translate-x-1/2 px-4 py-2 rounded-lg ${
                error ? 'bg-red-500/90' : 'bg-green-500/90'
              } text-white text-sm font-medium shadow-lg`}
            >
              {error || 'Profile updated successfully'}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );

  return isTab ? content : content;
};

export default ProfilePage;
