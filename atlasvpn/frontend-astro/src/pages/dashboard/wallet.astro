---
/**
 * @file dashboard/wallet.astro
 * @description Dashboard wallet page - transactions and balance
 */

import DashboardLayout from '../../layouts/DashboardLayout.astro';
import WalletTabContent from '../../components/react/WalletTabContent';

// Page metadata
const title = "VIPVerse Dashboard - Wallet";
const description = "Manage your wallet, view transactions and balance";
---

<DashboardLayout title={title} description={description} activeTab="wallet">
  <!-- Wallet Tab Content (React Island) -->
  <WalletTabContent client:load />
</DashboardLayout>
