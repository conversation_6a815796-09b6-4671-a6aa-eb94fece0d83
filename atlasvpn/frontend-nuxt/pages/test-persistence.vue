<template>
  <div class="min-h-screen bg-gray-900 text-white p-6">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-4xl font-bold mb-8 text-center">Persistence Test Page</h1>
      
      <!-- Status Indicator -->
      <div class="mb-8 p-4 rounded-lg" :class="persistenceStatusClass">
        <h2 class="text-xl font-semibold mb-2">Persistence Status</h2>
        <div class="flex items-center space-x-4">
          <span class="flex items-center">
            <div class="w-3 h-3 rounded-full mr-2" :class="persistenceStatusDot"></div>
            {{ persistenceStatus }}
          </span>
          <span class="text-sm text-gray-300">
            Last test: {{ lastTestTime }}
          </span>
        </div>
      </div>

      <!-- UI Store Test -->
      <div class="mb-8 p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">UI Store Persistence Test</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-sm font-medium mb-2">Theme</label>
            <select 
              v-model="uiStore.currentTheme" 
              class="w-full p-2 bg-gray-700 rounded border border-gray-600"
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">Welcome Message</label>
            <label class="flex items-center">
              <input 
                type="checkbox" 
                v-model="uiStore.showWelcomeMessage"
                class="mr-2"
              >
              Show Welcome Message
            </label>
          </div>
        </div>
        <div class="text-sm text-gray-400">
          Current values: Theme = {{ uiStore.currentTheme }}, Welcome = {{ uiStore.showWelcomeMessage }}
        </div>
      </div>

      <!-- User Store Test -->
      <div class="mb-8 p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">User Store Persistence Test</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-sm font-medium mb-2">Language</label>
            <select 
              v-model="userStore.preferences.language" 
              class="w-full p-2 bg-gray-700 rounded border border-gray-600"
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
            </select>
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">Currency</label>
            <select 
              v-model="userStore.preferences.currency" 
              class="w-full p-2 bg-gray-700 rounded border border-gray-600"
            >
              <option value="USD">USD</option>
              <option value="EUR">EUR</option>
              <option value="GBP">GBP</option>
              <option value="JPY">JPY</option>
            </select>
          </div>
        </div>
        <div class="text-sm text-gray-400">
          Current values: Language = {{ userStore.preferences.language }}, Currency = {{ userStore.preferences.currency }}
        </div>
      </div>

      <!-- VPN Store Test -->
      <div class="mb-8 p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">VPN Store Persistence Test</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-sm font-medium mb-2">Selected Package</label>
            <input 
              type="text" 
              v-model="vpnStore.selectedPackage" 
              placeholder="Enter package ID"
              class="w-full p-2 bg-gray-700 rounded border border-gray-600"
            >
          </div>
          <div>
            <label class="block text-sm font-medium mb-2">Selected Panel</label>
            <input 
              type="text" 
              v-model="vpnStore.selectedPanel" 
              placeholder="Enter panel ID"
              class="w-full p-2 bg-gray-700 rounded border border-gray-600"
            >
          </div>
        </div>
        <div class="text-sm text-gray-400">
          Current values: Package = {{ vpnStore.selectedPackage || 'None' }}, Panel = {{ vpnStore.selectedPanel || 'None' }}
        </div>
      </div>

      <!-- Test Actions -->
      <div class="mb-8 p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">Test Actions</h2>
        <div class="flex flex-wrap gap-4">
          <button 
            @click="testPersistence"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
          >
            Test Persistence
          </button>
          <button 
            @click="clearAllStorage"
            class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded transition-colors"
          >
            Clear All Storage
          </button>
          <button 
            @click="randomizeValues"
            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
          >
            Randomize Values
          </button>
          <button 
            @click="reloadPage"
            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded transition-colors"
          >
            Reload Page
          </button>
        </div>
      </div>

      <!-- Storage Inspection -->
      <div class="mb-8 p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">LocalStorage Inspection</h2>
        <div class="space-y-2">
          <div v-for="(value, key) in storageContents" :key="key" class="text-sm">
            <span class="font-mono text-blue-400">{{ key }}:</span>
            <span class="ml-2 text-gray-300">{{ value }}</span>
          </div>
        </div>
        <button 
          @click="refreshStorageContents"
          class="mt-4 px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors"
        >
          Refresh Storage
        </button>
      </div>

      <!-- Test Results -->
      <div class="p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">Test Results</h2>
        <div class="space-y-2">
          <div v-for="result in testResults" :key="result.id" class="p-2 rounded" :class="result.success ? 'bg-green-900' : 'bg-red-900'">
            <div class="flex items-center justify-between">
              <span>{{ result.message }}</span>
              <span class="text-sm text-gray-400">{{ result.timestamp }}</span>
            </div>
          </div>
        </div>
        <button 
          @click="clearResults"
          class="mt-4 px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded transition-colors"
        >
          Clear Results
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Import stores
const uiStore = useUiStore()
const userStore = useUserStore()
const vpnStore = useVpnStore()

// Reactive state
const persistenceStatus = ref('Unknown')
const lastTestTime = ref('Never')
const storageContents = ref<Record<string, string>>({})
const testResults = ref<Array<{
  id: string
  message: string
  success: boolean
  timestamp: string
}>>([])

// Computed properties
const persistenceStatusClass = computed(() => ({
  'bg-green-800': persistenceStatus.value === 'Working',
  'bg-red-800': persistenceStatus.value === 'Failed',
  'bg-yellow-800': persistenceStatus.value === 'Unknown'
}))

const persistenceStatusDot = computed(() => ({
  'bg-green-400': persistenceStatus.value === 'Working',
  'bg-red-400': persistenceStatus.value === 'Failed',
  'bg-yellow-400': persistenceStatus.value === 'Unknown'
}))

// Test functions
const testPersistence = () => {
  try {
    // Test if localStorage is available
    if (typeof localStorage === 'undefined') {
      addTestResult('LocalStorage not available', false)
      persistenceStatus.value = 'Failed'
      return
    }

    // Test writing to localStorage
    const testKey = 'atlas-persistence-test'
    const testValue = JSON.stringify({ test: true, timestamp: Date.now() })
    localStorage.setItem(testKey, testValue)
    
    // Test reading from localStorage
    const retrieved = localStorage.getItem(testKey)
    if (retrieved === testValue) {
      addTestResult('LocalStorage read/write test passed', true)
    } else {
      addTestResult('LocalStorage read/write test failed', false)
      persistenceStatus.value = 'Failed'
      return
    }

    // Clean up test
    localStorage.removeItem(testKey)

    // Check if store persistence is working
    const uiStoreData = localStorage.getItem('atlas-ui-store')
    const userStoreData = localStorage.getItem('atlas-user-store')
    const vpnStoreData = localStorage.getItem('atlas-vpn-store')

    if (uiStoreData) {
      addTestResult('UI Store persistence detected', true)
    } else {
      addTestResult('UI Store persistence not found', false)
    }

    if (userStoreData) {
      addTestResult('User Store persistence detected', true)
    } else {
      addTestResult('User Store persistence not found', false)
    }

    if (vpnStoreData) {
      addTestResult('VPN Store persistence detected', true)
    } else {
      addTestResult('VPN Store persistence not found', false)
    }

    persistenceStatus.value = 'Working'
    lastTestTime.value = new Date().toLocaleTimeString()
    
  } catch (error) {
    addTestResult(`Persistence test error: ${error}`, false)
    persistenceStatus.value = 'Failed'
  }
}

const addTestResult = (message: string, success: boolean) => {
  testResults.value.unshift({
    id: Date.now().toString(),
    message,
    success,
    timestamp: new Date().toLocaleTimeString()
  })
  
  // Keep only last 10 results
  if (testResults.value.length > 10) {
    testResults.value = testResults.value.slice(0, 10)
  }
}

const clearAllStorage = () => {
  try {
    localStorage.clear()
    addTestResult('All localStorage cleared', true)
    refreshStorageContents()
  } catch (error) {
    addTestResult(`Failed to clear storage: ${error}`, false)
  }
}

const randomizeValues = () => {
  const themes = ['light', 'dark']
  const languages = ['en', 'es', 'fr', 'de']
  const currencies = ['USD', 'EUR', 'GBP', 'JPY']
  
  uiStore.currentTheme = themes[Math.floor(Math.random() * themes.length)] as 'light' | 'dark'
  uiStore.showWelcomeMessage = Math.random() > 0.5
  userStore.preferences.language = languages[Math.floor(Math.random() * languages.length)]
  userStore.preferences.currency = currencies[Math.floor(Math.random() * currencies.length)]
  vpnStore.selectedPackage = Math.floor(Math.random() * 100).toString()
  vpnStore.selectedPanel = Math.floor(Math.random() * 10).toString()
  
  addTestResult('Values randomized', true)
}

const reloadPage = () => {
  window.location.reload()
}

const clearResults = () => {
  testResults.value = []
}

const refreshStorageContents = () => {
  if (typeof localStorage === 'undefined') return
  
  const contents: Record<string, string> = {}
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i)
    if (key && key.startsWith('atlas-')) {
      const value = localStorage.getItem(key)
      contents[key] = value ? value.substring(0, 100) + (value.length > 100 ? '...' : '') : 'null'
    }
  }
  storageContents.value = contents
}

// Initialize on mount
onMounted(() => {
  testPersistence()
  refreshStorageContents()
})

// Page meta
definePageMeta({
  title: 'Persistence Test',
  description: 'Test page for Pinia persistence functionality'
})
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
