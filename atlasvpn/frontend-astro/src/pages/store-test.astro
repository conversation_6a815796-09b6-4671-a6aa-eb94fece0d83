---
// Test page for Zustand stores
title: 'Store Test'
---

<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="generator" content={Astro.generator} />
    <title>Store Test - AtlasVPN</title>
    <script src="https://cdn.tailwindcss.com"></script>
  </head>
  <body class="bg-gray-900 min-h-screen">
    <div class="container mx-auto px-4 py-8">
      <h1 class="text-3xl font-bold text-white text-center mb-8">Zustand Store Migration Test</h1>
      <div class="max-w-4xl mx-auto">
        <div id="store-test-container"></div>
      </div>
    </div>
    
    <script>
      import StoreTest from '../components/react/StoreTest.tsx';
      import React from 'react';
      import { createRoot } from 'react-dom/client';
      
      const container = document.getElementById('store-test-container');
      if (container) {
        const root = createRoot(container);
        root.render(React.createElement(StoreTest));
      }
    </script>
  </body>
</html>