from sqlalchemy import create_engine, inspect
from database import DATABASE_URL


def check_schema() -> None:
    engine = create_engine(DATABASE_URL)
    inspector = inspect(engine)

    output_lines = []

    tables = inspector.get_table_names()
    if not tables:
        output_lines.append('No tables found in the database.')
    else:
        for table in tables:
            output_lines.append(f"## Table: {table}\n")

            # Columns
            output_lines.append("**Columns:**")
            columns = inspector.get_columns(table)
            for column in columns:
                col_name = column.get('name')
                col_type = column.get('type')
                nullable = column.get('nullable')
                default = column.get('default')
                output_lines.append(f"- **{col_name}**: {col_type} | Nullable: {nullable} | Default: {default}")

            # Primary Key Constraint
            pk = inspector.get_pk_constraint(table)
            pk_columns = pk.get('constrained_columns') if pk else []
            output_lines.append(f"**Primary Key:** {pk_columns}\n")

            # Foreign Keys
            fks = inspector.get_foreign_keys(table)
            if fks:
                output_lines.append("**Foreign Keys:**")
                for fk in fks:
                    constrained_columns = fk.get('constrained_columns')
                    referred_table = fk.get('referred_table')
                    referred_columns = fk.get('referred_columns')
                    output_lines.append(f"- {constrained_columns} -> {referred_table}({referred_columns})")
            else:
                output_lines.append("**Foreign Keys:** None")

            # Indexes
            indexes = inspector.get_indexes(table)
            if indexes:
                output_lines.append("**Indexes:**")
                for index in indexes:
                    index_name = index.get('name')
                    column_names = index.get('column_names')
                    unique = index.get('unique')
                    output_lines.append(f"- {index_name}: Columns: {column_names} | Unique: {unique}")
            else:
                output_lines.append("**Indexes:** None")

            output_lines.append("\n---\n")

    # Join all output into a single string
    output_str = "\n".join(output_lines)

    # Print to stdout
    print(output_str)

    # Write output to a markdown file
    try:
        with open('schema.md', 'w') as f:
            f.write(output_str)
        print("\nSchema details written to schema.md")
    except Exception as e:
        print(f"Error writing to markdown file: {e}")


if __name__ == "__main__":
    check_schema() 