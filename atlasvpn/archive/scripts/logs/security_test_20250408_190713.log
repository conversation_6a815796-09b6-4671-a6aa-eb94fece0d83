
Starting Security Task Tests...
2025-04-08 19:07:22,541 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:07:22,544 - INFO - Retrieved CSRF token for admin login
2025-04-08 19:07:23,640 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:07:23,681 - INFO - Admin login successful
2025-04-08 19:07:23,746 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:07:23,748 - INFO - Created test task with ID: 401
2025-04-08 19:07:23,748 - INFO - Test environment setup complete
2025-04-08 19:07:23,848 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:23,921 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/401/verify "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:07:24,508 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:24,561 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/401/claim "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:07:24,632 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/claim "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:24,796 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:24,878 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:24,967 - INFO - All unauthorized access tests passed
2025-04-08 19:07:25,503 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,544 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,618 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,694 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,768 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,835 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,901 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:25,961 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:32,535 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:32,577 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:32,647 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:32,727 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/analytics "HTTP/1.1 401 Unauthorized"
2025-04-08 19:07:32,769 - INFO - All invalid token tests passed
2025-04-08 19:07:42,599 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/tasks/list "HTTP/1.1 200 OK"
2025-04-08 19:07:42,660 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/401/toggle "HTTP/1.1 200 OK"
2025-04-08 19:07:42,731 - INFO - HTTP Request: GET http://localhost:8000/api/tasks/admin/analytics "HTTP/1.1 404 Not Found"
2025-04-08 19:07:42,773 - ERROR - Admin access to /api/tasks/admin/analytics failed with status 404
2025-04-08 19:07:42,773 - WARNING - TEST_MODE is enabled, rate limiting may not be active
2025-04-08 19:07:42,773 - INFO - For accurate rate limiting tests, run with TEST_MODE=false
2025-04-08 19:07:42,865 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:43,611 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:43,828 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:44,870 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:45,680 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:45,863 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:47,647 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:47,824 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:48,575 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:48,735 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:48,909 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:49,603 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 400 Bad Request"
2025-04-08 19:07:49,783 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:49,957 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:50,592 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:50,729 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:50,891 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:52,622 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:52,777 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:53,455 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:53,625 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:53,794 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:53,950 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:54,549 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:54,724 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:54,861 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:55,541 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:55,716 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:55,837 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:57,681 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:57,817 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:57,938 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:58,568 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:58,720 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:58,870 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:59,545 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:59,674 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:07:59,837 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:00,472 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:00,688 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:00,832 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:01,483 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:01,625 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:01,835 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:02,574 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:02,769 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:02,911 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:04,473 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:04,611 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:04,713 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:04,765 - WARNING - Rate limiting not detected - this might be expected in test mode
2025-04-08 19:08:04,854 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:04,855 - ERROR - Input validation failed for {'verification_code': ''}. Got status 500
2025-04-08 19:08:04,935 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/claim "HTTP/1.1 400 Bad Request"
2025-04-08 19:08:05,611 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 500 Internal Server Error"
2025-04-08 19:08:05,653 - ERROR - Verify without starting didn't return error. Got 500
2025-04-08 19:08:05,711 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/start "HTTP/1.1 401 Unauthorized"
2025-04-08 19:08:05,815 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/verify "HTTP/1.1 401 Unauthorized"
2025-04-08 19:08:05,901 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/401/claim "HTTP/1.1 401 Unauthorized"
2025-04-08 19:08:05,941 - INFO - CSRF protection test passed
2025-04-08 19:08:05,944 - INFO - Header injection was prevented by the HTTP library: Illegal header value b'127.0.0.1\r\nX-Injected-Header: value'
2025-04-08 19:08:05,945 - INFO - This is good security practice and the test is considered passed
  Setup completed 
  Running tests...

Security Task Test Report
      Test Summary       
┏━━━━━━━━━━━━━━┳━━━━━━━━┓
┃ Metric       ┃ Value  ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━┩
│ Total Tests  │ 8      │
│ Passed       │ 5      │
│ Failed       │ 0      │
│ Success Rate │ 62.50% │
└──────────────┴────────┘

Report saved to /root/project/backend/scripts/logs/security_task_test_report_20250408_190812.json
