<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Test Report - 20250316_215357</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .summary {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .success {
            color: #28a745;
        }
        .failure {
            color: #dc3545;
        }
        .test-result {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .test-name {
            font-weight: bold;
            font-size: 1.2em;
        }
        .test-status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
        }
        .status-failure {
            background-color: #f8d7da;
            color: #721c24;
        }
        .test-details {
            background-color: #f8f9fa;
            border: 1px solid #eee;
            border-radius: 3px;
            padding: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .timestamp {
            color: #6c757d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>Security Test Report</h1>
    <div class="timestamp">Generated on: 2025-03-16 21:53:57</div>
    
    <div class="summary">
        <h2>Summary</h2>
        <p>Total Tests: 2</p>
        <p class="success">Passed: 2</p>
        <p class="failure">Failed: 0</p>
        <p>Success Rate: 100.00%</p>
    </div>
    
    <h2>Test Results</h2>

    <div class="test-result">
        <div class="test-header">
            <div class="test-name">test_security_headers.py</div>
            <div class="test-status status-success">PASSED</div>
        </div>
        <div>Duration: 0.99 seconds</div>
        <div>Started: 2025-03-16T21:53:44.455234</div>
        
        <h3>Output</h3>
        <div class="test-details">
================================================================================
                    SECURITY HEADERS AND BOT DETECTION TEST                     
================================================================================


Testing security headers on main page...
2025-03-16 21:53:45,275 - INFO - Testing security headers for: http://localhost:8000
2025-03-16 21:53:45,288 - WARNING - Header X-Frame-Options has incorrect value: DENY
⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN
✓ All required security headers are present

Testing Content-Security-Policy header...
✓ Content-Security-Policy is properly configured

Testing security headers on API endpoints...
2025-03-16 21:53:45,289 - INFO - Testing security headers for: http://localhost:8000/api/tasks/available
2025-03-16 21:53:45,318 - WARNING - Header X-Frame-Options has incorrect value: DENY
⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN
✓ Endpoint /api/tasks/available has all required security headers
2025-03-16 21:53:45,319 - INFO - Testing security headers for: http://localhost:8000/api/security/verify-captcha
2025-03-16 21:53:45,330 - WARNING - Header X-Frame-Options has incorrect value: DENY
⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN
✓ Endpoint /api/security/verify-captcha has all required security headers
2025-03-16 21:53:45,331 - INFO - Testing security headers for: http://localhost:8000/auth/token
2025-03-16 21:53:45,341 - WARNING - Header X-Frame-Options has incorrect value: DENY
⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN
✓ Endpoint /auth/token has all required security headers

Testing bot detection...
2025-03-16 21:53:45,353 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)': 404
2025-03-16 21:53:45,368 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)': 404
2025-03-16 21:53:45,380 - INFO - Bot test with User-Agent 'python-requests/2.25.1': 404
2025-03-16 21:53:45,391 - INFO - Bot test with User-Agent 'Wget/1.20.3 (linux-gnu)': 404
2025-03-16 21:53:45,400 - INFO - Bot test with User-Agent 'curl/7.68.0': 404
⚠ Legitimate search engine bots blocked: Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)
⚠ Automation tools allowed without CAPTCHA: python-requests/2.25.1, Wget/1.20.3 (linux-gnu), curl/7.68.0
✗ Bot detection has issues

Detailed Bot Detection Results:
  Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html): ✓ OK
  Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm): ✓ OK
  python-requests/2.25.1: ✓ OK
  Wget/1.20.3 (linux-gnu): ✓ OK
  curl/7.68.0: ✓ OK
</div>
        
        
    </div>

    <div class="test-result">
        <div class="test-header">
            <div class="test-name">test_rate_limit_captcha.py</div>
            <div class="test-status status-success">PASSED</div>
        </div>
        <div>Duration: 12.18 seconds</div>
        <div>Started: 2025-03-16T21:53:45.440665</div>
        
        <h3>Output</h3>
        <div class="test-details">
================================================================================
                         RATE LIMITING AND CAPTCHA TEST                         
================================================================================


Testing login rate limiting...
2025-03-16 21:53:46,257 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts
2025-03-16 21:53:46,272 - INFO - Attempt 1: Status 500
2025-03-16 21:53:46,481 - INFO - Attempt 2: Status 500
2025-03-16 21:53:46,701 - INFO - Attempt 3: Status 500
2025-03-16 21:53:47,171 - INFO - Attempt 4: Status 500
2025-03-16 21:53:47,389 - INFO - Attempt 5: Status 500
2025-03-16 21:53:47,601 - INFO - Attempt 6: Status 500
2025-03-16 21:53:47,812 - INFO - Attempt 7: Status 500
2025-03-16 21:53:48,026 - INFO - Attempt 8: Status 500
2025-03-16 21:53:48,241 - INFO - Attempt 9: Status 500
2025-03-16 21:53:48,454 - INFO - Attempt 10: Status 500
2025-03-16 21:53:48,666 - INFO - Attempt 11: Status 500
2025-03-16 21:53:48,877 - INFO - Attempt 12: Status 500
✗ Rate limiting was not triggered after 12 attempts
✗ Login rate limiting is not working properly

Rate limit headers for login attempts:
  Attempt 1: OK - Headers: {}
  Attempt 2: OK - Headers: {}
  Attempt 3: OK - Headers: {}
  Attempt 4: OK - Headers: {}
  Attempt 5: OK - Headers: {}
  Attempt 6: OK - Headers: {}
  Attempt 7: OK - Headers: {}
  Attempt 8: OK - Headers: {}
  Attempt 9: OK - Headers: {}
  Attempt 10: OK - Headers: {}
  Attempt 11: OK - Headers: {}
  Attempt 12: OK - Headers: {}

Testing rate limiting for API endpoints...

Testing rate limiting for /api/tasks/available...
2025-03-16 21:53:49,082 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts
2025-03-16 21:53:49,103 - INFO - Attempt 1: Status 401
2025-03-16 21:53:49,352 - INFO - Attempt 2: Status 401
2025-03-16 21:53:49,577 - INFO - Attempt 3: Status 401
2025-03-16 21:53:49,802 - INFO - Attempt 4: Status 401
2025-03-16 21:53:50,029 - INFO - Attempt 5: Status 401
2025-03-16 21:53:50,276 - INFO - Attempt 6: Status 401
2025-03-16 21:53:50,505 - INFO - Attempt 7: Status 401
2025-03-16 21:53:50,743 - INFO - Attempt 8: Status 401
2025-03-16 21:53:50,966 - INFO - Attempt 9: Status 401
2025-03-16 21:53:51,190 - INFO - Attempt 10: Status 401
✗ Rate limiting was not triggered after 10 attempts
✗ Rate limiting is not working properly for /api/tasks/available

Testing rate limiting for /api/tasks/analytics...
2025-03-16 21:53:51,392 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts
2025-03-16 21:53:51,417 - INFO - Attempt 1: Status 401
2025-03-16 21:53:51,641 - INFO - Attempt 2: Status 401
2025-03-16 21:53:51,864 - INFO - Attempt 3: Status 401
2025-03-16 21:53:52,090 - INFO - Attempt 4: Status 401
2025-03-16 21:53:52,313 - INFO - Attempt 5: Status 401
2025-03-16 21:53:52,543 - INFO - Attempt 6: Status 401
2025-03-16 21:53:52,770 - INFO - Attempt 7: Status 401
2025-03-16 21:53:52,995 - INFO - Attempt 8: Status 401
2025-03-16 21:53:53,223 - INFO - Attempt 9: Status 401
2025-03-16 21:53:53,445 - INFO - Attempt 10: Status 401
✗ Rate limiting was not triggered after 10 attempts
✗ Rate limiting is not working properly for /api/tasks/analytics

Testing rate limiting for /auth/register...
2025-03-16 21:53:53,648 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts
2025-03-16 21:53:53,662 - INFO - Attempt 1: Status 405
2025-03-16 21:53:53,875 - INFO - Attempt 2: Status 405
2025-03-16 21:53:54,162 - INFO - Attempt 3: Status 405
2025-03-16 21:53:54,375 - INFO - Attempt 4: Status 405
2025-03-16 21:53:54,584 - INFO - Attempt 5: Status 405
2025-03-16 21:53:55,161 - INFO - Attempt 6: Status 405
2025-03-16 21:53:55,375 - INFO - Attempt 7: Status 405
2025-03-16 21:53:55,587 - INFO - Attempt 8: Status 405
2025-03-16 21:53:55,799 - INFO - Attempt 9: Status 405
2025-03-16 21:53:56,163 - INFO - Attempt 10: Status 405
✗ Rate limiting was not triggered after 10 attempts
✗ Rate limiting is not working properly for /auth/register

Testing Cloudflare CAPTCHA integration...
2025-03-16 21:53:56,364 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token
2025-03-16 21:53:56,377 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior
2025-03-16 21:53:56,385 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register
2025-03-16 21:53:56,416 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior
2025-03-16 21:53:56,416 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login
2025-03-16 21:53:56,428 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior
⚠ No CAPTCHA detected for login endpoint
⚠ No CAPTCHA detected for register endpoint
⚠ No CAPTCHA detected for admin_login endpoint

Simulating distributed attack...
2025-03-16 21:53:56,429 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent
2025-03-16 21:53:57,568 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored
✗ Distributed attack was not effectively blocked (block rate: 0.00%)

Sample distributed attack results:
  Request from IP **************: ALLOWED
  Request from IP **************: ALLOWED
  Request from IP ***************: ALLOWED
  Request from IP **************: ALLOWED
  Request from IP ***************: ALLOWED
</div>
        
        
    </div>

</body>
</html>
