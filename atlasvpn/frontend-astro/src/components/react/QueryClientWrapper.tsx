/**
 * @file QueryClientWrapper.tsx
 * @description QueryClient wrapper for React islands in Astro
 * Provides TanStack Query context to React components without global provider
 */

import React from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Create a client instance for this wrapper
const createQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      // Stale time: 5 minutes
      staleTime: 5 * 60 * 1000,
      // Cache time: 10 minutes  
      gcTime: 10 * 60 * 1000,
      // Don't refetch on window focus in Telegram WebApp
      refetchOnWindowFocus: false,
      // Don't refetch on mount by default (use stale data)
      refetchOnMount: false,
      // Refetch on reconnect
      refetchOnReconnect: true,
      // Retry configuration
      retry: (failureCount: number, error: any) => {
        // Never retry on authentication errors (401)
        if (error?.status === 401) {
          return false;
        }
        // Don't retry on most 4xx errors except 408 (timeout), 429 (rate limit)
        if (error?.status >= 400 && error?.status < 500) {
          if (error?.status === 408 || error?.status === 429) {
            return failureCount < 2;
          }
          return false;
        }
        // Retry on network errors and 5xx errors
        return failureCount < 3;
      },
      // Retry delay with exponential backoff
      retryDelay: (attemptIndex: number) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      // Retry mutations once on failure
      retry: 1,
      // Retry delay for mutations
      retryDelay: 1000,
    },
  },
});

interface QueryClientWrapperProps {
  children: React.ReactNode;
}

export default function QueryClientWrapper({ children }: QueryClientWrapperProps) {
  // Create a stable queryClient instance
  const [queryClient] = React.useState(() => createQueryClient());

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {import.meta.env.DEV && (
        <ReactQueryDevtools 
          initialIsOpen={false}
        />
      )}
    </QueryClientProvider>
  );
}
