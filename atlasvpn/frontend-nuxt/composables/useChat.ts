import type { Chat<PERSON>onversation, ChatMessage, SendMessageData } from '~/types'

/**
 * Modern chat composable using Nuxt 4 data fetching patterns
 * Optimized for Telegram Mini App with real-time messaging support
 */
export const useChat = () => {
  // Default fetch options optimized for Telegram Mini App
  const defaultOptions = {
    server: false, // Client-side only for Telegram Mini App
    retry: 3,
    retryDelay: 1000,
    timeout: 15000,
    onRequestError({ error }: { error: any }) {
      console.error('[Chat] Request error:', error)
    },
    onResponseError({ response }: { response: any }) {
      console.error('[Chat] Response error:', response.status, response.statusText)
    }
  }

  // Local state for real-time features
  const isConnected = ref(false)
  const isTyping = ref(false)
  const typingUsers = ref<string[]>([])
  const sendingMessage = ref(false)

  // Chat conversations with modern useFetch
  const { 
    data: conversations, 
    pending: isLoadingConversations, 
    error: conversationsError, 
    refresh: refreshConversations 
  } = useFetch<ChatConversation[]>('/api/chat/conversations', {
    ...defaultOptions,
    key: 'chat-conversations',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // Public chat messages with modern useFetch
  const { 
    data: publicMessages, 
    pending: isLoadingPublic, 
    error: publicError, 
    refresh: refreshPublicMessages 
  } = useFetch<ChatMessage[]>('/api/chat/public/messages', {
    ...defaultOptions,
    key: 'public-chat-messages',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // Private chat messages (reactive based on selected conversation)
  const selectedConversationId = ref<number | null>(null)
  
  const { 
    data: privateMessages, 
    pending: isLoadingPrivate, 
    error: privateError, 
    refresh: refreshPrivateMessages 
  } = useLazyFetch<ChatMessage[]>(() => `/api/chat/private/${selectedConversationId.value}/messages`, {
    ...defaultOptions,
    key: computed(() => `private-chat-messages-${selectedConversationId.value}`),
    default: () => [],
    transform: (data: any) => data?.data || data || [],
    watch: [selectedConversationId]
  })

  // Send message to public chat
  const sendPublicMessage = async (content: string): Promise<ChatMessage> => {
    if (sendingMessage.value) throw new Error('Already sending a message')

    sendingMessage.value = true

    // Optimistic update
    const tempMessage: ChatMessage = {
      id: Date.now(), // Temporary ID
      content,
      sender_id: 0, // Will be set by server
      sender_username: 'You',
      created_at: new Date().toISOString(),
      is_system: false,
      conversation_id: null
    }

    if (publicMessages.value) {
      publicMessages.value.push(tempMessage)
    }

    try {
      const response = await $fetch<{ data: ChatMessage }>('/api/chat/public/send', {
        method: 'POST',
        body: { content }
      })
      
      // Replace temp message with real one
      if (publicMessages.value) {
        const tempIndex = publicMessages.value.findIndex(m => m.id === tempMessage.id)
        if (tempIndex !== -1) {
          publicMessages.value[tempIndex] = response.data
        }
      }
      
      return response.data
    } catch (error: any) {
      console.error('[Chat] Failed to send public message:', error)
      
      // Remove temp message on error
      if (publicMessages.value) {
        const tempIndex = publicMessages.value.findIndex(m => m.id === tempMessage.id)
        if (tempIndex !== -1) {
          publicMessages.value.splice(tempIndex, 1)
        }
      }
      
      throw error
    } finally {
      sendingMessage.value = false
    }
  }

  // Send message to private chat
  const sendPrivateMessage = async (conversationId: number, content: string): Promise<ChatMessage> => {
    if (sendingMessage.value) throw new Error('Already sending a message')

    sendingMessage.value = true

    // Optimistic update
    const tempMessage: ChatMessage = {
      id: Date.now(), // Temporary ID
      content,
      sender_id: 0, // Will be set by server
      sender_username: 'You',
      created_at: new Date().toISOString(),
      is_system: false,
      conversation_id: conversationId
    }

    if (privateMessages.value && selectedConversationId.value === conversationId) {
      privateMessages.value.push(tempMessage)
    }

    try {
      const response = await $fetch<{ data: ChatMessage }>(`/api/chat/private/${conversationId}/send`, {
        method: 'POST',
        body: { content }
      })
      
      // Replace temp message with real one
      if (privateMessages.value && selectedConversationId.value === conversationId) {
        const tempIndex = privateMessages.value.findIndex(m => m.id === tempMessage.id)
        if (tempIndex !== -1) {
          privateMessages.value[tempIndex] = response.data
        }
      }
      
      return response.data
    } catch (error: any) {
      console.error('[Chat] Failed to send private message:', error)
      
      // Remove temp message on error
      if (privateMessages.value && selectedConversationId.value === conversationId) {
        const tempIndex = privateMessages.value.findIndex(m => m.id === tempMessage.id)
        if (tempIndex !== -1) {
          privateMessages.value.splice(tempIndex, 1)
        }
      }
      
      throw error
    } finally {
      sendingMessage.value = false
    }
  }

  // Create new conversation
  const createConversation = async (recipientId: number): Promise<ChatConversation> => {
    try {
      const response = await $fetch<{ data: ChatConversation }>('/api/chat/conversations/create', {
        method: 'POST',
        body: { recipient_id: recipientId }
      })
      
      // Refresh conversations to get updated list
      await refreshConversations()
      
      return response.data
    } catch (error: any) {
      console.error('[Chat] Failed to create conversation:', error)
      throw error
    }
  }

  // Delete conversation
  const deleteConversation = async (conversationId: number): Promise<void> => {
    try {
      await $fetch(`/api/chat/conversations/${conversationId}`, {
        method: 'DELETE'
      })
      
      // Remove from local state
      if (conversations.value) {
        const index = conversations.value.findIndex(c => c.id === conversationId)
        if (index !== -1) {
          conversations.value.splice(index, 1)
        }
      }
      
      // Clear selected conversation if it was deleted
      if (selectedConversationId.value === conversationId) {
        selectedConversationId.value = null
      }
    } catch (error: any) {
      console.error('[Chat] Failed to delete conversation:', error)
      throw error
    }
  }

  // Select conversation for private messaging
  const selectConversation = (conversationId: number | null) => {
    selectedConversationId.value = conversationId
  }

  // Refresh all chat data
  const refreshAllChat = async () => {
    await Promise.all([
      refreshConversations(),
      refreshPublicMessages(),
      selectedConversationId.value ? refreshPrivateMessages() : Promise.resolve()
    ])
  }

  // Computed properties
  const totalUnreadMessages = computed(() => 
    conversations.value?.reduce((total, conv) => total + (conv.unread_count || 0), 0) || 0
  )

  const hasUnreadMessages = computed(() => totalUnreadMessages.value > 0)

  const selectedConversation = computed(() => 
    conversations.value?.find(c => c.id === selectedConversationId.value) || null
  )

  const recentMessages = computed(() => {
    const recent = [...(publicMessages.value || [])]
    if (privateMessages.value) {
      recent.push(...privateMessages.value)
    }
    return recent
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 50)
  })

  // Utility functions
  const formatMessageTime = (timestamp: string): string => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    if (diffHours < 24) return `${diffHours}h ago`
    if (diffDays < 7) return `${diffDays}d ago`
    
    return date.toLocaleDateString()
  }

  const isMessageFromCurrentUser = (message: ChatMessage, currentUserId?: number): boolean => {
    return message.sender_id === currentUserId
  }

  return {
    // State (reactive from useFetch)
    conversations: readonly(conversations),
    publicMessages: readonly(publicMessages),
    privateMessages: readonly(privateMessages),
    selectedConversationId: readonly(selectedConversationId),
    isLoadingConversations: readonly(isLoadingConversations),
    isLoadingPublic: readonly(isLoadingPublic),
    isLoadingPrivate: readonly(isLoadingPrivate),
    conversationsError: readonly(conversationsError),
    publicError: readonly(publicError),
    privateError: readonly(privateError),
    isConnected: readonly(isConnected),
    isTyping: readonly(isTyping),
    typingUsers: readonly(typingUsers),
    sendingMessage: readonly(sendingMessage),

    // Actions
    refreshConversations,
    refreshPublicMessages,
    refreshPrivateMessages,
    refreshAllChat,
    sendPublicMessage,
    sendPrivateMessage,
    createConversation,
    deleteConversation,
    selectConversation,

    // Computed
    totalUnreadMessages,
    hasUnreadMessages,
    selectedConversation,
    recentMessages,

    // Utilities
    formatMessageTime,
    isMessageFromCurrentUser
  }
}
