<template>
  <div class="wallet-tab">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-white mb-2">Wallet</h1>
      <p class="text-gray-400">Manage your balance and transactions</p>
    </div>

    <!-- Balance Card -->
    <div class="card mb-6 bg-gradient-to-r from-purple-500/10 to-blue-500/10 border-purple-500/20">
      <div class="text-center">
        <div class="text-4xl font-bold text-white mb-2">
          {{ formattedBalance }}
        </div>
        <div class="text-gray-400 mb-6">Available Balance</div>
        
        <div class="flex space-x-3 justify-center">
          <UButton
            @click="showTopUpModal = true"
            color="primary"
            size="lg"
          >
            <template #leading>
              <Icon name="i-heroicons-plus" class="w-5 h-5" />
            </template>
            Top Up
          </UButton>
          
          <UButton
            @click="showSendModal = true"
            variant="outline"
            size="lg"
            :disabled="walletBalance <= 0"
          >
            <template #leading>
              <Icon name="i-heroicons-paper-airplane" class="w-5 h-5" />
            </template>
            Send
          </UButton>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-2 gap-4 mb-6">
      <div class="stat-card">
        <div class="text-2xl font-bold text-green-400 mb-1">
          ${{ totalEarnings.toFixed(2) }}
        </div>
        <div class="text-xs text-gray-400">Total Earned</div>
      </div>
      
      <div class="stat-card">
        <div class="text-2xl font-bold text-red-400 mb-1">
          ${{ totalSpending.toFixed(2) }}
        </div>
        <div class="text-xs text-gray-400">Total Spent</div>
      </div>
    </div>

    <!-- Transaction History -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-white">Transaction History</h2>
        <UButton
          @click="refreshTransactions"
          :loading="isLoadingTransactions"
          variant="ghost"
          size="sm"
          icon="i-heroicons-arrow-path"
        />
      </div>

      <!-- Loading State -->
      <div v-if="isLoadingTransactions" class="space-y-4">
        <div v-for="i in 5" :key="i" class="flex items-center space-x-4 animate-pulse">
          <div class="w-10 h-10 bg-gray-700 rounded-full"></div>
          <div class="flex-1">
            <div class="h-4 bg-gray-700 rounded mb-2"></div>
            <div class="h-3 bg-gray-700 rounded w-2/3"></div>
          </div>
          <div class="w-16 h-4 bg-gray-700 rounded"></div>
        </div>
      </div>

      <!-- Transactions List -->
      <div v-else-if="transactions.length > 0" class="space-y-3">
        <TransactionCard
          v-for="transaction in transactions"
          :key="transaction.id"
          :transaction="transaction"
        />
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <Icon name="i-heroicons-document-text" class="w-16 h-16 mx-auto mb-4 text-gray-600" />
        <h3 class="text-lg font-medium text-white mb-2">No transactions yet</h3>
        <p class="text-gray-400">Your transaction history will appear here</p>
      </div>

      <!-- Error State -->
      <div v-if="transactionsError" class="border-red-500/20 bg-red-500/5 rounded-lg p-4 mt-4">
        <div class="flex items-center space-x-3">
          <Icon name="i-heroicons-exclamation-triangle" class="w-6 h-6 text-red-400" />
          <div>
            <h4 class="text-red-400 font-medium">Failed to load transactions</h4>
            <p class="text-red-300 text-sm">{{ transactionsError }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Top Up Modal -->
    <UModal v-model="showTopUpModal">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-white mb-4">Top Up Wallet</h3>
        <p class="text-gray-400 mb-6">Add funds to your wallet to purchase VPN packages and services.</p>
        
        <div class="grid grid-cols-2 gap-3 mb-6">
          <UButton
            v-for="amount in topUpAmounts"
            :key="amount"
            @click="selectedTopUpAmount = amount"
            :variant="selectedTopUpAmount === amount ? 'solid' : 'outline'"
            class="h-12"
          >
            ${{ amount }}
          </UButton>
        </div>
        
        <div class="flex space-x-3">
          <UButton
            @click="handleTopUp"
            :disabled="!selectedTopUpAmount"
            color="primary"
            class="flex-1"
          >
            Top Up ${{ selectedTopUpAmount }}
          </UButton>
          <UButton
            @click="showTopUpModal = false"
            variant="outline"
          >
            Cancel
          </UButton>
        </div>
      </div>
    </UModal>

    <!-- Send Modal -->
    <UModal v-model="showSendModal">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-white mb-4">Send Money</h3>
        <p class="text-gray-400 mb-6">Send money to another user.</p>
        
        <div class="space-y-4 mb-6">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              Recipient Username
            </label>
            <UInput
              v-model="sendForm.recipient"
              placeholder="Enter username"
              class="w-full"
            />
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">
              Amount
            </label>
            <UInput
              v-model="sendForm.amount"
              type="number"
              placeholder="0.00"
              min="0.01"
              :max="walletBalance"
              step="0.01"
              class="w-full"
            />
          </div>
        </div>
        
        <div class="flex space-x-3">
          <UButton
            @click="handleSend"
            :disabled="!canSend"
            color="primary"
            class="flex-1"
          >
            Send
          </UButton>
          <UButton
            @click="showSendModal = false"
            variant="outline"
          >
            Cancel
          </UButton>
        </div>
      </div>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { format } from 'date-fns'

definePageMeta({
  middleware: 'auth',
  layout: 'dashboard'
})

// SEO
useHead({
  title: 'Wallet - AtlasVPN'
})

const userStore = useUserStore()
const { fetchTransactions } = useDashboard()

// Computed properties from store
const formattedBalance = computed(() => userStore.formattedBalance)
const walletBalance = computed(() => userStore.walletBalance)
const transactions = computed(() => userStore.transactions)
const isLoadingTransactions = computed(() => userStore.isLoadingTransactions)
const transactionsError = computed(() => userStore.transactionsError)
const totalEarnings = computed(() => userStore.totalEarnings)
const totalSpending = computed(() => userStore.totalSpending)

// Local state
const showTopUpModal = ref(false)
const showSendModal = ref(false)
const selectedTopUpAmount = ref(0)
const topUpAmounts = [10, 20, 50, 100, 200, 500]

const sendForm = reactive({
  recipient: '',
  amount: 0
})

const canSend = computed(() => {
  return sendForm.recipient.trim() && 
         sendForm.amount > 0 && 
         sendForm.amount <= walletBalance.value
})

// Actions
const refreshTransactions = async () => {
  await fetchTransactions()
}

const handleTopUp = async () => {
  // In a real app, this would integrate with a payment processor
  console.log('Top up:', selectedTopUpAmount.value)
  showTopUpModal.value = false
  selectedTopUpAmount.value = 0
  
  // Show info message
  const uiStore = useUiStore()
  uiStore.showInfoToast('Top-up feature coming soon!')
}

const handleSend = async () => {
  // In a real app, this would send money to another user
  console.log('Send:', sendForm)
  showSendModal.value = false
  sendForm.recipient = ''
  sendForm.amount = 0
  
  // Show info message
  const uiStore = useUiStore()
  uiStore.showInfoToast('Send money feature coming soon!')
}

// Helper functions - moved to TransactionCard component

// Initialize
onMounted(async () => {
  await refreshTransactions()
})
</script>
