{"summary": {"total_tests": 41, "successful_tests": 0, "failed_tests": 41, "success_rate": 0.0, "duration_seconds": 48.074565, "timestamp": "2025-04-01T18:43:45.792623"}, "test_results": {"security_headers": {"summary": {"total_endpoints": 30, "valid_endpoints": 0, "failed_endpoints": 30, "success_rate": 0.0}, "failed_endpoints": [{"endpoint": "/api/token", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "authentication"}, {"endpoint": "/api/admin/login", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "authentication"}, {"endpoint": "/api/register", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "authentication"}, {"endpoint": "/api/payment/create", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "payment"}, {"endpoint": "/api/payment/webhook", "status_code": 403, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "26", "Content-Type": "application/json"}, "category": "payment"}, {"endpoint": "/api/user/profile/update", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "user_data"}, {"endpoint": "/api/user/transactions", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "user_data"}, {"endpoint": "/api/admin/*", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "admin"}, {"endpoint": "/api/token", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/tasks/available", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/tasks/analytics", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/payment/create", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/payment/webhook", "status_code": 403, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "26", "Content-Type": "application/json"}}, {"endpoint": "/api/payment/transactions", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/security/verify-captcha", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/security/stats", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/security/bot-patterns", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/security/dashboard", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/security/bot-detection/config", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/metrics", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/system", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/redis", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/application", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/security", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/health", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/historical", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/task-stats", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/user-stats", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/performance", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/cache", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}], "all_results": [{"endpoint": "/api/token", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "authentication"}, {"endpoint": "/api/admin/login", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "authentication"}, {"endpoint": "/api/register", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "authentication"}, {"endpoint": "/api/payment/create", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "payment"}, {"endpoint": "/api/payment/webhook", "status_code": 403, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "26", "Content-Type": "application/json"}, "category": "payment"}, {"endpoint": "/api/user/profile/update", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "user_data"}, {"endpoint": "/api/user/transactions", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "user_data"}, {"endpoint": "/api/admin/*", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}, "category": "admin"}, {"endpoint": "/api/token", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/tasks/available", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/tasks/analytics", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/payment/create", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/payment/webhook", "status_code": 403, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:44 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "26", "Content-Type": "application/json"}}, {"endpoint": "/api/payment/transactions", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/security/verify-captcha", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/security/stats", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/security/bot-patterns", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/security/dashboard", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/security/bot-detection/config", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/metrics", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/system", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/redis", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/application", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/security", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/health", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/historical", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/task-stats", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/user-stats", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/performance", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}, {"endpoint": "/api/monitoring/cache", "status_code": 500, "is_valid": false, "missing_headers": ["Content-Security-Policy", "X-XSS-Protection", "X-Content-Type-Options", "X-Frame-Options", "Referrer-Policy", "Permissions-Policy", "Cache-Control", "Strict-Transport-Security"], "invalid_headers": [], "headers": {"Date": "Tu<PERSON>, 01 Apr 2025 18:43:46 GMT", "Server": "u<PERSON><PERSON>", "Content-Length": "21", "Content-Type": "text/plain; charset=utf-8"}}]}, "rate_limit": {"summary": {"total_endpoints": 8, "rate_limited_endpoints": 0, "unprotected_endpoints": 8, "protection_rate": 0.0}, "unprotected_endpoints": [{"endpoint": "/api/token", "total_requests": 20, "duration": 4.877221, "requests_per_second": 4.10069586758525, "status_counts": {"500": 20}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "authentication"}, {"endpoint": "/api/admin/login", "total_requests": 20, "duration": 4.817054, "requests_per_second": 4.151915257748824, "status_counts": {"500": 20}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "authentication"}, {"endpoint": "/api/register", "total_requests": 20, "duration": 4.210786, "requests_per_second": 4.749707061816963, "status_counts": {"500": 20}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "authentication"}, {"endpoint": "/api/payment/create", "total_requests": 50, "duration": 6.168476, "requests_per_second": 8.10572984315737, "status_counts": {"500": 50}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "payment"}, {"endpoint": "/api/payment/webhook", "total_requests": 50, "duration": 5.96139, "requests_per_second": 8.387305645159937, "status_counts": {"403": 50}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "payment"}, {"endpoint": "/api/user/profile/update", "total_requests": 50, "duration": 7.639376, "requests_per_second": 6.5450371862832775, "status_counts": {"500": 50}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "user_data"}, {"endpoint": "/api/user/transactions", "total_requests": 50, "duration": 6.412363, "requests_per_second": 7.79743754369489, "status_counts": {"500": 50}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "user_data"}, {"endpoint": "/api/admin/*", "total_requests": 50, "duration": 6.668983, "requests_per_second": 7.497395030096793, "status_counts": {"500": 50}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "admin"}], "all_results": [{"endpoint": "/api/token", "total_requests": 20, "duration": 4.877221, "requests_per_second": 4.10069586758525, "status_counts": {"500": 20}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "authentication"}, {"endpoint": "/api/admin/login", "total_requests": 20, "duration": 4.817054, "requests_per_second": 4.151915257748824, "status_counts": {"500": 20}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "authentication"}, {"endpoint": "/api/register", "total_requests": 20, "duration": 4.210786, "requests_per_second": 4.749707061816963, "status_counts": {"500": 20}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "authentication"}, {"endpoint": "/api/payment/create", "total_requests": 50, "duration": 6.168476, "requests_per_second": 8.10572984315737, "status_counts": {"500": 50}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "payment"}, {"endpoint": "/api/payment/webhook", "total_requests": 50, "duration": 5.96139, "requests_per_second": 8.387305645159937, "status_counts": {"403": 50}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "payment"}, {"endpoint": "/api/user/profile/update", "total_requests": 50, "duration": 7.639376, "requests_per_second": 6.5450371862832775, "status_counts": {"500": 50}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "user_data"}, {"endpoint": "/api/user/transactions", "total_requests": 50, "duration": 6.412363, "requests_per_second": 7.79743754369489, "status_counts": {"500": 50}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "user_data"}, {"endpoint": "/api/admin/*", "total_requests": 50, "duration": 6.668983, "requests_per_second": 7.497395030096793, "status_counts": {"500": 50}, "rate_limit_headers": [], "is_rate_limited": false, "too_many_requests": 0, "success_responses": 0, "category": "admin"}], "config": {"default": {"rate": "100/minute", "burst": 20}, "auth": {"rate": "5/minute", "burst": 3}, "critical": {"rate": "30/minute", "burst": 5}}}, "captcha": {"summary": {"total_tests": 6, "successful_tests": 0, "failed_tests": 6, "success_rate": 0.0}, "verification_result": {"endpoint": "/api/security/verify-captcha", "error": "[Errno 104] Connection reset by peer", "valid_captcha": {"success": false}, "invalid_captcha": {"success": false}}, "login_result": {"endpoint": "/api/token", "error": "[Errno 104] Connection reset by peer", "with_captcha": {"success": false}, "without_captcha": {"success": false}}, "registration_result": {"endpoint": "/api/register", "error": "[Errno None] Can not write request body for http://localhost:8000/api/register", "with_captcha": {"success": false}, "without_captcha": {"success": false}}}, "bot_detection": {"summary": {"total_tests": 5, "successful_tests": 0, "failed_tests": 5, "success_rate": 0.0}, "pattern_results": [{"pattern_name": "<PERSON><PERSON>", "success": false, "status": 500, "response": null}, {"pattern_name": "Web Crawler", "success": false, "status": 500, "response": null}, {"pattern_name": "<PERSON>", "error": "[Errno 104] Connection reset by peer", "success": false}, {"pattern_name": "<PERSON><PERSON><PERSON>", "success": false, "status": 500, "response": null}, {"pattern_name": "legitimate_user", "error": "[Errno 104] Connection reset by peer", "success": false}], "config_endpoint_result": {"endpoint": "/api/security/bot-detection/config", "with_auth": {"success": false, "status": 500, "response": null}, "without_auth": {"success": false, "status": 500, "response": null}}}}}