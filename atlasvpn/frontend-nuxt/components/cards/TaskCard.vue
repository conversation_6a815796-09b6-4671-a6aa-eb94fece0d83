<template>
  <div 
    class="task-card group relative overflow-hidden rounded-xl border transition-all duration-300 hover:scale-[1.02] cursor-pointer"
    :class="[
      getCardClasses(),
      { 'opacity-60': task.is_claimed }
    ]"
    @click="handleTaskClick"
  >
    <!-- Background Glow -->
    <div 
      class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      :class="getGlowClass()"
    />

    <!-- Corner Accent -->
    <div class="absolute top-0 right-0 w-8 h-8 opacity-50">
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
        <path d="M 20 0 H 32 V 12" :stroke="getAccentColor()" stroke-width="1" />
        <path d="M 24 0 H 32 V 8" :stroke="getAccentColor()" stroke-width="2" />
      </svg>
    </div>

    <div class="relative p-4">
      <!-- Header -->
      <div class="flex items-start justify-between mb-3">
        <div class="flex items-center space-x-3 flex-1 min-w-0">
          <!-- Task Icon -->
          <div 
            class="w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0"
            :class="getIconBgClass()"
          >
            <Icon 
              :name="getTaskIcon()" 
              class="w-5 h-5"
              :class="getIconColorClass()"
            />
          </div>

          <!-- Task Info -->
          <div class="flex-1 min-w-0">
            <h4 class="text-white font-semibold text-sm mb-1 truncate">
              {{ task.title }}
            </h4>
            <p class="text-gray-400 text-xs line-clamp-2">
              {{ task.description }}
            </p>
          </div>
        </div>

        <!-- Status Badge -->
        <div class="flex-shrink-0 ml-2">
          <div 
            class="px-2 py-1 rounded-full text-xs font-medium"
            :class="getStatusBadgeClass()"
          >
            {{ getStatusText() }}
          </div>
        </div>
      </div>

      <!-- Task Details -->
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center space-x-4 text-xs">
          <!-- Reward -->
          <div class="flex items-center space-x-1">
            <Icon name="i-heroicons-currency-dollar" class="w-3 h-3 text-green-400" />
            <span class="text-green-400 font-medium">
              +{{ task.reward_amount.toFixed(2) }}
            </span>
          </div>

          <!-- Difficulty -->
          <div class="flex items-center space-x-1">
            <Icon name="i-heroicons-signal" class="w-3 h-3 text-gray-400" />
            <span class="text-gray-400 capitalize">
              {{ task.difficulty }}
            </span>
          </div>

          <!-- Completion Time -->
          <div v-if="task.avg_completion_time" class="flex items-center space-x-1">
            <Icon name="i-heroicons-clock" class="w-3 h-3 text-gray-400" />
            <span class="text-gray-400">
              {{ formatTime(task.avg_completion_time) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Progress Bar (for pending tasks) -->
      <div v-if="task.status === 'pending'" class="mb-3">
        <div class="w-full bg-gray-700 rounded-full h-1">
          <div 
            class="h-1 rounded-full transition-all duration-300"
            :class="getProgressBarClass()"
            :style="{ width: '60%' }"
          />
        </div>
        <div class="text-xs text-gray-400 mt-1">
          Verification in progress...
        </div>
      </div>

      <!-- Action Button -->
      <div class="flex items-center justify-between">
        <div class="text-xs text-gray-500">
          {{ getTaskTypeLabel() }}
        </div>
        
        <UButton
          v-if="task.status === 'available'"
          @click.stop="handleStart"
          :loading="isStarting"
          size="sm"
          color="primary"
          class="min-w-[80px]"
        >
          {{ getActionText() }}
        </UButton>
        
        <UButton
          v-else-if="task.status === 'completed' && !task.is_claimed"
          @click.stop="handleClaim"
          :loading="isClaiming"
          size="sm"
          color="green"
          class="min-w-[80px]"
        >
          Claim
        </UButton>
        
        <UBadge
          v-else-if="task.is_claimed"
          color="green"
          variant="soft"
        >
          <Icon name="i-heroicons-check" class="w-3 h-3 mr-1" />
          Claimed
        </UBadge>
        
        <UBadge
          v-else
          color="gray"
          variant="soft"
        >
          {{ task.status }}
        </UBadge>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { Task } from '~/types'

interface Props {
  task: Task
  isStarting?: boolean
  isClaiming?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isStarting: false,
  isClaiming: false
})

const emit = defineEmits<{
  start: [task: Task]
  claim: [task: Task]
  click: [task: Task]
}>()

const handleTaskClick = () => {
  emit('click', props.task)
}

const handleStart = () => {
  emit('start', props.task)
}

const handleClaim = () => {
  emit('claim', props.task)
}

// Helper functions
const getTaskIcon = () => {
  const icons: Record<string, string> = {
    telegram: 'i-simple-icons-telegram',
    social: 'i-heroicons-share',
    referral: 'i-heroicons-user-group',
    daily: 'i-heroicons-calendar-days',
    special: 'i-heroicons-star',
    youtube: 'i-simple-icons-youtube',
    instagram: 'i-simple-icons-instagram',
    twitter: 'i-simple-icons-twitter'
  }
  return icons[props.task.type] || 'i-heroicons-clipboard-document-list'
}

const getTaskTypeLabel = () => {
  const labels: Record<string, string> = {
    telegram: 'Telegram',
    social: 'Social Media',
    referral: 'Referral',
    daily: 'Daily Task',
    special: 'Special',
    youtube: 'YouTube',
    instagram: 'Instagram',
    twitter: 'Twitter'
  }
  return labels[props.task.type] || 'Task'
}

const getCardClasses = () => {
  if (props.task.is_claimed) {
    return 'border-green-500/20 bg-green-500/5'
  }
  
  if (props.task.status === 'completed') {
    return 'border-green-500/30 bg-green-500/10'
  }
  
  if (props.task.status === 'pending') {
    return 'border-yellow-500/30 bg-yellow-500/10'
  }
  
  return 'border-gray-700 bg-gray-900/50 hover:border-purple-500/50'
}

const getIconBgClass = () => {
  const classes: Record<string, string> = {
    telegram: 'bg-blue-500/20',
    social: 'bg-purple-500/20',
    referral: 'bg-green-500/20',
    daily: 'bg-yellow-500/20',
    special: 'bg-pink-500/20'
  }
  return classes[props.task.type] || 'bg-gray-500/20'
}

const getIconColorClass = () => {
  const classes: Record<string, string> = {
    telegram: 'text-blue-400',
    social: 'text-purple-400',
    referral: 'text-green-400',
    daily: 'text-yellow-400',
    special: 'text-pink-400'
  }
  return classes[props.task.type] || 'text-gray-400'
}

const getStatusBadgeClass = () => {
  if (props.task.is_claimed) {
    return 'bg-green-500/20 text-green-400'
  }
  
  const classes: Record<string, string> = {
    available: 'bg-blue-500/20 text-blue-400',
    pending: 'bg-yellow-500/20 text-yellow-400',
    completed: 'bg-green-500/20 text-green-400',
    failed: 'bg-red-500/20 text-red-400'
  }
  return classes[props.task.status] || 'bg-gray-500/20 text-gray-400'
}

const getStatusText = () => {
  if (props.task.is_claimed) return 'Claimed'
  return props.task.status.charAt(0).toUpperCase() + props.task.status.slice(1)
}

const getActionText = () => {
  const actions: Record<string, string> = {
    telegram: 'Join',
    social: 'Follow',
    referral: 'Invite',
    daily: 'Check In',
    special: 'Start'
  }
  return actions[props.task.type] || 'Start'
}

const getAccentColor = () => {
  const colors: Record<string, string> = {
    telegram: '#3b82f6',
    social: '#8b5cf6',
    referral: '#10b981',
    daily: '#f59e0b',
    special: '#ec4899'
  }
  return colors[props.task.type] || '#6b7280'
}

const getGlowClass = () => {
  const classes: Record<string, string> = {
    telegram: 'bg-blue-500/5',
    social: 'bg-purple-500/5',
    referral: 'bg-green-500/5',
    daily: 'bg-yellow-500/5',
    special: 'bg-pink-500/5'
  }
  return classes[props.task.type] || 'bg-gray-500/5'
}

const getProgressBarClass = () => {
  const classes: Record<string, string> = {
    telegram: 'bg-blue-500',
    social: 'bg-purple-500',
    referral: 'bg-green-500',
    daily: 'bg-yellow-500',
    special: 'bg-pink-500'
  }
  return classes[props.task.type] || 'bg-gray-500'
}

const formatTime = (seconds: number) => {
  if (seconds < 60) return `${seconds}s`
  const minutes = Math.floor(seconds / 60)
  return `${minutes}m`
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
