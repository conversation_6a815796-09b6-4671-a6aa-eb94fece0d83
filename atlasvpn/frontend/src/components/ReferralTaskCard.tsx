import React, { useState, useId, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Task, TaskStatus } from '../types/task';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaUser<PERSON>riends, FaShare } from 'react-icons/fa';
import { formatCurrency } from '../utils/format';
import { useTaskStore } from '../stores/taskStore';
import { ReferralTaskStatus } from '../types/referral';

interface ReferralTaskCardProps {
  task: Task;
  onClick: () => void;
  onClaim?: (e: React.MouseEvent) => void;
  isClaimingTask?: boolean;
}

export const ReferralTaskCard: React.FC<ReferralTaskCardProps> = ({
  task,
  onClick,
  onClaim,
  isClaimingTask = false,
}) => {
  const cardId = useId();
  const [loading, setLoading] = useState(false);
  const [referralStats, setReferralStats] = useState<ReferralTaskStatus | null>(null);

  const glowColor = '#9333ea'; // purple-600
  const borderClass = 'border-purple-500/30';
  const shadowClass = 'shadow-purple-500/10';

  const progress = task.current_progress || 0;
  const target = task.target_value || 1;
  const progressPercentage = Math.min(100, (progress / target) * 100);
  const isClaimable = task.status === TaskStatus.COMPLETED && !task.is_claimed;

  // TODO: Implement referral status checking when backend support is added
  // useEffect(() => {
  //   const loadReferralStats = async () => {
  //     if (!task.id) return;
  //     try {
  //       setLoading(true);
  //       // const result = await checkReferralStatus(task.id);
  //       // setReferralStats(result);
  //     } catch (error) {
  //       console.error('Error fetching referral stats:', error);
  //     } finally {
  //       setLoading(false);
  //     }
  //   };
  //
  //   loadReferralStats();
  // }, [task.id]);

  const handleShare = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card onClick
    // Implement sharing functionality
    // This would connect to your sharing mechanism
  };

  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
      onClick={onClick}
      className={`relative bg-gradient-to-br from-black/70 via-base-900/60 to-base-900/80 rounded-xl overflow-hidden backdrop-blur-sm shadow-lg ${shadowClass} ${borderClass} border cursor-pointer h-full flex flex-col min-h-[130px]`}
    >
      {/* Centered Glow */}
      <div className="absolute inset-0 flex items-center justify-center overflow-hidden pointer-events-none -z-10">
        <svg
          className="absolute h-48 w-48 opacity-15"
          viewBox="0 0 100 100"
          preserveAspectRatio="xMidYMid slice"
        >
          <defs>
            <radialGradient id={cardId} cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor={glowColor} stopOpacity="0.6" />
              <stop offset="100%" stopColor={glowColor} stopOpacity="0" />
            </radialGradient>
          </defs>
          <circle cx="50" cy="50" r="50" fill={`url(#${cardId})`} />
        </svg>
      </div>

      {/* Content Area */}
      <div className="px-4 py-3 flex-1">
        <div className="flex justify-between items-start">
          <div className="flex-1">
            <h3 className="text-white font-semibold text-sm">{task.name}</h3>
            <p className="text-gray-300 text-xs mt-1 line-clamp-2">{task.description}</p>
          </div>
          <div className="bg-purple-600/40 p-2 rounded-full text-white flex-shrink-0">
            <FaUserFriends size={16} />
          </div>
        </div>

        {/* Referral Stats */}
        {referralStats ? (
          <div className="mt-3 bg-slate-900/50 rounded-lg p-2 text-xs text-slate-300 space-y-1">
            <div className="flex justify-between">
              <span>Current Referrals:</span>
              <span className="font-semibold text-purple-300">{referralStats.currentProgress}</span>
            </div>
            <div className="flex justify-between">
              <span>Target:</span>
              <span className="font-semibold text-purple-300">{referralStats.target}</span>
            </div>
            <div className="flex justify-between">
              <span>Earnings:</span>
              <span className="font-semibold text-emerald-300">
                {formatCurrency(referralStats.totalEarnings)}
              </span>
            </div>
          </div>
        ) : loading ? (
          <div className="mt-3 flex justify-center">
            <FaSpinner className="animate-spin text-purple-400" size={16} />
          </div>
        ) : null}

        {/* Progress bar */}
        <div className="mt-3">
          <div className="flex justify-between items-center text-xs text-gray-400 mb-1">
            <span>
              {progress} of {target} referrals
            </span>
            <span>{progressPercentage.toFixed(0)}%</span>
          </div>
          <div className="h-2 bg-gray-800 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-purple-600 to-purple-400 rounded-full"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>
      </div>

      {/* Action buttons */}
      <div className="px-4 pb-3 flex gap-2 mt-1">
        <button
          onClick={handleShare}
          className="flex-1 px-3 py-1.5 rounded-md text-sm font-medium bg-slate-700/40 text-slate-300 hover:bg-slate-700/60 flex items-center justify-center gap-1"
        >
          <FaShare size={12} />
          <span>Share</span>
        </button>

        {isClaimable && onClaim && (
          <button
            onClick={onClaim}
            disabled={isClaimingTask}
            className={`flex-1 px-3 py-1.5 rounded-md text-sm font-medium flex items-center justify-center gap-1
              ${
                isClaimingTask
                  ? 'bg-green-500/50 text-white/70 cursor-wait'
                  : 'bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700'
              }`}
          >
            {isClaimingTask ? (
              <>
                <FaSpinner className="animate-spin" size={12} />
                <span>Claiming...</span>
              </>
            ) : (
              <>
                <FaCoins size={12} />
                <span>Claim Reward</span>
              </>
            )}
          </button>
        )}
      </div>
    </motion.div>
  );
};

export default ReferralTaskCard;
