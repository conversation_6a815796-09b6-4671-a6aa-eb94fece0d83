/**
 * @file telegramWebApp.ts
 * @description Telegram Mini App utilities and optimizations
 * Based on Telegram Bot API 8.0+ specifications
 */

import type { TelegramWebApp } from '../types/telegram';

// Window interface is already declared in types/telegram.ts

// Telegram WebApp utilities
export class TelegramWebAppUtils {
  private static instance: TelegramWebAppUtils;
  private webApp: TelegramWebApp | null = null;
  private isInitialized = false;

  private constructor() {
    this.init();
  }

  public static getInstance(): TelegramWebAppUtils {
    if (!TelegramWebAppUtils.instance) {
      TelegramWebAppUtils.instance = new TelegramWebAppUtils();
    }
    return TelegramWebAppUtils.instance;
  }

  private init(): void {
    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      this.webApp = window.Telegram.WebApp;
      this.setupWebApp();
      this.isInitialized = true;
    }
  }

  private setupWebApp(): void {
    if (!this.webApp) return;

    // Initialize the WebApp
    this.webApp.ready();

    // Expand to full height by default
    this.webApp.expand();

    // Enable vertical swipes for better UX
    this.webApp.enableVerticalSwipes();

    // Set optimal colors for cyberpunk theme
    this.webApp.setHeaderColor('#000000');
    this.webApp.setBackgroundColor('#000000');
    this.webApp.setBottomBarColor?.('#000000');

    // Setup event listeners
    this.setupEventListeners();

    // Apply safe area CSS variables
    this.applySafeAreaStyles();
  }

  private setupEventListeners(): void {
    if (!this.webApp) return;

    // Theme change handler
    this.webApp.onEvent('themeChanged', () => {
      this.applySafeAreaStyles();
      this.updateThemeColors();
    });

    // Viewport change handler
    this.webApp.onEvent('viewportChanged', (data: any) => {
      this.updateViewportStyles();
      if (data.isStateStable) {
        this.handleStableViewport();
      }
    });

    // Safe area change handlers
    this.webApp.onEvent('safeAreaChanged', () => {
      this.applySafeAreaStyles();
    });

    this.webApp.onEvent('contentSafeAreaChanged', () => {
      this.applySafeAreaStyles();
    });

    // Fullscreen handlers
    this.webApp.onEvent('fullscreenChanged', () => {
      this.handleFullscreenChange();
    });

    // Back button handler
    this.webApp.onEvent('backButtonClicked', () => {
      this.handleBackButton();
    });
  }

  private applySafeAreaStyles(): void {
    if (!this.webApp) return;

    const root = document.documentElement;
    
    // Apply safe area insets as CSS variables
    if (this.webApp.safeAreaInset) {
      root.style.setProperty('--tg-safe-area-inset-top', `${this.webApp.safeAreaInset.top}px`);
      root.style.setProperty('--tg-safe-area-inset-bottom', `${this.webApp.safeAreaInset.bottom}px`);
      root.style.setProperty('--tg-safe-area-inset-left', `${this.webApp.safeAreaInset.left}px`);
      root.style.setProperty('--tg-safe-area-inset-right', `${this.webApp.safeAreaInset.right}px`);
    }

    // Apply content safe area insets
    if (this.webApp.contentSafeAreaInset) {
      root.style.setProperty('--tg-content-safe-area-inset-top', `${this.webApp.contentSafeAreaInset.top}px`);
      root.style.setProperty('--tg-content-safe-area-inset-bottom', `${this.webApp.contentSafeAreaInset.bottom}px`);
      root.style.setProperty('--tg-content-safe-area-inset-left', `${this.webApp.contentSafeAreaInset.left}px`);
      root.style.setProperty('--tg-content-safe-area-inset-right', `${this.webApp.contentSafeAreaInset.right}px`);
    }

    // Apply viewport height
    root.style.setProperty('--tg-viewport-height', `${this.webApp.viewportHeight}px`);
    root.style.setProperty('--tg-viewport-stable-height', `${this.webApp.viewportStableHeight}px`);
  }

  private updateViewportStyles(): void {
    if (!this.webApp) return;
    
    const root = document.documentElement;
    root.style.setProperty('--tg-viewport-height', `${this.webApp.viewportHeight}px`);
    root.style.setProperty('--tg-viewport-stable-height', `${this.webApp.viewportStableHeight}px`);
  }

  private updateThemeColors(): void {
    if (!this.webApp) return;

    // Update colors based on theme
    const isDark = this.webApp.colorScheme === 'dark';
    this.webApp.setHeaderColor(isDark ? '#000000' : '#000000');
    this.webApp.setBackgroundColor('#000000');
    this.webApp.setBottomBarColor?.('#000000');
  }

  private handleStableViewport(): void {
    // Handle stable viewport state
    console.log('Viewport is stable');
  }

  private handleFullscreenChange(): void {
    if (!this.webApp) return;
    
    const root = document.documentElement;
    root.classList.toggle('tg-fullscreen', this.webApp.isFullscreen);
  }

  private handleBackButton(): void {
    // Handle back button press
    if (window.history.length > 1) {
      window.history.back();
    } else {
      this.webApp?.close();
    }
  }

  // Public methods
  public isAvailable(): boolean {
    return this.isInitialized && this.webApp !== null;
  }

  public getWebApp(): TelegramWebApp | null {
    return this.webApp;
  }

  public getUserData(): any {
    return this.webApp?.initDataUnsafe?.user || null;
  }

  public getChatData(): any {
    return this.webApp?.initDataUnsafe?.chat || null;
  }

  public getStartParam(): string | null {
    return this.webApp?.initDataUnsafe?.start_param || null;
  }

  public showMainButton(text: string, onClick: () => void): void {
    if (!this.webApp?.MainButton) return;

    this.webApp.MainButton.text = text;
    this.webApp.MainButton.show();
    this.webApp.MainButton.onClick(onClick);
  }

  public hideMainButton(): void {
    if (!this.webApp?.MainButton) return;
    this.webApp.MainButton.hide();
  }

  public showBackButton(): void {
    if (!this.webApp?.BackButton) return;
    this.webApp.BackButton.show();
  }

  public hideBackButton(): void {
    if (!this.webApp?.BackButton) return;
    this.webApp.BackButton.hide();
  }

  public hapticFeedback(type: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft' = 'light'): void {
    if (!this.webApp?.HapticFeedback) return;
    
    switch (type) {
      case 'light':
        this.webApp.HapticFeedback.impactOccurred('light');
        break;
      case 'medium':
        this.webApp.HapticFeedback.impactOccurred('medium');
        break;
      case 'heavy':
        this.webApp.HapticFeedback.impactOccurred('heavy');
        break;
      case 'rigid':
        this.webApp.HapticFeedback.impactOccurred('rigid');
        break;
      case 'soft':
        this.webApp.HapticFeedback.impactOccurred('soft');
        break;
    }
  }

  public requestFullscreen(): void {
    if (!this.webApp) return;
    this.webApp.requestFullscreen?.();
  }

  public exitFullscreen(): void {
    if (!this.webApp) return;
    this.webApp.exitFullscreen?.();
  }

  public openTelegramLink(url: string): void {
    if (!this.webApp) return;
    this.webApp.openTelegramLink?.(url);
  }

  public openLink(url: string): void {
    if (!this.webApp) return;
    this.webApp.openLink?.(url);
  }

  public sendData(data: any): void {
    if (!this.webApp) return;
    this.webApp.sendData(JSON.stringify(data));
  }

  public close(): void {
    if (!this.webApp) return;
    this.webApp.close();
  }

  // Device performance detection
  public getDevicePerformance(): 'low' | 'medium' | 'high' {
    const userAgent = navigator.userAgent;
    
    // Check for Telegram Android performance class
    const performanceMatch = userAgent.match(/Telegram-Android\/[\d.]+.*?;\s*(LOW|AVERAGE|HIGH)\)/);
    if (performanceMatch) {
      const performance = performanceMatch[1];
      switch (performance) {
        case 'LOW': return 'low';
        case 'AVERAGE': return 'medium';
        case 'HIGH': return 'high';
        default: return 'medium';
      }
    }

    // Fallback performance detection
    const memory = (navigator as any).deviceMemory;
    const cores = navigator.hardwareConcurrency;
    
    if (memory && memory < 4) return 'low';
    if (cores && cores < 4) return 'low';
    if (memory && memory >= 8 && cores && cores >= 8) return 'high';
    
    return 'medium';
  }

  // Theme detection
  public isDarkTheme(): boolean {
    return this.webApp?.colorScheme === 'dark';
  }

  public getThemeParams(): any {
    return this.webApp?.themeParams || {};
  }
}

// Export singleton instance
export const telegramWebApp = TelegramWebAppUtils.getInstance();
