#!/usr/bin/env python3
"""
Resource Monitor for AtlasVPN Backend
Monitors CPU, memory, and other system resources to prevent overwhelming the host system
"""

import asyncio
import logging
import os
import psutil
import time
from datetime import datetime
from typing import Dict, List, Optional
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/app/logs/resource_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ResourceMonitor:
    def __init__(self):
        self.pid = os.getpid()
        self.process = psutil.Process(self.pid)
        self.monitoring = True
        self.metrics_history: List[Dict] = []
        self.max_history = 1000  # Keep last 1000 measurements
        
        # Resource thresholds
        self.cpu_threshold = 80.0  # CPU usage percentage
        self.memory_threshold = 400 * 1024 * 1024  # 400MB in bytes
        self.memory_critical = 450 * 1024 * 1024  # 450MB critical threshold
        
        # Alert cooldowns (seconds)
        self.last_cpu_alert = 0
        self.last_memory_alert = 0
        self.alert_cooldown = 300  # 5 minutes
        
        # Performance optimization flags
        self.gc_enabled = True
        self.optimization_level = 1

    async def get_system_metrics(self) -> Dict:
        """Get current system metrics"""
        try:
            # Process-specific metrics
            cpu_percent = self.process.cpu_percent()
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()
            
            # System-wide metrics
            system_cpu = psutil.cpu_percent(interval=1)
            system_memory = psutil.virtual_memory()
            
            # Network and disk I/O
            net_io = psutil.net_io_counters()
            disk_io = psutil.disk_io_counters()
            
            # Process details
            num_threads = self.process.num_threads()
            num_fds = self.process.num_fds() if hasattr(self.process, 'num_fds') else 0
            
            metrics = {
                'timestamp': datetime.now().isoformat(),
                'process': {
                    'pid': self.pid,
                    'cpu_percent': cpu_percent,
                    'memory_rss': memory_info.rss,
                    'memory_vms': memory_info.vms,
                    'memory_percent': memory_percent,
                    'num_threads': num_threads,
                    'num_fds': num_fds,
                    'status': self.process.status()
                },
                'system': {
                    'cpu_percent': system_cpu,
                    'memory_total': system_memory.total,
                    'memory_available': system_memory.available,
                    'memory_percent': system_memory.percent,
                    'load_avg': os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
                },
                'io': {
                    'net_bytes_sent': net_io.bytes_sent if net_io else 0,
                    'net_bytes_recv': net_io.bytes_recv if net_io else 0,
                    'disk_read_bytes': disk_io.read_bytes if disk_io else 0,
                    'disk_write_bytes': disk_io.write_bytes if disk_io else 0
                }
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            return {}

    async def check_resource_limits(self, metrics: Dict) -> None:
        """Check if resource usage exceeds thresholds and take action"""
        if not metrics:
            return
            
        process_metrics = metrics.get('process', {})
        cpu_percent = process_metrics.get('cpu_percent', 0)
        memory_rss = process_metrics.get('memory_rss', 0)
        
        current_time = time.time()
        
        # Check CPU usage
        if cpu_percent > self.cpu_threshold:
            if current_time - self.last_cpu_alert > self.alert_cooldown:
                logger.warning(f"High CPU usage detected: {cpu_percent:.2f}%")
                await self.optimize_cpu_usage()
                self.last_cpu_alert = current_time
        
        # Check memory usage
        if memory_rss > self.memory_threshold:
            if current_time - self.last_memory_alert > self.alert_cooldown:
                logger.warning(f"High memory usage detected: {memory_rss / 1024 / 1024:.2f}MB")
                await self.optimize_memory_usage()
                self.last_memory_alert = current_time
        
        # Critical memory check
        if memory_rss > self.memory_critical:
            logger.critical(f"Critical memory usage: {memory_rss / 1024 / 1024:.2f}MB")
            await self.emergency_memory_cleanup()

    async def optimize_cpu_usage(self) -> None:
        """Optimize CPU usage when threshold is exceeded"""
        logger.info("Applying CPU optimization measures")
        
        # Reduce optimization level temporarily
        if self.optimization_level > 0:
            self.optimization_level -= 1
            logger.info(f"Reduced optimization level to {self.optimization_level}")
        
        # Add small delay to reduce CPU pressure
        await asyncio.sleep(0.1)

    async def optimize_memory_usage(self) -> None:
        """Optimize memory usage when threshold is exceeded"""
        logger.info("Applying memory optimization measures")
        
        # Force garbage collection
        if self.gc_enabled:
            import gc
            collected = gc.collect()
            logger.info(f"Garbage collection freed {collected} objects")
        
        # Clear metrics history if it's too large
        if len(self.metrics_history) > 500:
            self.metrics_history = self.metrics_history[-250:]
            logger.info("Cleared old metrics history")

    async def emergency_memory_cleanup(self) -> None:
        """Emergency memory cleanup for critical situations"""
        logger.critical("Performing emergency memory cleanup")
        
        # Force aggressive garbage collection
        import gc
        gc.collect()
        gc.collect()  # Run twice for better cleanup
        
        # Clear all metrics history
        self.metrics_history.clear()
        
        # Disable non-essential features temporarily
        self.gc_enabled = False
        self.optimization_level = 0
        
        logger.critical("Emergency cleanup completed")

    async def save_metrics(self, metrics: Dict) -> None:
        """Save metrics to history and optionally to file"""
        self.metrics_history.append(metrics)
        
        # Keep history size manageable
        if len(self.metrics_history) > self.max_history:
            self.metrics_history = self.metrics_history[-self.max_history//2:]
        
        # Save to file every 100 measurements
        if len(self.metrics_history) % 100 == 0:
            await self.save_metrics_to_file()

    async def save_metrics_to_file(self) -> None:
        """Save metrics history to file"""
        try:
            metrics_file = '/app/logs/resource_metrics.json'
            with open(metrics_file, 'w') as f:
                json.dump(self.metrics_history[-100:], f, indent=2)  # Save last 100 only
        except Exception as e:
            logger.error(f"Error saving metrics to file: {e}")

    async def get_performance_summary(self) -> Dict:
        """Get performance summary from recent metrics"""
        if not self.metrics_history:
            return {}
        
        recent_metrics = self.metrics_history[-60:]  # Last 60 measurements (5 minutes)
        
        cpu_values = [m.get('process', {}).get('cpu_percent', 0) for m in recent_metrics]
        memory_values = [m.get('process', {}).get('memory_rss', 0) for m in recent_metrics]
        
        return {
            'avg_cpu_percent': sum(cpu_values) / len(cpu_values) if cpu_values else 0,
            'max_cpu_percent': max(cpu_values) if cpu_values else 0,
            'avg_memory_mb': sum(memory_values) / len(memory_values) / 1024 / 1024 if memory_values else 0,
            'max_memory_mb': max(memory_values) / 1024 / 1024 if memory_values else 0,
            'measurements_count': len(recent_metrics),
            'optimization_level': self.optimization_level,
            'gc_enabled': self.gc_enabled
        }

    async def monitor_loop(self) -> None:
        """Main monitoring loop"""
        logger.info("Starting resource monitoring")
        
        while self.monitoring:
            try:
                # Get current metrics
                metrics = await self.get_system_metrics()
                
                if metrics:
                    # Check resource limits
                    await self.check_resource_limits(metrics)
                    
                    # Save metrics
                    await self.save_metrics(metrics)
                    
                    # Log summary every 5 minutes
                    if len(self.metrics_history) % 60 == 0:
                        summary = await self.get_performance_summary()
                        logger.info(f"Performance summary: {summary}")
                
                # Wait before next measurement
                await asyncio.sleep(5)  # Monitor every 5 seconds
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(10)  # Wait longer on error

    def start_monitoring(self) -> None:
        """Start the monitoring process"""
        asyncio.create_task(self.monitor_loop())

    def stop_monitoring(self) -> None:
        """Stop the monitoring process"""
        self.monitoring = False
        logger.info("Resource monitoring stopped")

# Global monitor instance
monitor = ResourceMonitor()

def get_monitor() -> ResourceMonitor:
    """Get the global monitor instance"""
    return monitor

async def start_resource_monitoring():
    """Start resource monitoring (called from main app)"""
    monitor.start_monitoring()
    logger.info("Resource monitoring started")

async def stop_resource_monitoring():
    """Stop resource monitoring (called on app shutdown)"""
    monitor.stop_monitoring()
    await monitor.save_metrics_to_file()
    logger.info("Resource monitoring stopped and metrics saved")

if __name__ == "__main__":
    # Run standalone monitoring
    async def main():
        await start_resource_monitoring()
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            await stop_resource_monitoring()
    
    asyncio.run(main())
