/**
 * @file integration.test.ts
 * @description Integration tests for complete authentication flow
 * Tests end-to-end scenarios including Telegram login, React Query state management, and error handling
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, waitFor, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useCurrentUserQuery, useTelegramLoginMutation, useLogoutMutation } from '../api/hooks';
import { api } from '../api';
import { telegramWebApp } from '../utils/telegramWebApp';
import type { TelegramWebApp } from '../types/telegram';

// Mock dependencies
vi.mock('../api');
vi.mock('../utils/telegramWebApp');

const mockApi = vi.mocked(api);
const mockTelegramWebApp = vi.mocked(telegramWebApp);

// Test wrapper with QueryClient
const createTestWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false, staleTime: 0 },
      mutations: { retry: false },
    },
  });

  return {
    wrapper: ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    ),
    queryClient,
  };
};

// Mock user data
const mockUser = {
  id: 1,
  username: 'testuser',
  telegram_id: '12345',
  email: '<EMAIL>',
  status: 'active',
  created_at: '2023-01-01T00:00:00Z',
  last_seen: '2023-01-01T00:00:00Z',
};

describe('Authentication Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
    vi.useFakeTimers();
    
    // Reset localStorage and sessionStorage
    localStorage.clear();
    sessionStorage.clear();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Complete Auth Flow - Existing User', () => {
    it('should handle complete Telegram login flow for existing user', async () => {
      // Arrange
      const { wrapper, queryClient } = createTestWrapper();
      
      // Mock Telegram WebApp data
      const mockInitData = 'user=%7B%22id%22%3A12345%2C%22username%22%3A%22testuser%22%7D';
      mockTelegramWebApp.isAvailable.mockReturnValue(true);
      mockTelegramWebApp.getWebApp.mockReturnValue({
        initData: mockInitData,
        ready: vi.fn(),
        expand: vi.fn(),
        initDataUnsafe: {},
        version: '6.0',
        platform: 'web',
        colorScheme: 'light',
        themeParams: {},
        isExpanded: false,
        viewportHeight: 600,
        viewportStableHeight: 600,
        headerColor: '#ffffff',
        backgroundColor: '#ffffff',
        isClosingConfirmationEnabled: false,
        isVerticalSwipesEnabled: true,
        MainButton: {} as any,
        BackButton: {} as any,
        SettingsButton: {} as any,
        HapticFeedback: {} as any,
        CloudStorage: {} as any,
        BiometricManager: {} as any,
        setHeaderColor: vi.fn(),
        setBackgroundColor: vi.fn(),
        enableClosingConfirmation: vi.fn(),
        disableClosingConfirmation: vi.fn(),
        enableVerticalSwipes: vi.fn(),
        disableVerticalSwipes: vi.fn(),
        onEvent: vi.fn(),
        offEvent: vi.fn(),
        sendData: vi.fn(),
        switchInlineQuery: vi.fn(),
        openLink: vi.fn(),
        openTelegramLink: vi.fn(),
        openInvoice: vi.fn(),
        showPopup: vi.fn(),
        showAlert: vi.fn(),
        showConfirm: vi.fn(),
        showScanQrPopup: vi.fn(),
        closeScanQrPopup: vi.fn(),
        readTextFromClipboard: vi.fn(),
        requestWriteAccess: vi.fn(),
        requestContact: vi.fn(),
        close: vi.fn(),
        invokeCustomMethod: vi.fn(),
      } as unknown as TelegramWebApp);

      // Mock successful login response
      (mockApi.post as any).mockResolvedValueOnce({
        data: { data: mockUser },
      });

      // Mock session verification
      (mockApi.post as any).mockResolvedValueOnce({
        data: {
          status: 'authenticated',
          data: mockUser,
        },
      });

      // Act - Start with login mutation
      const { result: loginResult } = renderHook(() => useTelegramLoginMutation(), { wrapper });
      
      await act(async () => {
        loginResult.current.mutate({
          init_data: mockInitData,
          auth_method: 'telegram',
        });
      });

      // Wait for login to complete
      await waitFor(() => {
        expect(loginResult.current.isSuccess).toBe(true);
      });

      // Verify login result
      expect(loginResult.current.data).toEqual({
        isNewUser: false,
        user: mockUser,
      });

      // Act - Verify that auth query reflects the logged-in state
      const { result: authResult } = renderHook(() => useCurrentUserQuery(), { wrapper });

      await waitFor(() => {
        expect(authResult.current.isSuccess).toBe(true);
      });

      // Assert - User should be authenticated
      expect(authResult.current.data).toEqual(mockUser);

      // Verify API calls
      expect(mockApi.post).toHaveBeenCalledWith(
        '/auth/telegram/login',
        {
          init_data: mockInitData,
          auth_method: 'telegram',
        },
        expect.any(Object)
      );
    });
  });

  describe('Complete Auth Flow - New User', () => {
    it('should handle new user signup flow', async () => {
      // Arrange
      const { wrapper, queryClient } = createTestWrapper();
      
      const mockInitData = 'user=%7B%22id%22%3A67890%2C%22username%22%3A%22newuser%22%7D';
      const mockTelegramData = {
        user_id: '67890',
        username: 'newuser',
        first_name: 'New',
        last_name: 'User',
      };

      // Mock 404 response for new user
      (mockApi.post as any).mockRejectedValueOnce({
        response: {
          status: 404,
          data: { telegram_data: mockTelegramData },
        },
      });

      // Act
      const { result } = renderHook(() => useTelegramLoginMutation(), { wrapper });
      
      await act(async () => {
        result.current.mutate({
          init_data: mockInitData,
          auth_method: 'telegram',
        });
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual({
        isNewUser: true,
        telegram_data: mockTelegramData,
      });

      // Verify setup data is stored
      const storedSetupData = sessionStorage.getItem('telegram_setup_data');
      expect(storedSetupData).toBeTruthy();
      
      const parsedSetupData = JSON.parse(storedSetupData!);
      expect(parsedSetupData.telegramData).toEqual(mockTelegramData);
      expect(parsedSetupData.timestamp).toBeTypeOf('number');
    });
  });

  describe('Auth State Persistence', () => {
    it('should persist auth state across page reloads', async () => {
      // Arrange
      const { wrapper, queryClient } = createTestWrapper();

      // Mock cached auth data (simulating page reload)
      queryClient.setQueryData(['currentUser'], {
        status: 'authenticated',
        data: mockUser,
      });

      // Act
      const { result } = renderHook(() => useCurrentUserQuery(), { wrapper });

      // Assert - Should use cached data initially
      expect(result.current.data).toEqual({
        status: 'authenticated',
        data: mockUser,
      });
      expect(result.current.isLoading).toBe(false);
    });

    it('should clear auth state on logout', async () => {
      // Arrange
      const { wrapper, queryClient } = createTestWrapper();
      
      // Set initial authenticated state
      queryClient.setQueryData(['currentUser'], {
        status: 'authenticated',
        data: mockUser,
      });

      // Mock logout response
      (mockApi.post as any).mockResolvedValueOnce({ data: { success: true } });

      // Act
      const { result } = renderHook(() => useLogoutMutation(), { wrapper });
      
      await act(async () => {
        result.current.mutate();
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      // Verify cache is cleared
      const cachedData = queryClient.getQueryData(['currentUser']);
      expect(cachedData).toEqual({
        status: 'unauthenticated',
        data: null,
      });
    });
  });

  describe('Error Recovery', () => {
    it('should recover from network errors gracefully', async () => {
      // Arrange
      const { wrapper } = createTestWrapper();

      // Mock network error followed by success
      (mockApi.post as any)
        .mockRejectedValueOnce({ code: 'NETWORK_ERROR' })
        .mockResolvedValueOnce({
          data: {
            status: 'authenticated',
            data: mockUser,
          },
        });

      // Act
      const { result } = renderHook(() => useCurrentUserQuery(), { wrapper });

      // First call fails
      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      // Manual refetch should succeed
      await act(async () => {
        await result.current.refetch();
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual({
        status: 'authenticated',
        data: mockUser,
      });
    });

    it('should handle token refresh scenario', async () => {
      // Arrange
      const { wrapper } = createTestWrapper();

      // Mock 401 error (expired token) followed by successful refresh
      (mockApi.post as any)
        .mockRejectedValueOnce({ response: { status: 401 } })
        .mockResolvedValueOnce({
          data: {
            status: 'authenticated',
            data: mockUser,
          },
        });

      // Act
      const { result } = renderHook(() => useCurrentUserQuery(), { wrapper });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      // Should handle 401 gracefully and return unauthenticated
      expect(result.current.data).toEqual({
        status: 'unauthenticated',
        data: null,
      });
    });
  });

  describe('Concurrent Auth Operations', () => {
    it('should handle multiple simultaneous auth checks', async () => {
      // Arrange
      const { wrapper } = createTestWrapper();

      (mockApi.post as any).mockResolvedValue({
        data: {
          status: 'authenticated',
          data: mockUser,
        },
      });

      // Act - Create multiple concurrent auth queries
      const { result: result1 } = renderHook(() => useCurrentUserQuery(), { wrapper });
      const { result: result2 } = renderHook(() => useCurrentUserQuery(), { wrapper });

      // Assert - Both should complete successfully
      await waitFor(() => {
        expect(result1.current.isSuccess).toBe(true);
        expect(result2.current.isSuccess).toBe(true);
      });

      expect(result1.current.data).toEqual(result2.current.data);
      
      // Should only make one API call due to React Query deduplication
      expect(mockApi.post).toHaveBeenCalledTimes(1);
    });
  });

  describe('WebApp Environment Integration', () => {
    it('should handle Telegram WebApp initialization', async () => {
      // Arrange
      const mockWebApp = {
        initData: 'test_init_data',
        ready: vi.fn(),
        expand: vi.fn(),
        setHeaderColor: vi.fn(),
        setBackgroundColor: vi.fn(),
        initDataUnsafe: {},
        version: '6.0',
        platform: 'web',
        colorScheme: 'light',
        themeParams: {},
        isExpanded: false,
        viewportHeight: 600,
        viewportStableHeight: 600,
        headerColor: '#ffffff',
        backgroundColor: '#ffffff',
        isClosingConfirmationEnabled: false,
        isVerticalSwipesEnabled: true,
        MainButton: {} as any,
        BackButton: {} as any,
        SettingsButton: {} as any,
        HapticFeedback: {} as any,
        CloudStorage: {} as any,
        BiometricManager: {} as any,
        enableClosingConfirmation: vi.fn(),
        disableClosingConfirmation: vi.fn(),
        enableVerticalSwipes: vi.fn(),
        disableVerticalSwipes: vi.fn(),
        onEvent: vi.fn(),
        offEvent: vi.fn(),
        sendData: vi.fn(),
        switchInlineQuery: vi.fn(),
        openLink: vi.fn(),
        openTelegramLink: vi.fn(),
        openInvoice: vi.fn(),
        showPopup: vi.fn(),
        showAlert: vi.fn(),
        showConfirm: vi.fn(),
        showScanQrPopup: vi.fn(),
        closeScanQrPopup: vi.fn(),
        readTextFromClipboard: vi.fn(),
        requestWriteAccess: vi.fn(),
        requestContact: vi.fn(),
        close: vi.fn(),
        invokeCustomMethod: vi.fn(),
      } as unknown as TelegramWebApp;

      mockTelegramWebApp.isAvailable.mockReturnValue(true);
      mockTelegramWebApp.getWebApp.mockReturnValue(mockWebApp);

      // Act
      const isAvailable = mockTelegramWebApp.isAvailable();
      const webApp = mockTelegramWebApp.getWebApp();

      // Assert
      expect(isAvailable).toBe(true);
      expect(webApp).toBeTruthy();
      expect(webApp?.initData).toBe('test_init_data');
    });

    it('should handle non-Telegram environments', async () => {
      // Arrange
      mockTelegramWebApp.isAvailable.mockReturnValue(false);
      mockTelegramWebApp.getWebApp.mockReturnValue(null);

      // Act
      const isAvailable = mockTelegramWebApp.isAvailable();
      const webApp = mockTelegramWebApp.getWebApp();

      // Assert
      expect(isAvailable).toBe(false);
      expect(webApp).toBeNull();
    });
  });

  describe('Cache Optimization', () => {
    it('should optimize cache usage for auth queries', async () => {
      // Arrange
      const { wrapper, queryClient } = createTestWrapper();

      // Set up stale data
      queryClient.setQueryData(['currentUser'], {
        status: 'authenticated',
        data: mockUser,
      });

      // Mark data as stale
      queryClient.invalidateQueries({ queryKey: ['currentUser'] });

      // Act
      const { result } = renderHook(() => useCurrentUserQuery(), { wrapper });

      // Assert - Should use stale data while fetching fresh data
      expect(result.current.data).toEqual({
        status: 'authenticated',
        data: mockUser,
      });
      expect(result.current.isStale).toBe(true);
    });

    it('should handle cache invalidation properly', async () => {
      // Arrange
      const { wrapper, queryClient } = createTestWrapper();

      // Set initial data
      queryClient.setQueryData(['currentUser'], {
        status: 'authenticated',
        data: mockUser,
      });

      // Act - Invalidate cache
      await act(async () => {
        queryClient.invalidateQueries({ queryKey: ['currentUser'] });
      });

      // Assert
      const queryState = queryClient.getQueryState(['currentUser']);
      expect(queryState?.isInvalidated).toBe(true);
    });
  });

  describe('Performance Metrics', () => {
    it('should track auth query performance', async () => {
      // Arrange
      const { wrapper } = createTestWrapper();
      const startTime = performance.now();

      (mockApi.post as any).mockResolvedValueOnce({
        data: {
          status: 'authenticated',
          data: mockUser,
        },
      });

      // Act
      const { result } = renderHook(() => useCurrentUserQuery(), { wrapper });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      // Assert
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      // Auth query should complete reasonably quickly
      expect(duration).toBeLessThan(1000); // Less than 1 second in test environment
    });
  });
});