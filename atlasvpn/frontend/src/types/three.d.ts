/// <reference types="@react-three/fiber" />

import { extend } from '@react-three/fiber';
import * as THREE from 'three';

// Extend the fiber catalog with Three.js objects
extend(THREE);

// Augment the JSX namespace to include Three.js elements
declare global {
  namespace JSX {
    interface IntrinsicElements {
      // Geometry
      boxGeometry: any;
      sphereGeometry: any;
      coneGeometry: any;
      
      // Materials
      meshStandardMaterial: any;
      meshBasicMaterial: any;
      lineBasicMaterial: any;
      
      // Objects
      mesh: any;
      group: any;
      instancedMesh: any;
      lineSegments: any;
      
      // Lights
      ambientLight: any;
      directionalLight: any;
    }
  }
} 