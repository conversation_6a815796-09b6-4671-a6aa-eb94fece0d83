#!/usr/bin/env python
"""
Create Test Tasks Script

This script connects to the API as an admin and creates a set of test tasks
with proper data for all supported task types.
"""
import sys
import os
from pathlib import Path
import asyncio
import logging
from datetime import datetime
import json
import httpx
import time
import traceback
from typing import Dict, List, Optional, Any

# Add backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

# Set up logs directory
SCRIPT_DIR = Path(__file__).parent
LOGS_DIR = SCRIPT_DIR / "logs"
LOGS_DIR.mkdir(exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOGS_DIR / 'create_test_tasks.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TaskCreator:
    """Connect as admin and create test tasks"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.admin_token = None
        self.admin_headers = None
        self.client = httpx.AsyncClient(timeout=30.0, verify=False)
        
        # Admin credentials
        self.admin_credentials = {
            "username": "admin_test",
            "password": "admin123"
        }
        
        # Stats about the operation
        self.stats = {
            "created_tasks": 0,
            "failed_tasks": 0,
            "errors": []
        }
    
    async def admin_login(self):
        """Login as admin"""
        try:
            # First make a GET request to get the CSRF token
            auth_url = f"{self.base_url}/auth/token"
            
            # Manual GET request to get CSRF token
            async with httpx.AsyncClient(timeout=30.0, verify=False) as client:
                try:
                    response = await client.get(auth_url)
                    csrf_token = response.cookies.get("csrf_token")
                except Exception as e:
                    logger.warning(f"Failed to get CSRF token: {str(e)}")
                    csrf_token = None
                
                if csrf_token:
                    logger.info(f"Retrieved CSRF token for admin login")
                    # Now make the POST request with the CSRF token
                    headers = {
                        "Content-Type": "application/json",
                        "X-CSRF-Token": csrf_token,
                        "X-Captcha-Token": "bypass_captcha_in_test_mode"
                    }
                    
                    response = await client.post(
                        auth_url,
                        json={
                            "username": self.admin_credentials["username"],
                            "password": self.admin_credentials["password"]
                        },
                        headers=headers,
                        cookies={"csrf_token": csrf_token}
                    )
                else:
                    # Fallback to no CSRF token
                    logger.warning("No CSRF token found for admin login")
                    
                    # Try direct login
                    login_url = f"{self.base_url}/auth/login"
                    response = await client.post(
                        login_url,
                        json={
                            "username": self.admin_credentials["username"],
                            "password": self.admin_credentials["password"]
                        },
                        headers={"X-Captcha-Token": "bypass_captcha_in_test_mode"}
                    )
            
            if response.status_code == 200:
                data = response.json()
                self.admin_token = data["access_token"]
                self.admin_headers = {"Authorization": f"Bearer {self.admin_token}"}
                logger.info("Admin login successful")
                return True
            else:
                logger.error(f"Admin login failed: {response.status_code} {response.text}")
                return False
        except Exception as e:
            logger.error(f"Admin login error: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    async def create_task(self, task_data):
        """Create a new task using admin API"""
        try:
            response = await self.client.post(
                f"{self.base_url}/api/tasks/admin/tasks/create",
                headers=self.admin_headers,
                json=task_data
            )
            
            if response.status_code == 200:
                task = response.json()
                logger.info(f"Created task: {task['name']} (ID: {task['id']})")
                self.stats["created_tasks"] += 1
                return task
            else:
                logger.error(f"Failed to create task: {response.status_code} {response.text}")
                self.stats["failed_tasks"] += 1
                return None
        except Exception as e:
            logger.error(f"Error creating task: {str(e)}")
            self.stats["errors"].append(str(e))
            self.stats["failed_tasks"] += 1
            return None
    
    def prepare_test_tasks(self):
        """Prepare test task data"""
        test_tasks = [
            # YouTube View Task
            {
                "name": "Watch Tutorial Video",
                "description": "Watch our tutorial video on YouTube to learn more about the platform",
                "type": "YOUTUBE_VIEW",
                "reward_type": "WALLET_BONUS",
                "reward_value": 5.0,
                "target_value": 1,
                "is_active": True,
                "social_task": {
                    "platform": "youtube",
                    "platform_url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
                    "platform_id": "dQw4w9WgXcQ",
                    "verify_key": "VPN123",
                    "max_verification_attempts": 3,
                    "verification_interval": 60
                }
            },
            
            # Instagram View Task
            {
                "name": "View Instagram Post",
                "description": "Check out our latest post on Instagram to stay updated",
                "type": "INSTAGRAM_VIEW",
                "reward_type": "WALLET_BONUS",
                "reward_value": 3.0,
                "target_value": 1,
                "is_active": True,
                "social_task": {
                    "platform": "instagram",
                    "platform_url": "https://www.instagram.com/p/CxYzAbcDEf/",
                    "platform_id": "CxYzAbcDEf",
                    "verify_key": "INSTA456",
                    "max_verification_attempts": 3,
                    "verification_interval": 60
                }
            },
            
            # Instagram Follow Task
            {
                "name": "Follow Us on Instagram",
                "description": "Follow our Instagram page for regular updates",
                "type": "INSTAGRAM_FOLLOW",
                "reward_type": "WALLET_BONUS",
                "reward_value": 10.0,
                "target_value": 1,
                "is_active": True,
                "social_task": {
                    "platform": "instagram",
                    "platform_url": "https://www.instagram.com/ourcompany/",
                    "platform_id": "ourcompany",
                    "verify_key": "FOLLOW_KEY",
                    "max_verification_attempts": 3,
                    "verification_interval": 60
                }
            },
            
            # Telegram Channel Task
            {
                "name": "Join Our Telegram Channel",
                "description": "Join our Telegram channel for news and support",
                "type": "TELEGRAM_CHANNEL",
                "reward_type": "WALLET_BONUS",
                "reward_value": 15.0,
                "target_value": 1,
                "is_active": True,
                "social_task": {
                    "platform": "telegram",
                    "platform_url": "https://t.me/our_channel",
                    "platform_id": "our_channel",
                    "verify_key": None,
                    "max_verification_attempts": 3,
                    "verification_interval": 60
                }
            },
            
            # Twitter Follow Task
            {
                "name": "Follow Us on Twitter",
                "description": "Follow our Twitter account for the latest news",
                "type": "TWITTER_FOLLOW",
                "reward_type": "WALLET_BONUS",
                "reward_value": 8.0,
                "target_value": 1,
                "is_active": True,
                "social_task": {
                    "platform": "twitter",
                    "platform_url": "https://twitter.com/ourcompany",
                    "platform_id": "ourcompany",
                    "verify_key": "FOLLOW_KEY",
                    "max_verification_attempts": 3,
                    "verification_interval": 60
                }
            },
            
            # Website Visit Task
            {
                "name": "Visit Our Website",
                "description": "Explore our website and learn more about our services",
                "type": "WEBSITE_VISIT",
                "reward_type": "WALLET_BONUS",
                "reward_value": 5.0,
                "target_value": 1,
                "is_active": True,
                "social_task": {
                    "platform": "website",
                    "platform_url": "https://ourwebsite.com/features",
                    "platform_id": "features_page",
                    "verify_key": None,
                    "max_verification_attempts": 3,
                    "verification_interval": 60
                }
            },
            
            # Referral Task
            {
                "name": "Refer a Friend",
                "description": "Invite friends to join our platform and earn rewards",
                "type": "REFERRAL",
                "reward_type": "WALLET_BONUS",
                "reward_value": 20.0,
                "target_value": 5,
                "is_active": True,
                "platform_url": None,
                "platform_id": "referral_program",
                "verify_key": None
            },
            
            # Daily Check-in Task
            {
                "name": "Daily Check-in",
                "description": "Check in daily to earn rewards and build streak",
                "type": "DAILY_CHECKIN",
                "reward_type": "WALLET_BONUS",
                "reward_value": 2.0,
                "target_value": 1,
                "is_active": True,
                "cycle_length": 7,
                "daily_rewards": [2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 15.0],
                "cycle_bonus_reward": 10.0,
                "reset_streak_after_hours": 48
            }
        ]
        
        return test_tasks
    
    def print_report(self):
        """Print final report"""
        logger.info("=" * 50)
        logger.info("Task Creation Report")
        logger.info("=" * 50)
        logger.info(f"Tasks created: {self.stats['created_tasks']}")
        logger.info(f"Tasks failed: {self.stats['failed_tasks']}")
        
        if self.stats['errors']:
            logger.info(f"Errors encountered: {len(self.stats['errors'])}")
            for error in self.stats['errors'][:5]:  # Show only first 5 errors
                logger.info(f"  - {error}")
                
        logger.info("=" * 50)
    
    async def run(self):
        """Main function to create test tasks"""
        try:
            logger.info("Starting task creation process...")
            
            # Login as admin
            success = await self.admin_login()
            if not success:
                logger.error("Admin login failed, cannot continue")
                return False
            
            # Prepare test tasks
            test_tasks = self.prepare_test_tasks()
            logger.info(f"Prepared {len(test_tasks)} test tasks")
            
            # Create tasks
            for task_data in test_tasks:
                # Transform data for API compatibility
                api_task_data = task_data.copy()
                
                # Extract social_task fields and flatten them for the API
                if "social_task" in api_task_data:
                    social_task = api_task_data.pop("social_task")
                    # Add social task fields to the main task
                    api_task_data["platform_url"] = social_task.get("platform_url")
                    api_task_data["platform_id"] = social_task.get("platform_id")
                    api_task_data["verify_key"] = social_task.get("verify_key")
                    api_task_data["max_verification_attempts"] = social_task.get("max_verification_attempts", 3)
                    api_task_data["verification_cooldown"] = social_task.get("verification_interval", 60)
                
                # DO NOT convert daily_rewards to JSON here - API expects a list
                # if "daily_rewards" in api_task_data and isinstance(api_task_data["daily_rewards"], list):
                #     api_task_data["daily_rewards"] = json.dumps(api_task_data["daily_rewards"])
                
                # Create task
                await self.create_task(api_task_data)
                
                # Small delay between creations
                await asyncio.sleep(0.5)
            
            # Print report
            self.print_report()
            
            return self.stats["failed_tasks"] == 0
        except Exception as e:
            logger.error(f"Error in task creation process: {str(e)}")
            logger.error(traceback.format_exc())
            return False
        finally:
            await self.client.aclose()

async def main():
    """Main function"""
    # Get API base URL from environment variable or use default
    api_base_url = os.environ.get("API_BASE_URL", "http://localhost:8000")
    
    # Create task creator
    task_creator = TaskCreator(base_url=api_base_url)
    
    # Run creation process
    await task_creator.run()

if __name__ == "__main__":
    asyncio.run(main()) 