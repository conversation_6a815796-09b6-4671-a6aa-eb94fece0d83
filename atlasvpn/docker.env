# AtlasVPN Docker Environment Configuration
# Copy values to your .env file

# Database Configuration
DATABASE_URL=***************************************************************************/atlasvpn
POSTGRES_DB=atlasvpn
POSTGRES_USER=atlasvpn_user
POSTGRES_PASSWORD=atlasvpn_secure_pass_2025

# Redis Configuration
REDIS_URL=redis://atlasvpn-redis:6379/0

# Application Configuration
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=dev-secret-key-change-in-production-use-openssl-rand-hex-32
DOMAIN=app.atlasvip.cloud

# Telegram Bot Configuration (REPLACE WITH YOUR VALUES)
BOT_TOKEN=YOUR_SAMPLE_BOT_TOKEN_HERE
TELEGRAM_BOT_TOKEN=YOUR_SAMPLE_TELEGRAM_BOT_TOKEN_HERE
TELEGRAM_BOT_USERNAME=your_sample_bot_username

# Security Configuration
CORS_ORIGINS=https://app.atlasvip.cloud,http://localhost:3005
COOKIE_DOMAIN=app.atlasvip.cloud

# Frontend Configuration
VITE_API_URL=https://app.atlasvip.cloud/api
VITE_WS_BASE_URL=wss://app.atlasvip.cloud/ws
VITE_TELEGRAM_WEBAPP_URL=https://app.atlasvip.cloud
VITE_TELEGRAM_BOT_USERNAME=your_bot_username

# Performance Configuration
WORKERS=1
RELOAD=true 