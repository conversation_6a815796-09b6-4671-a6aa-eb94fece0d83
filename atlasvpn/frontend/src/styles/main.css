/* Base Input Styles */
.form-input {
  width: 100%;
  background-color: #0f1520;
  border: 1px solid #1f2937;
  border-radius: 8px;
  padding: 10px 16px;
  color: white;
  font-size: 14px;
  line-height: 20px;
  transition: all 0.2s ease;
}

.form-input::placeholder {
  color: #6b7280;
}

.form-input:hover {
  border-color: #374151;
  background-color: #131926;
}

.form-input:focus {
  outline: none;
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Input Container */
.input-container {
  position: relative;
  margin-bottom: 16px;
}

/* Label */
.input-label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #9ca3af;
  margin-bottom: 6px;
}

/* Validation States */
.input-error {
  border-color: rgba(239, 68, 68, 0.3);
  background-color: rgba(239, 68, 68, 0.05);
}

.input-error:focus {
  border-color: rgba(239, 68, 68, 0.5);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-success {
  border-color: rgba(16, 185, 129, 0.3);
  background-color: rgba(16, 185, 129, 0.05);
}

.input-success:focus {
  border-color: rgba(16, 185, 129, 0.5);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Validation Icon */
.validation-icon-wrapper {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.validation-icon {
  width: 16px;
  height: 16px;
  transition: all 0.2s ease;
}

.validation-icon-success {
  color: #10b981;
}

.validation-icon-error {
  color: #ef4444;
}

/* Validation Message */
.error-message {
  margin-top: 4px;
  font-size: 11px;
  color: #ef4444;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.success-message {
  margin-top: 4px;
  font-size: 11px;
  color: #10b981;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

@media (min-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr 1fr;
  }
}

/* Required Field */
.required-field::after {
  content: ' *';
  color: #ef4444;
}

/* Disabled State */
.form-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #1a1f2e;
}

/* Role Select */
.role-select {
  width: 100%;
  background-color: #0f1520;
  border: 1px solid #1f2937;
  border-radius: 8px;
  padding: 10px 16px;
  color: white;
  font-size: 14px;
  line-height: 20px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.role-select:hover {
  border-color: #374151;
}

.role-select:focus {
  outline: none;
  border-color: rgba(139, 92, 246, 0.5);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Checkbox */
.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-checkbox {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 1px solid #1f2937;
  background-color: #0f1520;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-checkbox:checked {
  background-color: #8b5cf6;
  border-color: #8b5cf6;
}

/* Animation */
@keyframes validationPulse {
  0%,
  100% {
    transform: scale(1) translateY(-50%);
  }
  50% {
    transform: scale(1.1) translateY(-50%);
  }
}

.validation-icon-wrapper {
  animation: validationPulse 0.3s ease-in-out;
}
