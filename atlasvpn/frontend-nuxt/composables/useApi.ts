import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import type { ApiResponse } from '~/types'

// API Error class
export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message)
    this.name = 'APIError'
  }
}

class ApiClient {
  private axiosInstance: AxiosInstance
  private baseURL: string
  private wsBaseURL: string

  constructor() {
    const config = useRuntimeConfig()
    this.baseURL = config.public.apiUrl
    this.wsBaseURL = config.public.wsBaseUrl

    // Create axios instance
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      withCredentials: true,
      timeout: 15000,
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      (config) => {
        const fullUrl = (config.baseURL || '') + (config.url || '')
        console.log('🚀 [ApiClient] Request:', config.method?.toUpperCase(), fullUrl)
        return config
      },
      (error) => {
        console.error('[API] Request interceptor error:', error)
        return Promise.reject(error)
      }
    )

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => {
        if (process.dev) {
          console.log('[API] Response:', {
            url: response.config.url,
            status: response.status,
            data: response.data,
          })
        }
        return response
      },
      async (error) => {
        if (error.response?.status === 401) {
          // Handle authentication errors
          if (typeof window !== 'undefined') {
            await navigateTo('/')
          }
        }
        return Promise.reject(this.handleError(error))
      }
    )
  }

  private handleError(error: any): APIError {
    let finalMessage = 'An unknown error occurred. Please try again.'
    let specificMessageFound = false

    // Handle network errors first
    if (error?.code === 'ERR_NETWORK' || error?.code === 'NETWORK_ERROR') {
      finalMessage = 'Network connection error. Please check your internet connection and try again.'
      specificMessageFound = true
    }
    
    // Handle timeout errors
    else if (error?.code === 'ECONNABORTED' || error?.message?.includes('timeout')) {
      finalMessage = 'Request timed out. Please try again.'
      specificMessageFound = true
    }
    
    // Handle cancelled requests
    else if (error?.code === 'ERR_CANCELED' || error?.name === 'AbortError') {
      finalMessage = 'Request was cancelled.'
      specificMessageFound = true
    }

    // Handle axios error response
    else if (error?.response?.data) {
      const data = error.response.data
      
      // Check for FastAPI error format
      if (data.detail) {
        if (typeof data.detail === 'string') {
          finalMessage = data.detail
          specificMessageFound = true
        } else if (typeof data.detail === 'object' && data.detail.message) {
          finalMessage = data.detail.message
          specificMessageFound = true
        }
      } else if (data.message) {
        finalMessage = data.message
        specificMessageFound = true
      }
    }

    // Handle specific HTTP status codes
    if (!specificMessageFound && error?.response?.status) {
      switch (error.response.status) {
        case 400:
          finalMessage = 'Bad request. Please check your input and try again.'
          break
        case 401:
          finalMessage = 'Authentication required. Please log in again.'
          break
        case 403:
          finalMessage = 'Access denied. You do not have permission to perform this action.'
          break
        case 404:
          finalMessage = 'The requested resource was not found.'
          break
        case 429:
          finalMessage = 'Too many requests. Please wait a moment and try again.'
          break
        case 500:
          finalMessage = 'Server error. Please try again later.'
          break
        case 502:
        case 503:
        case 504:
          finalMessage = 'Service temporarily unavailable. Please try again later.'
          break
        default:
          finalMessage = `Request failed with status code ${error.response.status}`
      }
    }

    // Create error with additional context
    const apiError = new APIError(
      finalMessage,
      error?.response?.status || error?.status || 500,
      error?.code
    )
    return apiError
  }

  // Generic HTTP methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.get<T>(url, config)
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.post<T>(url, data, config)
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.put<T>(url, data, config)
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.patch<T>(url, data, config)
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.axiosInstance.delete<T>(url, config)
  }

  // WebSocket connection
  createWebSocket(path: string = '') {
    return new WebSocket(`${this.wsBaseURL}${path}`)
  }
}

export const useApi = () => {
  return new ApiClient()
}
