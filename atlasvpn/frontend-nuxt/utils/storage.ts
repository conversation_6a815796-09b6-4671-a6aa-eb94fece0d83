/**
 * Storage utilities for consistent data persistence across the application
 */

import { STORAGE_KEYS } from './constants';

/**
 * Storage interface for consistent API
 */
interface StorageInterface {
  getItem(key: string): string | null;
  setItem(key: string, value: string): void;
  removeItem(key: string): void;
  clear(): void;
}

/**
 * Safe storage wrapper that handles errors gracefully
 */
class SafeStorage implements StorageInterface {
  private storage: Storage | null = null;
  private fallbackStorage: Map<string, string> = new Map();

  constructor(storageType: 'localStorage' | 'sessionStorage' = 'localStorage') {
    if (typeof window !== 'undefined') {
      try {
        this.storage = window[storageType];
        // Test if storage is available
        const testKey = '__storage_test__';
        this.storage.setItem(testKey, 'test');
        this.storage.removeItem(testKey);
      } catch (error) {
        console.warn(`${storageType} is not available, using fallback storage:`, error);
        this.storage = null;
      }
    }
  }

  getItem(key: string): string | null {
    try {
      if (this.storage) {
        return this.storage.getItem(key);
      }
      return this.fallbackStorage.get(key) || null;
    } catch (error) {
      console.warn('Storage getItem error:', error);
      return this.fallbackStorage.get(key) || null;
    }
  }

  setItem(key: string, value: string): void {
    try {
      if (this.storage) {
        this.storage.setItem(key, value);
      }
      this.fallbackStorage.set(key, value);
    } catch (error) {
      console.warn('Storage setItem error:', error);
      this.fallbackStorage.set(key, value);
    }
  }

  removeItem(key: string): void {
    try {
      if (this.storage) {
        this.storage.removeItem(key);
      }
      this.fallbackStorage.delete(key);
    } catch (error) {
      console.warn('Storage removeItem error:', error);
      this.fallbackStorage.delete(key);
    }
  }

  clear(): void {
    try {
      if (this.storage) {
        this.storage.clear();
      }
      this.fallbackStorage.clear();
    } catch (error) {
      console.warn('Storage clear error:', error);
      this.fallbackStorage.clear();
    }
  }
}

// Create storage instances
const localStorage = new SafeStorage('localStorage');
const sessionStorage = new SafeStorage('sessionStorage');

/**
 * Generic storage utility with JSON serialization
 */
class StorageUtil {
  constructor(private storage: StorageInterface) {}

  /**
   * Get item from storage with JSON parsing
   * @param key - Storage key
   * @param defaultValue - Default value if key doesn't exist
   * @returns Parsed value or default value
   */
  get<T>(key: string, defaultValue?: T): T | null {
    try {
      const item = this.storage.getItem(key);
      if (item === null) {
        return defaultValue !== undefined ? defaultValue : null;
      }
      return JSON.parse(item);
    } catch (error) {
      console.warn(`Failed to parse storage item "${key}":`, error);
      return defaultValue !== undefined ? defaultValue : null;
    }
  }

  /**
   * Set item in storage with JSON serialization
   * @param key - Storage key
   * @param value - Value to store
   */
  set<T>(key: string, value: T): void {
    try {
      this.storage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.warn(`Failed to set storage item "${key}":`, error);
    }
  }

  /**
   * Remove item from storage
   * @param key - Storage key
   */
  remove(key: string): void {
    this.storage.removeItem(key);
  }

  /**
   * Check if key exists in storage
   * @param key - Storage key
   * @returns True if key exists
   */
  has(key: string): boolean {
    return this.storage.getItem(key) !== null;
  }

  /**
   * Clear all items from storage
   */
  clear(): void {
    this.storage.clear();
  }

  /**
   * Get all keys from storage
   * @returns Array of storage keys
   */
  keys(): string[] {
    if (typeof window === 'undefined') return [];
    
    try {
      const keys: string[] = [];
      for (let i = 0; i < (this.storage as any).length; i++) {
        const key = (this.storage as any).key(i);
        if (key) keys.push(key);
      }
      return keys;
    } catch (error) {
      console.warn('Failed to get storage keys:', error);
      return [];
    }
  }

  /**
   * Get storage size in bytes (approximate)
   * @returns Storage size in bytes
   */
  getSize(): number {
    let size = 0;
    try {
      const keys = this.keys();
      keys.forEach(key => {
        const value = this.storage.getItem(key);
        if (value) {
          size += key.length + value.length;
        }
      });
    } catch (error) {
      console.warn('Failed to calculate storage size:', error);
    }
    return size;
  }
}

// Create utility instances
export const localStorageUtil = new StorageUtil(localStorage);
export const sessionStorageUtil = new StorageUtil(sessionStorage);

/**
 * Application-specific storage utilities
 */
export const appStorage = {
  // Authentication
  getAuthToken: (): string | null => localStorageUtil.get(STORAGE_KEYS.AUTH_TOKEN),
  setAuthToken: (token: string) => localStorageUtil.set(STORAGE_KEYS.AUTH_TOKEN, token),
  removeAuthToken: () => localStorageUtil.remove(STORAGE_KEYS.AUTH_TOKEN),

  // User data
  getUserData: (): any => localStorageUtil.get(STORAGE_KEYS.USER_DATA),
  setUserData: (userData: any) => localStorageUtil.set(STORAGE_KEYS.USER_DATA, userData),
  removeUserData: () => localStorageUtil.remove(STORAGE_KEYS.USER_DATA),

  // Theme
  getTheme: (): string => localStorageUtil.get(STORAGE_KEYS.THEME, 'dark'),
  setTheme: (theme: string) => localStorageUtil.set(STORAGE_KEYS.THEME, theme),

  // Language
  getLanguage: (): string => localStorageUtil.get(STORAGE_KEYS.LANGUAGE, 'en'),
  setLanguage: (language: string) => localStorageUtil.set(STORAGE_KEYS.LANGUAGE, language),

  // Settings
  getSettings: (): any => localStorageUtil.get(STORAGE_KEYS.SETTINGS, {}),
  setSettings: (settings: any) => localStorageUtil.set(STORAGE_KEYS.SETTINGS, settings),
  updateSettings: (partialSettings: any) => {
    const currentSettings = appStorage.getSettings();
    appStorage.setSettings({ ...currentSettings, ...partialSettings });
  },

  // Telegram data
  getTelegramData: (): any => localStorageUtil.get(STORAGE_KEYS.TELEGRAM_DATA),
  setTelegramData: (data: any) => localStorageUtil.set(STORAGE_KEYS.TELEGRAM_DATA, data),
  removeTelegramData: () => localStorageUtil.remove(STORAGE_KEYS.TELEGRAM_DATA),

  // Performance mode
  getPerformanceMode: (): boolean => localStorageUtil.get(STORAGE_KEYS.PERFORMANCE_MODE, false),
  setPerformanceMode: (enabled: boolean) => localStorageUtil.set(STORAGE_KEYS.PERFORMANCE_MODE, enabled),

  // Last sync
  getLastSync: (): number => localStorageUtil.get(STORAGE_KEYS.LAST_SYNC, 0),
  setLastSync: (timestamp: number) => localStorageUtil.set(STORAGE_KEYS.LAST_SYNC, timestamp),

  // Clear all app data
  clearAll: () => {
    Object.values(STORAGE_KEYS).forEach(key => {
      localStorageUtil.remove(key);
    });
  },
};

/**
 * Cache utility with expiration
 */
export class CacheStorage {
  private prefix: string;

  constructor(prefix: string = 'cache_') {
    this.prefix = prefix;
  }

  /**
   * Set cache item with expiration
   * @param key - Cache key
   * @param value - Value to cache
   * @param ttl - Time to live in milliseconds
   */
  set<T>(key: string, value: T, ttl: number = 3600000): void { // Default 1 hour
    const cacheKey = this.prefix + key;
    const cacheValue = {
      data: value,
      expires: Date.now() + ttl,
    };
    localStorageUtil.set(cacheKey, cacheValue);
  }

  /**
   * Get cache item
   * @param key - Cache key
   * @returns Cached value or null if expired/not found
   */
  get<T>(key: string): T | null {
    const cacheKey = this.prefix + key;
    const cacheValue = localStorageUtil.get<{ data: T; expires: number }>(cacheKey);
    
    if (!cacheValue) return null;
    
    if (Date.now() > cacheValue.expires) {
      this.remove(key);
      return null;
    }
    
    return cacheValue.data;
  }

  /**
   * Remove cache item
   * @param key - Cache key
   */
  remove(key: string): void {
    const cacheKey = this.prefix + key;
    localStorageUtil.remove(cacheKey);
  }

  /**
   * Clear all cache items
   */
  clear(): void {
    const keys = localStorageUtil.keys();
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        localStorageUtil.remove(key);
      }
    });
  }

  /**
   * Clean expired cache items
   */
  cleanExpired(): void {
    const keys = localStorageUtil.keys();
    const now = Date.now();
    
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        const cacheValue = localStorageUtil.get<{ expires: number }>(key);
        if (cacheValue && now > cacheValue.expires) {
          localStorageUtil.remove(key);
        }
      }
    });
  }
}

// Create default cache instance
export const cache = new CacheStorage();

/**
 * Temporary storage for session data
 */
export const tempStorage = {
  set: <T>(key: string, value: T) => sessionStorageUtil.set(key, value),
  get: <T>(key: string, defaultValue?: T) => sessionStorageUtil.get(key, defaultValue),
  remove: (key: string) => sessionStorageUtil.remove(key),
  clear: () => sessionStorageUtil.clear(),
};

/**
 * Storage event listener for cross-tab communication
 */
export function onStorageChange(callback: (key: string, newValue: any, oldValue: any) => void): () => void {
  if (typeof window === 'undefined') return () => {};

  const handler = (event: StorageEvent) => {
    if (event.key && event.storageArea === window.localStorage) {
      try {
        const newValue = event.newValue ? JSON.parse(event.newValue) : null;
        const oldValue = event.oldValue ? JSON.parse(event.oldValue) : null;
        callback(event.key, newValue, oldValue);
      } catch (error) {
        console.warn('Failed to parse storage event values:', error);
      }
    }
  };

  window.addEventListener('storage', handler);
  
  return () => {
    window.removeEventListener('storage', handler);
  };
}

/**
 * Check storage quota and usage
 */
export async function getStorageQuota(): Promise<{ quota: number; usage: number } | null> {
  if (typeof navigator === 'undefined' || !navigator.storage?.estimate) {
    return null;
  }

  try {
    const estimate = await navigator.storage.estimate();
    return {
      quota: estimate.quota || 0,
      usage: estimate.usage || 0,
    };
  } catch (error) {
    console.warn('Failed to get storage quota:', error);
    return null;
  }
}
