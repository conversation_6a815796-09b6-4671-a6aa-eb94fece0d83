/**
 * Offline handling composable for Telegram Mini App
 * Provides offline detection, caching, and sync capabilities
 */

interface OfflineAction {
  id: string
  type: 'api_call' | 'user_action'
  endpoint: string
  method: string
  data: any
  timestamp: number
  retryCount: number
}

interface CachedData {
  key: string
  data: any
  timestamp: number
  ttl: number
}

export const useOffline = () => {
  // Reactive state
  const isOnline = ref(navigator?.onLine ?? true)
  const isConnecting = ref(false)
  const pendingActions = ref<OfflineAction[]>([])
  const cachedData = ref<Map<string, CachedData>>(new Map())
  const syncInProgress = ref(false)

  // Storage keys
  const PENDING_ACTIONS_KEY = 'atlas_pending_actions'
  const CACHED_DATA_KEY = 'atlas_cached_data'

  // Initialize offline handling
  const init = () => {
    if (process.server) return

    // Load persisted data
    loadPersistedData()

    // Listen for online/offline events
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    window.addEventListener('connection-change', handleConnectionChange)

    // Periodic sync attempt when online
    setInterval(() => {
      if (isOnline.value && pendingActions.value.length > 0) {
        syncPendingActions()
      }
    }, 30000) // Every 30 seconds

    // Cleanup expired cache
    setInterval(cleanupExpiredCache, 60000) // Every minute
  }

  // Handle online event
  const handleOnline = () => {
    console.log('[Offline] Connection restored')
    isOnline.value = true
    isConnecting.value = true
    
    // Attempt to sync pending actions
    syncPendingActions().finally(() => {
      isConnecting.value = false
    })
  }

  // Handle offline event
  const handleOffline = () => {
    console.log('[Offline] Connection lost')
    isOnline.value = false
    isConnecting.value = false
  }

  // Handle custom connection change event
  const handleConnectionChange = (event: CustomEvent) => {
    const { isOnline: online } = event.detail
    if (online !== isOnline.value) {
      if (online) {
        handleOnline()
      } else {
        handleOffline()
      }
    }
  }

  // Load persisted data from localStorage
  const loadPersistedData = () => {
    try {
      // Load pending actions
      const storedActions = localStorage.getItem(PENDING_ACTIONS_KEY)
      if (storedActions) {
        pendingActions.value = JSON.parse(storedActions)
      }

      // Load cached data
      const storedCache = localStorage.getItem(CACHED_DATA_KEY)
      if (storedCache) {
        const cacheArray = JSON.parse(storedCache)
        cachedData.value = new Map(cacheArray)
      }
    } catch (error) {
      console.error('[Offline] Failed to load persisted data:', error)
    }
  }

  // Persist data to localStorage
  const persistData = () => {
    try {
      localStorage.setItem(PENDING_ACTIONS_KEY, JSON.stringify(pendingActions.value))
      localStorage.setItem(CACHED_DATA_KEY, JSON.stringify(Array.from(cachedData.value.entries())))
    } catch (error) {
      console.error('[Offline] Failed to persist data:', error)
    }
  }

  // Add action to pending queue
  const queueAction = (action: Omit<OfflineAction, 'id' | 'timestamp' | 'retryCount'>) => {
    const offlineAction: OfflineAction = {
      ...action,
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: Date.now(),
      retryCount: 0
    }

    pendingActions.value.push(offlineAction)
    persistData()

    console.log('[Offline] Action queued:', offlineAction.type, offlineAction.endpoint)
  }

  // Sync pending actions when online
  const syncPendingActions = async () => {
    if (!isOnline.value || syncInProgress.value || pendingActions.value.length === 0) {
      return
    }

    syncInProgress.value = true
    const actionsToSync = [...pendingActions.value]
    const successfulActions: string[] = []

    console.log(`[Offline] Syncing ${actionsToSync.length} pending actions`)

    for (const action of actionsToSync) {
      try {
        await $fetch(action.endpoint, {
          method: action.method as any,
          body: action.data
        })

        successfulActions.push(action.id)
        console.log('[Offline] Action synced successfully:', action.type, action.endpoint)
      } catch (error) {
        console.error('[Offline] Failed to sync action:', action.type, action.endpoint, error)
        
        // Increment retry count
        action.retryCount++
        
        // Remove action if it has failed too many times
        if (action.retryCount >= 3) {
          successfulActions.push(action.id) // Remove from queue
          console.warn('[Offline] Action removed after 3 failed attempts:', action.type, action.endpoint)
        }
      }
    }

    // Remove successful actions from queue
    pendingActions.value = pendingActions.value.filter(action => !successfulActions.includes(action.id))
    persistData()

    syncInProgress.value = false
    console.log(`[Offline] Sync completed. ${successfulActions.length} actions processed.`)
  }

  // Cache data with TTL
  const cacheData = (key: string, data: any, ttlMinutes = 30) => {
    const cached: CachedData = {
      key,
      data,
      timestamp: Date.now(),
      ttl: ttlMinutes * 60 * 1000 // Convert to milliseconds
    }

    cachedData.value.set(key, cached)
    persistData()
  }

  // Get cached data
  const getCachedData = (key: string): any | null => {
    const cached = cachedData.value.get(key)
    
    if (!cached) return null
    
    // Check if cache has expired
    if (Date.now() - cached.timestamp > cached.ttl) {
      cachedData.value.delete(key)
      persistData()
      return null
    }

    return cached.data
  }

  // Clear specific cache
  const clearCache = (key?: string) => {
    if (key) {
      cachedData.value.delete(key)
    } else {
      cachedData.value.clear()
    }
    persistData()
  }

  // Cleanup expired cache entries
  const cleanupExpiredCache = () => {
    const now = Date.now()
    let cleaned = 0

    for (const [key, cached] of cachedData.value.entries()) {
      if (now - cached.timestamp > cached.ttl) {
        cachedData.value.delete(key)
        cleaned++
      }
    }

    if (cleaned > 0) {
      persistData()
      console.log(`[Offline] Cleaned up ${cleaned} expired cache entries`)
    }
  }

  // Offline-aware fetch wrapper
  const offlineFetch = async <T>(
    endpoint: string, 
    options: {
      method?: string
      body?: any
      cacheKey?: string
      cacheTTL?: number
      queueWhenOffline?: boolean
    } = {}
  ): Promise<T> => {
    const {
      method = 'GET',
      body,
      cacheKey,
      cacheTTL = 30,
      queueWhenOffline = true
    } = options

    // Try to get from cache first for GET requests
    if (method === 'GET' && cacheKey) {
      const cached = getCachedData(cacheKey)
      if (cached) {
        console.log('[Offline] Returning cached data for:', endpoint)
        return cached
      }
    }

    // If offline and it's a mutation, queue it
    if (!isOnline.value) {
      if (method !== 'GET' && queueWhenOffline) {
        queueAction({
          type: 'api_call',
          endpoint,
          method,
          data: body
        })
        
        // Return a placeholder response for mutations
        throw new Error('Offline: Action queued for later sync')
      }
      
      // For GET requests when offline, try cache or throw
      if (cacheKey) {
        const cached = getCachedData(cacheKey)
        if (cached) {
          return cached
        }
      }
      
      throw new Error('Offline: No cached data available')
    }

    // Online - make the request
    try {
      const response = await $fetch<T>(endpoint, {
        method: method as any,
        body
      })

      // Cache GET responses
      if (method === 'GET' && cacheKey) {
        cacheData(cacheKey, response, cacheTTL)
      }

      return response
    } catch (error) {
      // If request fails and we have cached data, return it
      if (method === 'GET' && cacheKey) {
        const cached = getCachedData(cacheKey)
        if (cached) {
          console.warn('[Offline] Request failed, returning cached data:', endpoint)
          return cached
        }
      }
      
      throw error
    }
  }

  // Computed properties
  const connectionStatus = computed(() => {
    if (isConnecting.value) return 'connecting'
    return isOnline.value ? 'online' : 'offline'
  })

  const hasPendingActions = computed(() => pendingActions.value.length > 0)

  const cacheSize = computed(() => cachedData.value.size)

  // Initialize on mount
  onMounted(() => {
    init()
  })

  // Cleanup on unmount
  onUnmounted(() => {
    if (process.client) {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      window.removeEventListener('connection-change', handleConnectionChange)
    }
  })

  return {
    // State
    isOnline: readonly(isOnline),
    isConnecting: readonly(isConnecting),
    syncInProgress: readonly(syncInProgress),
    connectionStatus,
    hasPendingActions,
    cacheSize,
    pendingActionsCount: computed(() => pendingActions.value.length),

    // Methods
    queueAction,
    syncPendingActions,
    cacheData,
    getCachedData,
    clearCache,
    offlineFetch,

    // Utilities
    init,
    persistData,
    cleanupExpiredCache
  }
}
