import React, { useState, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import PackageCard from './PackageCard';
import ServiceCard from './ServiceCard';
import LoadingSpinner from './LoadingSpinner';
import { FaPlus, FaServer, FaBox } from 'react-icons/fa';
import { haptics } from '../utils/haptics';
import { VPNPackage, MarzbanPanel, VPNSubscription, User } from '../types';
import Section from './layout/Section';
import { usePurchaseVpnPackageMutation } from '../hooks/vpnHooks';
import { useQueryClient } from '@tanstack/react-query';

// Sub-component for selecting a server when purchasing a package
interface ServerSelectorProps {
  panels: MarzbanPanel[];
  loading: boolean;
  selectedPanel: MarzbanPanel | null;
  onSelectPanel: (panel: MarzbanPanel) => void;
}

interface ExtendedMarzbanPanel extends MarzbanPanel {
  location?: string;
}

const ServerSelector: React.FC<ServerSelectorProps> = ({
  panels,
  loading,
  selectedPanel,
  onSelectPanel,
}) => {
  return (
    <div className="mb-6">
      <h3 className="text-lg font-medium text-white mb-3">Select Server Location</h3>
      {loading ? (
        <div className="flex justify-center py-4">
          <LoadingSpinner size="md" />
        </div>
      ) : (
        <div className="grid grid-cols-2 gap-3">
          {panels.map(panel => (
            <div
              key={panel.id}
              onClick={() => onSelectPanel(panel)}
              className={`p-3 rounded-lg border ${
                selectedPanel?.id === panel.id
                  ? 'border-purple-500 bg-purple-500/20'
                  : 'border-white/10 bg-white/5 hover:bg-white/10'
              } cursor-pointer transition-all`}
            >
              <div className="flex items-center gap-2">
                <FaServer className="text-purple-400" />
                <span className="text-white font-medium">{panel.name}</span>
              </div>
              <div className="text-xs text-white/60 mt-1">
                {(panel as ExtendedMarzbanPanel).location || 'Unknown location'}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// Sub-component for the package selection and purchase process
interface PackagePurchaseProps {
  packages: VPNPackage[];
  panels: MarzbanPanel[];
  loading: boolean;
  onPurchase: (packageId: number, panelId: number) => Promise<boolean>;
  onCancel: () => void;
}

const PackagePurchase: React.FC<PackagePurchaseProps> = ({
  packages,
  panels,
  loading,
  onPurchase,
  onCancel,
}) => {
  const [selectedPackage, setSelectedPackage] = useState<VPNPackage | null>(null);
  const [selectedPanel, setSelectedPanel] = useState<MarzbanPanel | null>(null);
  const [step, setStep] = useState<'package' | 'server' | 'confirm'>('package');
  const [processingPackageId, setProcessingPackageId] = useState<number | null>(null);

  const handleSelectPackage = useCallback((pkg: VPNPackage) => {
    haptics.impact('light');
    setSelectedPackage(pkg);
    setStep('server');
  }, []);

  const handleSelectPanel = useCallback((panel: MarzbanPanel) => {
    haptics.impact('light');
    setSelectedPanel(panel);
    setStep('confirm');
  }, []);

  const handlePurchase = useCallback(async () => {
    if (!selectedPackage || !selectedPanel) return;

    haptics.impact('medium');
    setProcessingPackageId(selectedPackage.id);

    try {
      const success = await onPurchase(selectedPackage.id, selectedPanel.id);
      if (success) {
        onCancel(); // Close the purchase flow on success
      }
    } finally {
      setProcessingPackageId(null);
    }
  }, [selectedPackage, selectedPanel, onPurchase, onCancel]);

  const handleBack = useCallback(() => {
    haptics.impact('light');
    if (step === 'server') {
      setStep('package');
    } else if (step === 'confirm') {
      setStep('server');
    }
  }, [step]);

  return (
    <div className="bg-gradient-to-br from-black/70 via-base-900/60 to-base-900/80 rounded-xl border border-purple-500/20 p-4">
      {/* Header with back button when needed */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          {step !== 'package' && (
            <button onClick={handleBack} className="p-2 rounded-full bg-white/5 hover:bg-white/10">
              <svg
                className="w-5 h-5 text-white"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
          )}
          <h2 className="text-xl font-bold text-white">
            {step === 'package'
              ? 'Choose Package'
              : step === 'server'
                ? 'Select Server'
                : 'Confirm Purchase'}
          </h2>
        </div>
        <button onClick={onCancel} className="p-2 rounded-full bg-white/5 hover:bg-white/10">
          <svg className="w-5 h-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </button>
      </div>

      {/* Step content */}
      <AnimatePresence mode="wait">
        {step === 'package' && (
          <motion.div
            key="package-step"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="space-y-4">
              {packages.map(pkg => (
                <PackageCard
                  key={pkg.id}
                  vpnPackage={pkg}
                  onSelect={() => handleSelectPackage(pkg)}
                  loading={processingPackageId === pkg.id}
                />
              ))}
            </div>
          </motion.div>
        )}

        {step === 'server' && selectedPackage && (
          <motion.div
            key="server-step"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="mb-4">
              <div className="p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                <h3 className="text-md font-medium text-white">Selected Package</h3>
                <div className="text-lg font-bold text-purple-300 mt-1">{selectedPackage.name}</div>
                <div className="text-sm text-white/60 mt-1">{selectedPackage.description}</div>
              </div>
            </div>

            <ServerSelector
              panels={panels}
              loading={loading}
              selectedPanel={selectedPanel}
              onSelectPanel={handleSelectPanel}
            />
          </motion.div>
        )}

        {step === 'confirm' && selectedPackage && selectedPanel && (
          <motion.div
            key="confirm-step"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="space-y-4 mb-6">
              <div className="p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                <h3 className="text-md font-medium text-white">Package</h3>
                <div className="text-lg font-bold text-purple-300 mt-1">{selectedPackage.name}</div>
                <div className="text-sm text-white/60 mt-1">{selectedPackage.description}</div>
              </div>

              <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg">
                <h3 className="text-md font-medium text-white">Server</h3>
                <div className="text-lg font-bold text-blue-300 mt-1">{selectedPanel.name}</div>
                <div className="text-sm text-white/60 mt-1">
                  {(selectedPanel as ExtendedMarzbanPanel).location || 'Unknown location'}
                </div>
              </div>

              <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg">
                <h3 className="text-md font-medium text-white">Total Price</h3>
                <div className="text-lg font-bold text-green-300 mt-1">
                  ${selectedPackage.price.toFixed(2)}
                </div>
              </div>
            </div>

            <button
              onClick={handlePurchase}
              disabled={processingPackageId !== null}
              className="w-full py-3 bg-gradient-to-r from-purple-500 to-blue-500 text-white font-medium rounded-lg disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {processingPackageId !== null ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span>Processing...</span>
                </>
              ) : (
                <span>Complete Purchase</span>
              )}
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

interface PremiumTabProps {
  vpnSubscriptions?: VPNSubscription[];
  vpnPackages?: VPNPackage[];
  vpnPanels?: MarzbanPanel[];
  isLoadingPremium: boolean;
  user?: User;
  onTabChange?: (tabId: string) => void;
}

const PremiumTab: React.FC<PremiumTabProps> = ({
  vpnSubscriptions = [],
  vpnPackages = [],
  vpnPanels = [],
  isLoadingPremium,
  user,
  onTabChange,
}) => {
  // Select individual values and actions from the store
  const [showPurchaseFlow, setShowPurchaseFlow] = useState(false);

  const handleOpenPurchaseFlow = useCallback(() => {
    haptics.impact('light');
    setShowPurchaseFlow(true);
  }, []);

  const handleClosePurchaseFlow = useCallback(() => {
    haptics.impact('light');
    setShowPurchaseFlow(false);
  }, []);

  const { mutateAsync: purchaseVpnPackage } = usePurchaseVpnPackageMutation();
  const queryClient = useQueryClient();

  const hasSubscriptions = vpnSubscriptions.length > 0;

  const handlePurchase = useCallback(
    async (packageId: number, panelId: number) => {
      const response = await purchaseVpnPackage({ packageId, panelId });
      const success = !!response && typeof response === 'object' && 'data' in response;
      if (success) {
        queryClient.invalidateQueries({ queryKey: ['vpnSubscriptions'] });
      }
      return success;
    },
    [purchaseVpnPackage, queryClient]
  );

  // Define handleRefresh specifically for ServiceCard prop
  const handleServiceRefresh = useCallback(() => {
    queryClient.invalidateQueries({ queryKey: ['vpnSubscriptions'] });
  }, [queryClient]);

  return (
    <div>
      <Section>
        {/* Header with action buttons */}
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold text-white">Premium Services</h2>
          {/* Button to add service now always visible if purchase flow can be opened */}
          <button
            onClick={handleOpenPurchaseFlow}
            className="px-3 py-1.5 bg-gradient-to-r from-purple-500/20 to-blue-500/20 
                    rounded-lg text-white hover:from-purple-500/30 hover:to-blue-500/30 
                    transition-colors text-sm font-medium flex items-center gap-1"
          >
            <FaPlus className="text-xs" />
            <span>Add Service</span>
          </button>
        </div>
      </Section>

      {/* Loading state - Show only if relevant data is loading and purchase flow is closed */}
      {isLoadingPremium && !showPurchaseFlow && (
        <Section>
          <div className="flex justify-center items-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        </Section>
      )}

      {/* List of current subscriptions - Conditionally render based on loading state */}
      {!isLoadingPremium && hasSubscriptions && !showPurchaseFlow && (
        <Section title="My Services">
          <div className="space-y-4">
            {vpnSubscriptions.map((subscription: VPNSubscription) => (
              <ServiceCard
                key={subscription.id}
                subscription={subscription}
                onRefresh={handleServiceRefresh} // Pass the specific refresh handler
              />
            ))}
          </div>
        </Section>
      )}

      {/* Empty state - Shown when not loading, no subscriptions, and purchase flow closed */}
      {!isLoadingPremium && !hasSubscriptions && !showPurchaseFlow && (
        <Section>
          <div className="text-center py-16">
            <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-500/20 mb-4">
              <FaBox className="text-3xl text-purple-400" />
            </div>
            <div className="text-white/60 max-w-md mx-auto">
              <h3 className="text-xl font-bold text-white mb-2">No Premium Services Yet</h3>
              <p className="mb-8">
                Unlock premium VPN access to secure your connection and browse privately from
                anywhere.
              </p>
            </div>
            {/* Button to open purchase flow directly */}
            <button
              onClick={handleOpenPurchaseFlow}
              className="px-6 py-2.5 bg-gradient-to-r from-purple-500 to-blue-500 
                       rounded-lg text-white font-medium hover:from-purple-600 
                       hover:to-blue-600 transition-colors"
            >
              Purchase Your First Service
            </button>
          </div>
        </Section>
      )}

      {/* Package purchase flow modal */}
      <AnimatePresence>
        {showPurchaseFlow && (
          <motion.div
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="w-full max-w-xl"
              initial={{ scale: 0.9, y: 20 }}
              animate={{ scale: 1, y: 0 }}
              exit={{ scale: 0.9, y: 20 }}
              transition={{ type: 'spring', damping: 25 }}
            >
              <PackagePurchase
                packages={vpnPackages} // Pass data from props
                panels={vpnPanels} // Pass data from props
                loading={isLoadingPremium} // Pass loading state from props
                onPurchase={handlePurchase} // Pass purchase handler
                onCancel={handleClosePurchaseFlow} // Pass close handler
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default PremiumTab;
