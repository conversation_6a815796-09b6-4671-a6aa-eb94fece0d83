

🔴 1. ALL SERVICES (Combined logs):
docker-compose logs -f

🟢 2. <PERSON>CKEND (FastAPI - Port 8000):
docker-compose logs -f backend

🔵 3. FRONTEND (Astro - Port 3005):
docker-compose logs -f frontend

🟠 4. NGINX (Reverse Proxy - Port 80/443):
docker-compose logs -f nginx

🟡 5. REDIS (Cache/Sessions - Port 6379):
docker-compose logs -f redis

🟣 6. POSTGRESQL (Database - Port 5432):
docker-compose logs -f postgres
AtlasX1#
1=== ADVANCED LOG COMMANDS ===

📊 7. RECENT LOGS (Last 50 lines):
docker-compose logs --tail=50

🔍 8. FILTERED LOGS (Search for specific terms):
docker-compose logs | grep ERROR
docker-compose logs | grep ApiClient
docker-compose logs | grep "session verification"

⏰ 9. TIMESTAMPED LOGS:
docker-compose logs -f -t

🎯 10. SPECIFIC SERVICE + FILTER:
docker-compose logs -f backend | grep -E "(ERROR|WARNING|auth)"
docker-compose logs -f frontend | grep -E "(ApiClient|TEST DOMAIN|error)"
docker-compose logs -f nginx | grep -E "(error|404|500|502)"

💾 11. SAVE LOGS TO FILE:
docker-compose logs > /tmp/all-services.log
docker-compose logs backend > /tmp/backend.log
AtlasX1#

