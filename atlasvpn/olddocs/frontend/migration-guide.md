# Migration Guide: Moving from Hooks to Zustand Stores

This guide explains how to refactor your app to use the new Zustand stores instead of custom hooks and context providers.

## Overview of Changes

1. Replace `useAuthContext` with direct `useAuthStore` calls
2. Replace `useTask` with the new `useTaskStore`
3. Replace `useVpnServices` with direct `useVpnStore` calls
4. Delete redundant files after migration

## Files to Delete After Migration

Once the migration is complete, these files can be safely deleted:

- `frontend/src/hooks/useAuth.ts` (replaced by authStore)
- `frontend/src/hooks/useVpnServices.ts` (replaced by vpnStore)
- `frontend/src/hooks/useTask.ts` (replaced by taskStore)
- `frontend/src/contexts/AuthContext.tsx` (no longer needed)

## Step 1: Refactor UserDashboard.tsx

Here's how to update your UserDashboard component:

```typescript
// Before:
import { useAuthContext } from '../contexts/AuthContext';
import { useTask } from '../hooks/useTask';
// ...other imports

const UserDashboard: React.FC = () => {
  const { user, refreshAuth } = useAuthContext();
  // ...
  const { tasks, loading: tasksLoading, loadTasks } = useTask();
  // ...
}

// After:
import { useAuthStore } from '../stores/authStore';
import { useTaskStore } from '../stores/taskStore';
import { useVpnStore } from '../stores/vpnStore';
import { useDashboardStore } from '../stores/dashboardStore';
// ...other imports

const UserDashboard: React.FC = () => {
  // Auth state
  const user = useAuthStore((state) => state.user);
  const refreshAuth = useAuthStore((state) => state.refreshAuth);
  
  // Task state
  const tasks = useTaskStore((state) => state.tasks);
  const tasksLoading = useTaskStore((state) => state.loading);
  const loadTasks = useTaskStore((state) => state.loadTasks);
  
  // VPN state
  const subscriptions = useVpnStore((state) => state.subscriptions); 
  const packages = useVpnStore((state) => state.packages);
  const availablePanels = useVpnStore((state) => state.availablePanels);
  const fetchSubscriptions = useVpnStore((state) => state.fetchSubscriptions);
  const fetchPackages = useVpnStore((state) => state.fetchPackages);
  const fetchPanels = useVpnStore((state) => state.fetchPanels);
  
  // Dashboard state
  const dashboardStats = useDashboardStore((state) => state.stats);
  const fetchStats = useDashboardStore((state) => state.fetchStats);
  
  // ...rest of the component
}
```

## Step 2: Replace fetchData and fetchTabData Functions

```typescript
// Before:
const fetchData = useCallback(async (target: RefreshTarget, silent: boolean = false) => {
  // ... existing implementation with API calls
}, [loadTasks]);

// After:
const fetchData = useCallback(async (target: RefreshTarget, silent: boolean = false) => {
  // Skip if fetch already in progress
  if (fetchInProgressRef.current[target]) {
    return { success: false };
  }
  
  // Throttle fetch operations
  const now = Date.now();
  if (!silent && now - lastFetchTimeRef.current[target] < 5000) {
    return { success: true }; // Return success but don't actually fetch
  }
  
  // Mark fetch as in progress
  fetchInProgressRef.current[target] = true;
  
  if (!silent) {
    dispatch({ type: 'SET_LOADING', payload: true });
  }
  
  try {
    switch (target) {
      case 'subscriptions':
        await fetchSubscriptions(silent);
        break;
      case 'stats':
        await fetchStats(silent);
        break;
      case 'packages':
        await fetchPackages(silent);
        break;
      case 'panels':
        await fetchPanels(silent);
        break;
      case 'tasks':
        await loadTasks();
        break;
      // ... other cases
    }
    
    // Update last fetch time
    lastFetchTimeRef.current[target] = now;
    return { success: true };
  } catch (error) {
    console.error(`Error fetching ${target}:`, error);
    return { success: false, error };
  } finally {
    // Mark fetch as completed
    fetchInProgressRef.current[target] = false;
    
    if (!silent) {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  }
}, [fetchSubscriptions, fetchStats, fetchPackages, fetchPanels, loadTasks]);
```

## Step 3: Replace Task-Related Functions

```typescript
// Before:
const [selectedTask, setSelectedTask] = useState<Task | null>(null);
const { tasks, loading: tasksLoading, loadTasks } = useTask();

const handleTaskSelect = useCallback((task: Task) => {
  setSelectedTask(task);
  haptics.impact('light');
}, []);

const handleTaskUpdated = useCallback(() => {
  fetchTabData('earn');
  setSelectedTask(null);
}, [fetchTabData]);

// After:
const [selectedTask, setSelectedTask] = useState<Task | null>(null);
const tasks = useTaskStore((state) => state.tasks);
const tasksLoading = useTaskStore((state) => state.loading);
const loadTasks = useTaskStore((state) => state.loadTasks);

const handleTaskSelect = useCallback((task: Task) => {
  setSelectedTask(task);
  haptics.impact('light');
}, []);

const handleTaskUpdated = useCallback(() => {
  loadTasks(); // Direct call to Zustand store action
  setSelectedTask(null);
}, [loadTasks]);
```

## Step 4: Replace Service/Package Functions

```typescript
// Before:
const handleServiceRefresh = useCallback(async () => {
  await fetchTabData('services', false);
}, [fetchTabData]);

// After:
const handleServiceRefresh = useCallback(async () => {
  await fetchSubscriptions(false);
}, [fetchSubscriptions]);

// Before:
const handleNewServiceSuccess = useCallback(async () => {
  handleCloseForm();
  await fetchTabData('services');
  toast.success('Service created successfully');
}, [handleCloseForm, fetchTabData]);

// After:
const handleNewServiceSuccess = useCallback(async () => {
  handleCloseForm();
  await fetchSubscriptions();
  toast.success('Service created successfully');
}, [handleCloseForm, fetchSubscriptions]);
```

## Step 5: Initialize Stores in App Root

Add these lines to your app's entry point (e.g., `App.tsx` or `main.tsx`):

```typescript
import { initializeAuth } from './stores/authStore';
import { initializeDashboardStore } from './stores/dashboardStore';
import { initializeVpnStore } from './stores/vpnStore';

// Initialize stores
initializeAuth();
initializeDashboardStore();
initializeVpnStore();
```

## Performance Optimization

For better performance, you can use the Zustand selector pattern to prevent unnecessary rerenders:

```typescript
import { shallow } from 'zustand/shallow';

// This will only rerender when any of the selected values change
const { subscriptions, loading } = useVpnStore(
  (state) => ({ 
    subscriptions: state.subscriptions,
    loading: state.loading
  }),
  shallow
);
```

## Testing

After making these changes, test each feature thoroughly to ensure:

1. Authentication still works properly
2. Tasks can be loaded, started, verified, and claimed
3. VPN services can be fetched and managed
4. Dashboard stats are displayed correctly

## Rollback Plan

If you encounter issues, you can temporarily revert to the original implementation by:

1. Keeping both implementations side by side
2. Using a feature flag to switch between them
3. Gradually migrating one component at a time 