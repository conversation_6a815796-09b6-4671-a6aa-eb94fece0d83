/**
 * @file PremiumTabContent.tsx
 * @description Premium tab content component - VPN services and subscriptions
 * Placeholder for the full premium tab implementation
 */

import React from 'react';

const PremiumTabContent: React.FC = () => {
  return (
    <div className="flex-1 overflow-y-auto p-4">
      <div className="max-w-md mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-2">Premium VPN</h1>
          <p className="text-white/70">Secure, fast, and private internet access</p>
        </div>

        {/* Connection Status */}
        <div className="bg-gradient-to-br from-red-500/20 to-orange-600/20 backdrop-blur-sm rounded-xl p-6 border border-red-500/30">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <div className="w-8 h-8 bg-red-500 rounded-full"></div>
            </div>
            <div className="text-white text-lg font-semibold mb-2">Disconnected</div>
            <div className="text-white/70 text-sm mb-4">Not protected</div>
            <button className="bg-accent-500 hover:bg-accent-600 text-white px-6 py-2 rounded-lg font-medium transition-colors">
              Connect VPN
            </button>
          </div>
        </div>

        {/* Subscription Plans */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-white">Subscription Plans</h2>
          <div className="space-y-3">
            {/* Free Plan */}
            <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-white font-medium">Free Plan</div>
                  <div className="text-white/60 text-sm">Limited servers • 1GB/month</div>
                </div>
                <div className="text-accent-400 font-bold">Current</div>
              </div>
            </div>

            {/* Premium Plan */}
            <div className="bg-gradient-to-br from-accent-500/20 to-purple-600/20 backdrop-blur-sm rounded-xl p-4 border border-accent-500/30">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <div className="text-white font-medium">Premium Plan</div>
                  <div className="text-white/70 text-sm">All servers • Unlimited data</div>
                </div>
                <div className="text-white font-bold">$9.99/mo</div>
              </div>
              <button className="w-full bg-accent-500 hover:bg-accent-600 text-white py-2 rounded-lg text-sm font-medium transition-colors">
                Upgrade Now
              </button>
            </div>
          </div>
        </div>

        {/* Server Locations */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-white">Server Locations</h2>
          <div className="space-y-2">
            <div className="flex items-center justify-between bg-white/5 backdrop-blur-sm rounded-xl p-3 border border-white/10">
              <div className="flex items-center space-x-3">
                <div className="text-lg">🇺🇸</div>
                <div>
                  <div className="text-white font-medium">United States</div>
                  <div className="text-green-400 text-sm">Fast • 15ms</div>
                </div>
              </div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
            <div className="flex items-center justify-between bg-white/5 backdrop-blur-sm rounded-xl p-3 border border-white/10">
              <div className="flex items-center space-x-3">
                <div className="text-lg">🇬🇧</div>
                <div>
                  <div className="text-white font-medium">United Kingdom</div>
                  <div className="text-yellow-400 text-sm">Medium • 45ms</div>
                </div>
              </div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            </div>
            <div className="flex items-center justify-between bg-white/5 backdrop-blur-sm rounded-xl p-3 border border-white/10 opacity-50">
              <div className="flex items-center space-x-3">
                <div className="text-lg">🇯🇵</div>
                <div>
                  <div className="text-white font-medium">Japan</div>
                  <div className="text-white/60 text-sm">Premium only</div>
                </div>
              </div>
              <div className="text-accent-400 text-sm">🔒</div>
            </div>
          </div>
        </div>

        {/* Features */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-white">Features</h2>
          <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="text-green-400">✓</div>
                <div className="text-white">Military-grade encryption</div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="text-green-400">✓</div>
                <div className="text-white">No-logs policy</div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="text-green-400">✓</div>
                <div className="text-white">Kill switch protection</div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="text-white/40">✓</div>
                <div className="text-white/60">Unlimited bandwidth (Premium)</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PremiumTabContent;
