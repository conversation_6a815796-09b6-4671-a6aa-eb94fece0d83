"""User-related schemas"""
from pydantic import Field, field_validator, ConfigDict
from typing import Optional, List, Any
from datetime import datetime
from .base import BaseModelConfig, RoleEnum

class UserBase(BaseModelConfig):
    """Base user schema with common fields"""
    username: str
    telegram_id: Optional[int] = None
    role: Optional[RoleEnum] = None
    wallet_balance: Optional[float] = 0.0
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    telegram_photo_url: Optional[str] = None
    discount_percent: Optional[float] = 0.0

class UserOut(BaseModelConfig):
    """User output schema with all related data"""
    id: int
    username: str
    telegram_id: Optional[int] = None
    role: str  # Changed from Role enum to str to fix serialization warnings
    wallet_balance: float
    created_at: datetime
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    is_active: bool
    telegram_photo_url: Optional[str] = None
    discount_percent: float
    referral_code: str
    referred_by: Optional[int] = None
    total_referrals: Optional[int] = None
    active_referrals: Optional[int] = None
    referral_earnings: Optional[float] = None
    total_passive_hourly_income: float = 0.0
    
    # Related data - using Any to avoid forward reference issues
    vpn_subscriptions: List[Any] = []
    task_completions: List[Any] = []
    daily_streak: Optional[Any] = None
    cards: List[Any] = []
    
    model_config = ConfigDict(from_attributes=True)

class UserCreate(BaseModelConfig):
    """Schema for creating a new user"""
    username: str
    email: str
    password: str
    first_name: str
    last_name: str
    role: str = 'user'
    is_active: bool = True
    discount_percent: float = 0.0
    wallet_balance: float = 0.0
    wallet_charge: float = 0.0
    telegram_id: Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True)

    @field_validator('username')
    @classmethod
    def validate_username(cls, v):
        if not v or len(v) < 3:
            raise ValueError('Username must be at least 3 characters long')
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username can only contain letters, numbers, hyphens and underscores')
        return v.lower()

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        if not v or '@' not in v:
            raise ValueError('Invalid email format')
        return v.lower()

    @field_validator('first_name', 'last_name')
    @classmethod
    def validate_names(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('Names must be at least 2 characters long')
        return v.strip().title()

    @field_validator('discount_percent')
    @classmethod
    def validate_discount(cls, v):
        if v < 0 or v > 100:
            raise ValueError('Discount must be between 0 and 100 percent')
        return v

    @field_validator('wallet_balance')
    @classmethod
    def validate_balance(cls, v):
        if v < 0:
            raise ValueError('Wallet balance cannot be negative')
        return v

    @field_validator('role')
    @classmethod
    def validate_role(cls, v):
        valid_roles = ['admin', 'reseller', 'user']
        if v not in valid_roles:
            raise ValueError(f'Role must be one of: {", ".join(valid_roles)}')
        return v

    @field_validator('wallet_charge')
    @classmethod
    def validate_charge(cls, v):
        if v < 0:
            raise ValueError('Wallet charge amount cannot be negative')
        return v

    @field_validator('telegram_id')
    @classmethod
    def validate_telegram_id(cls, v):
        if v and not v.isdigit():
            raise ValueError('Telegram ID must be a number')
        return v

class UserUpdate(BaseModelConfig):
    """Schema for user self-updates"""
    current_password: Optional[str] = None
    new_password: Optional[str] = None

class AdminUserUpdate(BaseModelConfig):
    """Schema for admin user updates"""
    email: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    wallet_balance: Optional[float] = None
    wallet_charge: Optional[float] = None
    is_active: Optional[bool] = None
    role: Optional[str] = None
    discount_percent: Optional[float] = None
    telegram_id: Optional[int] = None

    model_config = ConfigDict(from_attributes=True)

    @field_validator('email')
    @classmethod
    def validate_email(cls, v):
        if v is not None:
            if not v or '@' not in v:
                raise ValueError('Invalid email format')
            return v.lower()
        return v

    @field_validator('first_name', 'last_name')
    @classmethod
    def validate_names(cls, v):
        if v is not None:
            if len(v.strip()) < 2:
                raise ValueError('Names must be at least 2 characters long')
            return v.strip().title()
        return v

    @field_validator('discount_percent')
    @classmethod
    def validate_discount(cls, v):
        if v is not None and (v < 0 or v > 100):
            raise ValueError('Discount must be between 0 and 100 percent')
        return v

    @field_validator('wallet_balance')
    @classmethod
    def validate_balance(cls, v):
        if v is not None and v < 0:
            raise ValueError('Wallet balance cannot be negative')
        return v

    @field_validator('role')
    @classmethod
    def validate_role(cls, v):
        if v is not None:
            valid_roles = ['admin', 'reseller', 'user']
            if v not in valid_roles:
                raise ValueError(f'Role must be one of: {", ".join(valid_roles)}')
        return v

    @field_validator('wallet_charge')
    @classmethod
    def validate_charge(cls, v):
        if v is not None and v < 0:
            raise ValueError('Wallet charge amount cannot be negative')
        return v

    @field_validator('telegram_id')
    @classmethod
    def validate_telegram_id(cls, v):
        if v is not None and v < 0:
            raise ValueError('Telegram ID cannot be negative')
        return v

class UserProfile(BaseModelConfig):
    """User profile schema"""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    telegram_photo_url: Optional[str] = None

class UserProfileUpdate(UserProfile):
    """User profile update schema"""
    pass

class ResellerProfileUpdate(UserProfile):
    """Reseller profile update schema"""
    discount_percent: Optional[float] = None

class UserVPNUpdate(BaseModelConfig):
    """VPN-specific user updates"""
    marzban_username: Optional[str] = None
    marzban_subscription_url: Optional[str] = None

class ReferralInfo(BaseModelConfig):
    """Referral information schema"""
    referral_code: str
    total_referrals: int
    active_referrals: int
    total_earnings: float
    referral_link: str

class ReferredUser(BaseModelConfig):
    """Referred user information schema"""
    username: str
    joined_at: datetime
    is_active: bool
    total_spent: float
    commission_earned: float
    telegram_photo_url: Optional[str] = None 