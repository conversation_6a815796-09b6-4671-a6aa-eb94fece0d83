/**
 * @file api/hooks/auth.ts
 * @description Authentication-related React Query hooks
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type {
  User,
  VerifySessionData,
  TelegramLoginParams,
  TelegramLoginResult,
  AuthError,
  QueryOptions,
  MutationOptions
} from '../types';
import { authService } from '../services';

// Query Keys
export const AUTH_QUERY_KEYS = {
  CURRENT_USER: ['auth', 'currentUser'],
  VERIFY_SESSION: ['auth', 'verifySession']
} as const;

// Default query options
const DEFAULT_QUERY_OPTIONS: QueryOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  retry: false // Disable retries to prevent authentication loops
};

// Authentication Hooks
export function useCurrentUserQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: AUTH_QUERY_KEYS.CURRENT_USER,
    queryFn: async (): Promise<VerifySessionData> => {
      try {
        return await authService.verifySession();
      } catch (error: any) {
        if (error.response?.status === 401) {
          return { status: 'unauthenticated', data: undefined };
        }
        throw error;
      }
    },
    select: (verifySessionData: VerifySessionData): User | undefined => {
      if (verifySessionData?.status === 'authenticated' && verifySessionData.data) {
        return verifySessionData.data as User;
      }
      return undefined;
    },
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function useVerifySessionQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: AUTH_QUERY_KEYS.VERIFY_SESSION,
    queryFn: () => authService.verifySession(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function useTelegramLoginMutation(options?: MutationOptions) {
  const queryClient = useQueryClient();
  
  return useMutation<TelegramLoginResult, AuthError, TelegramLoginParams>({
    mutationFn: async (variables: TelegramLoginParams): Promise<TelegramLoginResult> => {
      try {
        const response = await authService.telegramLogin(variables);
        return {
          isNewUser: false,
          user: response.data || response
        };
      } catch (error: any) {
        if (error.response?.status === 404) {
          return {
            isNewUser: true,
            telegram_data: error.response?.data?.telegram_data || null
          };
        }
        throw error;
      }
    },
    onSuccess: (data: TelegramLoginResult) => {
      if (data?.user) {
        const verifyData: VerifySessionData = {
          status: 'authenticated',
          data: data.user,
        };
        queryClient.setQueryData(AUTH_QUERY_KEYS.CURRENT_USER, verifyData);
        sessionStorage.removeItem('telegram_setup_data');
      } else if (data?.isNewUser && data.telegram_data) {
        sessionStorage.setItem(
          'telegram_setup_data',
          JSON.stringify({ 
            telegramData: data.telegram_data, 
            timestamp: Date.now() 
          })
        );
      }
    },
    onError: () => {
      queryClient.setQueryData(AUTH_QUERY_KEYS.CURRENT_USER, { status: 'unauthenticated', data: undefined });
    },
    ...options
  });
}

export function useLogoutMutation(options?: MutationOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => authService.logout(),
    onSuccess: () => {
      queryClient.clear();
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    },
    ...options
  });
}