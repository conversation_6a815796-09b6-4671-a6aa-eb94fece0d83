# database.py

from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, declarative_base, Session
from sqlalchemy.engine import Engine
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import SQLAlchemyError
from typing import AsyncGenerator, Generator, Optional
import logging
import os
from dotenv import load_dotenv
import sqlite3
from contextlib import contextmanager
import time
import threading
import gc
from fastapi import Request, HTTPException

load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Define Base class for models using SQLAlchemy 2.0 style
Base = declarative_base()

# Database configuration with optimized defaults
# Use DATABASE_URL from environment, fallback to SQLite for local development
SQLALCHEMY_DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./users.db")
POOL_SIZE = int(os.getenv("DB_POOL_SIZE", "10"))
MAX_OVERFLOW = int(os.getenv("DB_MAX_OVERFLOW", "20"))
POOL_TIMEOUT = int(os.getenv("DB_POOL_TIMEOUT", "30"))
POOL_RECYCLE = int(os.getenv("DB_POOL_RECYCLE", "3600"))

# Determine database type
IS_POSTGRESQL = SQLALCHEMY_DATABASE_URL.startswith('postgresql')
IS_SQLITE = SQLALCHEMY_DATABASE_URL.startswith('sqlite')

# Track active connections for debugging
_active_connections = {}
_connection_lock = threading.Lock()

def get_sqlite_connection():
    """Get a direct SQLite connection for maintenance operations"""
    db_path = SQLALCHEMY_DATABASE_URL.replace("sqlite:///", "")
    return sqlite3.connect(db_path)

def checkpoint_database():
    """Force a WAL checkpoint and optimize the database (SQLite only)"""
    if not IS_SQLITE:
        logger.info("Database checkpoint skipped - not using SQLite")
        return
        
    try:
        with get_sqlite_connection() as conn:
            conn.execute("PRAGMA wal_checkpoint(TRUNCATE)")
            conn.execute("PRAGMA optimize")
        logger.info("SQLite database checkpoint completed")
    except Exception as e:
        logger.error(f"Error during database checkpoint: {e}")

# Create engine with database-specific configuration
if IS_POSTGRESQL:
    # PostgreSQL configuration
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        poolclass=QueuePool,
        pool_size=POOL_SIZE,
        max_overflow=MAX_OVERFLOW,
        pool_timeout=POOL_TIMEOUT,
        pool_recycle=POOL_RECYCLE,
        echo=os.getenv("DEBUG", "false").lower() == "true"
    )
    
    # Async engine for PostgreSQL
    async_database_url = SQLALCHEMY_DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    async_engine = create_async_engine(
        async_database_url,
        echo=False,
        pool_size=POOL_SIZE,
        max_overflow=MAX_OVERFLOW,
        pool_timeout=POOL_TIMEOUT,
        pool_recycle=POOL_RECYCLE
    )
else:
    # SQLite configuration (fallback for local development)
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        connect_args={
            "check_same_thread": False,
            "timeout": 30
        },
        # Enable Write-Ahead Logging for SQLite
        execution_options={"isolation_level": "AUTOCOMMIT"},
        poolclass=QueuePool,
        pool_size=POOL_SIZE,
        max_overflow=MAX_OVERFLOW,
        pool_timeout=POOL_TIMEOUT,
        pool_recycle=POOL_RECYCLE
    )
    
    # Async engine for SQLite
    async_engine = create_async_engine(
        SQLALCHEMY_DATABASE_URL.replace("sqlite:///", "sqlite+aiosqlite:///"),
        echo=False,
        connect_args={"check_same_thread": False}
    )

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Configure SQLite for WAL mode (only for SQLite databases)
if IS_SQLITE:
    @event.listens_for(Engine, "connect")
    def set_sqlite_pragma(dbapi_connection, connection_record):
        cursor = dbapi_connection.cursor()
        # Enable WAL mode
        cursor.execute("PRAGMA journal_mode=WAL")
        # Set busy timeout
        cursor.execute("PRAGMA busy_timeout=5000")
        # Set synchronous mode
        cursor.execute("PRAGMA synchronous=NORMAL")
        cursor.close()

# Create session factories with SQLAlchemy 2.0 style
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    autocommit=False,
    autoflush=False,
    expire_on_commit=False,
    class_=AsyncSession
)

# Event listeners for connection pool management
@event.listens_for(engine, "connect")
def connect(dbapi_connection, connection_record):
    """Set up connection-level security settings and track connections"""
    conn_id = id(dbapi_connection)
    with _connection_lock:
        _active_connections[conn_id] = {
            "timestamp": time.time(),
            "thread": threading.current_thread().name,
            "connection": dbapi_connection
        }
    
    if IS_SQLITE:
        cursor = dbapi_connection.cursor()
        # Enable foreign key constraints and optimize SQLite
        cursor.executescript("""
            PRAGMA foreign_keys=ON;
            PRAGMA journal_mode=WAL;
            PRAGMA synchronous=NORMAL;
            PRAGMA temp_store=MEMORY;
            PRAGMA cache_size=-2000;
            PRAGMA busy_timeout=5000;
            PRAGMA mmap_size=268435456;
        """)
        cursor.close()
    elif IS_POSTGRESQL:
        # PostgreSQL-specific connection settings
        with dbapi_connection.cursor() as cursor:
            cursor.execute("SET timezone = 'UTC'")
            cursor.execute("SET statement_timeout = '30s'")
            dbapi_connection.commit()

@event.listens_for(engine, "close")
def close(dbapi_connection, connection_record):
    """Track connection close events"""
    conn_id = id(dbapi_connection)
    with _connection_lock:
        if conn_id in _active_connections:
            del _active_connections[conn_id]

@event.listens_for(engine, "checkout")
def checkout(dbapi_connection, connection_record, connection_proxy):
    """Verify connection is active before using"""
    try:
        cursor = dbapi_connection.cursor()
        cursor.execute("SELECT 1")
        cursor.close()
    except Exception as e:
        logger.error(f"Connection verification failed: {str(e)}")
        raise SQLAlchemyError("Database connection is invalid")

# Add event listener for checkpointing after commits
@event.listens_for(engine, "after_cursor_execute")
def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Periodically checkpoint the database after writes"""
    if statement.lower().startswith(("insert", "update", "delete")):
        try:
            conn.execute("PRAGMA wal_checkpoint(PASSIVE)")
        except Exception as e:
            logger.warning(f"Passive checkpoint failed: {e}")

@contextmanager
def get_db_context() -> Generator[Session, None, None]:
    """Get database session as a context manager"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_db() -> Generator[Session, None, None]:
    """Get database session as a FastAPI dependency"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def get_init_db() -> Optional[Session]:
    """Get database session for initialization"""
    try:
        db = SessionLocal()
        return db
    except Exception as e:
        logger.error(f"Error getting init db session: {str(e)}")
        return None

async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    """Get async database session"""
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception as e:
            logger.error(f"Async database session error: {str(e)}")
            await session.rollback()
            raise
        finally:
            # Ensure session is closed even if an exception occurs
            try:
                await session.close()
                logger.debug("Async session explicitly closed")
            except Exception as e:
                logger.error(f"Error closing async session: {str(e)}")

def init_db() -> None:
    """Initialize database and create all tables"""
    try:
        Base.metadata.create_all(bind=engine)
        if IS_SQLITE:
            checkpoint_database()
        elif IS_POSTGRESQL:
            logger.info("PostgreSQL database tables created successfully")
        logger.info(f"Database initialized successfully ({'PostgreSQL' if IS_POSTGRESQL else 'SQLite'})")
    except Exception as e:
        logger.error(f"Database initialization failed: {str(e)}")
        raise

def close_db() -> None:
    """Close database connections and perform final checkpoint"""
    try:
        if IS_SQLITE:
            checkpoint_database()
        # SessionLocal is a factory, not a scoped_session, so we don't call remove()
        # Just dispose of the engine
        engine.dispose()
        logger.info("Database connections closed")
    except Exception as e:
        logger.error(f"Error closing database connections: {str(e)}")

def cleanup_db() -> None:
    """Clean up database connections"""
    engine.dispose()
    async_engine.dispose()

def get_connection_stats() -> dict:
    """Get statistics about active database connections"""
    with _connection_lock:
        active_count = len(_active_connections)
        oldest = None
        if active_count > 0:
            oldest = min([conn["timestamp"] for conn in _active_connections.values()])
            oldest = time.time() - oldest if oldest else None
    
    # Get SQLAlchemy pool stats
    pool_status = {
        "pool_size": engine.pool.size(),
        "checkedin": engine.pool.checkedin(),
        "checkedout": engine.pool.checkedout(),
        "overflow": engine.pool.overflow(),
        "active_count": active_count,
        "oldest_connection_age": oldest
    }
    
    return pool_status

def force_close_connections() -> int:
    """Force close any connections that might be leaking (emergency use only)"""
    closed_count = 0
    with _connection_lock:
        for conn_id, conn_info in list(_active_connections.items()):
            try:
                # Only force close connections older than 10 minutes
                if time.time() - conn_info["timestamp"] > 600:
                    conn = conn_info["connection"]
                    conn.close()
                    del _active_connections[conn_id]
                    closed_count += 1
            except:
                pass
    
    # Force garbage collection to help remove any lingering references
    gc.collect()
    
    # Return how many connections were force-closed
    return closed_count

# New function for Redis dependency
async def get_redis_dependency(context_obj = None):
    """
    FastAPI dependency function to get Redis client from app state.
    This dependency retrieves the initialized Redis client wrapper from app.state
    and returns the actual Redis connection for use in route handlers or WebSocket endpoints.
    
    Works with both HTTP requests and WebSocket connections.
    
    Args:
        context_obj: The request, WebSocket, or other context object; uses current request context by default
    """
    app_obj = None
    context_type = "unknown"
    logger.info(f"get_redis_dependency invoked. Type of context object: {type(context_obj).__name__ if context_obj else 'None (using default)'}")
    
    # Special case: If a string parameter is passed instead of Request/WebSocket, 
    # try to access app from current_app
    if isinstance(context_obj, str):
        logger.info(f"String value passed to get_redis_dependency: '{context_obj}'. "
                  f"This indicates a parameter name conflict. Attempting to get app from current context.")
        
        # Import here to avoid circular imports
        from fastapi import FastAPI
        from starlette.applications import get_app
        
        try:
            # Try to get the global app instance
            app_obj = get_app()
            context_type = "global app instance"
            logger.info(f"Retrieved app from global context: {app_obj}")
        except Exception as e:
            logger.error(f"Failed to get app from global context: {e}")
            
            # Fallback to app from main module
            try:
                import sys
                if 'main' in sys.modules:
                    main_module = sys.modules['main']
                    if hasattr(main_module, 'app') and isinstance(main_module.app, FastAPI):
                        app_obj = main_module.app
                        context_type = "main module app"
                        logger.info(f"Retrieved app from main module: {app_obj}")
            except Exception as e2:
                logger.error(f"Failed to get app from main module: {e2}")
            
    # Standard request object handling
    elif isinstance(context_obj, Request): # FastAPI Request
        app_obj = context_obj.app
        context_type = "FastAPI Request"
        logger.info(f"Context is FastAPI Request. App instance: {app_obj}")
    # Check for WebSocket by checking for a 'scope' attribute that is a dict
    elif hasattr(context_obj, 'scope') and isinstance(getattr(context_obj, 'scope'), dict):
        app_obj = context_obj.scope.get('app')
        context_type = f"WebSocket or similar (from scope, type: {type(context_obj).__name__})"
        logger.info(f"Context is WebSocket-like. App instance from scope: {app_obj}")
    # Check if context_obj itself is the ASGI scope (a dict)
    elif isinstance(context_obj, dict) and 'app' in context_obj:
        app_obj = context_obj.get('app')
        context_type = "ASGI scope (dict)"
        logger.info(f"Context is ASGI scope. App instance from scope: {app_obj}")
    # Fallback for other types that might have an .app attribute (less common for this specific dep)
    elif hasattr(context_obj, 'app'): 
        app_obj = context_obj.app
        context_type = f"Object with .app attribute ({type(context_obj).__name__})"
        logger.info(f"Context is an object with .app. App instance: {app_obj}")
        
    # Direct app import as last resort
    if app_obj is None:
        logger.warning(f"Could not get app from context object. Trying direct import...")
        try:
            # Try to import the app directly from your main module
            import main
            if hasattr(main, 'app'):
                app_obj = main.app
                context_type = "direct import from main"
                logger.info(f"Got app via direct import from main module")
        except Exception as e:
            logger.error(f"Failed to import app directly: {e}")
    
    if app_obj is None:
        logger.error(f"Could not retrieve FastAPI app instance from context ({context_type}). Original context object: {context_obj}")
        # Try one last workaround: access app via Redis client in global scope
        try:
            import main
            if hasattr(main, 'redis_client') and hasattr(main.redis_client, 'get_client'):
                logger.info("Attempting to directly use global redis_client from main module...")
                return await main.redis_client.get_client()
        except Exception as e:
            logger.error(f"Failed to use global redis_client as fallback: {e}")
            
        # If we still don't have the app, we can't proceed
        if isinstance(context_obj, Request):
             raise HTTPException(status_code=500, detail="Internal server error: App context not found.")
        else:
             # This path is likely for middleware if it passes scope directly and app is not found
             raise RuntimeError(f"Internal server error: App context not found from {context_type}.")

    if not hasattr(app_obj, 'state'):
        logger.error(f"FastAPI app instance (from {context_type}, app_obj: {app_obj}) does not have 'state' attribute.")
        if isinstance(context_obj, Request):
            raise HTTPException(status_code=500, detail="Internal server error: App state not found.")
        else:
            raise RuntimeError(f"Internal server error: App state not found on app from {context_type}.")

    # Log current app.state content for debugging
    current_state_keys = list(app_obj.state.__dict__.keys()) if hasattr(app_obj.state, '__dict__') else "N/A (state has no __dict__)"
    logger.info(f"app.state object: {app_obj.state}. Keys in app.state: {current_state_keys}")

    if not hasattr(app_obj.state, 'redis') or app_obj.state.redis is None:
        logger.error(f"Redis client 'redis' not found or is None in app.state (from {context_type}). App: {app_obj}, State: {app_obj.state}")
        if isinstance(context_obj, Request):
            raise HTTPException(status_code=500, detail="Redis client not properly configured in app state.")
        else:
            # This is the error path hit by the middleware
            raise RuntimeError(f"Redis client not configured in app.state via {context_type}.")

    redis_client_wrapper_from_state = app_obj.state.redis
    logger.info(f"Found app.state.redis. Type: {type(redis_client_wrapper_from_state).__name__}")
    
    if not hasattr(redis_client_wrapper_from_state, 'get_client') or not callable(getattr(redis_client_wrapper_from_state, 'get_client')):
        logger.error(f"Object in app.state.redis (type: {type(redis_client_wrapper_from_state).__name__}) does not have a callable 'get_client' method.")
        if isinstance(context_obj, Request):
            raise HTTPException(status_code=500, detail="Redis service misconfiguration: get_client method missing.")
        else:
            raise RuntimeError("Redis service misconfiguration: get_client method missing.")

    try:
        # Get the actual Redis connection from the wrapper
        logger.info("Attempting to call app.state.redis.get_client()...")
        redis_connection = await redis_client_wrapper_from_state.get_client()
        
        if not redis_connection:
            logger.error(f"app.state.redis.get_client() returned None or a non-Redis client object (from {context_type}).")
            if isinstance(context_obj, Request):
                raise HTTPException(status_code=500, detail="Failed to get valid Redis connection from service.")
            else:
                raise RuntimeError("Failed to get valid Redis connection from service.")
        
        logger.info(f"Successfully retrieved Redis connection via get_client(). Type: {type(redis_connection).__name__}")
        return redis_connection
    except Exception as e:
        logger.error(f"Error calling app.state.redis.get_client() or using the connection (from {context_type}): {str(e)}", exc_info=True)
        if isinstance(context_obj, Request):
            raise HTTPException(status_code=500, detail=f"Redis connection error: {str(e)}")
        else:
            raise RuntimeError(f"Redis connection error: {str(e)}")

