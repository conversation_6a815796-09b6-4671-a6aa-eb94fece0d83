2025-04-02 19:26:28,737 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 403 Forbidden"
2025-04-02 19:26:28,739 - ERROR - Admin login failed: {"detail":"CSRF token missing or invalid"}
2025-04-02 19:26:28,753 - ERROR - Admin login failed, cannot continue tests
2025-04-05 20:58:03,870 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 403 Forbidden"
2025-04-05 20:58:03,889 - ERROR - Admin login failed: {"detail":"CSRF token missing or invalid"}
2025-04-05 20:58:03,889 - ERROR - Admin login failed, cannot continue tests
2025-04-05 21:16:25,830 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 21:16:25,876 - INFO - Retrieved CSRF token for admin login
2025-04-05 21:16:25,921 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 403 Forbidden"
2025-04-05 21:16:26,466 - ERROR - Admin login failed: 403 {"detail": "CAPTCHA verification required"}
2025-04-05 21:16:26,467 - ERROR - Admin login failed, cannot continue tests
2025-04-05 21:26:21,794 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 21:26:21,795 - INFO - Retrieved CSRF token for admin login
2025-04-05 21:26:21,869 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 422 Unprocessable Entity"
2025-04-05 21:26:21,909 - ERROR - Admin login failed: 422 {"detail":[{"type":"extra_forbidden","loc":["body","csrf_token"],"msg":"Extra inputs are not permitted","input":"a3d353788eb3a833c5f1c728eb33f963a5412b1ff1297490f563a6835c8b2e7a"},{"type":"extra_forbidden","loc":["body","captcha_token"],"msg":"Extra inputs are not permitted","input":"bypass_captcha_in_test_mode"}]}
2025-04-05 21:26:21,909 - ERROR - Admin login failed, cannot continue tests
2025-04-05 21:30:22,539 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 21:30:22,542 - INFO - Retrieved CSRF token for admin login
2025-04-05 21:30:27,530 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 21:30:27,593 - INFO - Admin login successful
2025-04-05 21:30:27,669 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 21:30:27,671 - INFO - Retrieved CSRF token for user login
2025-04-05 21:30:28,684 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 21:30:28,685 - INFO - User login successful
2025-04-05 21:30:28,813 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 21:30:28,814 - INFO - Created YOUTUBE_VIEW task with ID: 384
2025-04-05 21:30:28,890 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 21:30:28,933 - INFO - Created TELEGRAM_CHANNEL task with ID: 385
2025-04-05 21:30:29,558 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/384/start "HTTP/1.1 200 OK"
2025-04-05 21:30:29,601 - INFO - Started task 384
2025-04-05 21:30:29,809 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/384/verify "HTTP/1.1 200 OK"
2025-04-05 21:30:29,853 - INFO - Verified task 384: True
2025-04-05 21:30:31,732 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/384/verify "HTTP/1.1 200 OK"
2025-04-05 21:30:31,733 - INFO - Verified task 384: True
2025-04-05 21:30:31,811 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/384/claim "HTTP/1.1 200 OK"
2025-04-05 21:30:31,849 - INFO - Claimed reward for task 384
2025-04-05 21:30:31,850 - INFO - YouTube task flow completed successfully
2025-04-05 21:30:31,925 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/385/start "HTTP/1.1 200 OK"
2025-04-05 21:30:32,455 - INFO - Started task 385
2025-04-05 21:30:32,829 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/385/verify "HTTP/1.1 200 OK"
2025-04-05 21:30:32,870 - INFO - Verified task 385: True
2025-04-05 21:30:34,786 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/385/claim "HTTP/1.1 200 OK"
2025-04-05 21:30:34,829 - INFO - Claimed reward for task 385
2025-04-05 21:30:34,830 - INFO - Telegram task flow completed successfully
2025-04-05 21:30:34,924 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/384/toggle "HTTP/1.1 200 OK"
2025-04-05 21:30:35,455 - INFO - Toggled task 384 active status to False
2025-04-05 21:30:35,705 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/384/toggle "HTTP/1.1 200 OK"
2025-04-05 21:30:35,745 - INFO - Toggled task 384 active status to True
2025-04-05 21:30:35,853 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/384/start "HTTP/1.1 400 Bad Request"
2025-04-05 21:30:35,893 - ERROR - Failed to start task 384: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
2025-04-05 21:30:35,893 - INFO - Admin functions tested successfully
2025-04-05 22:11:54,492 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:11:54,496 - INFO - Retrieved CSRF token for admin login
2025-04-05 22:11:56,685 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 22:11:56,726 - INFO - Admin login successful
2025-04-05 22:11:56,770 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:11:56,773 - INFO - Retrieved CSRF token for user login
2025-04-05 22:11:59,526 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 22:11:59,612 - INFO - User login successful
2025-04-05 22:11:59,802 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:11:59,804 - INFO - Created YOUTUBE_VIEW task with ID: 387
2025-04-05 22:11:59,893 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:11:59,937 - INFO - Created TELEGRAM_CHANNEL task with ID: 388
2025-04-05 22:12:00,515 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/387/start "HTTP/1.1 200 OK"
2025-04-05 22:12:00,557 - INFO - Started task 387
2025-04-05 22:12:00,676 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/387/verify "HTTP/1.1 200 OK"
2025-04-05 22:12:00,717 - INFO - Verified task 387: True
2025-04-05 22:12:01,814 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/387/verify "HTTP/1.1 200 OK"
2025-04-05 22:12:01,815 - INFO - Verified task 387: True
2025-04-05 22:12:01,917 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/387/claim "HTTP/1.1 200 OK"
2025-04-05 22:12:01,961 - INFO - Claimed reward for task 387
2025-04-05 22:12:01,962 - INFO - YouTube task flow completed successfully
2025-04-05 22:12:02,718 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/388/start "HTTP/1.1 200 OK"
2025-04-05 22:12:02,761 - INFO - Started task 388
2025-04-05 22:12:02,907 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/388/verify "HTTP/1.1 200 OK"
2025-04-05 22:12:02,949 - INFO - Verified task 388: True
2025-04-05 22:12:03,560 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/388/claim "HTTP/1.1 200 OK"
2025-04-05 22:12:03,601 - INFO - Claimed reward for task 388
2025-04-05 22:12:03,601 - INFO - Telegram task flow completed successfully
2025-04-05 22:12:03,714 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/387/toggle "HTTP/1.1 200 OK"
2025-04-05 22:12:03,753 - INFO - Toggled task 387 active status to False
2025-04-05 22:12:03,851 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/387/toggle "HTTP/1.1 200 OK"
2025-04-05 22:12:03,893 - INFO - Toggled task 387 active status to True
2025-04-05 22:12:03,957 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/387/start "HTTP/1.1 400 Bad Request"
2025-04-05 22:12:04,464 - ERROR - Failed to start task 387: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
2025-04-05 22:12:04,464 - INFO - Admin functions tested successfully
2025-04-08 19:00:23,466 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:00:23,469 - INFO - Retrieved CSRF token for admin login
2025-04-08 19:00:24,607 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:00:24,647 - INFO - Admin login successful
2025-04-08 19:00:24,705 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:00:24,714 - INFO - Retrieved CSRF token for user login
2025-04-08 19:00:26,881 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:00:26,883 - INFO - User login successful
2025-04-08 19:00:27,473 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:00:27,474 - INFO - Created YOUTUBE_VIEW task with ID: 396
2025-04-08 19:00:27,582 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:00:27,625 - INFO - Created TELEGRAM_CHANNEL task with ID: 397
2025-04-08 19:00:27,737 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/396/start "HTTP/1.1 200 OK"
2025-04-08 19:00:27,785 - INFO - Started task 396
2025-04-08 19:00:27,933 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/396/verify "HTTP/1.1 200 OK"
2025-04-08 19:00:28,458 - INFO - Verified task 396: True
2025-04-08 19:00:29,590 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/396/verify "HTTP/1.1 200 OK"
2025-04-08 19:00:29,591 - INFO - Verified task 396: True
2025-04-08 19:00:29,722 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/396/claim "HTTP/1.1 200 OK"
2025-04-08 19:00:29,765 - INFO - Claimed reward for task 396
2025-04-08 19:00:29,766 - INFO - YouTube task flow completed successfully
2025-04-08 19:00:29,901 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/397/start "HTTP/1.1 200 OK"
2025-04-08 19:00:29,950 - INFO - Started task 397
2025-04-08 19:00:30,746 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/397/verify "HTTP/1.1 200 OK"
2025-04-08 19:00:30,789 - INFO - Verified task 397: True
2025-04-08 19:00:30,861 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/397/claim "HTTP/1.1 200 OK"
2025-04-08 19:00:30,914 - INFO - Claimed reward for task 397
2025-04-08 19:00:30,915 - INFO - Telegram task flow completed successfully
2025-04-08 19:00:32,479 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/396/toggle "HTTP/1.1 200 OK"
2025-04-08 19:00:32,481 - INFO - Toggled task 396 active status to False
2025-04-08 19:00:32,608 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/396/toggle "HTTP/1.1 200 OK"
2025-04-08 19:00:32,649 - INFO - Toggled task 396 active status to True
2025-04-08 19:00:32,737 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/396/start "HTTP/1.1 400 Bad Request"
2025-04-08 19:00:32,778 - ERROR - Failed to start task 396: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
2025-04-08 19:00:32,778 - INFO - Admin functions tested successfully
2025-04-08 19:04:51,766 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:04:51,768 - INFO - Retrieved CSRF token for admin login
2025-04-08 19:04:53,814 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:04:53,873 - INFO - Admin login successful
2025-04-08 19:04:54,463 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:04:54,477 - INFO - Retrieved CSRF token for user login
2025-04-08 19:04:55,688 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:04:55,730 - INFO - User login successful
2025-04-08 19:04:55,848 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:04:55,850 - INFO - Created YOUTUBE_VIEW task with ID: 398
2025-04-08 19:04:55,957 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:04:56,457 - INFO - Created TELEGRAM_CHANNEL task with ID: 399
2025-04-08 19:04:56,569 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/398/start "HTTP/1.1 200 OK"
2025-04-08 19:04:56,613 - INFO - Started task 398
2025-04-08 19:04:56,762 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/398/verify "HTTP/1.1 200 OK"
2025-04-08 19:04:56,809 - INFO - Verified task 398: True
2025-04-08 19:04:57,947 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/398/verify "HTTP/1.1 200 OK"
2025-04-08 19:04:57,948 - INFO - Verified task 398: True
2025-04-08 19:04:58,547 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/398/claim "HTTP/1.1 200 OK"
2025-04-08 19:04:58,593 - INFO - Claimed reward for task 398
2025-04-08 19:04:58,594 - INFO - YouTube task flow completed successfully
2025-04-08 19:04:58,696 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/399/start "HTTP/1.1 200 OK"
2025-04-08 19:04:58,742 - INFO - Started task 399
2025-04-08 19:04:58,944 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/399/verify "HTTP/1.1 200 OK"
2025-04-08 19:04:59,456 - INFO - Verified task 399: True
2025-04-08 19:04:59,600 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/399/claim "HTTP/1.1 200 OK"
2025-04-08 19:04:59,641 - INFO - Claimed reward for task 399
2025-04-08 19:04:59,642 - INFO - Telegram task flow completed successfully
2025-04-08 19:04:59,727 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/398/toggle "HTTP/1.1 200 OK"
2025-04-08 19:04:59,769 - INFO - Toggled task 398 active status to False
2025-04-08 19:04:59,847 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/398/toggle "HTTP/1.1 200 OK"
2025-04-08 19:04:59,889 - INFO - Toggled task 398 active status to True
2025-04-08 19:05:00,471 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/398/start "HTTP/1.1 400 Bad Request"
2025-04-08 19:05:00,509 - ERROR - Failed to start task 398: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
2025-04-08 19:05:00,510 - INFO - Admin functions tested successfully
2025-04-08 21:37:58,704 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:37:58,707 - INFO - Retrieved CSRF token for admin login
2025-04-08 21:38:00,627 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 21:38:00,670 - INFO - Admin login successful
2025-04-08 21:38:00,705 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 21:38:00,712 - INFO - Retrieved CSRF token for user login
2025-04-08 21:38:01,733 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 21:38:01,774 - INFO - User login successful
2025-04-08 21:38:01,909 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:38:01,910 - INFO - Created YOUTUBE_VIEW task with ID: 412
2025-04-08 21:38:02,934 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 21:38:03,466 - INFO - Created TELEGRAM_CHANNEL task with ID: 413
2025-04-08 21:38:03,570 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/412/start "HTTP/1.1 200 OK"
2025-04-08 21:38:03,613 - INFO - Started task 412
2025-04-08 21:38:03,797 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/412/verify "HTTP/1.1 200 OK"
2025-04-08 21:38:03,837 - INFO - Verified task 412: True
2025-04-08 21:38:04,901 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/412/verify "HTTP/1.1 200 OK"
2025-04-08 21:38:04,902 - INFO - Verified task 412: True
2025-04-08 21:38:05,518 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/412/claim "HTTP/1.1 200 OK"
2025-04-08 21:38:05,561 - INFO - Claimed reward for task 412
2025-04-08 21:38:05,562 - INFO - YouTube task flow completed successfully
2025-04-08 21:38:05,682 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/413/start "HTTP/1.1 200 OK"
2025-04-08 21:38:05,729 - INFO - Started task 413
2025-04-08 21:38:05,863 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/413/verify "HTTP/1.1 200 OK"
2025-04-08 21:38:05,905 - INFO - Verified task 413: True
2025-04-08 21:38:07,477 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/413/claim "HTTP/1.1 200 OK"
2025-04-08 21:38:07,517 - INFO - Claimed reward for task 413
2025-04-08 21:38:07,517 - INFO - Telegram task flow completed successfully
2025-04-08 21:38:07,588 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/412/toggle "HTTP/1.1 200 OK"
2025-04-08 21:38:07,629 - INFO - Toggled task 412 active status to False
2025-04-08 21:38:07,685 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/412/toggle "HTTP/1.1 200 OK"
2025-04-08 21:38:07,725 - INFO - Toggled task 412 active status to True
2025-04-08 21:38:07,771 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/412/start "HTTP/1.1 400 Bad Request"
2025-04-08 21:38:07,813 - ERROR - Failed to start task 412: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
2025-04-08 21:38:07,814 - INFO - Admin functions tested successfully
