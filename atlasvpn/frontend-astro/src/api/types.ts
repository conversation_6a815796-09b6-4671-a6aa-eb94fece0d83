/**
 * @file api/types.ts
 * @description Centralized API types for AtlasVPN
 * Consolidates types from various sources
 */

// User types
export interface User {
  id: number;
  username: string;
  email?: string;
  telegram_id?: number;
  telegram_photo_url?: string;
  wallet_balance: number;
  total_passive_hourly_income: number;
  last_passive_claim_at?: string;
  created_at: string;
  updated_at: string;
  is_active?: boolean;
  role?: string;
}

export interface UserStats {
  wallet_balance: number;
  total_accumulated_card_profit: number;
  total_passive_hourly_income: number;
  active_subscriptions: number;
  total_earned: number;
  pending_rewards: number;
  total_data_used?: number;
  total_data_limit?: number;
  last_passive_claim_at?: string;
}

export interface UserProfile {
  id: number;
  username: string;
  email?: string;
  telegram_id?: number;
  telegram_photo_url?: string;
  wallet_balance: number;
  created_at: string;
  updated_at: string;
}

// Authentication types
export interface VerifySessionData {
  status: string;
  data?: User;
}

export interface LoginParams {
  username: string;
  password: string;
}

export interface TelegramLoginParams {
  init_data: string;
  auth_method: 'telegram';
}

export interface AuthError extends Error {
  response?: {
    data?: {
      message?: string;
      detail?: string;
    };
    status?: number;
  };
}

export interface TelegramLoginResult {
  isNewUser: boolean;
  user?: User;
  telegram_data?: any;
}

// Card types
export interface Card {
  id: number;
  name: string;
  level: number;
  profit_per_hour: number;
  upgrade_cost: number;
  image?: string;
  category: string;
  is_owned: boolean;
  description?: string;
  max_level?: number;
}

// Task types
export interface Task {
  id: number;
  title: string;
  description: string;
  reward: number;
  status: 'available' | 'in_progress' | 'completed';
  type: 'daily' | 'social' | 'achievement';
  requirements?: any;
  url?: string;
  verification_type?: string;
}

export interface TaskStats {
  total_tasks: number;
  completed_tasks: number;
  pending_tasks: number;
  total_rewards: number;
}

// Referral types
export interface ReferralInfo {
  total_referred: number;
  total_earned: number;
  pending_rewards: number;
  referral_code: string;
  referral_link: string;
}

export interface ReferredUser {
  id: number;
  username: string;
  joined_at: string;
  earned_from: number;
  status: 'active' | 'pending';
}

// VPN types
export interface VpnPackage {
  id: number;
  name: string;
  description: string;
  price: number;
  duration_days: number;
  data_limit?: number;
  speed_limit?: number;
  server_locations: string[];
  is_active: boolean;
}

export interface VpnSubscription {
  id: number;
  package_id: number;
  package_name: string;
  start_date: string;
  end_date: string;
  is_active: boolean;
  data_used: number;
  data_limit?: number;
  remaining_days: number;
}

export interface VpnPanel {
  id: number;
  name: string;
  location: string;
  server_url: string;
  is_active: boolean;
  load_percentage: number;
  ping_ms: number;
}

// Transaction types
export interface Transaction {
  id: number;
  type: 'purchase' | 'reward' | 'referral' | 'task' | 'card_profit';
  amount: number;
  description: string;
  created_at: string;
  status: 'completed' | 'pending' | 'failed';
}

// Chat types
export interface ChatMessage {
  id: number;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: string;
  type?: 'text' | 'image' | 'file';
}

export interface ChatConversation {
  id: number;
  title: string;
  messages: ChatMessage[];
  created_at: string;
  updated_at: string;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  status: string;
  message?: string;
}

export interface ApiError {
  message: string;
  code?: string;
  status: number;
}

export interface HealthCheck {
  status: string;
  timestamp: string;
  version: string;
}

// Generic API operation responses
export interface SuccessResponse {
  success: boolean;
  message: string;
}

export interface PurchaseResponse extends SuccessResponse {
  transaction_id?: number;
}

export interface RewardResponse extends SuccessResponse {
  reward?: number;
  amount?: number;
}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// Query types for React Query
export interface QueryOptions {
  enabled?: boolean;
  staleTime?: number;
  cacheTime?: number;
  refetchOnWindowFocus?: boolean;
  refetchOnMount?: boolean;
  retry?: boolean | number;
}

export interface MutationOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: any) => void;
  onSettled?: () => void;
}