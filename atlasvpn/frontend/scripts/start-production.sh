#!/bin/bash

# Production start script for VIPVerse Telegram Mini App
# This script sets up the environment and starts the development server with production settings

echo "🚀 Starting VIPVerse in production mode..."

# Set NODE_ENV to production
export NODE_ENV=production

# Load production environment variables
if [ -f .env.production ]; then
    echo "📄 Loading .env.production..."
    export $(cat .env.production | grep -v '^#' | xargs)
fi

# Load main .env file (will override .env.production if variables exist)
if [ -f .env ]; then
    echo "📄 Loading .env..."
    export $(cat .env | grep -v '^#' | xargs)
fi

echo "🌐 API URL: $VITE_API_URL"
echo "🔌 WebSocket URL: $VITE_WS_BASE_URL"
echo "📱 Environment: $VITE_ENVIRONMENT"

# Start the development server with production environment
echo "🏃 Starting development server..."
pnpm dev --mode production --host 0.0.0.0 --port 3000

echo "✅ Server started successfully!"
echo "🌍 Access your app at: http://dev.atlasvip.cloud:3000" 