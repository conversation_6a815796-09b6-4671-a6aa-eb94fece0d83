/* Cyberpunk Gaming Theme */

:root {
  /* Primary Cyberpunk Colors */
  --cyber-black: #0a0a0a;
  --cyber-dark: #111111;
  --cyber-darker: #1a1a1a;
  --cyber-gray: #2a2a2a;
  --cyber-light-gray: #3a3a3a;
  
  /* Neon Colors */
  --neon-cyan: #00ffff;
  --neon-magenta: #ff00ff;
  --neon-yellow: #ffff00;
  --neon-green: #00ff88;
  --neon-blue: #0088ff;
  --neon-purple: #8800ff;
  --neon-orange: #ff8800;
  
  /* Accent Colors */
  --accent-primary: #a855f7;
  --accent-secondary: #3b82f6;
  --accent-tertiary: #06b6d4;
  --accent-success: #10b981;
  --accent-warning: #f59e0b;
  --accent-error: #ef4444;
  
  /* Gradient Backgrounds */
  --gradient-cyber: linear-gradient(135deg, var(--cyber-black) 0%, var(--cyber-dark) 50%, var(--cyber-darker) 100%);
  --gradient-neon: linear-gradient(45deg, var(--neon-cyan), var(--neon-magenta), var(--neon-yellow));
  --gradient-purple: linear-gradient(135deg, #a855f7, #3b82f6, #06b6d4);
  --gradient-gold: linear-gradient(135deg, #fbbf24, #f59e0b, #d97706);
  
  /* Shadows & Glows */
  --shadow-cyber: 0 4px 20px rgba(168, 85, 247, 0.3);
  --shadow-neon: 0 0 20px var(--neon-cyan);
  --shadow-purple: 0 0 20px var(--accent-primary);
  --shadow-gold: 0 0 20px #fbbf24;
  
  /* Border Styles */
  --border-cyber: 1px solid rgba(168, 85, 247, 0.3);
  --border-neon: 1px solid var(--neon-cyan);
  --border-glow: 1px solid rgba(0, 255, 255, 0.5);
}

/* Base Cyberpunk Styles */
.cyber-bg {
  background: var(--gradient-cyber);
}

.cyber-bg-dark {
  background: var(--cyber-black);
}

.cyber-bg-darker {
  background: var(--cyber-darker);
}

.cyber-bg-glass {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(12px);
  border: var(--border-cyber);
}

.cyber-bg-glass-strong {
  background: rgba(26, 26, 26, 0.95);
  backdrop-filter: blur(16px);
  border: var(--border-glow);
}

/* Neon Effects */
.neon-border {
  border: 2px solid var(--neon-cyan);
  box-shadow: 
    0 0 5px var(--neon-cyan),
    inset 0 0 5px var(--neon-cyan);
}

.neon-border-purple {
  border: 2px solid var(--accent-primary);
  box-shadow: 
    0 0 10px var(--accent-primary),
    inset 0 0 5px var(--accent-primary);
}

.neon-border-gold {
  border: 2px solid #fbbf24;
  box-shadow: 
    0 0 10px #fbbf24,
    inset 0 0 5px #fbbf24;
}

/* Cyberpunk Cards */
.cyber-card {
  background: var(--cyber-bg-glass);
  border-radius: 12px;
  border: var(--border-cyber);
  box-shadow: var(--shadow-cyber);
  position: relative;
  overflow: hidden;
}

.cyber-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 2px;
  background: var(--gradient-neon);
  animation: cyber-scan 3s linear infinite;
}

@keyframes cyber-scan {
  0% { left: -100%; }
  100% { left: 100%; }
}

.cyber-card-premium {
  background: linear-gradient(135deg, 
    rgba(168, 85, 247, 0.1) 0%, 
    rgba(59, 130, 246, 0.1) 50%, 
    rgba(6, 182, 212, 0.1) 100%);
  border: 1px solid rgba(168, 85, 247, 0.4);
  box-shadow: 
    0 8px 32px rgba(168, 85, 247, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Gaming Buttons */
.cyber-button {
  background: var(--gradient-purple);
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-family: var(--font-heading);
  font-weight: var(--font-weight-semibold);
  letter-spacing: var(--letter-spacing-wide);
  text-transform: uppercase;
  color: white;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.cyber-button:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 25px rgba(168, 85, 247, 0.4),
    0 0 20px rgba(168, 85, 247, 0.3);
}

.cyber-button:active {
  transform: translateY(0);
}

.cyber-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.cyber-button:hover::before {
  left: 100%;
}

.cyber-button-secondary {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(8px);
}

.cyber-button-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--accent-primary);
}

/* Gaming Progress Bars */
.cyber-progress {
  width: 100%;
  height: 8px;
  background: var(--cyber-gray);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.cyber-progress-bar {
  height: 100%;
  background: var(--gradient-purple);
  border-radius: 4px;
  position: relative;
  transition: width 0.3s ease;
}

.cyber-progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: progress-shine 2s linear infinite;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Cyberpunk Animations */
@keyframes cyber-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes cyber-glow {
  0%, 100% { 
    box-shadow: 0 0 5px var(--accent-primary);
  }
  50% { 
    box-shadow: 0 0 20px var(--accent-primary);
  }
}

@keyframes cyber-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.cyber-pulse {
  animation: cyber-pulse 2s ease-in-out infinite;
}

.cyber-glow {
  animation: cyber-glow 2s ease-in-out infinite;
}

.cyber-float {
  animation: cyber-float 3s ease-in-out infinite;
}

/* Gaming Stats Display */
.cyber-stat {
  background: var(--cyber-bg-glass);
  border-radius: 8px;
  padding: 16px;
  border: var(--border-cyber);
  position: relative;
}

.cyber-stat-value {
  font-family: var(--font-mono);
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--neon-green);
  text-shadow: 0 0 10px var(--neon-green);
}

.cyber-stat-label {
  font-family: var(--font-heading);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  color: #94a3b8;
  text-transform: uppercase;
  letter-spacing: var(--letter-spacing-wide);
}

/* Responsive Cyberpunk Design */
@media (max-width: 640px) {
  .cyber-card {
    border-radius: 8px;
  }
  
  .cyber-button {
    padding: 10px 20px;
    font-size: 0.875rem;
  }
  
  .cyber-stat-value {
    font-size: 1.25rem;
  }
}
