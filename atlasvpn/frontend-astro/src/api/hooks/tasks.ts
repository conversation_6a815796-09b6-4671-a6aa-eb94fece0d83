/**
 * @file api/hooks/tasks.ts
 * @description Task-related React Query hooks
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type {
  Task,
  TaskStats,
  QueryOptions,
  MutationOptions
} from '../types';
import { taskService } from '../services';

// Query Keys
export const TASK_QUERY_KEYS = {
  AVAILABLE_TASKS: ['tasks', 'available'],
  DAILY_STREAK: ['tasks', 'dailyStreak'],
  TASK_ANALYTICS: ['tasks', 'analytics']
} as const;

// Default query options
const DEFAULT_QUERY_OPTIONS: QueryOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  retry: false
};

// Task Hooks
export function useTasksQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: TASK_QUERY_KEYS.AVAILABLE_TASKS,
    queryFn: () => taskService.getAvailableTasks(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function useDailyStreakQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: TASK_QUERY_KEYS.DAILY_STREAK,
    queryFn: () => taskService.getDailyStreak(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function useTaskAnalyticsQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: TASK_QUERY_KEYS.TASK_ANALYTICS,
    queryFn: () => taskService.getTaskAnalytics(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function useStartTaskMutation(options?: MutationOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (taskId: number) => taskService.startTask(taskId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.AVAILABLE_TASKS });
    },
    ...options
  });
}

export function useClaimTaskMutation(options?: MutationOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (taskId: number) => taskService.claimTaskReward(taskId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.AVAILABLE_TASKS });
      // Also invalidate user stats as task completion affects user stats
      queryClient.invalidateQueries({ queryKey: ['user', 'stats'] });
    },
    ...options
  });
}

export function useVerifyTaskMutation(options?: MutationOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ taskId, data }: { taskId: number; data?: any }) => 
      taskService.verifyTask(taskId, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: TASK_QUERY_KEYS.AVAILABLE_TASKS });
    },
    ...options
  });
}