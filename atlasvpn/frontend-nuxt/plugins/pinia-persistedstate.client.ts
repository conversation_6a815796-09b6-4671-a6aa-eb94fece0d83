import { createPersistedState } from 'pinia-plugin-persistedstate'

export default defineNuxtPlugin({
  name: 'pinia-persistedstate',
  parallel: true,
  setup(nuxtApp) {
    // Only run on client side
    if (process.client && nuxtApp.$pinia) {
      try {
        // Create the persisted state plugin
        const persistedStatePlugin = createPersistedState({
          storage: localStorage,
          auto: true,
          debug: process.env.NODE_ENV === 'development'
        })
        
        // Install the plugin
        nuxtApp.$pinia.use(persistedStatePlugin)
        
        if (process.env.NODE_ENV === 'development') {
          console.log('[Pinia Persistedstate] Plugin installed successfully')
        }
      } catch (error) {
        console.error('[Pinia Persistedstate] Failed to install plugin:', error)
      }
    }
  }
})
