from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, and_
from sqlalchemy.orm import selectinload
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import logging

from database import get_async_db
from models import (
    User, Task, TaskType, DailyTaskStreak, DailyCycle, 
    TaskCompletion, TaskStatus
)
from auth import get_current_admin

router = APIRouter(
    prefix="/daily-tasks",
    tags=["Admin - Daily Tasks"]
)

logger = logging.getLogger(__name__)

# Daily Task Management
@router.get("/")
async def get_daily_tasks(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get all daily tasks with streak information"""
    try:
        # Get daily tasks
        stmt = select(Task).filter(Task.type == TaskType.daily).order_by(Task.created_at.desc())
        result = await db.execute(stmt)
        daily_tasks = result.scalars().all()
        
        tasks_data = []
        for task in daily_tasks:
            # Get streak statistics for this task
            streak_stats_stmt = select(
                func.count(DailyTaskStreak.id),
                func.avg(DailyTaskStreak.current_streak),
                func.max(DailyTaskStreak.current_streak),
                func.count(DailyTaskStreak.id).filter(DailyTaskStreak.current_streak >= task.cycle_length)
            ).filter(DailyTaskStreak.task_id == task.id)
            
            streak_stats_result = await db.execute(streak_stats_stmt)
            total_users, avg_streak, max_streak, completed_cycles = streak_stats_result.first()
            
            # Get recent completions
            recent_completions_stmt = select(func.count(TaskCompletion.id)).filter(
                TaskCompletion.task_id == task.id,
                TaskCompletion.completed_at >= datetime.utcnow() - timedelta(days=7),
                TaskCompletion.status == TaskStatus.COMPLETED
            )
            recent_completions_result = await db.execute(recent_completions_stmt)
            recent_completions = recent_completions_result.scalar() or 0
            
            tasks_data.append({
                "id": task.id,
                "name": task.name,
                "description": task.description,
                "reward_type": task.reward_type.value,
                "reward_value": task.reward_value,
                "cycle_length": task.cycle_length,
                "daily_rewards": task.daily_rewards,
                "cycle_bonus_reward": task.cycle_bonus_reward,
                "total_cycle_reward": task.total_cycle_reward,
                "reset_streak_after_hours": task.reset_streak_after_hours,
                "is_active": task.is_active,
                "created_at": task.created_at.isoformat(),
                "stats": {
                    "total_users": total_users or 0,
                    "average_streak": round(float(avg_streak or 0), 2),
                    "max_streak": max_streak or 0,
                    "completed_cycles": completed_cycles or 0,
                    "recent_completions": recent_completions
                }
            })
        
        return tasks_data
        
    except Exception as e:
        logger.error(f"Error fetching daily tasks: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{task_id}/cycles")
async def get_daily_task_cycles(
    task_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    limit: int = 100
):
    """Get daily task cycles with detailed information"""
    try:
        # Verify task exists and is daily
        task_stmt = select(Task).filter(Task.id == task_id, Task.type == TaskType.daily)
        task_result = await db.execute(task_stmt)
        task = task_result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(status_code=404, detail="Daily task not found")
        
        # Get daily cycles
        cycles_stmt = select(DailyCycle).options(
            selectinload(DailyCycle.user)
        ).filter(
            DailyCycle.task_id == task_id
        ).order_by(desc(DailyCycle.created_at)).limit(limit)
        
        cycles_result = await db.execute(cycles_stmt)
        cycles = cycles_result.scalars().all()
        
        cycles_data = []
        for cycle in cycles:
            cycles_data.append({
                "id": cycle.id,
                "user_id": cycle.user_id,
                "username": cycle.user.username if cycle.user else None,
                "cycle_number": cycle.cycle_number,
                "days_completed": cycle.days_completed,
                "cycle_length": cycle.cycle_length,
                "is_completed": cycle.is_completed,
                "is_bonus_claimed": cycle.is_bonus_claimed,
                "reward_claimed": cycle.reward_claimed,
                "bonus_reward_claimed": cycle.bonus_reward_claimed,
                "started_at": cycle.started_at.isoformat(),
                "completed_at": cycle.completed_at.isoformat() if cycle.completed_at else None,
                "created_at": cycle.created_at.isoformat()
            })
        
        return {
            "task": {
                "id": task.id,
                "name": task.name,
                "cycle_length": task.cycle_length
            },
            "cycles": cycles_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting daily task cycles: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/reset-streak/{task_id}")
async def admin_reset_user_streak(
    task_id: int,
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Reset a user's streak for a specific daily task"""
    try:
        # Verify task exists and is daily
        task_stmt = select(Task).filter(Task.id == task_id, Task.type == TaskType.daily)
        task_result = await db.execute(task_stmt)
        task = task_result.scalar_one_or_none()
        
        if not task:
            raise HTTPException(status_code=404, detail="Daily task not found")
        
        # Verify user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Get user's streak
        streak_stmt = select(DailyTaskStreak).filter(
            DailyTaskStreak.user_id == user_id,
            DailyTaskStreak.task_id == task_id
        )
        streak_result = await db.execute(streak_stmt)
        streak = streak_result.scalar_one_or_none()
        
        if not streak:
            raise HTTPException(
                status_code=404, 
                detail="No streak found for this user and task"
            )
        
        # Reset streak
        old_streak = streak.current_streak
        streak.current_streak = 0
        streak.last_completion_date = None
        streak.updated_at = datetime.utcnow()
        
        # Mark any active cycles as incomplete
        active_cycles_stmt = select(DailyCycle).filter(
            DailyCycle.user_id == user_id,
            DailyCycle.task_id == task_id,
            DailyCycle.is_completed == False
        )
        active_cycles_result = await db.execute(active_cycles_stmt)
        active_cycles = active_cycles_result.scalars().all()
        
        for cycle in active_cycles:
            cycle.is_completed = False
            cycle.days_completed = 0
        
        await db.commit()
        
        logger.info(
            f"Admin {current_user.username} reset streak for user {user.username} "
            f"on task {task.name} (was {old_streak} days)"
        )
        
        return {
            "message": f"Streak reset successfully for user {user.username}",
            "previous_streak": old_streak,
            "reset_cycles": len(active_cycles)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resetting user streak: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/streak/{streak_id}")
async def update_user_streak(
    streak_id: int,
    update_data: dict,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Manually update a user's daily task streak"""
    try:
        # Get the streak
        streak_stmt = select(DailyTaskStreak).options(
            selectinload(DailyTaskStreak.user),
            selectinload(DailyTaskStreak.task)
        ).filter(DailyTaskStreak.id == streak_id)
        
        streak_result = await db.execute(streak_stmt)
        streak = streak_result.scalar_one_or_none()
        
        if not streak:
            raise HTTPException(status_code=404, detail="Streak not found")
        
        # Update allowed fields
        if "current_streak" in update_data:
            new_streak = update_data["current_streak"]
            if new_streak < 0:
                raise HTTPException(status_code=400, detail="Streak cannot be negative")
            streak.current_streak = new_streak
        
        if "best_streak" in update_data:
            new_best = update_data["best_streak"]
            if new_best < 0:
                raise HTTPException(status_code=400, detail="Best streak cannot be negative")
            streak.best_streak = new_best
        
        if "total_completions" in update_data:
            new_total = update_data["total_completions"]
            if new_total < 0:
                raise HTTPException(status_code=400, detail="Total completions cannot be negative")
            streak.total_completions = new_total
        
        streak.updated_at = datetime.utcnow()
        
        await db.commit()
        
        return {
            "id": streak.id,
            "user_id": streak.user_id,
            "username": streak.user.username if streak.user else None,
            "task_name": streak.task.name if streak.task else None,
            "current_streak": streak.current_streak,
            "best_streak": streak.best_streak,
            "total_completions": streak.total_completions,
            "message": "Streak updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating user streak: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_daily_task_stats(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 30
):
    """Get comprehensive daily task statistics"""
    try:
        start_date = datetime.utcnow() - timedelta(days=days)
        
        # Total daily tasks
        total_tasks_stmt = select(func.count(Task.id)).filter(Task.type == TaskType.daily)
        total_tasks_result = await db.execute(total_tasks_stmt)
        total_tasks = total_tasks_result.scalar() or 0
        
        # Active daily tasks
        active_tasks_stmt = select(func.count(Task.id)).filter(
            Task.type == TaskType.daily,
            Task.is_active == True
        )
        active_tasks_result = await db.execute(active_tasks_stmt)
        active_tasks = active_tasks_result.scalar() or 0
        
        # Total users with streaks
        users_with_streaks_stmt = select(func.count(DailyTaskStreak.user_id.distinct()))
        users_with_streaks_result = await db.execute(users_with_streaks_stmt)
        users_with_streaks = users_with_streaks_result.scalar() or 0
        
        # Active streaks (completed in last 48 hours)
        active_streaks_stmt = select(func.count(DailyTaskStreak.id)).filter(
            DailyTaskStreak.last_completion_date >= datetime.utcnow() - timedelta(hours=48)
        )
        active_streaks_result = await db.execute(active_streaks_stmt)
        active_streaks = active_streaks_result.scalar() or 0
        
        # Average streak length
        avg_streak_stmt = select(func.avg(DailyTaskStreak.current_streak)).filter(
            DailyTaskStreak.current_streak > 0
        )
        avg_streak_result = await db.execute(avg_streak_stmt)
        avg_streak = avg_streak_result.scalar() or 0
        
        # Completed cycles in period
        completed_cycles_stmt = select(func.count(DailyCycle.id)).filter(
            DailyCycle.completed_at >= start_date,
            DailyCycle.is_completed == True
        )
        completed_cycles_result = await db.execute(completed_cycles_stmt)
        completed_cycles = completed_cycles_result.scalar() or 0
        
        # Daily completions trend
        daily_completions_stmt = select(
            func.date(TaskCompletion.completed_at),
            func.count(TaskCompletion.id)
        ).join(
            Task, TaskCompletion.task_id == Task.id
        ).filter(
            Task.type == TaskType.daily,
            TaskCompletion.completed_at >= start_date,
            TaskCompletion.status == TaskStatus.COMPLETED
        ).group_by(
            func.date(TaskCompletion.completed_at)
        ).order_by(
            func.date(TaskCompletion.completed_at)
        )
        
        daily_completions_result = await db.execute(daily_completions_stmt)
        daily_completions = [
            {
                "date": date.isoformat(),
                "completions": completions
            }
            for date, completions in daily_completions_result.all()
        ]
        
        # Top performing tasks
        top_tasks_stmt = select(
            Task.name,
            func.count(TaskCompletion.id),
            func.avg(DailyTaskStreak.current_streak)
        ).join(
            TaskCompletion, Task.id == TaskCompletion.task_id
        ).join(
            DailyTaskStreak, Task.id == DailyTaskStreak.task_id
        ).filter(
            Task.type == TaskType.daily,
            TaskCompletion.completed_at >= start_date,
            TaskCompletion.status == TaskStatus.COMPLETED
        ).group_by(
            Task.id, Task.name
        ).order_by(
            func.count(TaskCompletion.id).desc()
        ).limit(5)
        
        top_tasks_result = await db.execute(top_tasks_stmt)
        top_tasks = [
            {
                "task_name": name,
                "completions": completions,
                "avg_streak": round(float(avg_streak or 0), 2)
            }
            for name, completions, avg_streak in top_tasks_result.all()
        ]
        
        # Streak distribution
        streak_distribution_stmt = select(
            func.case(
                (DailyTaskStreak.current_streak == 0, "0"),
                (DailyTaskStreak.current_streak.between(1, 3), "1-3"),
                (DailyTaskStreak.current_streak.between(4, 7), "4-7"),
                (DailyTaskStreak.current_streak.between(8, 14), "8-14"),
                (DailyTaskStreak.current_streak.between(15, 30), "15-30"),
                else_="30+"
            ).label("streak_range"),
            func.count(DailyTaskStreak.id)
        ).group_by("streak_range")
        
        streak_distribution_result = await db.execute(streak_distribution_stmt)
        streak_distribution = [
            {
                "range": range_name,
                "count": count
            }
            for range_name, count in streak_distribution_result.all()
        ]
        
        return {
            "overview": {
                "total_daily_tasks": total_tasks,
                "active_daily_tasks": active_tasks,
                "users_with_streaks": users_with_streaks,
                "active_streaks": active_streaks,
                "average_streak_length": round(float(avg_streak), 2),
                "completed_cycles": completed_cycles
            },
            "trends": {
                "daily_completions": daily_completions
            },
            "top_performing_tasks": top_tasks,
            "streak_distribution": streak_distribution
        }
        
    except Exception as e:
        logger.error(f"Error getting daily task stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/streaks")
async def get_user_streaks(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    task_id: Optional[int] = None,
    min_streak: Optional[int] = None,
    active_only: bool = False,
    limit: int = 100
):
    """Get user streaks with filtering options"""
    try:
        stmt = select(DailyTaskStreak).options(
            selectinload(DailyTaskStreak.user),
            selectinload(DailyTaskStreak.task)
        )
        
        # Apply filters
        if task_id:
            stmt = stmt.filter(DailyTaskStreak.task_id == task_id)
        
        if min_streak:
            stmt = stmt.filter(DailyTaskStreak.current_streak >= min_streak)
        
        if active_only:
            # Active streaks are those completed within the last 48 hours
            stmt = stmt.filter(
                DailyTaskStreak.last_completion_date >= datetime.utcnow() - timedelta(hours=48)
            )
        
        stmt = stmt.order_by(desc(DailyTaskStreak.current_streak)).limit(limit)
        
        result = await db.execute(stmt)
        streaks = result.scalars().all()
        
        streaks_data = []
        for streak in streaks:
            streaks_data.append({
                "id": streak.id,
                "user_id": streak.user_id,
                "username": streak.user.username if streak.user else None,
                "task_id": streak.task_id,
                "task_name": streak.task.name if streak.task else None,
                "current_streak": streak.current_streak,
                "best_streak": streak.best_streak,
                "total_completions": streak.total_completions,
                "last_completion": streak.last_completion_date.isoformat() if streak.last_completion_date else None,
                "created_at": streak.created_at.isoformat(),
                "updated_at": streak.updated_at.isoformat()
            })
        
        return streaks_data
        
    except Exception as e:
        logger.error(f"Error getting user streaks: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/cycles/{cycle_id}")
async def delete_daily_cycle(
    cycle_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a daily task cycle"""
    try:
        stmt = select(DailyCycle).options(
            selectinload(DailyCycle.user),
            selectinload(DailyCycle.task)
        ).filter(DailyCycle.id == cycle_id)
        
        result = await db.execute(stmt)
        cycle = result.scalar_one_or_none()
        
        if not cycle:
            raise HTTPException(status_code=404, detail="Daily cycle not found")
        
        user_name = cycle.user.username if cycle.user else "Unknown"
        task_name = cycle.task.name if cycle.task else "Unknown"
        
        await db.delete(cycle)
        await db.commit()
        
        logger.info(
            f"Admin {current_user.username} deleted daily cycle {cycle_id} "
            f"for user {user_name} on task {task_name}"
        )
        
        return {"message": f"Daily cycle deleted for user {user_name} on task {task_name}"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting daily cycle: {str(e)}")
        await db.rollback()
        raise HTTPException(status_code=500, detail=str(e)) 