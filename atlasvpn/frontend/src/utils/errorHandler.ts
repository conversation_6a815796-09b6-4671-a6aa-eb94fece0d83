// Add monitoring service interface
interface MonitoringService {
  logError: (error: any) => void;
}

// Create monitoring service based on environment
const createMonitoringService = (): MonitoringService => {
  if (import.meta.env.PROD) {
    return {
      logError: (error: any) => {
        // Send to monitoring service (e.g., Sentry, LogRocket)
        console.error('[Monitoring]', error);
      },
    };
  }

  return {
    logError: (error: any) => {
      const errorDetails = {
        message: error?.message || 'Unknown error',
        name: error?.name || 'Error',
        code: error?.code || 'UNKNOWN_ERROR',
        status: error?.response?.status,
        statusText: error?.response?.statusText,
        url: error?.config?.url,
        method: error?.config?.method,
        timestamp: new Date().toISOString(),
        requestHeaders: error?.config?.headers,
        responseHeaders: error?.response?.headers,
        data: error?.response?.data,
      };

      // Log detailed error information in development
      console.error('[Development] Error Details:', {
        type: errorDetails.name,
        code: errorDetails.code,
        message: errorDetails.message,
        endpoint: `${errorDetails.method?.toUpperCase()} ${errorDetails.url}`,
        status: errorDetails.status
          ? `${errorDetails.status} (${errorDetails.statusText})`
          : 'No Response',
        timestamp: errorDetails.timestamp,
        fullError: error, // Original error object for debugging
      });
    },
  };
};

const monitoringService = createMonitoringService();

export const handleError = (error: any) => {
  // Enhanced error object with more details
  const sanitizedError = {
    message: error?.message || 'An error occurred',
    code: error?.code || 'UNKNOWN_ERROR',
    status: error?.response?.status,
    details: error?.response?.data?.detail || error?.response?.data || null,
    timestamp: new Date().toISOString(),
  };

  // Add network-specific error details
  if (error?.code === 'ERR_NETWORK') {
    sanitizedError.message =
      'Network connection error. Please check your internet connection and try again.';
    sanitizedError.details = {
      url: error?.config?.url,
      method: error?.config?.method,
      timeout: error?.config?.timeout,
      lastRequestTime: new Date().toISOString(),
    };
  }

  // Log the error
  monitoringService.logError(error);

  return sanitizedError;
};
