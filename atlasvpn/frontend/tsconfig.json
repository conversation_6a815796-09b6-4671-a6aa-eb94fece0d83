{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noImplicitAny": false, "strictNullChecks": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["@types/node", "@types/three"], "rootDirs": [".", "./.react-router/types"]}, "include": ["src", "src/**/*.ts", "src/**/*.tsx", "src/types/**/*.d.ts", ".react-router/types/**/*"], "exclude": ["node_modules", "dist", "build", "**/*.test.ts", "**/*.test.tsx", "**/test/**/*", "src/test/**/*"]}