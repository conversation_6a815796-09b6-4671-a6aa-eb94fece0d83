const { chromium } = require('playwright');

async function testApplication() {
  console.log('Starting browser test...');
  
  const browser = await chromium.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  // Listen for console messages
  page.on('console', msg => {
    const text = msg.text();
    console.log(`[BROWSER ${msg.type().toUpperCase()}]:`, text);

    // Look for Pinia persistence messages
    if (text.includes('Pinia') || text.includes('persistedstate') || text.includes('Plugin installed')) {
      console.log('*** PERSISTENCE MESSAGE ***:', text);
    }
  });
  
  // Listen for page errors
  page.on('pageerror', error => {
    console.log(`[PAGE ERROR]:`, error.message);
    console.log(`[ERROR STACK]:`, error.stack);
  });
  
  try {
    console.log('Navigating to https://app.atlasvip.cloud/...');
    await page.goto('https://app.atlasvip.cloud/', {
      waitUntil: 'networkidle',
      timeout: 30000
    });

    console.log('Page loaded successfully!');

    // Wait longer for Nuxt app and stores to initialize
    await page.waitForTimeout(10000);
    
    // Take a screenshot
    await page.screenshot({ path: '/opt/atlasvpn/frontend-nuxt/test-screenshot.png' });
    console.log('Screenshot saved to test-screenshot.png');
    
    // Check if there are any errors in the console
    const title = await page.title();
    console.log('Page title:', title);
    
    // Test Pinia persistence functionality
    const persistenceTest = await page.evaluate(() => {
      try {
        // Check if Pinia stores are available and working
        const results = {
          localStorageBasic: false,
          piniaStoresAvailable: false,
          persistenceWorking: false,
          storeData: {}
        };

        // Basic localStorage test
        localStorage.setItem('test', 'value');
        const value = localStorage.getItem('test');
        localStorage.removeItem('test');
        results.localStorageBasic = value === 'value';

        // Check if Pinia stores are available
        if (window.$nuxt && window.$nuxt.$pinia) {
          results.piniaStoresAvailable = true;

          // Check for Atlas store data in localStorage
          const uiStore = localStorage.getItem('atlas-ui-store');
          const userStore = localStorage.getItem('atlas-user-store');
          const vpnStore = localStorage.getItem('atlas-vpn-store');

          results.storeData = {
            uiStore: uiStore ? 'Found' : 'Not found',
            userStore: userStore ? 'Found' : 'Not found',
            vpnStore: vpnStore ? 'Found' : 'Not found'
          };

          if (uiStore || userStore || vpnStore) {
            results.persistenceWorking = true;
          }
        }

        return results;
      } catch (error) {
        return { error: error.message };
      }
    });

    console.log('Persistence test results:', JSON.stringify(persistenceTest, null, 2));
    
    // Check for any persistence-related errors and get more details
    const errorDetails = await page.evaluate(() => {
      // Check if persistedState is defined anywhere
      const persistedStateCheck = {
        global: typeof persistedState !== 'undefined',
        window: typeof window.persistedState !== 'undefined',
        globalThis: typeof globalThis.persistedState !== 'undefined'
      };

      return {
        persistedStateAvailable: persistedStateCheck,
        hasConsole: !!window.console,
        userAgent: navigator.userAgent
      };
    });

    console.log('Error details:', JSON.stringify(errorDetails, null, 2));
    
  } catch (error) {
    console.error('Test failed:', error.message);
  } finally {
    await browser.close();
    console.log('Browser closed.');
  }
}

testApplication().catch(console.error);
