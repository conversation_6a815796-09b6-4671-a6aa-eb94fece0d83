from fastapi import API<PERSON>out<PERSON>, Depends, HTTPException, status, Response, Request, Form, Header, Body, <PERSON>ie, BackgroundTasks
from fastapi.responses import JSONResponse
from fastapi.security import OAuth2Pass<PERSON><PERSON>earer, OAuth2PasswordRequestForm, HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any, List, Union
from datetime import timedelta, datetime, timezone
import logging
from jose import JWTError, jwt
import urllib
from urllib.parse import parse_qs, unquote
import json
from sqlalchemy import or_, select, func
from sqlalchemy.exc import SQLAlchemyError
from pydantic import BaseModel, ConfigDict
import random
import string
import os
import secrets
from services.rate_limit_service import RateLimitService, RateLimitExceeded
from config.redis_config import RedisClient
from services.security_service import SecurityService
from services.bot_detection_service import BotDetectionService

from database import get_async_db, get_db
from models import User, Role, SystemConfig
from schemas import Token, UserLogin, TelegramAuthData, UserCreate, UserOut, UserProfile, UserProfileUpdate, AdminLoginRequest
from auth import (
    Auth,
    get_auth,
    verify_admin,
    TelegramAuthError,
    InvalidTelegramData,
    verify_password,
    create_access_token,
    process_telegram_auth,
    ACCESS_TOKEN_EXPIRE_MINUTES,
    SECRET_KEY,
    ALGORITHM,
    verify_telegram_webapp_data,
    get_password_hash,
    COOKIE_NAME,
    create_secure_token,
    generate_secure_password,
    REFRESH_TOKEN_COOKIE_NAME,
    DOMAIN,
    get_current_active_user,
    get_current_admin,
    get_current_reseller,
    create_refresh_token,
    authenticate_user,
    set_auth_cookies,
    clear_auth_cookies,
    verify_refresh_token,
    get_current_user
)

async def generate_unique_referral_code(db: AsyncSession) -> str:
    """Generate a unique 8-character alphanumeric referral code."""
    chars = string.ascii_letters + string.digits  # Use both upper and lower case letters + digits
    while True:
        code = ''.join(secrets.choice(chars) for _ in range(8))
        # Check if the code already exists
        stmt = select(User).where(User.referral_code == code)
        result = await db.execute(stmt)
        existing_user = result.scalar_one_or_none()
        if not existing_user:
            return code

router = APIRouter(tags=["authentication"])
logger = logging.getLogger(__name__)

# OAuth2 scheme that checks both cookie and header
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="token", auto_error=False)

async def get_token_from_request(request: Request, token: Optional[str] = Depends(oauth2_scheme)) -> Optional[str]:
    """Get token from either cookie or authorization header"""
    if token:
        return token
    return request.cookies.get(COOKIE_NAME)

async def get_current_user(
    request: Request,
    token: Optional[str] = Depends(get_token_from_request),
    db: AsyncSession = Depends(get_async_db)
) -> User:
    """Validate token and return current user"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail={"message": "Could not validate credentials", "code": "INVALID_TOKEN"},
        headers={"WWW-Authenticate": "Bearer"},
    )

    if not token:
        logger.warning("No token provided in request")
        raise credentials_exception

    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            logger.warning("Token payload missing username")
            raise credentials_exception
        
        # Check token type
        if payload.get("type") != "access":
            logger.warning(f"Invalid token type: {payload.get('type')}")
            raise credentials_exception
            
        # Check token expiration explicitly
        exp = payload.get("exp")
        if exp and datetime.fromtimestamp(exp, tz=timezone.utc) < datetime.now(tz=timezone.utc):
            logger.warning(f"Token expired for user {username}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail={"message": "Token has expired", "code": "TOKEN_EXPIRED"},
                headers={"WWW-Authenticate": "Bearer"},
            )
            
    except JWTError as e:
        logger.error(f"JWT validation error: {str(e)}")
        raise credentials_exception

    stmt = select(User).where(User.username == username)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    if not user:
        logger.warning(f"User not found: {username}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail={"message": "User not found", "code": "USER_NOT_FOUND"}
        )
        
    if not user.is_active:
        logger.warning(f"Inactive user attempted access: {username}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail={"message": "User account is disabled", "code": "USER_DISABLED"}
        )

    return user

# Update Token model
class Token(BaseModel):
    access_token: str
    token_type: str
    role: str
    username: str
    id: int
    telegram_id: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

@router.post("/token", response_model=Token)
async def login_for_access_token(
    request: Request, 
    response: Response,
    form_data: OAuth2PasswordRequestForm = Depends(), 
    auth: Auth = Depends(get_auth) 
):
    """
    Simplified login endpoint that sets HTTP-only cookies
    """
    try:
        # Authenticate user
        user, access_token, refresh_token = await auth.authenticate_user(
            form_data.username, form_data.password, request
        )
        
        # Set auth cookies
        auth.set_auth_cookies(response, access_token, refresh_token)
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "role": user.role.value,
            "username": user.username,
            "id": user.id,
            "telegram_id": str(user.telegram_id) if user.telegram_id else None
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during login"
        )

@router.post("/admin/login", response_model=Token)
async def admin_login(
    request: Request, 
    response: Response,
    login_data: AdminLoginRequest,
    auth: Auth = Depends(get_auth) 
):
    """
    Admin login endpoint with additional security
    Note: CSRF protection is bypassed for this endpoint to avoid circular dependency
    """
    # Note: CAPTCHA verification is handled at the middleware level and has been
    # disabled for the admin login endpoint to support Telegram WebView access
    try:
        # Authenticate user using login_data
        user, access_token, refresh_token = await auth.authenticate_user(
            login_data.username,
            login_data.password,
            request
        )
        
        # Verify user is admin
        if user.role != Role.admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="User is not an admin"
            )
        
        # Set auth cookies
        auth.set_auth_cookies(response, access_token, refresh_token)
        
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "role": user.role.value,
            "username": user.username,
            "id": user.id,
            "telegram_id": str(user.telegram_id) if user.telegram_id else None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Admin login error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/logout")
async def logout(
    request: Request,
    response: Response,
    current_user: User = Depends(get_current_user),
    auth: Auth = Depends(get_auth)
):
    """Simplified logout endpoint"""
    try:
        # Get current token and revoke if available
        token = await Auth.get_token_from_request(request)
        if token and auth.security_service:
            await auth.security_service.revoke_session(
                current_user.id,
                token,
                "user_logout"
            )
        
        # Clear cookies
        Auth.clear_auth_cookies(response)
        
        return {"message": "Successfully logged out"}
        
    except Exception as e:
        logger.error(f"Logout error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/refresh")
async def refresh_token(
    request: Request,
    response: Response,
    auth: Auth = Depends(get_auth)
):
    """Refresh access token using refresh token"""
    try:
        # Get refresh token from cookie
        refresh_token = request.cookies.get(REFRESH_TOKEN_COOKIE_NAME)
        if not refresh_token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Refresh token missing"
            )
        
        # Validate refresh token and get user
        user = await Auth.validate_refresh_token(refresh_token, auth.db)
        if not user:
            # Clear potentially invalid cookies if refresh fails
            Auth.clear_auth_cookies(response)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired refresh token"
            )
        
        # Create new tokens
        access_token, new_refresh_token = Auth.create_auth_tokens(user)
        
        # Set new cookies
        Auth.set_auth_cookies(response, access_token, new_refresh_token)
        
        logger.info(f"Token refreshed successfully for user {user.username}")
        return {
            "access_token": access_token,
            "token_type": "bearer"
        }
            
    except HTTPException as http_exc:
        # Log the specific HTTP exception details
        logger.warning(f"Token refresh failed for user: {http_exc.detail} (Status: {http_exc.status_code})")
        # Re-raise the exception
        raise http_exc
    except Exception as e:
        logger.error(f"Token refresh error: {str(e)}", exc_info=True) # Log stack trace
        # Clear cookies on unexpected errors too
        Auth.clear_auth_cookies(response)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during token refresh"
        )

@router.post("/telegram/login")
async def telegram_login(
    request: Request,
    response: Response,
    data: dict = Body(...),
    db: AsyncSession = Depends(get_async_db),
    auth: Auth = Depends(get_auth)
):
    """Unified Telegram login/register endpoint"""
    try:
        init_data = data.get('init_data')
        if not init_data:
            raise HTTPException(status_code=400, detail="Missing init_data")

        # Verify Telegram data
        is_valid, telegram_data = await verify_telegram_webapp_data(init_data, request)
        if not is_valid or not telegram_data:
            raise HTTPException(status_code=401, detail="Invalid Telegram verification")

        telegram_id = telegram_data.get('id')
        if not telegram_id:
            raise HTTPException(status_code=400, detail="Missing Telegram ID")

        # Find existing user
        result = await db.execute(
            select(User).where(User.telegram_id == telegram_id)
        )
        user = result.scalar_one_or_none()

        if not user:
            # Return 404 for frontend to handle new user flow
            raise HTTPException(
                status_code=404, 
                detail={
                    "message": "User not found. Please create an account first.",
                    "error_code": "USER_NOT_FOUND",
                    "telegram_data": telegram_data
                }
            )

        if not user.is_active:
            raise HTTPException(status_code=403, detail="Account is disabled")

        # Create tokens and set cookies
        access_token, refresh_token = auth.create_auth_tokens(user)
        auth.set_auth_cookies(response, access_token, refresh_token)

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "role": user.role.value,
            "username": user.username,
            "id": user.id,
            "telegram_id": user.telegram_id,
            "wallet_balance": user.wallet_balance,
            "is_active": user.is_active,
            "created_at": user.created_at.isoformat() if user.created_at else None
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Telegram login error: {str(e)}")
        raise HTTPException(status_code=500, detail="Authentication failed")

@router.post("/telegram/register")
async def telegram_register(
    request: Request,
    response: Response,
    data: dict = Body(...),
    db: AsyncSession = Depends(get_async_db),
    auth: Auth = Depends(get_auth)
):
    """Register a new user from Telegram data, including referral logic."""
    # Define referrer_id early to handle potential errors before user creation
    referrer_id = None
    cleaned_referral_code = None # For logging
    username = data.get('username') # Get username for logging early

    try:
        # Extract data from request body
        init_data = data.get('init_data')
        # username = data.get('username') # Already extracted
        selected_avatar = data.get('selected_avatar')
        device_id = data.get('device_id')
        device_platform = data.get('platform')
        incoming_referral_code = data.get('referral_code') # Step 1: Extract incoming code
        
        # Verify Telegram data
        is_valid, telegram_data = await verify_telegram_webapp_data(init_data, request)
        if not is_valid or not telegram_data:
            raise HTTPException(
                status_code=401,
                detail={"message": "Invalid Telegram data", "error_code": "INVALID_TG_DATA"}
            )

        # --- Step 2: Find Referrer (if code provided) ---
        if incoming_referral_code:
            cleaned_referral_code = str(incoming_referral_code).strip()
            if cleaned_referral_code:
                try:
                    referrer_stmt = select(User).where(User.referral_code == cleaned_referral_code)
                    referrer_result = await db.execute(referrer_stmt)
                    referrer = referrer_result.scalar_one_or_none()
                    if referrer:
                        referrer_id = referrer.id
                        logger.info(f"Telegram registration: User {username} referred by user ID {referrer_id} with code {cleaned_referral_code}")
                    else:
                        logger.warning(f"Telegram registration: Referral code '{cleaned_referral_code}' provided but no matching user found.")
                except Exception as find_referrer_error:
                    logger.error(f"Error finding referrer for code '{cleaned_referral_code}': {find_referrer_error}", exc_info=True)
                    # Decide if this should be fatal? For now, just log and continue without referrer.
                    referrer_id = None # Ensure referrer_id is None if lookup failed
            else:
                 logger.info("Empty referral code provided during Telegram registration.")
        # --- End Step 2 ---

        # Generate secure password
        temp_password = generate_secure_password()
        hashed_password = get_password_hash(temp_password)
        
        # Generate unique referral code for the NEW user
        # Use the helper function we created earlier
        new_user_referral_code = await generate_unique_referral_code(db)

        # Create new user object - Step 3: Link New User
        new_user = User(
            username=username,
            hashed_password=hashed_password,
            telegram_id=telegram_data["id"],
            role=Role.user,
            is_active=True,
            device_platform=device_platform,
            device_id=device_id,
            last_login=datetime.utcnow(),
            telegram_photo_url=selected_avatar,
            referral_code=new_user_referral_code, # Assign the generated unique code
            referred_by=referrer_id, # Assign the found referrer ID (or None)
            discount_percent=0.0,
            wallet_balance=0.0,
            created_at=datetime.now(timezone.utc) # Ensure consistent timezone
        )
        
        # Add user, commit, refresh within a try block for rollback
        try:
            db.add(new_user)
            await db.commit()
            await db.refresh(new_user)
            logger.info(f"Successfully created Telegram user {new_user.username} with ID {new_user.id} and referral code {new_user.referral_code}")

            # --- Step 4: Process Referral (AFTER commit) ---
            if referrer_id:
                try:
                    from services.referral_service import ReferralService
                    referral_service = ReferralService(db)
                    await referral_service.process_new_referral(new_user.id, referrer_id)
                    logger.info(f"Processed referral for new Telegram user {new_user.id} referred by {referrer_id}")
                except Exception as referral_error:
                    logger.error(f"Failed to process referral for Telegram user {new_user.id} (referrer {referrer_id}): {referral_error}", exc_info=True)
                    # Log the error, but don't let referral processing failure break the registration.
            # --- End Step 4 ---

        except SQLAlchemyError as db_error:
            await db.rollback()
            logger.error(f"Database error during Telegram registration for {username}: {db_error}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Database error during registration."
            )
        except Exception as commit_error: # Catch other potential errors during commit/refresh/referral
            await db.rollback()
            logger.error(f"Unexpected error during Telegram registration commit/referral for {username}: {commit_error}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An unexpected error occurred during registration finalization."
            )
        
        # Generate tokens using Auth class
        access_token, refresh_token = auth.create_auth_tokens(new_user)
        
        # Set cookies for new user
        auth.set_auth_cookies(response, access_token, refresh_token)
        
        # Return credentials - password will only be shown once
        return {
            "username": new_user.username,
            "temp_password": temp_password, # Only returned once, never stored
            "message": "Please save this password securely. It will not be shown again."
        }

    except HTTPException as http_exc:
        # Re-raise specific HTTP exceptions (like invalid Telegram data)
        raise http_exc
    except Exception as e:
        # Catch any other unexpected errors during initial setup/validation
        logger.error(f"Unexpected error during Telegram registration setup for {username}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={"message": "Registration failed due to an unexpected server error", "error_code": "REGISTRATION_FAILED"}
        )

@router.post("/verify-session")
async def verify_session_post(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """Verify if current session is valid using standard authentication check (POST method)."""
    return await _verify_session_logic(current_user)

@router.get("/verify-session")
async def verify_session_get(
    request: Request,
    current_user: User = Depends(get_current_user)
):
    """Verify if current session is valid using standard authentication check (GET method)."""
    return await _verify_session_logic(current_user)

async def _verify_session_logic(current_user: User):
    """Shared logic for session verification."""
    try:
        # User is already validated by the dependency injection
        if current_user:
            return {
                "status": "authenticated",
                "data": {
                    "id": current_user.id,
                    "username": current_user.username,
                    "role": current_user.role.value,
                    "telegram_id": current_user.telegram_id,
                    "is_active": current_user.is_active,
                    "wallet_balance": current_user.wallet_balance,
                    "created_at": current_user.created_at.isoformat() if current_user.created_at else None
                }
            }
        
        return {
            "status": "unauthenticated",
            "detail": "No valid session found"
        }

    except HTTPException as http_exc:
        # Handle specific HTTP exceptions (like 401, 403) as unauthenticated
        return {
            "status": "unauthenticated",
            "detail": "No valid session found"
        }
    except Exception as e:
        logger.error(f"Unexpected Session verification error: {str(e)}")
        return {
            "status": "error",
            "detail": "Unexpected server error during session verification"
        }

@router.post("/register", response_model=UserOut)
async def register(
    request: Request,
    user_data: UserCreate,
    referral_code: Optional[str] = None,
    db: AsyncSession = Depends(get_async_db)
):
    """Register a new user, handling referral logic."""
    # Check rate limits first
    client_ip = request.client.host
    redis_client_instance = request.app.state.redis
    redis_conn = await redis_client_instance.get_client()
    rate_limit_service = RateLimitService(redis_conn, db)

    # Check rate limit - this method raises RateLimitExceeded on failure by default
    try:
        await rate_limit_service.check_rate_limit(
            action="register",
            ip=client_ip,
            user_agent=request.headers.get("user-agent")
            # identifier is not needed for general registration limit
        )
    except RateLimitExceeded as e:
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=f"{e.message}. Try again in {e.reset_after} seconds.",
            headers={"Retry-After": str(e.reset_after)}
        )

    # Check for existing user with username or email
    stmt = select(User).filter(
        or_(User.username == user_data.username, User.email == user_data.email)
    )
    result = await db.execute(stmt)
    existing_user = result.scalar_one_or_none()
    
    if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username or email already registered"
            )
    
    # Generate a unique referral code for the NEW user
    new_user_referral_code = await generate_unique_referral_code(db)

    # Check if referred by someone using the provided referral_code
    referrer_id = None
    referrer = None
    if referral_code:
        # Clean the input referral code
        cleaned_referral_code = referral_code.strip()
        if cleaned_referral_code:
            referrer_stmt = select(User).where(User.referral_code == cleaned_referral_code)
            referrer_result = await db.execute(referrer_stmt)
            referrer = referrer_result.scalar_one_or_none()
            if referrer:
                referrer_id = referrer.id
                logger.info(f"New user {user_data.username} referred by user ID {referrer_id} with code {cleaned_referral_code}")
            else:
                 logger.warning(f"Referral code '{cleaned_referral_code}' provided but no matching user found.")
        else:
             logger.info("Empty referral code provided.")
            
        # Create new user
        hashed_password = get_password_hash(user_data.password)
        new_user = User(
            username=user_data.username,
            email=user_data.email,
            hashed_password=hashed_password,
        first_name=user_data.first_name,
        last_name=user_data.last_name,
        role=Role.user,  # Default role
        is_active=True,
        referral_code=new_user_referral_code, # Assign the generated unique code to the new user
        referred_by=referrer_id, # Link to the referrer if found
        created_at=datetime.now(timezone.utc)
        )
        
    try:
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        logger.info(f"Successfully created user {new_user.username} with ID {new_user.id} and referral code {new_user.referral_code}")

        # Process referral AFTER the new user is successfully committed
        if referrer_id:
            try:
                # Import here to avoid circular dependencies
                from services.referral_service import ReferralService
                referral_service = ReferralService(db)
                await referral_service.process_new_referral(new_user.id, referrer_id)
                logger.info(f"Processed referral for new user {new_user.id} referred by {referrer_id}")
            except Exception as referral_error:
                logger.error(f"Failed to process referral for user {new_user.id} (referrer {referrer_id}): {referral_error}", exc_info=True)
                # Don't fail the registration if referral processing fails, but log it.

        # Return a dictionary conforming to UserOut, not the raw object
        return {
            "id": new_user.id,
            "username": new_user.username,
            "telegram_id": new_user.telegram_id, # Might be None
            "role": new_user.role.value,
            "wallet_balance": new_user.wallet_balance,
            "created_at": new_user.created_at,
            "first_name": new_user.first_name,
            "last_name": new_user.last_name,
            "email": new_user.email,
            "is_active": new_user.is_active,
            "telegram_photo_url": new_user.telegram_photo_url, # Might be None
            "discount_percent": new_user.discount_percent,
            "referral_code": new_user.referral_code,
            "referred_by": new_user.referred_by, # Might be None
            # Explicitly set relationship fields to empty lists for UserOut
            "vpn_subscriptions": [],
            "task_completions": [],
            "daily_streak": None,
            "cards": [],
            "total_passive_hourly_income": 0.0 # Calculate if needed, default 0
        }

    except SQLAlchemyError as e:
        await db.rollback()
        logger.error(f"Database error during user registration for {user_data.username}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Database error during registration."
        )
    except Exception as e:
        await db.rollback() # Rollback on any other error during commit/referral processing
        logger.error(f"Unexpected error during registration for {user_data.username}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred during registration."
        )