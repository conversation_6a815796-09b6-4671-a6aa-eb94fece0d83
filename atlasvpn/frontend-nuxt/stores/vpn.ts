import { defineStore } from 'pinia'
import type { VPNPackage, VPNSubscription, MarzbanPanel } from '~/types'

export interface VPNState {
  // VPN data
  packages: VPNPackage[]
  subscriptions: VPNSubscription[]
  panels: MarzbanPanel[]
  
  // Loading states
  isLoadingPackages: boolean
  isLoadingSubscriptions: boolean
  isLoadingPanels: boolean
  isPurchasing: boolean
  
  // Error states
  packagesError: string | null
  subscriptionsError: string | null
  panelsError: string | null
  purchaseError: string | null
  
  // Cache timestamps
  lastPackagesFetch: number
  lastSubscriptionsFetch: number
  lastPanelsFetch: number
  
  // UI state
  selectedPackage: VPNPackage | null
  selectedPanel: MarzbanPanel | null
}

export const useVPNStore = defineStore('vpn', {
  state: (): VPNState => ({
    // VPN data
    packages: [],
    subscriptions: [],
    panels: [],
    
    // Loading states
    isLoadingPackages: false,
    isLoadingSubscriptions: false,
    isLoadingPanels: false,
    isPurchasing: false,
    
    // Error states
    packagesError: null,
    subscriptionsError: null,
    panelsError: null,
    purchaseError: null,
    
    // Cache timestamps
    lastPackagesFetch: 0,
    lastSubscriptionsFetch: 0,
    lastPanelsFetch: 0,
    
    // UI state
    selectedPackage: null,
    selectedPanel: null
  }),

  persist: {
    key: 'atlas-vpn-store',
    storage: typeof window !== 'undefined' ? localStorage : undefined,
    pick: [
      'selectedPackage',
      'selectedPanel'
    ]
  },

  getters: {
    // Package filtering
    activePackages: (state) => 
      state.packages.filter(pkg => pkg.is_active),
    
    packagesByPrice: (state) => 
      [...state.packages].sort((a, b) => a.price - b.price),
    
    // Subscription filtering
    activeSubscriptions: (state) => 
      state.subscriptions.filter(sub => 
        sub.is_active && new Date(sub.expires_at) > new Date()
      ),
    
    expiredSubscriptions: (state) => 
      state.subscriptions.filter(sub => 
        !sub.is_active || new Date(sub.expires_at) <= new Date()
      ),
    
    // Panel filtering
    availablePanels: (state) => 
      state.panels.filter(panel => panel.is_active),
    
    // Data usage calculations
    totalDataUsed: (state) => 
      state.subscriptions.reduce((total, sub) => total + sub.data_used, 0),
    
    totalDataLimit: (state) => 
      state.activeSubscriptions.reduce((total, sub) => total + sub.data_limit, 0),
    
    dataUsagePercentage: (state) => {
      const totalLimit = state.subscriptions
        .filter(sub => sub.is_active && new Date(sub.expires_at) > new Date())
        .reduce((total, sub) => total + sub.data_limit, 0)
      
      if (totalLimit === 0) return 0
      
      const totalUsed = state.subscriptions
        .filter(sub => sub.is_active && new Date(sub.expires_at) > new Date())
        .reduce((total, sub) => total + sub.data_used, 0)
      
      return Math.min((totalUsed / totalLimit) * 100, 100)
    },
    
    // Subscription status
    hasActiveSubscription: (state) => 
      state.subscriptions.some(sub => 
        sub.is_active && new Date(sub.expires_at) > new Date()
      ),
    
    nextExpiringSubscription: (state) => {
      const active = state.subscriptions.filter(sub => 
        sub.is_active && new Date(sub.expires_at) > new Date()
      )
      
      if (active.length === 0) return null
      
      return active.reduce((earliest, current) => 
        new Date(current.expires_at) < new Date(earliest.expires_at) ? current : earliest
      )
    },
    
    // Package recommendations
    recommendedPackage: (state) => {
      // Simple logic: recommend the most popular mid-range package
      const sorted = [...state.packages]
        .filter(pkg => pkg.is_active)
        .sort((a, b) => a.price - b.price)
      
      const midIndex = Math.floor(sorted.length / 2)
      return sorted[midIndex] || null
    },
    
    // Cache status
    isPackagesStale: (state) => {
      const CACHE_TTL = 10 * 60 * 1000 // 10 minutes
      return Date.now() - state.lastPackagesFetch > CACHE_TTL
    },
    
    isSubscriptionsStale: (state) => {
      const CACHE_TTL = 2 * 60 * 1000 // 2 minutes
      return Date.now() - state.lastSubscriptionsFetch > CACHE_TTL
    },
    
    isPanelsStale: (state) => {
      const CACHE_TTL = 30 * 60 * 1000 // 30 minutes
      return Date.now() - state.lastPanelsFetch > CACHE_TTL
    }
  },

  actions: {
    // Package management
    setPackages(packages: VPNPackage[]) {
      this.packages = packages
      this.lastPackagesFetch = Date.now()
    },

    updatePackage(packageId: number, updates: Partial<VPNPackage>) {
      const index = this.packages.findIndex(pkg => pkg.id === packageId)
      if (index !== -1) {
        this.packages[index] = { ...this.packages[index], ...updates }
      }
    },

    // Subscription management
    setSubscriptions(subscriptions: VPNSubscription[]) {
      this.subscriptions = subscriptions
      this.lastSubscriptionsFetch = Date.now()
    },

    addSubscription(subscription: VPNSubscription) {
      this.subscriptions.unshift(subscription)
    },

    updateSubscription(subscriptionId: number, updates: Partial<VPNSubscription>) {
      const index = this.subscriptions.findIndex(sub => sub.id === subscriptionId)
      if (index !== -1) {
        this.subscriptions[index] = { ...this.subscriptions[index], ...updates }
      }
    },

    removeSubscription(subscriptionId: number) {
      this.subscriptions = this.subscriptions.filter(sub => sub.id !== subscriptionId)
    },

    // Panel management
    setPanels(panels: MarzbanPanel[]) {
      this.panels = panels
      this.lastPanelsFetch = Date.now()
    },

    updatePanel(panelId: number, updates: Partial<MarzbanPanel>) {
      const index = this.panels.findIndex(panel => panel.id === panelId)
      if (index !== -1) {
        this.panels[index] = { ...this.panels[index], ...updates }
      }
    },

    // Loading states
    setPackagesLoading(loading: boolean) {
      this.isLoadingPackages = loading
    },

    setSubscriptionsLoading(loading: boolean) {
      this.isLoadingSubscriptions = loading
    },

    setPanelsLoading(loading: boolean) {
      this.isLoadingPanels = loading
    },

    setPurchasing(purchasing: boolean) {
      this.isPurchasing = purchasing
    },

    // Error states
    setPackagesError(error: string | null) {
      this.packagesError = error
    },

    setSubscriptionsError(error: string | null) {
      this.subscriptionsError = error
    },

    setPanelsError(error: string | null) {
      this.panelsError = error
    },

    setPurchaseError(error: string | null) {
      this.purchaseError = error
    },

    // UI state
    selectPackage(pkg: VPNPackage | null) {
      this.selectedPackage = pkg
    },

    selectPanel(panel: MarzbanPanel | null) {
      this.selectedPanel = panel
    },

    // Utility actions
    getPackageById(packageId: number): VPNPackage | undefined {
      return this.packages.find(pkg => pkg.id === packageId)
    },

    getSubscriptionById(subscriptionId: number): VPNSubscription | undefined {
      return this.subscriptions.find(sub => sub.id === subscriptionId)
    },

    getPanelById(panelId: number): MarzbanPanel | undefined {
      return this.panels.find(panel => panel.id === panelId)
    },

    // Data formatting utilities
    formatDataUsage(bytes: number): string {
      if (bytes === 0) return '0 GB'
      const gb = bytes / (1024 * 1024 * 1024)
      return `${gb.toFixed(2)} GB`
    },

    formatPrice(price: number): string {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(price)
    },

    getDaysUntilExpiry(expiresAt: string): number {
      const now = new Date()
      const expiry = new Date(expiresAt)
      const diffTime = expiry.getTime() - now.getTime()
      return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    },

    // Clear all data
    clearVPNData() {
      this.packages = []
      this.subscriptions = []
      this.panels = []
      this.packagesError = null
      this.subscriptionsError = null
      this.panelsError = null
      this.purchaseError = null
      this.lastPackagesFetch = 0
      this.lastSubscriptionsFetch = 0
      this.lastPanelsFetch = 0
      this.selectedPackage = null
      this.selectedPanel = null
    }
  }
})
