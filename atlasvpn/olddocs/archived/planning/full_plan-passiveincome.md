# Full Plan: Card System with Hourly Profit & Total Passive Income (Manual Schema <PERSON>)

This plan outlines the steps to implement a collectible card system where cards generate hourly passive profit, capped at 3 hours claimable at a time, and calculate the user's total hourly passive income rate. **Database schema changes will be applied via a custom Python script instead of Alembic.**

## Economic Rationale: The Seed Economy of Atlas Kombat

**(Start of Economic Novel Section)**

**The Genesis of Seeds: A Web3 Airdrop Ecosystem**

In the burgeoning digital realm of Atlas Kombat, the core economic lifeblood flows not from traditional fiat, but from **Seeds** - the native in-game currency and potentially a future cryptographic token. This isn't just a game; it's envisioned as a self-sustaining micro-economy built on web3 principles, rewarding engagement, strategy, and community participation, with an eye towards a future airdrop for early adopters and active players.

The initial distribution of Seeds and valuable Card NFTs (Non-Fungible Tokens, representing the unique ownership of leveled cards) is designed as a multi-stage airdrop campaign. Early sign-ups, beta testers, and achievers of specific onboarding milestones receive a foundational allocation, bootstrapping the economy and incentivizing initial growth. This initial seeding ensures that value isn't just created out of thin air but is tied to the genesis of the Atlas Kombat universe.

**Earning Seeds: Proof-of-Play and Engagement**

Seed generation is intrinsically linked to player activity – a concept akin to "Proof-of-Play." The primary mechanisms are:

1.  **Task Completion:** The most direct way to earn. Tasks represent contributions to the ecosystem – engaging with social features, completing tutorials, participating in community events, or even providing valuable feedback. Rewards are calibrated based on effort and value to the platform, ensuring active participation is the most reliable path to wealth.
2.  **Card-Based Passive Income:** This represents a player's strategic investment within the game. Acquiring and upgrading **Atlas Cards** (which could be NFTs) allows players to generate Seeds passively over time. This isn't free money; it's a return on investment for resources spent (earned Seeds) on acquiring and leveling these assets. The 3-hour claim cap acts as an anti-inflationary measure and an engagement mechanic, requiring players to return periodically to claim their yield, keeping the ecosystem active.
3.  **Mini-Games & Challenges:** Optional, skill-based or strategic mini-games offer additional earning opportunities. These act as dynamic faucets, potentially requiring small Seed entry fees (sinks) to participate, adding a layer of risk/reward and ensuring these aren't purely inflationary mechanisms.
4.  **Referrals & Community Growth:** Incentivizing network effects. Players are rewarded (with Seeds or exclusive Card NFTs) for bringing new, active users into the ecosystem, directly contributing to its growth and value.

**Spending Seeds: Utility and Value Circulation**

For the Seed economy to thrive, the currency needs tangible utility – reasons to spend and circulate value. Key Seed sinks include:

1.  **Card Upgrades:** The primary progression sink. Leveling up Card NFTs requires significant Seed investment, reflecting the increased passive earning potential. The escalating costs ensure a deflationary pressure, requiring players to reinvest their earnings for greater long-term yields.
2.  **Card Acquisition (Packs/Marketplace):** While some cards are earned, rarer or specific cards might be acquired through Seed-purchased **Booster Packs** (offering randomized drops) or eventually, a player-to-player marketplace where Card NFTs can be traded using Seeds as the medium of exchange.
3.  **Mini-Game Entry Fees:** As mentioned, providing risk/reward loops.
4.  **Cosmetics & Customization:** Non-essential sinks like profile badges, avatar flair, or habitat decorations allow players to express individuality without impacting core gameplay balance.
5.  **Future Utility:** Potential integration with other web3 platforms, staking mechanisms for earning yield on Seeds themselves, or governance rights tied to Seed holding.

**Inflation Control & Sustainability: The Balancing Act**

The longevity of the Atlas Kombat economy hinges on carefully balancing Seed sources (faucets) and sinks. The goal is controlled inflation that encourages spending and investment, rather than runaway devaluation or hyper-deflation that stalls activity.

*   **Claim Cap:** Prevents infinite offline farming.
*   **Exponential Costs:** Upgrade costs rise faster than profit generation, requiring strategic reinvestment.
*   **Limited Resources:** Card drops from tasks or packs are managed to control the supply of high-earning assets.
*   **Data-Driven Adjustments:** The development team must actively monitor the Seed velocity, total supply, faucet/sink rates, and player behavior, making adjustments to task rewards, upgrade costs, pack odds, and event parameters as needed to maintain equilibrium. This requires robust analytics.
*   **Potential Token Burn:** If Seeds become a true crypto token, mechanisms like burning a percentage of marketplace transaction fees or specific sink activities can introduce deflationary pressure.

**The Airdrop Vision: Rewarding the Pioneers**

The ultimate goal of this web3-inspired economy is to reward the community that builds it. Active participation, strategic card collection and upgrading, and contributions to growth are tracked. A future potential airdrop of a governance or utility token (perhaps distinct from, or convertible with, Seeds) would target these engaged users, granting them a tangible stake in the success and future direction of Atlas Kombat. The Card NFTs themselves, representing leveled, valuable in-game assets, would hold inherent value within this ecosystem and potentially on external marketplaces.

By intertwining engaging gameplay with sound economic principles and web3 potential, Atlas Kombat aims to create more than just a game – it seeks to build a player-driven, sustainable digital nation powered by Seeds.

**(End of Economic Novel Section)**

---

## Phase 1: Backend Implementation (Current Focus)

- [ ] **1. Implement Database Models (`models.py`)**
    - [ ] Define `CardCatalog` model (id, name, rarity, `level_profits_json`, `level_costs_json`, max_level, `image_url`, created_at). *(Replaced base profit/cost and growth rates with JSON fields)*.
    - [ ] Define `UserCard` model (id, user_id, card_catalog_id, level, last_claim_at, acquired_at).
    - [ ] Add relationship `cards: Mapped[List["UserCard"]]` to `User` model.
    - [ ] Update calculated property `current_hourly_profit` on `UserCard` to read from `level_profits_json` based on `level`.
    - [ ] Update calculated property `next_upgrade_cost` on `UserCard` to read from `level_costs_json` based on `level`.
    - [ ] Add calculated property `claimable_profit` (with 3-hour cap) to `UserCard`.
    - [ ] Add calculated property `total_passive_hourly_income` to `User` model (summing profit from `user.cards`).
    - [ ] Add new `TransactionType` enum values: `card_profit`, `card_profit_all`, `card_upgrade`.
- [ ] **2. Implement Pydantic Schemas (`schemas.py`)**
    - [ ] Define `CardCatalog` schemas (Base, Create, Out) reflecting JSON fields for profit/cost per level.
    - [ ] Define `UserCard` schemas (Base, Create, Out - including calculated properties based on new model logic).
    - [ ] Define `ClaimResult`, `UpgradeResult`, `ClaimAllResponse` schemas.
    - [ ] Add `total_passive_hourly_income: float` field to `UserOut` schema.
- [ ] **3. Implement API Router (`routers/card_router.py`)**
    - [ ] Create new file `backend/routers/card_router.py`.
    - [ ] Add endpoint `GET /api/cards/catalog` (list card types).
    - [ ] Add endpoint `GET /api/cards/my-cards` (list user's cards, eager load catalog).
    - [ ] Add endpoint `POST /api/cards/{user_card_id}/claim` (claim single card profit, update wallet, log transaction).
    - [ ] Add endpoint `POST /api/cards/claim-all` (claim all cards profit, update wallet, log transaction).
    - [ ] Add endpoint `POST /api/cards/{user_card_id}/upgrade` (upgrade card, deduct cost, log transaction).
    - [ ] Add helper endpoint `POST /api/cards/grant-card` (Admin/Debug: give card to user).
- [ ] **4. Integrate Router (`main.py`)**
    - [ ] Import `card_router` in `main.py`.
    - [ ] Include `card_router.router` in the FastAPI app instance.
- [ ] **5. Update User Endpoints for Total Profit (`routers/auth_router.py` or similar)**
    - [ ] Identify endpoints returning `UserOut` (e.g., `/users/me`).
    - [ ] Implement eager loading (`selectinload`) for `User.cards` and nested `UserCard.card_catalog` in relevant queries.
- [ ] **6. Database Schema Creation (Manual Script)**
    - [X] Create script `backend/create_card_tables.py`.
    - [X] Implement logic in script to connect to DB (`users.db`).
    - [X] Add `CREATE TABLE IF NOT EXISTS card_catalog ...` statement with new JSON columns and `image_url`.
    - [X] Add `CREATE TABLE IF NOT EXISTS user_cards ...` statement.
    - [X] Add relevant `CREATE INDEX IF NOT EXISTS ...` statements.
    - [ ] **Manual Step:** Run the `python backend/create_card_tables.py` script.
- [ ] **7. Populate Card Catalog**
    - [ ] **Manual/Scripted Step:** Add entries to the `card_catalog` table defining the available cards. This now requires providing the full JSON strings for `level_profits_json` and `level_costs_json` for each card type (e.g., `'[1.0, 1.2, 1.5, ...]'` for profits, `'[50, 75, 110, ...]'` for costs).

## Phase 2: Frontend Implementation (Future)

- [ ] **1. Card Display UI**
    - [ ] Component to display individual cards (image, name, level, profit/hr).
    - [ ] Card gallery/collection view for the user.
    - [ ] Display total passive hourly income rate.
- [ ] **2. Claim UI**
    - [ ] Display claimable profit amount (total and/or per card).
    - [ ] "Claim All" button.
    - [ ] Individual "Claim" buttons (optional).
    - [ ] Visual feedback on successful claim.
- [ ] **3. Upgrade UI**
    - [ ] "Upgrade" button on card details.
    - [ ] Display next level cost (read from level data) and profit increase.
    - [ ] Confirmation dialog.
    - [ ] Visual feedback on successful upgrade.
- [ ] **4. Card Acquisition UI**
    - [ ] Interface for opening card packs (if implemented).
    - [ ] Display cards earned from tasks/achievements.

## Phase 3: Testing & Refinement

- [ ] **1. Backend Testing**
    - [ ] Unit tests for model properties (reading JSON, calculating profit/cost).
    - [ ] Integration tests for all API endpoints.
    - [ ] Test edge cases (zero cards, max level, insufficient funds, claim cap, invalid JSON data).
- [ ] **2. Frontend Testing**
    - [ ] Component tests for UI elements.
    - [ ] End-to-end tests for claim/upgrade flows.
- [ ] **3. Economy Balancing**
    - [ ] Monitor coin generation vs. sinks.
    - [ ] Adjust predefined level profit/cost JSON data in `card_catalog` as needed.
    - [ ] Adjust task rewards, pack odds, etc. 