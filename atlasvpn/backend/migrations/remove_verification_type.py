"""Remove verification_type column and associated indexes

This migration removes the verification_type column from tasks and social_tasks tables,
along with their associated indexes.
"""

from typing import Sequence
from alembic import op
import sqlalchemy as sa
from sqlalchemy.engine.reflection import Inspector


# revision identifiers, used by Alembic
revision = 'remove_verification_type'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Drop indexes first
    conn = op.get_bind()
    inspector = Inspector.from_engine(conn)
    
    # Drop verification type indexes if they exist
    indexes = inspector.get_indexes('tasks')
    if any(idx['name'] == 'idx_task_verification' for idx in indexes):
        op.drop_index('idx_task_verification', table_name='tasks')
    
    indexes = inspector.get_indexes('social_tasks')
    if any(idx['name'] == 'idx_social_tasks_verification' for idx in indexes):
        op.drop_index('idx_social_tasks_verification', table_name='social_tasks')
    
    # Drop verification_type column from tasks table if it exists
    with op.batch_alter_table('tasks') as batch_op:
        batch_op.drop_column('verification_type')
    
    # Drop verification_type column from social_tasks table if it exists
    with op.batch_alter_table('social_tasks') as batch_op:
        batch_op.drop_column('verification_type')


def downgrade() -> None:
    # Add verification_type column back to tasks
    with op.batch_alter_table('tasks') as batch_op:
        batch_op.add_column(sa.Column('verification_type', sa.String(), nullable=True))
    
    # Add verification_type column back to social_tasks
    with op.batch_alter_table('social_tasks') as batch_op:
        batch_op.add_column(sa.Column('verification_type', sa.String(), nullable=True))
    
    # Recreate indexes
    op.create_index('idx_task_verification', 'tasks',
                   ['type', 'verification_type', 'is_active'])
    op.create_index('idx_social_tasks_verification', 'social_tasks',
                   ['verification_type', 'is_active']) 