# Add directories or file patterns to ignore during indexing (e.g. foo/ or *.csv)

# Frontend ignores
frontend/node_modules/
frontend/.next/
frontend/build/
frontend/dist/
frontend/coverage/
frontend/.cache/
frontend/public/build/
frontend/build1/
frontend/pnpm-lock.yaml

# Backend ignores
backend/__pycache__/
backend/**/__pycache__/
backend/.pytest_cache/
backend/.mypy_cache/
backend/.coverage
backend/htmlcov/
backend/.tox/
backend/reports/

# Database files
backend/*.db
backend/*.db-shm
backend/*.db-wal
backend/database_debug_results.json
backend/*.backup_*
backend/security_results.txt

# Log files
backend/*.log
backend/logs/

# Other backend directories to ignore
backend/backups/
backend/test_reports/
backend/exports/

# Virtual environment ignores
backend/venv/
backend/.venv/
backend/*.pyc

# Common large files
*.csv
*.parquet
*.sqlite
*.sqlite3
archive/backups
archive/database
archive/scripts
archive/others
archive/logs
archive/reports
archive/monitoring
archive/setup
archive/exports