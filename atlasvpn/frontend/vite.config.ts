import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react-swc';
import tailwindcss from '@tailwindcss/vite';
import { visualizer } from 'rollup-plugin-visualizer';
import * as path from 'path';

// Define proxy configuration - only used in development mode
const createProxyConfig = (env: Record<string, any>) => {
  const backendUrl = env.VITE_API_URL || 'http://localhost:8000';
  const wsUrl = env.VITE_WS_BASE_URL || 'ws://localhost:8000/ws';

  return {
    '/api': {
      target: backendUrl,
      changeOrigin: true,
      secure: false,
      ws: true,
      rewrite: (path: string) => path.replace(/^\/api/, ''),
    },
    '/ws': {
      target: wsUrl.replace(/^ws/, 'http'),
      changeOrigin: true,
      secure: false,
      ws: true,
    },
    '/docs': {
      target: backendUrl,
      changeOrigin: true,
      secure: false,
    },
    '/redoc': {
      target: backendUrl,
      changeOrigin: true,
      secure: false,
    },
    '/openapi.json': {
      target: backendUrl,
      changeOrigin: true,
      secure: false,
    },
  };
};

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const isProduction = mode === 'production';
  const isDevelopment = mode === 'development';

  // Create proxy config based on environment
  const proxyConfig = isDevelopment ? createProxyConfig(env) : {};

  return {
    base: isDevelopment ? '/' : '/',
    plugins: [
      react({
        plugins: [],
        jsxImportSource: 'react',
      }),
      tailwindcss(),
      // Add bundle analyzer for production builds
      isProduction && visualizer({
        open: true,
        gzipSize: true,
        brotliSize: true,
        template: 'treemap',
        filename: 'stats.html',
      }),
    ].filter(Boolean),
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '~': path.resolve(__dirname, './src'),
      },
    },
    server: {
      host: '0.0.0.0',
      port: 3000,
      strictPort: true,
      open: false,
      cors: true,
      // Workaround for Vite 6.0.9+ allowedHosts bug
      // Use 'all' to allow all hosts (safe behind NGINX proxy)
      allowedHosts: 'all',
      hmr: {
        port: 3000,
        // Use 0.0.0.0 to allow connections from any host (NGINX proxy)
        host: '0.0.0.0',
        protocol: 'ws',
        clientPort: 3000,
      },
      proxy: proxyConfig,
      watch: {
        usePolling: false,
        interval: 300,
      },
      headers: {
        'Cross-Origin-Embedder-Policy': 'credentialless',
        'Cross-Origin-Opener-Policy': 'same-origin',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Origin, Content-Type, Accept',
      },
    },
    build: {
      outDir: 'dist',
      sourcemap: false,
      minify: 'esbuild',
      target: 'es2020',
      reportCompressedSize: true,
      chunkSizeWarningLimit: 200,
      base: isProduction ? '/' : '/',
      define: {
        'process.env.NODE_ENV': JSON.stringify(isProduction ? 'production' : 'development'),
        'import.meta.env.VITE_WS_BASE_URL': JSON.stringify(env.VITE_WS_BASE_URL),
      },
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Core React - keep small and separate
            if (id.includes('react') && !id.includes('react-router') && !id.includes('react-three') && !id.includes('react-icons')) {
              return 'react-core';
            }
            
            // Router - separate chunk
            if (id.includes('react-router')) {
              return 'router';
            }
            
            // THREE.JS AND 3D GRAPHICS - BIGGEST OPTIMIZATION
            if (id.includes('three') || id.includes('@react-three') || id.includes('three-stdlib')) {
              return 'three-graphics';
            }
            
            // FABRIC.JS - Canvas drawing library
            if (id.includes('fabric')) {
              return 'fabric-canvas';
            }
            
            // FRAMER MOTION - Animation library
            if (id.includes('framer-motion') || id.includes('motion-dom')) {
              return 'animations';
            }
            
            // Icons - separate chunk
            if (id.includes('react-icons') || id.includes('@heroicons') || id.includes('lucide-react')) {
              return 'icons';
            }
            
            // Form libraries
            if (id.includes('react-hook-form') || id.includes('react-colorful') || id.includes('react-select')) {
              return 'forms';
            }
            
            // Utilities and crypto
            if (id.includes('crypto-js') || id.includes('date-fns') || id.includes('lodash') || id.includes('clsx') || id.includes('tailwind-merge')) {
              return 'utilities';
            }
            
            // Telegram and external services
            if (id.includes('@telegram-apps') || id.includes('@sentry') || id.includes('axios')) {
              return 'external-services';
            }
            
            // State management
            if (id.includes('zustand') || id.includes('@tanstack/react-query')) {
              return 'state-management';
            }
            
            // Toast and UI feedback
            if (id.includes('react-hot-toast') || id.includes('emoji-picker')) {
              return 'ui-feedback';
            }
            
            // App pages - lazy loaded
            if (id.includes('src/pages/')) {
              const pageName = id.split('src/pages/')[1].split('/')[0].replace('.tsx', '').replace('.ts', '');
              return `page-${pageName}`;
            }
            
            // Verse components - heavy 3D stuff
            if (id.includes('src/components/verse/')) {
              return 'verse-components';
            }
            
            // Other app chunks
            if (id.includes('src/hooks/')) {
              return 'hooks';
            }
            
            if (id.includes('src/stores/')) {
              return 'stores';
            }
            
            // Everything else from node_modules
            if (id.includes('node_modules')) {
              return 'vendor-misc';
            }
          },
          chunkFileNames: 'js/[name]-[hash].js',
          entryFileNames: 'js/[name]-[hash].js',
          assetFileNames: '[ext]/[name]-[hash][extname]',
        },
      },
      assetsInlineLimit: 2048,
    },
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-dom/client',
        'react-router-dom',
        'react/jsx-runtime',
        'react/jsx-dev-runtime',
        '@tanstack/react-query',
        'axios',
        'zustand',
        'react-hot-toast',
        'crypto-js',
        'date-fns',
        // Include icon libraries for proper resolution
        'react-icons',
        'react-icons/fa',
        'react-icons/md',
        'react-icons/hi',
        'react-icons/ai',
        'react-icons/bs',
        'react-icons/fi',
        'react-icons/go',
        'react-icons/gr',
        'react-icons/im',
        'react-icons/io',
        'react-icons/io5',
        'react-icons/ri',
        'react-icons/si',
        'react-icons/ti',
        'react-icons/vsc',
        '@heroicons/react',
        '@heroicons/react/24/outline',
        '@heroicons/react/24/solid',
        '@heroicons/react/20/solid',
        'lucide-react',
      ],
      exclude: [
        'three',
        '@react-three/fiber',
        '@react-three/drei',
        'three-stdlib',
        'fabric',
        'react-canvas-draw',
        'framer-motion',
        'motion-utils',
        'dompurify',
        '@mediapipe/tasks-vision',
        'camera-controls',
        'meshline',
        'tunnel-rat',
        '@tanstack/react-query-devtools',
        '@sentry/browser',
        '@sentry/react',
        'react-hook-form',
        'react-colorful',
        'react-select',
        'qrcode.react',
        'emoji-picker-react',
        'react-type-animation',
      ],
      force: isDevelopment,
      holdUntilCrawlEnd: !isDevelopment,
      noDiscovery: isDevelopment,
      esbuildOptions: {
        target: 'es2020',
        jsx: 'automatic',
      },
    },
    css: {
      devSourcemap: false,
      modules: {
        localsConvention: 'camelCase',
      },
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/styles/variables";`,
        },
      },
    },
    worker: {
      format: 'es',
      rollupOptions: {
        output: {
          format: 'es',
          chunkFileNames: 'assets/worker/[name]-[hash].js',
          entryFileNames: 'assets/worker/[name]-[hash].js',
        },
      },
      sourcemap: !isProduction,
    },
    cacheDir: 'node_modules/.vite',
    logLevel: isDevelopment ? 'error' : 'warn',
    cssCodeSplit: isProduction,
    modulePreload: {
      polyfill: false,
    },
    ssr: {
      noExternal: true,
    },
    define: {
      'process.env.NODE_ENV': JSON.stringify(isProduction ? 'production' : 'development'),
      'import.meta.env.VITE_API_URL': JSON.stringify(env.VITE_API_URL),
      'import.meta.env.VITE_WS_BASE_URL': JSON.stringify(env.VITE_WS_BASE_URL),
      // React 18 environment
      'process.env.REACT_VERSION': JSON.stringify('18'),
    },

    esbuild: {
      target: 'es2020',
      keepNames: true,
    },
  };
});