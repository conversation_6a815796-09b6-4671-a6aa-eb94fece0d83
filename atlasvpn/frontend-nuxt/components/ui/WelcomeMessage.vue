<template>
  <div 
    v-if="show"
    class="welcome-message relative overflow-hidden rounded-xl border border-blue-500/20 bg-gradient-to-br from-blue-900/20 to-purple-900/20 backdrop-blur-sm p-4 mb-6"
  >
    <!-- Animated Background -->
    <div class="absolute inset-0 opacity-30">
      <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-blue-500/10 animate-pulse"></div>
    </div>

    <!-- Close Button -->
    <UButton
      @click="dismiss"
      variant="ghost"
      size="sm"
      icon="i-heroicons-x-mark"
      class="absolute top-2 right-2 text-gray-400 hover:text-white"
    />

    <div class="relative">
      <!-- Welcome Icon -->
      <div class="flex items-center justify-center mb-3">
        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center animate-float">
          <Icon name="i-heroicons-hand-raised" class="w-6 h-6 text-white" />
        </div>
      </div>

      <!-- Welcome Text -->
      <div class="text-center">
        <h3 class="text-lg font-bold text-white mb-2">
          {{ isNewUser ? 'Welcome to AtlasVPN!' : 'Welcome back!' }}
        </h3>
        <p class="text-gray-300 text-sm mb-4">
          {{ welcomeText }}
        </p>

        <!-- Action Buttons -->
        <div class="flex space-x-2 justify-center">
          <UButton
            v-if="isNewUser"
            @click="navigateTo('/dashboard/earn')"
            color="primary"
            size="sm"
          >
            Start Earning
          </UButton>
          <UButton
            v-else
            @click="navigateTo('/dashboard/premium')"
            color="primary"
            size="sm"
          >
            Explore Premium
          </UButton>
          
          <UButton
            @click="navigateTo('/verse')"
            variant="outline"
            size="sm"
          >
            Visit AtlasVerse
          </UButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  isNewUser?: boolean
  username?: string
}

const props = withDefaults(defineProps<Props>(), {
  isNewUser: false,
  username: 'User'
})

const uiStore = useUiStore()

const show = computed(() => uiStore.showWelcomeMessage)

const welcomeText = computed(() => {
  if (props.isNewUser) {
    return `Hi ${props.username}! Complete tasks, earn rewards, and explore our secure VPN services. Start your journey now!`
  } else {
    return `Great to see you again, ${props.username}! Check out your latest earnings and explore new features.`
  }
})

const dismiss = () => {
  uiStore.dismissWelcomeMessage()
}
</script>

<style scoped>
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}
</style>
