/**
 * Haptic feedback utilities for enhanced user experience
 * Provides consistent haptic feedback across the application
 */

import { triggerHapticFeedback } from './telegram';
import { TELEGRAM_CONFIG } from './constants';

/**
 * Haptic feedback patterns
 */
export const HAPTIC_PATTERNS = {
  // Impact feedback
  LIGHT_IMPACT: 'light',
  MEDIUM_IMPACT: 'medium',
  HEAVY_IMPACT: 'heavy',
  RIGID_IMPACT: 'rigid',
  SOFT_IMPACT: 'soft',
  
  // Notification feedback
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
  
  // Selection feedback
  SELECTION: 'selection',
} as const;

/**
 * Haptic feedback manager class
 */
class HapticManager {
  private isEnabled: boolean = true;
  private lastFeedbackTime: number = 0;
  private minInterval: number = 50; // Minimum interval between haptic feedback (ms)

  /**
   * Enable haptic feedback
   */
  enable(): void {
    this.isEnabled = true;
  }

  /**
   * Disable haptic feedback
   */
  disable(): void {
    this.isEnabled = false;
  }

  /**
   * Check if haptic feedback is enabled
   */
  isHapticEnabled(): boolean {
    return this.isEnabled;
  }

  /**
   * Set minimum interval between haptic feedback
   * @param interval - Minimum interval in milliseconds
   */
  setMinInterval(interval: number): void {
    this.minInterval = interval;
  }

  /**
   * Trigger haptic feedback with throttling
   * @param type - Type of haptic feedback
   * @param style - Style of haptic feedback
   */
  private triggerFeedback(
    type: 'impact' | 'notification' | 'selection',
    style?: string
  ): void {
    if (!this.isEnabled) return;

    const now = Date.now();
    if (now - this.lastFeedbackTime < this.minInterval) return;

    this.lastFeedbackTime = now;
    triggerHapticFeedback(type, style as any);
  }

  /**
   * Trigger light impact feedback
   * Used for subtle interactions like button taps
   */
  lightImpact(): void {
    this.triggerFeedback('impact', HAPTIC_PATTERNS.LIGHT_IMPACT);
  }

  /**
   * Trigger medium impact feedback
   * Used for standard interactions like button presses
   */
  mediumImpact(): void {
    this.triggerFeedback('impact', HAPTIC_PATTERNS.MEDIUM_IMPACT);
  }

  /**
   * Trigger heavy impact feedback
   * Used for important interactions like confirmations
   */
  heavyImpact(): void {
    this.triggerFeedback('impact', HAPTIC_PATTERNS.HEAVY_IMPACT);
  }

  /**
   * Trigger rigid impact feedback
   * Used for precise interactions
   */
  rigidImpact(): void {
    this.triggerFeedback('impact', HAPTIC_PATTERNS.RIGID_IMPACT);
  }

  /**
   * Trigger soft impact feedback
   * Used for gentle interactions
   */
  softImpact(): void {
    this.triggerFeedback('impact', HAPTIC_PATTERNS.SOFT_IMPACT);
  }

  /**
   * Trigger success notification feedback
   * Used for successful operations
   */
  success(): void {
    this.triggerFeedback('notification', HAPTIC_PATTERNS.SUCCESS);
  }

  /**
   * Trigger warning notification feedback
   * Used for warning messages
   */
  warning(): void {
    this.triggerFeedback('notification', HAPTIC_PATTERNS.WARNING);
  }

  /**
   * Trigger error notification feedback
   * Used for error messages
   */
  error(): void {
    this.triggerFeedback('notification', HAPTIC_PATTERNS.ERROR);
  }

  /**
   * Trigger selection feedback
   * Used for selection changes
   */
  selection(): void {
    this.triggerFeedback('selection');
  }

  /**
   * Trigger feedback for button interactions
   * @param type - Button type
   */
  button(type: 'primary' | 'secondary' | 'danger' | 'ghost' = 'primary'): void {
    switch (type) {
      case 'primary':
        this.mediumImpact();
        break;
      case 'secondary':
        this.lightImpact();
        break;
      case 'danger':
        this.heavyImpact();
        break;
      case 'ghost':
        this.softImpact();
        break;
    }
  }

  /**
   * Trigger feedback for navigation
   * @param type - Navigation type
   */
  navigation(type: 'tab' | 'page' | 'modal' = 'tab'): void {
    switch (type) {
      case 'tab':
        this.selection();
        break;
      case 'page':
        this.lightImpact();
        break;
      case 'modal':
        this.mediumImpact();
        break;
    }
  }

  /**
   * Trigger feedback for form interactions
   * @param type - Form interaction type
   */
  form(type: 'input' | 'submit' | 'error' | 'success' = 'input'): void {
    switch (type) {
      case 'input':
        this.selection();
        break;
      case 'submit':
        this.mediumImpact();
        break;
      case 'error':
        this.error();
        break;
      case 'success':
        this.success();
        break;
    }
  }

  /**
   * Trigger feedback for game interactions
   * @param type - Game interaction type
   */
  game(type: 'collect' | 'upgrade' | 'achievement' | 'fail' = 'collect'): void {
    switch (type) {
      case 'collect':
        this.success();
        break;
      case 'upgrade':
        this.heavyImpact();
        break;
      case 'achievement':
        // Double success feedback for achievements
        this.success();
        setTimeout(() => this.success(), 100);
        break;
      case 'fail':
        this.error();
        break;
    }
  }

  /**
   * Trigger feedback for card interactions
   * @param type - Card interaction type
   */
  card(type: 'tap' | 'swipe' | 'flip' | 'drag' = 'tap'): void {
    switch (type) {
      case 'tap':
        this.lightImpact();
        break;
      case 'swipe':
        this.selection();
        break;
      case 'flip':
        this.mediumImpact();
        break;
      case 'drag':
        this.rigidImpact();
        break;
    }
  }

  /**
   * Trigger feedback for toggle interactions
   * @param isOn - Whether the toggle is being turned on
   */
  toggle(isOn: boolean): void {
    if (isOn) {
      this.success();
    } else {
      this.lightImpact();
    }
  }

  /**
   * Trigger feedback for slider interactions
   */
  slider(): void {
    this.selection();
  }

  /**
   * Trigger feedback for refresh actions
   */
  refresh(): void {
    this.mediumImpact();
  }

  /**
   * Trigger feedback for delete actions
   */
  delete(): void {
    this.heavyImpact();
  }

  /**
   * Trigger feedback for copy actions
   */
  copy(): void {
    this.lightImpact();
  }

  /**
   * Trigger feedback for share actions
   */
  share(): void {
    this.mediumImpact();
  }

  /**
   * Trigger custom feedback pattern
   * @param pattern - Array of feedback timings and types
   */
  customPattern(pattern: Array<{ delay: number; type: 'impact' | 'notification' | 'selection'; style?: string }>): void {
    pattern.forEach(({ delay, type, style }, index) => {
      setTimeout(() => {
        this.triggerFeedback(type, style);
      }, delay * index);
    });
  }
}

// Create singleton instance
export const haptics = new HapticManager();

// Convenience functions for common interactions
export const hapticFeedback = {
  // Button interactions
  buttonTap: () => haptics.button('primary'),
  buttonSecondary: () => haptics.button('secondary'),
  buttonDanger: () => haptics.button('danger'),
  buttonGhost: () => haptics.button('ghost'),

  // Navigation
  tabSwitch: () => haptics.navigation('tab'),
  pageTransition: () => haptics.navigation('page'),
  modalOpen: () => haptics.navigation('modal'),

  // Form interactions
  inputFocus: () => haptics.form('input'),
  formSubmit: () => haptics.form('submit'),
  formError: () => haptics.form('error'),
  formSuccess: () => haptics.form('success'),

  // Game interactions
  coinCollect: () => haptics.game('collect'),
  cardUpgrade: () => haptics.game('upgrade'),
  achievement: () => haptics.game('achievement'),
  gameFail: () => haptics.game('fail'),

  // Card interactions
  cardTap: () => haptics.card('tap'),
  cardSwipe: () => haptics.card('swipe'),
  cardFlip: () => haptics.card('flip'),
  cardDrag: () => haptics.card('drag'),

  // Common actions
  toggle: (isOn: boolean) => haptics.toggle(isOn),
  slider: () => haptics.slider(),
  refresh: () => haptics.refresh(),
  delete: () => haptics.delete(),
  copy: () => haptics.copy(),
  share: () => haptics.share(),

  // Notifications
  success: () => haptics.success(),
  warning: () => haptics.warning(),
  error: () => haptics.error(),

  // Impact levels
  light: () => haptics.lightImpact(),
  medium: () => haptics.mediumImpact(),
  heavy: () => haptics.heavyImpact(),
  rigid: () => haptics.rigidImpact(),
  soft: () => haptics.softImpact(),

  // Selection
  selection: () => haptics.selection(),
};

// Export the manager instance
export default haptics;
