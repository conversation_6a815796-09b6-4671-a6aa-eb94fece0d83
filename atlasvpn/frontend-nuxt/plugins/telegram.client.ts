import { init } from '@telegram-apps/sdk'

export default defineNuxtPlugin(() => {
  // Initialize Telegram WebApp when the plugin loads
  if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
    console.log('[Plugin] Initializing Telegram WebApp...')

    try {
      // Initialize the modern SDK
      init()
      console.log('[Plugin] Telegram SDK initialized successfully')

      const webApp = window.Telegram.WebApp

      // Basic initialization
      webApp.ready()
      webApp.expand()

      // Set theme colors to match our design
      webApp.setHeaderColor('#1f2937') // gray-800
      webApp.setBackgroundColor('#111827') // gray-900

      // Disable vertical swipes if available
      if ('disableVerticalSwipes' in webApp) {
        ;(webApp as any).disableVerticalSwipes()
      }

      console.log('[Plugin] Telegram WebApp configured successfully')
    } catch (error) {
      console.error('[Plugin] Failed to initialize Telegram SDK:', error)

      // Fallback to legacy initialization
      const webApp = window.Telegram.WebApp
      webApp.ready()
      webApp.expand()
      webApp.setHeaderColor('#1f2937')
      webApp.setBackgroundColor('#111827')

      console.log('[Plugin] Fallback to legacy Telegram WebApp initialization')
    }
    
    // Log initialization info
    console.log('[Telegram] WebApp initialized:', {
      platform: webApp.platform,
      colorScheme: webApp.colorScheme,
      viewportHeight: webApp.viewportHeight,
      isExpanded: webApp.isExpanded,
      user: webApp.initDataUnsafe?.user
    })
    
    // Set up global error handling for Telegram
    window.addEventListener('error', (event) => {
      console.error('[Telegram] Global error:', event.error)
    })
    
    // Mark Telegram as ready
    window.telegramReady = true
  } else {
    if (process.env.NODE_ENV === 'development') {
      console.info('[Telegram] WebApp not available - running in browser mode (this is normal for development)')
    }
    window.telegramReady = false
  }
})
