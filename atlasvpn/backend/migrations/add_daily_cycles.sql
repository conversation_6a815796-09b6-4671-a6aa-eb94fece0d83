-- Create daily_cycles table
CREATE TABLE IF NOT EXISTS daily_cycles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    cycle_number INTEGER NOT NULL DEFAULT 1,
    start_date TIMESTAMP NOT NULL,
    end_date TIM<PERSON><PERSON><PERSON>,
    completed_days TEXT NOT NULL DEFAULT '[]',
    total_reward FLOAT NOT NULL DEFAULT 0.0,
    is_completed BOOLEAN NOT NULL DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    <PERSON>OR<PERSON>G<PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE (user_id, task_id, cycle_number)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_cycle_dates ON daily_cycles(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_cycle_completion ON daily_cycles(is_completed, cycle_number);

-- Add cycle_id to task_completions
ALTER TABLE task_completions ADD COLUMN cycle_id INTEGER REFERENCES daily_cycles(id) ON DELETE SET NULL;

-- Add index for cycle_id
CREATE INDEX IF NOT EXISTS idx_completion_cycle ON task_completions(cycle_id); 