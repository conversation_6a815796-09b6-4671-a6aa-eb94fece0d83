
Starting Core Task API Tests...
2025-04-08 19:04:51,766 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:04:51,768 - INFO - Retrieved CSRF token for admin login
2025-04-08 19:04:53,814 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:04:53,873 - INFO - Admin login successful
2025-04-08 19:04:54,463 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 19:04:54,477 - INFO - Retrieved CSRF token for user login
2025-04-08 19:04:55,688 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 19:04:55,730 - INFO - User login successful
2025-04-08 19:04:55,848 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:04:55,850 - INFO - Created YOUTUBE_VIEW task with ID: 398
2025-04-08 19:04:55,957 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 19:04:56,457 - INFO - Created TELEGRAM_CHANNEL task with ID: 399
2025-04-08 19:04:56,569 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/398/start "HTTP/1.1 200 OK"
2025-04-08 19:04:56,613 - INFO - Started task 398
2025-04-08 19:04:56,762 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/398/verify "HTTP/1.1 200 OK"
2025-04-08 19:04:56,809 - INFO - Verified task 398: True
2025-04-08 19:04:57,947 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/398/verify "HTTP/1.1 200 OK"
2025-04-08 19:04:57,948 - INFO - Verified task 398: True
2025-04-08 19:04:58,547 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/398/claim "HTTP/1.1 200 OK"
2025-04-08 19:04:58,593 - INFO - Claimed reward for task 398
2025-04-08 19:04:58,594 - INFO - YouTube task flow completed successfully
2025-04-08 19:04:58,696 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/399/start "HTTP/1.1 200 OK"
2025-04-08 19:04:58,742 - INFO - Started task 399
2025-04-08 19:04:58,944 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/399/verify "HTTP/1.1 200 OK"
2025-04-08 19:04:59,456 - INFO - Verified task 399: True
2025-04-08 19:04:59,600 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/399/claim "HTTP/1.1 200 OK"
2025-04-08 19:04:59,641 - INFO - Claimed reward for task 399
2025-04-08 19:04:59,642 - INFO - Telegram task flow completed successfully
2025-04-08 19:04:59,727 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/398/toggle "HTTP/1.1 200 OK"
2025-04-08 19:04:59,769 - INFO - Toggled task 398 active status to False
2025-04-08 19:04:59,847 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/398/toggle "HTTP/1.1 200 OK"
2025-04-08 19:04:59,889 - INFO - Toggled task 398 active status to True
2025-04-08 19:05:00,471 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/398/start "HTTP/1.1 400 Bad Request"
2025-04-08 19:05:00,509 - ERROR - Failed to start task 398: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
2025-04-08 19:05:00,510 - INFO - Admin functions tested successfully
  Setup completed 
  Running tests...

Task API Test Report
       Test Summary       
┏━━━━━━━━━━━━━━┳━━━━━━━━━┓
┃ Metric       ┃ Value   ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━┩
│ Total Tests  │ 3       │
│ Passed       │ 3       │
│ Failed       │ 0       │
│ Success Rate │ 100.00% │
└──────────────┴─────────┘

Report saved to /root/project/backend/scripts/logs/task_api_test_report_20250408_190500.json
