/* Enhanced Components for AtlasVPN - Compatible with @nuxt/ui Tailwind CSS v4 */

/* Glass morphism effects */
.glass-card {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 58, 237, 0.2);
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.glass-card-subtle {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
}

/* Dashboard Layout */
.dashboard-container {
  min-height: 100vh;
  background-color: #000000;
  color: white;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(124, 58, 237, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.05) 0%, transparent 70%);
}

.dashboard-content {
  flex: 1;
  overflow: hidden;
  padding-bottom: env(safe-area-inset-bottom, 80px);
}

/* Header */
.header-container {
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid #1f2937;
  padding-top: env(safe-area-inset-top, 0);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
}

/* Bottom Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(12px);
  border-top: 1px solid #1f2937;
  padding-bottom: env(safe-area-inset-bottom, 0);
}

.bottom-nav-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0.5rem 0;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

.nav-item.active {
  color: #a855f7;
}

.nav-item:not(.active) {
  color: #9ca3af;
}

.nav-item:not(.active):hover {
  color: white;
}

/* Cards */
.stat-card {
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.8), rgba(31, 41, 55, 0.8));
  backdrop-filter: blur(4px);
  border: 1px solid #374151;
  border-radius: 1rem;
  padding: 1.5rem;
  background-image: linear-gradient(135deg, rgba(139, 92, 246, 0.05), rgba(59, 130, 246, 0.05));
}

.profit-card {
  background: linear-gradient(135deg, rgba(6, 78, 59, 0.2), rgba(6, 95, 70, 0.2));
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
}

/* Enhanced Button Styles */
.btn-primary-enhanced {
  background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
  border: none;
  color: white;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);
}

.btn-primary-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.btn-gold-enhanced {
  background: linear-gradient(135deg, #ffd700 0%, #b8860b 100%);
  border: none;
  color: #000000;
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.btn-gold-enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
  background: linear-gradient(135deg, #ffed4e 0%, #ffd700 100%);
}

/* Text Gradients */
.text-gradient-purple {
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-gold {
  background: linear-gradient(135deg, #ffd700 0%, #f59e0b 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading States */
.skeleton {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 0.05) 25%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
  border-radius: 0.5rem;
}

/* Status Indicators */
.status-online {
  position: relative;
}

.status-online::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background: #10b981;
  border: 2px solid #000000;
  border-radius: 50%;
  animation: pulse-soft 2s infinite;
}

/* Progress Bars */
.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #7c3aed 0%, #3b82f6 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .glass-card {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .btn-primary-enhanced:hover,
  .btn-gold-enhanced:hover {
    transform: none;
  }
}

/*
 * Simplified approach - let @nuxt/ui handle most styling
 * Use Tailwind classes directly in components instead of @apply directives
 */
