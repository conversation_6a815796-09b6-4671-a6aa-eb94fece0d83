/**
 * @file StoreTest.tsx
 * @description Test component to verify Zustand stores are working correctly
 * This is a temporary component for testing store functionality
 */

import React from 'react';
import { useUIStore, useTaskStore, useChatStore, useUIChatStore } from '../../stores';

const StoreTest: React.FC = () => {
  // Test UI Store
  const theme = useUIStore((state) => state.theme);
  const setTheme = useUIStore((state) => state.setTheme);
  const hasSeenWelcome = useUIStore((state) => state.hasSeenWelcomeMessage);
  const setHasSeenWelcome = useUIStore((state) => state.setHasSeenWelcomeMessage);
  
  // Test Task Store
  const tasks = useTaskStore((state) => state.tasks);
  const isLoading = useTaskStore((state) => state.isLoading);
  const setLoading = useTaskStore((state) => state.setLoading);
  
  // Test Chat Store
  const conversations = useChatStore((state) => state.conversations);
  const isConnected = useChatStore((state) => state.isConnected);
  const setConnected = useChatStore((state) => state.setConnected);
  
  // Test UI Chat Store
  const selectedChatId = useUIChatStore((state) => state.selectedChatId);
  const viewMode = useUIChatStore((state) => state.viewMode);
  const setViewMode = useUIChatStore((state) => state.setViewMode);

  const handleThemeToggle = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
  };

  const handleWelcomeToggle = () => {
    setHasSeenWelcome(!hasSeenWelcome);
  };

  const handleLoadingToggle = () => {
    setLoading(!isLoading);
  };

  const handleConnectionToggle = () => {
    setConnected(!isConnected);
  };

  const handleViewModeToggle = () => {
    const newMode = viewMode === 'list' ? 'conversation' : 'list';
    setViewMode(newMode);
  };

  return (
    <div className="p-6 bg-gray-900 text-white rounded-lg space-y-4">
      <h2 className="text-xl font-bold text-green-400 mb-4">🧪 Store Test Component</h2>
      
      {/* UI Store Test */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-blue-400 mb-2">UI Store</h3>
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span>Theme: {theme}</span>
            <button 
              onClick={handleThemeToggle}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm"
            >
              Toggle Theme
            </button>
          </div>
          <div className="flex items-center justify-between">
            <span>Has Seen Welcome: {hasSeenWelcome ? 'Yes' : 'No'}</span>
            <button 
              onClick={handleWelcomeToggle}
              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm"
            >
              Toggle Welcome
            </button>
          </div>
        </div>
      </div>

      {/* Task Store Test */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-purple-400 mb-2">Task Store</h3>
        <div className="space-y-2">
          <div>Tasks Count: {tasks.length}</div>
          <div className="flex items-center justify-between">
            <span>Loading: {isLoading ? 'Yes' : 'No'}</span>
            <button 
              onClick={handleLoadingToggle}
              className="px-3 py-1 bg-purple-600 hover:bg-purple-700 rounded text-sm"
            >
              Toggle Loading
            </button>
          </div>
        </div>
      </div>

      {/* Chat Store Test */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-yellow-400 mb-2">Chat Store</h3>
        <div className="space-y-2">
          <div>Conversations Count: {conversations.length}</div>
          <div className="flex items-center justify-between">
            <span>Connected: {isConnected ? 'Yes' : 'No'}</span>
            <button 
              onClick={handleConnectionToggle}
              className="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 rounded text-sm"
            >
              Toggle Connection
            </button>
          </div>
        </div>
      </div>

      {/* UI Chat Store Test */}
      <div className="bg-gray-800 p-4 rounded-lg">
        <h3 className="text-lg font-semibold text-green-400 mb-2">UI Chat Store</h3>
        <div className="space-y-2">
          <div>Selected Chat: {selectedChatId || 'None'}</div>
          <div className="flex items-center justify-between">
            <span>View Mode: {viewMode}</span>
            <button 
              onClick={handleViewModeToggle}
              className="px-3 py-1 bg-green-600 hover:bg-green-700 rounded text-sm"
            >
              Toggle View Mode
            </button>
          </div>
        </div>
      </div>

      <div className="text-center text-green-400 font-semibold">
        ✅ All stores are working correctly!
      </div>
    </div>
  );
};

export default StoreTest;