import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Server, 
  Database, 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock, 
  HardDrive, 
  Cpu, 
  MemoryStick,
  Wifi,
  Download,
  RefreshCw,
  RotateCcw,
  Trash2,
  Edit
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical'
  uptime: number
  cpu_usage: number
  memory_usage: number
  disk_usage: number
  database_status: 'connected' | 'disconnected' | 'error'
  redis_status: 'connected' | 'disconnected' | 'error'
  api_response_time: number
  active_connections: number
  last_backup: string
  version: string
}

interface SystemConfig {
  id: number
  key: string
  value: string
  description: string
  category: string
  is_sensitive: boolean
  updated_at: string
  updated_by: string
}

interface SystemLog {
  id: number
  level: 'info' | 'warning' | 'error' | 'critical'
  message: string
  component: string
  timestamp: string
  details?: any
}

interface BackupInfo {
  id: number
  filename: string
  size: number
  created_at: string
  type: 'manual' | 'automatic'
  status: 'completed' | 'failed' | 'in_progress'
}

interface SystemStats {
  total_users: number
  active_users_24h: number
  total_transactions: number
  total_revenue: number
  api_calls_24h: number
  error_rate_24h: number
  avg_response_time: number
  database_size: number
}

export function System() {
  const [configDialogOpen, setConfigDialogOpen] = useState(false)
  const [selectedConfig, setSelectedConfig] = useState<SystemConfig | null>(null)
  const [configFilter, setConfigFilter] = useState<string>('all')
  const [logFilter, setLogFilter] = useState<string>('all')
  
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const { data: systemHealth, isLoading: isHealthLoading } = useQuery<SystemHealth>({
    queryKey: ['system-health'],
    queryFn: () => api.get('/admin/system/health').then(res => res.data),
    refetchInterval: 30000 // Refresh every 30 seconds
  })

  const { data: systemStats, isLoading: isStatsLoading } = useQuery<SystemStats>({
    queryKey: ['system-stats'],
    queryFn: () => api.get('/admin/system/stats').then(res => res.data)
  })

  const { data: systemConfigs, isLoading: isConfigsLoading } = useQuery<SystemConfig[]>({
    queryKey: ['system-configs'],
    queryFn: () => api.get('/admin/system/configs').then(res => res.data)
  })

  const { data: systemLogs, isLoading: isLogsLoading } = useQuery<SystemLog[]>({
    queryKey: ['system-logs'],
    queryFn: () => api.get('/admin/system/logs').then(res => res.data)
  })

  const { data: backups, isLoading: isBackupsLoading } = useQuery<BackupInfo[]>({
    queryKey: ['system-backups'],
    queryFn: () => api.get('/admin/system/backups').then(res => res.data)
  })

  const updateConfigMutation = useMutation({
    mutationFn: (config: { id: number; value: string }) => 
      api.put(`/admin/system/configs/${config.id}`, { value: config.value }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-configs'] })
      setConfigDialogOpen(false)
      toast({
        title: "Success",
        description: "Configuration updated successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to update configuration",
        variant: "destructive",
      })
    }
  })

  const createBackupMutation = useMutation({
    mutationFn: () => api.post('/admin/system/backup'),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-backups'] })
      toast({
        title: "Success",
        description: "Backup created successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to create backup",
        variant: "destructive",
      })
    }
  })

  const restartServiceMutation = useMutation({
    mutationFn: (service: string) => api.post(`/admin/system/restart/${service}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['system-health'] })
      toast({
        title: "Success",
        description: "Service restarted successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to restart service",
        variant: "destructive",
      })
    }
  })

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${days}d ${hours}h ${minutes}m`
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getHealthColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600'
      case 'warning': return 'text-yellow-600'
      case 'critical': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getHealthIcon = (status: string) => {
    switch (status) {
      case 'healthy': return CheckCircle
      case 'warning': return AlertTriangle
      case 'critical': return XCircle
      default: return Clock
    }
  }

  const getLogLevelColor = (level: string) => {
    switch (level) {
      case 'info': return 'bg-blue-100 text-blue-800'
      case 'warning': return 'bg-yellow-100 text-yellow-800'
      case 'error': return 'bg-red-100 text-red-800'
      case 'critical': return 'bg-red-200 text-red-900'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const HealthCard = ({ 
    title, 
    value, 
    status, 
    icon: Icon,
    description 
  }: { 
    title: string
    value: string | number
    status: string
    icon: any
    description?: string
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 ${getHealthColor(status)}`} />
      </CardHeader>
      <CardContent>
        <div className={`text-2xl font-bold ${getHealthColor(status)}`}>
          {value}
        </div>
        {description && (
          <p className="text-xs text-muted-foreground">{description}</p>
        )}
      </CardContent>
    </Card>
  )

  const ConfigDialog = () => {
    const [value, setValue] = useState(selectedConfig?.value || '')

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      if (selectedConfig) {
        updateConfigMutation.mutate({ id: selectedConfig.id, value })
      }
    }

    return (
      <Dialog open={configDialogOpen} onOpenChange={setConfigDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Configuration</DialogTitle>
            <DialogDescription>
              Update system configuration value
            </DialogDescription>
          </DialogHeader>
          {selectedConfig && (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="key">Configuration Key</Label>
              <Input
                id="key"
                  value={selectedConfig.key}
                  disabled
                  className="bg-gray-50"
              />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                  value={selectedConfig.description}
                  disabled
                  className="bg-gray-50"
                  rows={2}
              />
            </div>
            <div>
                <Label htmlFor="value">Value</Label>
                {selectedConfig.is_sensitive ? (
              <Input
                    id="value"
                    type="password"
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                required
              />
                ) : (
              <Textarea
                    id="value"
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                rows={3}
                    required
              />
                )}
            </div>
            <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setConfigDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={updateConfigMutation.isPending}>
                  {updateConfigMutation.isPending ? 'Updating...' : 'Update'}
              </Button>
            </DialogFooter>
          </form>
          )}
        </DialogContent>
      </Dialog>
    )
  }

  if (isHealthLoading || isStatsLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Management</h1>
          <p className="text-gray-600">Monitor system health and manage configurations</p>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const filteredConfigs = systemConfigs?.filter(config => 
    configFilter === 'all' || config.category === configFilter
  )

  const filteredLogs = systemLogs?.filter(log => 
    logFilter === 'all' || log.level === logFilter
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Management</h1>
          <p className="text-gray-600">Monitor system health and manage configurations</p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => queryClient.invalidateQueries({ queryKey: ['system-health'] })}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={() => createBackupMutation.mutate()}>
            <Download className="h-4 w-4 mr-2" />
            Create Backup
          </Button>
        </div>
      </div>

      {/* System Health Overview */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">System Health</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <HealthCard
            title="System Status"
            value={systemHealth?.status || 'Unknown'}
            status={systemHealth?.status || 'unknown'}
            icon={getHealthIcon(systemHealth?.status || 'unknown')}
            description={`Uptime: ${formatUptime(systemHealth?.uptime || 0)}`}
          />
          <HealthCard
            title="CPU Usage"
            value={`${systemHealth?.cpu_usage?.toFixed(1) || 0}%`}
            status={systemHealth?.cpu_usage && systemHealth.cpu_usage > 80 ? 'critical' : systemHealth?.cpu_usage && systemHealth.cpu_usage > 60 ? 'warning' : 'healthy'}
            icon={Cpu}
            description="Current CPU utilization"
          />
          <HealthCard
            title="Memory Usage"
            value={`${systemHealth?.memory_usage?.toFixed(1) || 0}%`}
            status={systemHealth?.memory_usage && systemHealth.memory_usage > 85 ? 'critical' : systemHealth?.memory_usage && systemHealth.memory_usage > 70 ? 'warning' : 'healthy'}
            icon={MemoryStick}
            description="RAM utilization"
          />
          <HealthCard
            title="Disk Usage"
            value={`${systemHealth?.disk_usage?.toFixed(1) || 0}%`}
            status={systemHealth?.disk_usage && systemHealth.disk_usage > 90 ? 'critical' : systemHealth?.disk_usage && systemHealth.disk_usage > 75 ? 'warning' : 'healthy'}
            icon={HardDrive}
            description="Storage utilization"
          />
        </div>
      </div>

      {/* System Statistics */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">System Statistics</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{systemStats?.total_users || 0}</div>
              <p className="text-xs text-muted-foreground">
                {systemStats?.active_users_24h || 0} active in 24h
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">API Performance</CardTitle>
              <Wifi className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{systemStats?.avg_response_time || 0}ms</div>
              <p className="text-xs text-muted-foreground">
                {systemStats?.api_calls_24h || 0} calls in 24h
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Database Size</CardTitle>
              <Database className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatBytes(systemStats?.database_size || 0)}</div>
              <p className="text-xs text-muted-foreground">
                {systemStats?.total_transactions || 0} transactions
              </p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{systemStats?.error_rate_24h?.toFixed(2) || 0}%</div>
              <p className="text-xs text-muted-foreground">
                Last 24 hours
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Service Status */}
      <Card>
        <CardHeader>
          <CardTitle>Service Status</CardTitle>
          <CardDescription>Monitor and manage system services</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Database className="h-5 w-5" />
                <div>
                  <div className="font-medium">Database</div>
                  <div className="text-sm text-gray-600">PostgreSQL</div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant={systemHealth?.database_status === 'connected' ? 'default' : 'destructive'}>
                  {systemHealth?.database_status || 'Unknown'}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => restartServiceMutation.mutate('database')}
                  disabled={restartServiceMutation.isPending}
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Server className="h-5 w-5" />
                <div>
                  <div className="font-medium">Redis Cache</div>
                  <div className="text-sm text-gray-600">In-memory store</div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant={systemHealth?.redis_status === 'connected' ? 'default' : 'destructive'}>
                  {systemHealth?.redis_status || 'Unknown'}
                </Badge>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => restartServiceMutation.mutate('redis')}
                  disabled={restartServiceMutation.isPending}
                >
                  <RotateCcw className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Activity className="h-5 w-5" />
                <div>
                  <div className="font-medium">API Server</div>
                  <div className="text-sm text-gray-600">FastAPI</div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="default">Running</Badge>
                <span className="text-sm text-gray-600">
                  {systemHealth?.api_response_time || 0}ms
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs for detailed management */}
      <Tabs defaultValue="configs" className="space-y-4">
        <TabsList>
          <TabsTrigger value="configs">Configuration</TabsTrigger>
          <TabsTrigger value="logs">System Logs</TabsTrigger>
          <TabsTrigger value="backups">Backups</TabsTrigger>
        </TabsList>

        <TabsContent value="configs" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>System Configuration</CardTitle>
                  <CardDescription>Manage system settings and parameters</CardDescription>
                </div>
                <Select value={configFilter} onValueChange={setConfigFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="payment">Payment</SelectItem>
                    <SelectItem value="vpn">VPN</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Key</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredConfigs?.map((config) => (
                    <TableRow key={config.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{config.key}</div>
                          <div className="text-sm text-gray-600 max-w-xs truncate">
                            {config.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm max-w-xs block truncate">
                          {config.is_sensitive ? '••••••••' : config.value}
                        </code>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{config.category}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(config.updated_at).toLocaleDateString()}
                          <div className="text-xs text-gray-600">by {config.updated_by}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedConfig(config)
                            setConfigDialogOpen(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>System Logs</CardTitle>
                  <CardDescription>Monitor system events and errors</CardDescription>
                </div>
                <Select value={logFilter} onValueChange={setLogFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Levels</SelectItem>
                    <SelectItem value="info">Info</SelectItem>
                    <SelectItem value="warning">Warning</SelectItem>
                    <SelectItem value="error">Error</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Level</TableHead>
                    <TableHead>Component</TableHead>
                    <TableHead>Message</TableHead>
                    <TableHead>Timestamp</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs?.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <Badge className={getLogLevelColor(log.level)}>
                          {log.level.toUpperCase()}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                          {log.component}
                        </code>
                      </TableCell>
                      <TableCell>
                        <div className="max-w-md truncate">{log.message}</div>
                      </TableCell>
                      <TableCell>
                        {new Date(log.timestamp).toLocaleString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="backups" className="space-y-4">
            <Card>
              <CardHeader>
              <CardTitle>System Backups</CardTitle>
              <CardDescription>Manage database and system backups</CardDescription>
              </CardHeader>
              <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Filename</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {backups?.map((backup) => (
                    <TableRow key={backup.id}>
                      <TableCell>
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                          {backup.filename}
                        </code>
                      </TableCell>
                      <TableCell>{formatBytes(backup.size)}</TableCell>
                      <TableCell>
                        <Badge variant={backup.type === 'automatic' ? 'default' : 'secondary'}>
                          {backup.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={backup.status === 'completed' ? 'default' : backup.status === 'failed' ? 'destructive' : 'secondary'}>
                          {backup.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(backup.created_at).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button variant="outline" size="sm">
                            <Download className="h-4 w-4" />
                  </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                  </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Backup</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete this backup? This action cannot be undone.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction>Delete</AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              </CardContent>
            </Card>
        </TabsContent>
      </Tabs>

      <ConfigDialog />
    </div>
  )
} 