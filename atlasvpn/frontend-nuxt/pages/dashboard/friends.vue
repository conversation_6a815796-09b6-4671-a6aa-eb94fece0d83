<template>
  <div class="friends-tab">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-white mb-2">Invite Friends</h1>
      <p class="text-gray-400">Earn rewards by inviting friends to AtlasVPN</p>
    </div>

    <!-- Referral Stats -->
    <div class="grid grid-cols-2 gap-4 mb-6">
      <div class="stat-card">
        <div class="text-2xl font-bold text-purple-400 mb-1">
          {{ referralStats.totalReferrals }}
        </div>
        <div class="text-xs text-gray-400">Friends Invited</div>
      </div>
      
      <div class="stat-card">
        <div class="text-2xl font-bold text-green-400 mb-1">
          ${{ referralStats.totalEarned.toFixed(2) }}
        </div>
        <div class="text-xs text-gray-400">Referral Earnings</div>
      </div>
    </div>

    <!-- Referral Card -->
    <div class="mb-6">
      <ReferralCard
        :referral-code="referralCode"
        :referral-link="referralLink"
        :total-referrals="referralStats.totalReferrals"
        :total-earned="referralStats.totalEarned"
        @share="handleShare"
      />
    </div>

    <!-- How It Works -->
    <div class="card mb-6">
      <h3 class="text-lg font-semibold text-white mb-4">How It Works</h3>
      <div class="space-y-4">
        <div class="flex items-start space-x-3">
          <div class="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0">
            <span class="text-purple-400 font-bold text-sm">1</span>
          </div>
          <div>
            <h4 class="text-white font-medium">Share Your Link</h4>
            <p class="text-gray-400 text-sm">Send your referral link to friends</p>
          </div>
        </div>
        
        <div class="flex items-start space-x-3">
          <div class="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0">
            <span class="text-purple-400 font-bold text-sm">2</span>
          </div>
          <div>
            <h4 class="text-white font-medium">Friend Joins</h4>
            <p class="text-gray-400 text-sm">They sign up using your link</p>
          </div>
        </div>
        
        <div class="flex items-start space-x-3">
          <div class="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center flex-shrink-0">
            <span class="text-purple-400 font-bold text-sm">3</span>
          </div>
          <div>
            <h4 class="text-white font-medium">Earn Rewards</h4>
            <p class="text-gray-400 text-sm">Get $5 when they make their first purchase</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Referred Friends List -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-white">Your Referrals</h3>
        <UButton
          @click="refreshReferrals"
          :loading="isLoadingReferrals"
          variant="ghost"
          size="sm"
          icon="i-heroicons-arrow-path"
        />
      </div>

      <!-- Loading State -->
      <div v-if="isLoadingReferrals" class="space-y-4">
        <div v-for="i in 3" :key="i" class="flex items-center space-x-4 animate-pulse">
          <div class="w-10 h-10 bg-gray-700 rounded-full"></div>
          <div class="flex-1">
            <div class="h-4 bg-gray-700 rounded mb-2"></div>
            <div class="h-3 bg-gray-700 rounded w-2/3"></div>
          </div>
          <div class="w-16 h-4 bg-gray-700 rounded"></div>
        </div>
      </div>

      <!-- Referrals List -->
      <div v-else-if="referrals.length > 0" class="space-y-4">
        <div
          v-for="referral in referrals"
          :key="referral.id"
          class="flex items-center justify-between py-3 border-b border-gray-800 last:border-b-0"
        >
          <div class="flex items-center space-x-4">
            <div class="w-10 h-10 rounded-full bg-gray-800 flex items-center justify-center">
              <span class="text-white font-medium">
                {{ referral.username.charAt(0).toUpperCase() }}
              </span>
            </div>
            <div>
              <div class="text-white font-medium">{{ referral.username }}</div>
              <div class="text-gray-400 text-sm">
                Joined {{ formatDate(referral.created_at) }}
              </div>
            </div>
          </div>
          
          <div class="text-right">
            <div class="text-green-400 font-medium">
              +${{ referral.commission_earned.toFixed(2) }}
            </div>
            <div class="text-gray-400 text-xs">
              {{ referral.status }}
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <Icon name="i-heroicons-user-group" class="w-16 h-16 mx-auto mb-4 text-gray-600" />
        <h4 class="text-lg font-medium text-white mb-2">No referrals yet</h4>
        <p class="text-gray-400">Start inviting friends to earn rewards!</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { format } from 'date-fns'

definePageMeta({
  middleware: 'auth',
  layout: 'dashboard'
})

// SEO
useHead({
  title: 'Invite Friends - AtlasVPN'
})

const userStore = useUserStore()
const config = useRuntimeConfig()

// Computed properties from store
const referralCode = computed(() => userStore.referralCode)

// Local state
const isLoadingReferrals = ref(false)
const referrals = ref([])
const referralStats = ref({
  totalReferrals: 0,
  totalEarned: 0
})

// Computed
const referralLink = computed(() => {
  return `${config.public.telegramWebappUrl}?ref=${referralCode.value}`
})

// Actions
const handleShare = (platform: string) => {
  console.log('Shared on platform:', platform)
  // Track sharing analytics here if needed
}

const refreshReferrals = async () => {
  isLoadingReferrals.value = true
  try {
    // In a real app, this would fetch referral data from the API
    // For now, we'll simulate some data
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    referrals.value = []
    referralStats.value = {
      totalReferrals: 0,
      totalEarned: 0
    }
  } catch (error) {
    console.error('Failed to load referrals:', error)
  } finally {
    isLoadingReferrals.value = false
  }
}

const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'MMM d, yyyy')
}

// Initialize
onMounted(() => {
  refreshReferrals()
})
</script>
