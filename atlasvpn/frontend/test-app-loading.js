#!/usr/bin/env node

const puppeteer = require('puppeteer-core');
const path = require('path');

async function testAppLoading() {
  console.log('🚀 Testing app loading performance...');
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Track performance metrics
    const startTime = Date.now();
    
    // Navigate to the app
    await page.goto('http://localhost:3000', { 
      waitUntil: 'networkidle0',
      timeout: 10000 
    });
    
    // Wait for React to mount
    await page.waitForSelector('#root > div', { timeout: 5000 });
    
    const loadTime = Date.now() - startTime;
    
    console.log(`✅ App loaded successfully in ${loadTime}ms`);
    
    // Check for error messages
    const errorElements = await page.$$eval('*', els => 
      els.filter(el => el.textContent && el.textContent.includes('Loading Issue'))
    );
    
    if (errorElements.length > 0) {
      console.log('⚠️  Loading issues detected');
    } else {
      console.log('✅ No loading issues detected');
    }
    
    return { success: true, loadTime };
    
  } catch (error) {
    console.error('❌ App loading failed:', error.message);
    return { success: false, error: error.message };
  } finally {
    await browser.close();
  }
}

if (require.main === module) {
  testAppLoading()
    .then(result => {
      if (result.success) {
        console.log('🎉 App loading test completed successfully');
        process.exit(0);
      } else {
        console.log('💥 App loading test failed');
        process.exit(1);
      }
    })
    .catch(console.error);
}

module.exports = testAppLoading; 