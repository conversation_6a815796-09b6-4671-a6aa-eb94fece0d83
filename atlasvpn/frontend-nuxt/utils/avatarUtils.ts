/**
 * Avatar utilities for managing user avatars and profile images
 */

/**
 * Available avatar categories and their corresponding images
 */
export const AVATAR_CATEGORIES = {
  guardians: [
    'Antiddos Gaurdian',
    'Datacenter Gauridan',
  ],
  animals: [
    'Artificial Rabbit',
    'Bugaddict Panda',
    'Cipherfire Fox',
    'Cipherlord Cat',
    'Cipherweaver Wolf',
    'Cryptkeeper Cat',
    'Cryptshade Beast',
    'Cryptweaver Rabbit',
    'Databrigner Seahorse',
    'Datacracker Beast',
    'Dataforge Seahorse',
    'Datashade Rabbit',
    'Debugger Penguin',
    'Devleader Penguin',
    'Encrypted Spider',
    'Mysticssl Raven',
    'Proxyweaver Rabbit',
    'Uptime Penguin',
  ],
  mystical: [
    'Lovely Angel',
    'Netshade Angel',
    'Shadow Decoder',
  ],
} as const;

/**
 * All available avatars
 */
export const ALL_AVATARS = [
  ...AVATAR_CATEGORIES.guardians,
  ...AVATAR_CATEGORIES.animals,
  ...AVATAR_CATEGORIES.mystical,
] as const;

/**
 * Avatar rarity levels
 */
export const AVATAR_RARITY = {
  common: ['Bugaddict Panda', 'Debugger Penguin', 'Devleader Penguin', 'Uptime Penguin'],
  uncommon: [
    'Cipherfire Fox',
    'Cipherlord Cat',
    'Cryptkeeper Cat',
    'Databrigner Seahorse',
    'Dataforge Seahorse',
    'Datashade Rabbit',
    'Encrypted Spider',
    'Proxyweaver Rabbit',
  ],
  rare: [
    'Artificial Rabbit',
    'Cipherweaver Wolf',
    'Cryptshade Beast',
    'Cryptweaver Rabbit',
    'Datacracker Beast',
    'Mysticssl Raven',
  ],
  epic: [
    'Lovely Angel',
    'Netshade Angel',
    'Shadow Decoder',
  ],
  legendary: [
    'Antiddos Gaurdian',
    'Datacenter Gauridan',
  ],
} as const;

/**
 * Get avatar URL
 * @param avatarName - Name of the avatar
 * @returns Avatar URL
 */
export function getAvatarUrl(avatarName: string): string {
  return `/avatars/${avatarName}.webp`;
}

/**
 * Get avatar category
 * @param avatarName - Name of the avatar
 * @returns Avatar category
 */
export function getAvatarCategory(avatarName: string): keyof typeof AVATAR_CATEGORIES | null {
  for (const [category, avatars] of Object.entries(AVATAR_CATEGORIES)) {
    if (avatars.includes(avatarName as any)) {
      return category as keyof typeof AVATAR_CATEGORIES;
    }
  }
  return null;
}

/**
 * Get avatar rarity
 * @param avatarName - Name of the avatar
 * @returns Avatar rarity
 */
export function getAvatarRarity(avatarName: string): keyof typeof AVATAR_RARITY | null {
  for (const [rarity, avatars] of Object.entries(AVATAR_RARITY)) {
    if (avatars.includes(avatarName as any)) {
      return rarity as keyof typeof AVATAR_RARITY;
    }
  }
  return null;
}

/**
 * Get rarity color
 * @param rarity - Avatar rarity
 * @returns CSS color class
 */
export function getRarityColor(rarity: keyof typeof AVATAR_RARITY): string {
  const colors = {
    common: 'text-gray-400',
    uncommon: 'text-green-400',
    rare: 'text-blue-400',
    epic: 'text-purple-400',
    legendary: 'text-yellow-400',
  };
  return colors[rarity];
}

/**
 * Get rarity background color
 * @param rarity - Avatar rarity
 * @returns CSS background color class
 */
export function getRarityBgColor(rarity: keyof typeof AVATAR_RARITY): string {
  const colors = {
    common: 'bg-gray-500/20',
    uncommon: 'bg-green-500/20',
    rare: 'bg-blue-500/20',
    epic: 'bg-purple-500/20',
    legendary: 'bg-yellow-500/20',
  };
  return colors[rarity];
}

/**
 * Get random avatar from a specific category
 * @param category - Avatar category
 * @returns Random avatar name
 */
export function getRandomAvatarFromCategory(category: keyof typeof AVATAR_CATEGORIES): string {
  const avatars = AVATAR_CATEGORIES[category];
  return avatars[Math.floor(Math.random() * avatars.length)];
}

/**
 * Get random avatar from a specific rarity
 * @param rarity - Avatar rarity
 * @returns Random avatar name
 */
export function getRandomAvatarFromRarity(rarity: keyof typeof AVATAR_RARITY): string {
  const avatars = AVATAR_RARITY[rarity];
  return avatars[Math.floor(Math.random() * avatars.length)];
}

/**
 * Get random avatar
 * @returns Random avatar name
 */
export function getRandomAvatar(): string {
  return ALL_AVATARS[Math.floor(Math.random() * ALL_AVATARS.length)];
}

/**
 * Generate user initials from name
 * @param firstName - First name
 * @param lastName - Last name
 * @returns User initials (max 2 characters)
 */
export function generateInitials(firstName?: string, lastName?: string): string {
  if (!firstName && !lastName) return 'U';
  
  const first = firstName?.charAt(0).toUpperCase() || '';
  const last = lastName?.charAt(0).toUpperCase() || '';
  
  return (first + last) || 'U';
}

/**
 * Generate avatar color based on user ID or name
 * @param seed - Seed for color generation (user ID or name)
 * @returns CSS color class
 */
export function generateAvatarColor(seed: string | number): string {
  const colors = [
    'bg-red-500',
    'bg-orange-500',
    'bg-amber-500',
    'bg-yellow-500',
    'bg-lime-500',
    'bg-green-500',
    'bg-emerald-500',
    'bg-teal-500',
    'bg-cyan-500',
    'bg-sky-500',
    'bg-blue-500',
    'bg-indigo-500',
    'bg-violet-500',
    'bg-purple-500',
    'bg-fuchsia-500',
    'bg-pink-500',
    'bg-rose-500',
  ];
  
  const hash = typeof seed === 'string' 
    ? seed.split('').reduce((a, b) => a + b.charCodeAt(0), 0)
    : seed;
    
  return colors[hash % colors.length];
}

/**
 * Check if avatar exists
 * @param avatarName - Name of the avatar
 * @returns True if avatar exists
 */
export function avatarExists(avatarName: string): boolean {
  return ALL_AVATARS.includes(avatarName as any);
}

/**
 * Get avatar display name (formatted)
 * @param avatarName - Name of the avatar
 * @returns Formatted display name
 */
export function getAvatarDisplayName(avatarName: string): string {
  return avatarName.replace(/([A-Z])/g, ' $1').trim();
}

/**
 * Get avatars by rarity with metadata
 * @param rarity - Avatar rarity
 * @returns Array of avatar objects with metadata
 */
export function getAvatarsByRarity(rarity: keyof typeof AVATAR_RARITY) {
  return AVATAR_RARITY[rarity].map(name => ({
    name,
    displayName: getAvatarDisplayName(name),
    url: getAvatarUrl(name),
    category: getAvatarCategory(name),
    rarity,
    rarityColor: getRarityColor(rarity),
    rarityBgColor: getRarityBgColor(rarity),
  }));
}

/**
 * Get all avatars with metadata
 * @returns Array of all avatar objects with metadata
 */
export function getAllAvatarsWithMetadata() {
  return ALL_AVATARS.map(name => ({
    name,
    displayName: getAvatarDisplayName(name),
    url: getAvatarUrl(name),
    category: getAvatarCategory(name),
    rarity: getAvatarRarity(name),
    rarityColor: getRarityColor(getAvatarRarity(name)!),
    rarityBgColor: getRarityBgColor(getAvatarRarity(name)!),
  }));
}

/**
 * Avatar selection utilities for 3D world
 */
export const avatar3D = {
  /**
   * Get default 3D avatar configuration
   */
  getDefaultConfig() {
    return {
      hairStyle: 'short',
      hairColor: 'brown',
      skinTone: 'medium',
      outfit: 'casual',
      accessories: [],
    };
  },

  /**
   * Available hair styles
   */
  hairStyles: [
    { id: 'short', name: 'Short Hair', preview: '/avatars/hair/short.webp' },
    { id: 'long', name: 'Long Hair', preview: '/avatars/hair/long.webp' },
    { id: 'curly', name: 'Curly Hair', preview: '/avatars/hair/curly.webp' },
    { id: 'bald', name: 'Bald', preview: '/avatars/hair/bald.webp' },
  ],

  /**
   * Available hair colors
   */
  hairColors: [
    { id: 'black', name: 'Black', color: '#000000' },
    { id: 'brown', name: 'Brown', color: '#8B4513' },
    { id: 'blonde', name: 'Blonde', color: '#FFD700' },
    { id: 'red', name: 'Red', color: '#DC143C' },
    { id: 'blue', name: 'Blue', color: '#0000FF' },
    { id: 'green', name: 'Green', color: '#008000' },
    { id: 'purple', name: 'Purple', color: '#800080' },
  ],

  /**
   * Available outfits
   */
  outfits: [
    { id: 'casual', name: 'Casual', preview: '/avatars/outfits/casual.webp' },
    { id: 'formal', name: 'Formal', preview: '/avatars/outfits/formal.webp' },
    { id: 'sports', name: 'Sports', preview: '/avatars/outfits/sports.webp' },
    { id: 'futuristic', name: 'Futuristic', preview: '/avatars/outfits/futuristic.webp' },
  ],

  /**
   * Available accessories
   */
  accessories: [
    { id: 'glasses', name: 'Glasses', preview: '/avatars/accessories/glasses.webp' },
    { id: 'hat', name: 'Hat', preview: '/avatars/accessories/hat.webp' },
    { id: 'earrings', name: 'Earrings', preview: '/avatars/accessories/earrings.webp' },
    { id: 'watch', name: 'Watch', preview: '/avatars/accessories/watch.webp' },
  ],
};

/**
 * Validate avatar configuration
 * @param config - Avatar configuration
 * @returns Validation result
 */
export function validateAvatarConfig(config: any): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (config.hairStyle && !avatar3D.hairStyles.find(h => h.id === config.hairStyle)) {
    errors.push('Invalid hair style');
  }
  
  if (config.hairColor && !avatar3D.hairColors.find(c => c.id === config.hairColor)) {
    errors.push('Invalid hair color');
  }
  
  if (config.outfit && !avatar3D.outfits.find(o => o.id === config.outfit)) {
    errors.push('Invalid outfit');
  }
  
  if (config.accessories && Array.isArray(config.accessories)) {
    config.accessories.forEach((accessory: string) => {
      if (!avatar3D.accessories.find(a => a.id === accessory)) {
        errors.push(`Invalid accessory: ${accessory}`);
      }
    });
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}
