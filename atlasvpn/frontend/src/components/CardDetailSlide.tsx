import React, { useCallback, memo, useRef, useEffect, useState, useMemo, useId } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { createPortal } from 'react-dom';
import { UserCard, CardCatalog, UpgradeResult, UnifiedCardResponse } from '../types/cardTypes';
import { formatCurrency } from '../utils/format';
import { FaCoins, FaArrowUp, FaSpinner, FaTimes, FaBolt, FaShieldAlt } from 'react-icons/fa';
import { isLowEndDevice } from '../utils/performance';
import { tg } from '../utils/telegram'; // Import tg
import { toast } from 'react-hot-toast'; // Import toast for notifications
import { haptics } from '../utils/haptics'; // Import haptics
import { decodeHtmlEntities } from '../utils/html';

// Define PanInfo type for drag handling
interface PanInfo {
  point: {
    x: number;
    y: number;
  };
  delta: {
    x: number;
    y: number;
  };
  offset: {
    x: number;
    y: number;
  };
  velocity: {
    x: number;
    y: number;
  };
}

// Rarity color map (can be shared or redefined)
const rarityColors: Record<string, string> = {
  common: 'border-gray-400 shadow-gray-300/10',
  rare: 'border-blue-400 shadow-blue-300/20',
  epic: 'border-purple-500 shadow-purple-400/30',
  legendary: 'border-yellow-400 shadow-yellow-300/40',
  mythic: 'border-pink-400 shadow-pink-300/50',
};

const rarityTextColors: Record<string, string> = {
  common: 'text-gray-300',
  rare: 'text-blue-300',
  epic: 'text-purple-300',
  legendary: 'text-yellow-300',
  mythic: 'text-pink-300',
};

interface CardDetailSlideProps {
  unifiedCardData?: UnifiedCardResponse | null; // Use this as the main data source
  isOpen: boolean;
  onClose: () => void;
  onCardUpdated: () => void; // Callback to refresh list in parent
  onUpgrade?: (userCardId: number) => Promise<UpgradeResult>; // For owned cards
  onBuy?: (cardCatalogId: number) => Promise<{ success: boolean; error?: string }>; // For unowned cards
}

const CardDetailSlide: React.FC<CardDetailSlideProps> = memo(
  ({ unifiedCardData, isOpen, onClose, onCardUpdated, onUpgrade, onBuy }) => {
    // Internal state for processing
    const [isProcessing, setIsProcessing] = useState(false);
    const slideGradientId = useId();
    const isLowEnd = useMemo(() => isLowEndDevice(), []);
    const [slideY, setSlideY] = useState('0%');
    const slideRef = useRef<HTMLDivElement>(null);

    // Reset slide position when opening
    useEffect(() => {
      if (isOpen) setSlideY('0%');
    }, [isOpen]);

    // Telegram swipe/scroll lock
    useEffect(() => {
      if (isOpen) {
        if (
          tg &&
          tg.isVersionAtLeast &&
          tg.isVersionAtLeast('6.1') &&
          typeof tg.disableVerticalSwipes === 'function'
        ) {
          tg.disableVerticalSwipes();
        }
        document.body.style.overflow = 'hidden';
      } else {
        if (
          tg &&
          tg.isVersionAtLeast &&
          tg.isVersionAtLeast('6.1') &&
          typeof tg.enableVerticalSwipes === 'function'
        ) {
          tg.enableVerticalSwipes();
        }
        document.body.style.overflow = '';
      }
      return () => {
        if (
          tg &&
          tg.isVersionAtLeast &&
          tg.isVersionAtLeast('6.1') &&
          typeof tg.enableVerticalSwipes === 'function'
        ) {
          tg.enableVerticalSwipes();
        }
        document.body.style.overflow = '';
      };
    }, [isOpen]);

    // Keyboard ESC close
    const handleClose = useCallback(
      (e?: React.MouseEvent<HTMLElement>) => {
        if (e) e.stopPropagation();
        if (isProcessing) return;
        onClose();
      },
      [onClose, isProcessing]
    );

    // Drag close
    const handleDragEnd = useCallback(
      (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
        if (info.offset.y > 100 && !isProcessing) handleClose();
        else setSlideY('0%');
      },
      [handleClose, isProcessing]
    );

    useEffect(() => {
      const handleKeyDown = (event: KeyboardEvent) => {
        if (event.key === 'Escape') handleClose();
      };
      window.addEventListener('keydown', handleKeyDown);
      return () => window.removeEventListener('keydown', handleKeyDown);
    }, [handleClose]);

    // --- Main derived data ---
    const cardInfo = useMemo(() => {
      if (!unifiedCardData) return null;
      return {
        id: unifiedCardData.id,
        name: unifiedCardData.name,
        description: unifiedCardData.description,
        image_url: unifiedCardData.image_url,
        rarity: unifiedCardData.rarity || 'common',
        price: unifiedCardData.price,
        profit_rate: unifiedCardData.profit_rate,
        max_level: unifiedCardData.max_level,
        level_profits: unifiedCardData.level_profits,
        is_owned: unifiedCardData.is_owned,
      } as CardCatalog;
    }, [unifiedCardData]);

    const userCard = useMemo(() => {
      if (!unifiedCardData?.is_owned || !unifiedCardData.user_card) return null;
      return {
        ...unifiedCardData.user_card,
        card: cardInfo,
      } as UserCard;
    }, [unifiedCardData, cardInfo]);

    // Debug logs for tracing data
    useEffect(() => {
      console.log('[CardDetailSlide] unifiedCardData:', unifiedCardData);
      console.log('[CardDetailSlide] cardInfo:', cardInfo);
      console.log('[CardDetailSlide] userCard:', userCard);
    }, [unifiedCardData, cardInfo, userCard]);

    // Parse profits/costs arrays
    const parsedLevelProfits = useMemo(() => {
      if (!cardInfo) return [];
      if (Array.isArray(cardInfo.level_profits)) return cardInfo.level_profits;
      if (typeof (cardInfo as any).level_profits_json === 'string') {
        try {
          return JSON.parse((cardInfo as any).level_profits_json);
        } catch {
          return [];
        }
      }
      return [];
    }, [cardInfo]);
    const parsedLevelCosts = useMemo(() => {
      if (!cardInfo) return [];
      if (Array.isArray((cardInfo as any).level_costs)) return (cardInfo as any).level_costs;
      if (typeof (cardInfo as any).level_costs_json === 'string') {
        try {
          return JSON.parse((cardInfo as any).level_costs_json);
        } catch {
          return [];
        }
      }
      return [];
    }, [cardInfo]);
    const rarity = cardInfo?.rarity || 'common';
    const borderColor = rarityColors[rarity] || rarityColors['common'];
    const textColor = rarityTextColors[rarity] || rarityTextColors['common'];
    const glowColor = useMemo(() => {
      switch (rarity) {
        case 'rare':
          return '#60a5fa';
        case 'epic':
          return '#a855f7';
        case 'legendary':
          return '#facc15';
        case 'mythic':
          return '#ec4899';
        case 'common':
        default:
          return '#94a3b8';
      }
    }, [rarity]);
    const shadowClass = useMemo(() => {
      switch (rarity) {
        case 'rare':
          return 'shadow-blue-500/20';
        case 'epic':
          return 'shadow-purple-500/25';
        case 'legendary':
          return 'shadow-yellow-500/30';
        case 'mythic':
          return 'shadow-pink-500/35';
        case 'common':
        default:
          return 'shadow-slate-500/15';
      }
    }, [rarity]);

    // --- Owned card derived values ---
    const level = userCard?.level ?? 1;
    const isMaxLevel = userCard && cardInfo ? level >= cardInfo.max_level : false;
    const hourlyProfit = userCard?.current_hourly_profit ?? cardInfo?.profit_rate ?? 0;
    const nextLevelProfit =
      !isMaxLevel && cardInfo?.level_profits?.[level] ? cardInfo.level_profits[level] : null;
    const profitIncrease =
      nextLevelProfit !== null && userCard ? nextLevelProfit - hourlyProfit : null;
    const nextUpgradeCost = !isMaxLevel ? (userCard?.next_upgrade_cost ?? null) : null;
    const canUpgrade = !!(userCard && !isMaxLevel && nextUpgradeCost !== null);

    // --- Callbacks ---
    const handleUpgrade = useCallback(
      async (e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        if (!userCard || !canUpgrade || !onUpgrade) return;
        setIsProcessing(true);
        if (tg && tg.isVersionAtLeast && tg.isVersionAtLeast('6.1')) haptics.impact('medium');
        try {
          const result = await onUpgrade(userCard.id);
          if (result.success && result.updatedCard) {
            toast.success(`${result.updatedCard.card.name ?? 'Card'} upgraded!`, { icon: '✨' });
            if (tg && tg.isVersionAtLeast && tg.isVersionAtLeast('6.1'))
              haptics.notification('success');
            onCardUpdated();
          }
        } catch (error: any) {
          toast.error('Upgrade failed. Please try again.');
          if (tg && tg.isVersionAtLeast && tg.isVersionAtLeast('6.1'))
            haptics.notification('error');
        } finally {
          setIsProcessing(false);
        }
      },
      [userCard, canUpgrade, onUpgrade, onCardUpdated]
    );

    const handleBuy = useCallback(
      async (e: React.MouseEvent<HTMLElement>) => {
        e.stopPropagation();
        if (userCard || !onBuy || !cardInfo) return;
        setIsProcessing(true);
        haptics.impact('medium');
        try {
          const result = await onBuy(cardInfo.id);
          if (result.success) {
            toast.success(`${cardInfo.name ?? 'Card'} purchased!`, { icon: '🛒' });
            haptics.notification('success');
          } else {
            toast.error(result.error || 'Purchase failed.');
            haptics.notification('error');
          }
        } catch (error: any) {
          toast.error('Purchase failed. Please try again.');
          haptics.notification('error');
        } finally {
          setIsProcessing(false);
        }
      },
      [userCard, onBuy, cardInfo]
    );

    // --- UI ---
    const PullIndicator = () => (
      <div className="w-full flex justify-center mb-1 -mt-1 opacity-60">
        <div className="w-10 h-1 bg-white/30 rounded-full"></div>
      </div>
    );

    // Define getRarityStars here, doesn't depend on state/props directly
    const getRarityStars = (rarity: string): string => {
      switch (rarity) {
        case 'common':
          return '★';
        case 'rare':
          return '★★';
        case 'epic':
          return '★★★';
        case 'legendary':
          return '★★★★';
        case 'mythic':
          return '★★★★★';
        default:
          return '★';
      }
    };

    return createPortal(
      <AnimatePresence>
        {isOpen && (
          <motion.div
            key="card-slide-backdrop"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: isLowEnd ? 0.15 : 0.2 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-md z-50 flex items-end justify-center will-change-transform overscroll-none"
            onClick={handleClose}
            style={{ touchAction: 'none', overscrollBehavior: 'contain' }}
          >
            <motion.div
              key="card-slide-content"
              initial={{ y: '100%' }}
              animate={{ y: slideY }}
              exit={{ y: '100%' }}
              transition={{
                type: isLowEnd ? 'tween' : 'spring',
                stiffness: isLowEnd ? undefined : 300,
                damping: isLowEnd ? undefined : 30,
                mass: isLowEnd ? undefined : 0.8,
                duration: isLowEnd ? 0.25 : undefined,
              }}
              drag="y"
              dragConstraints={{ top: 0, bottom: 0 }}
              dragElastic={{ top: 0, bottom: 0.5 }}
              onDragEnd={handleDragEnd}
              {...({} as any)}
              className={`relative w-full max-w-md bg-gradient-to-b from-gray-900/80 to-[#13131e]/90 rounded-t-2xl border-t-2 ${cardInfo ? borderColor.split(' ')[0] : 'border-gray-500'} ${shadowClass} overflow-hidden flex flex-col max-h-[85vh] will-change-transform backdrop-blur-md`}
              onClick={e => {
                if (e) e.stopPropagation();
              }}
            >
              <div className="absolute inset-0 flex items-center justify-center overflow-hidden pointer-events-none -z-10">
                <svg
                  className="absolute h-80 w-80 opacity-25"
                  viewBox="0 0 100 100"
                  preserveAspectRatio="xMidYMid slice"
                >
                  <defs>
                    <radialGradient id={slideGradientId} cx="50%" cy="50%" r="50%">
                      <stop offset="0%" stopColor={glowColor} stopOpacity="0.6" />
                      <stop offset="100%" stopColor={glowColor} stopOpacity="0" />
                    </radialGradient>
                  </defs>
                  <circle cx="50" cy="50" r="50" fill={`url(#${slideGradientId})`} />
                </svg>
              </div>
              <div ref={slideRef} className="w-full h-full flex flex-col overflow-hidden">
                <PullIndicator />
                {isProcessing && (
                  <div className="absolute inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center">
                    <div className="bg-black/80 p-4 rounded-xl shadow-lg flex flex-col items-center">
                      <FaSpinner className="animate-spin text-2xl text-purple-400 mb-2" />
                      <div className="text-white/90 text-sm">Processing...</div>
                    </div>
                  </div>
                )}
                <button
                  onClick={handleClose}
                  className="absolute top-3 right-3 z-20 p-1.5 rounded-full text-white/50 bg-black/30 hover:text-white hover:bg-white/10 transition-all"
                  aria-label="Close card details"
                >
                  <FaTimes className="h-4 w-4" />
                </button>
                {cardInfo ? (
                  <>
                    <div className="absolute top-3 left-3 z-20 px-3 py-1 rounded-full bg-black/50 backdrop-blur-md border border-white/10">
                      <div className={`text-xs font-bold flex items-center gap-1 ${textColor}`}>
                        <span>{rarity.toUpperCase()}</span>
                        <span className="text-yellow-400">{getRarityStars(rarity)}</span>
                      </div>
                    </div>
                    <div
                      className={`h-48 bg-cover bg-center relative flex items-end p-4 shadow-inner`}
                      style={{
                        backgroundImage: `url(${cardInfo?.image_url || '/img/card-placeholder.jpg'})`,
                        backgroundSize: 'cover',
                        display: 'flex',
                        willChange: 'transform',
                      }}
                    >
                      <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent" />
                      <div className="relative z-10 w-full">
                        <h2 className={`text-xl font-bold text-white drop-shadow-lg ${textColor}`}>
                          {cardInfo?.name}
                        </h2>
                        {userCard && (
                          <div className="flex items-center mt-1">
                            <span
                              className={`text-xs font-medium px-3 py-1 rounded-full text-white/90 bg-black/50 backdrop-blur-sm border border-white/10`}
                            >
                              Level {level} / {cardInfo?.max_level}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div
                      className="p-4 flex-grow overflow-y-auto space-y-4 custom-scrollbar"
                      style={{ overscrollBehavior: 'contain' }}
                    >
                      <p className="text-sm text-white/80 leading-relaxed font-display">
                        {decodeHtmlEntities(cardInfo?.description || 'No description available.')}
                      </p>
                      <div className="grid grid-cols-2 gap-3 text-xs">
                        <div className="bg-black/30 p-3 rounded-lg border border-purple-500/30 shadow-inner shadow-purple-900/20">
                          <div className="text-purple-300/80 mb-1 text-xs font-medium">
                            Hourly Profit
                          </div>
                          <div className="text-white text-sm font-semibold flex items-center gap-1.5">
                            <FaBolt className="text-yellow-400 text-xs" />{' '}
                            {formatCurrency(hourlyProfit)}
                          </div>
                        </div>
                        <div className="bg-black/30 p-3 rounded-lg border border-green-500/30 shadow-inner shadow-green-900/20">
                          <div className="text-green-300/80 mb-1 text-xs font-medium">
                            Next Level Profit
                          </div>
                          <div className="text-white text-sm font-semibold flex items-center gap-1.5">
                            {profitIncrease !== null && profitIncrease > 0 ? (
                              <>
                                <FaArrowUp className="text-green-400 text-xs" /> +
                                {formatCurrency(profitIncrease)}/hr
                              </>
                            ) : (
                              <span className="text-xs italic text-white/60">
                                {isMaxLevel ? 'Max Level' : 'None'}
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="bg-black/30 p-3 rounded-lg border border-blue-500/30 shadow-inner shadow-blue-900/20 col-span-2">
                          <div className="text-blue-300/80 mb-1 text-xs font-medium">
                            Upgrade Price
                          </div>
                          <div className="text-white text-sm font-semibold flex items-center gap-1.5">
                            {nextUpgradeCost !== null ? (
                              <>
                                <FaCoins className="text-blue-400 text-xs" />{' '}
                                {formatCurrency(nextUpgradeCost)}
                              </>
                            ) : (
                              <span className="text-xs italic text-white/60">Max Level</span>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="p-4 border-t border-white/10 bg-gradient-to-t from-black/50 to-transparent flex-shrink-0 flex gap-3">
                      {userCard ? (
                        <button
                          onClick={handleUpgrade}
                          disabled={isProcessing || !canUpgrade || !onUpgrade}
                          className={`w-full py-3 rounded-lg text-sm font-semibold flex items-center justify-center gap-1.5 transition-colors duration-200
                        ${
                          isProcessing
                            ? 'bg-gray-600 text-white/70'
                            : !canUpgrade
                              ? 'bg-gray-700 text-white/50'
                              : !onUpgrade
                                ? 'bg-gray-700 text-white/50'
                                : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-lg shadow-purple-500/20 active:scale-[0.98]'
                        }`}
                        >
                          {isProcessing ? <FaSpinner className="animate-spin" /> : <FaArrowUp />}
                          <span>
                            {canUpgrade && nextUpgradeCost !== null
                              ? `Upgrade (${formatCurrency(nextUpgradeCost)})`
                              : 'Upgrade'}
                            {canUpgrade && profitIncrease && profitIncrease > 0 && (
                              <span className="ml-1 text-green-300 text-opacity-80">
                                {' '}
                                (+{formatCurrency(profitIncrease)}/hr)
                              </span>
                            )}
                          </span>
                        </button>
                      ) : (
                        <button
                          onClick={handleBuy}
                          disabled={isProcessing || !onBuy}
                          className={`w-full py-3 rounded-lg text-sm font-semibold flex items-center justify-center gap-1.5 transition-colors duration-200
                          ${
                            isProcessing
                              ? 'bg-gray-600 text-white/70'
                              : !onBuy
                                ? 'bg-gray-700 text-white/50'
                                : 'bg-gradient-to-r from-amber-500 to-yellow-500 text-white hover:from-amber-600 hover:to-yellow-600 shadow-lg shadow-yellow-500/20 active:scale-[0.98]'
                          }`}
                        >
                          {isProcessing ? <FaSpinner className="animate-spin" /> : <FaCoins />}
                          <span>{`Buy (${formatCurrency(cardInfo.price)})`}</span>
                        </button>
                      )}
                    </div>
                  </>
                ) : (
                  <div className="flex-grow flex items-center justify-center p-10">
                    <FaSpinner className="animate-spin text-purple-400 text-3xl" />
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>,
      document.body
    );
  }
);

CardDetailSlide.displayName = 'CardDetailSlide';

export default CardDetailSlide;
