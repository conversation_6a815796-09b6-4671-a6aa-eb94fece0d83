import sqlite3
import os
import json
import datetime

DB_PATH = "users.db"

def check_for_column(cursor, table, column):
    """Check if a column exists in a table."""
    cursor.execute(f"PRAGMA table_info({table})")
    columns = [column_info[1] for column_info in cursor.fetchall()]
    return column in columns

def check_for_index(cursor, index_name):
    """Check if an index exists in the database."""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name=?", (index_name,))
    return bool(cursor.fetchone())

def check_for_table(cursor, table_name):
    """Check if a table exists in the database."""
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
    return bool(cursor.fetchone())

def add_column(cursor, table, column, column_def):
    """Add a column to a table if it doesn't exist."""
    if not check_for_column(cursor, table, column):
        print(f"Adding missing column: {column} to {table} table...")
        cursor.execute(f"ALTER TABLE {table} ADD COLUMN {column} {column_def}")
        print(f"Column {column} added successfully.")
    else:
        print(f"Column {column} already exists in table {table}. Skipping.")

def create_index(cursor, index_name, table, columns, unique=False):
    """Create an index if it doesn't exist."""
    if not check_for_index(cursor, index_name):
        unique_sql = "UNIQUE" if unique else ""
        columns_str = ", ".join(columns)
        print(f"Creating index: {index_name} on {table}({columns_str})...")
        cursor.execute(f"CREATE {unique_sql} INDEX IF NOT EXISTS {index_name} ON {table}({columns_str})")
        print(f"Index {index_name} created successfully.")
    else:
        print(f"Index {index_name} already exists. Skipping.")

def drop_and_recreate_table(cursor, table_name, sql_create_table):
    """Drop a table and recreate it with the correct structure."""
    print(f"Dropping table {table_name} to recreate with correct structure...")
    cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
    cursor.execute(sql_create_table)
    print(f"Table {table_name} recreated successfully.")

def verify_table_structure(cursor, table_name, required_columns):
    """Verify that a table has all required columns."""
    cursor.execute(f"PRAGMA table_info({table_name})")
    existing_columns = [column_info[1] for column_info in cursor.fetchall()]
    
    missing_columns = [col for col in required_columns if col not in existing_columns]
    
    if missing_columns:
        print(f"Table {table_name} is missing columns: {', '.join(missing_columns)}")
        return False
    return True

def create_table_if_not_exists(cursor, table_name, sql_create_table, required_columns=None):
    """Create a table if it doesn't exist, or recreate if missing required columns."""
    if not check_for_table(cursor, table_name):
        print(f"Creating table: {table_name}...")
        cursor.execute(sql_create_table)
        print(f"Table {table_name} created successfully.")
    elif required_columns and not verify_table_structure(cursor, table_name, required_columns):
        drop_and_recreate_table(cursor, table_name, sql_create_table)
    else:
        print(f"Table {table_name} already exists with all required columns. Skipping.")

def add_test_cards(cursor):
    """Add test cards to the catalog if none exist."""
    cursor.execute("SELECT COUNT(*) FROM card_catalog")
    card_count = cursor.fetchone()[0]
    
    if card_count == 0:
        print("No cards found in catalog. Adding test cards...")
        
        # Common starter card
        cursor.execute("""
        INSERT INTO card_catalog 
        (name, rarity, level_profits_json, level_costs_json, max_level, image_url, description) 
        VALUES 
        (?, ?, ?, ?, ?, ?, ?)
        """, (
            "Network Novice", 
            "common", 
            json.dumps([0.1, 0.15, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]), 
            json.dumps([50, 100, 150, 200, 300, 400, 500, 600, 800, 1000]), 
            10,
            "https://img.icons8.com/fluency/96/network-card.png",
            "A beginner's network card that provides a small but steady stream of income."
        ))
        
        # Rare mid-tier card
        cursor.execute("""
        INSERT INTO card_catalog 
        (name, rarity, level_profits_json, level_costs_json, max_level, image_url, description) 
        VALUES 
        (?, ?, ?, ?, ?, ?, ?)
        """, (
            "Data Miner", 
            "rare", 
            json.dumps([0.3, 0.45, 0.6, 0.8, 1.0, 1.2, 1.4, 1.7, 2.0, 2.5]), 
            json.dumps([200, 300, 400, 500, 650, 800, 1000, 1200, 1500, 2000]), 
            10,
            "https://img.icons8.com/fluency/96/data-mining.png",
            "This specialized card mines data faster, earning you more coins per hour."
        ))
        
        # Epic advanced card
        cursor.execute("""
        INSERT INTO card_catalog 
        (name, rarity, level_profits_json, level_costs_json, max_level, image_url, description) 
        VALUES 
        (?, ?, ?, ?, ?, ?, ?)
        """, (
            "VPN Guardian", 
            "epic", 
            json.dumps([0.7, 1.0, 1.4, 1.8, 2.3, 2.8, 3.4, 4.0, 4.8, 6.0]), 
            json.dumps([500, 700, 900, 1200, 1500, 1800, 2200, 2700, 3500, 5000]), 
            10,
            "https://img.icons8.com/fluency/96/security-shield-green.png",
            "A powerful security card that protects networks and generates substantial income."
        ))
        
        # Legendary premium card
        cursor.execute("""
        INSERT INTO card_catalog 
        (name, rarity, level_profits_json, level_costs_json, max_level, image_url, description) 
        VALUES 
        (?, ?, ?, ?, ?, ?, ?)
        """, (
            "Quantum Router", 
            "legendary", 
            json.dumps([1.5, 2.2, 3.0, 4.0, 5.2, 6.5, 8.0, 10.0, 12.5, 15.0]), 
            json.dumps([1000, 1500, 2000, 2800, 3500, 4500, 6000, 8000, 11000, 15000]), 
            10,
            "https://img.icons8.com/fluency/96/router.png",
            "The ultimate networking card with quantum technology, providing incredible hourly profits."
        ))
        
        print("Added 4 test cards to the card catalog.")
        
        # Optionally assign the starter card to an existing user for testing
        cursor.execute("SELECT id FROM users LIMIT 1")
        first_user = cursor.fetchone()
        
        if first_user:
            user_id = first_user[0]
            # Add the Network Novice card to the first user
            cursor.execute("""
            INSERT INTO user_cards
            (user_id, card_catalog_id, level, last_claim_at, acquired_at, total_claimed_profit, times_claimed)
            VALUES
            (?, ?, ?, ?, ?, ?, ?)
            """, (
                user_id, 
                1,  # The first card (Network Novice)
                1,  # Starting at level 1
                datetime.datetime.utcnow().isoformat(),
                datetime.datetime.utcnow().isoformat(),
                0.0,
                0
            ))
            print(f"Assigned 'Network Novice' card to user ID {user_id} for testing.")
    else:
        print(f"Found {card_count} existing cards in the catalog. Skipping test data creation.")

def update_schema():
    """Updates the database schema with missing columns, tables, and indexes."""
    if not os.path.exists(DB_PATH):
        print(f"Error: Database file not found at {DB_PATH}")
        return

    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        print("Database connected.")
        
        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON;")

        # 1. Check and create required tables
        
        # CardCatalog table
        SQL_CREATE_CARD_CATALOG = """
        CREATE TABLE card_catalog (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL UNIQUE,
            rarity VARCHAR(20) NOT NULL DEFAULT 'common',
            level_profits_json TEXT NOT NULL,
            level_costs_json TEXT NOT NULL,
            max_level INTEGER NOT NULL DEFAULT 10,
            image_url VARCHAR(255),
            description TEXT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        ); 
        """
        create_table_if_not_exists(
            cursor, 
            "card_catalog", 
            SQL_CREATE_CARD_CATALOG,
            ["id", "name", "rarity", "level_profits_json", "level_costs_json", "max_level", "image_url", "created_at"]
        )

        # UserCards table
        SQL_CREATE_USER_CARDS = """
        CREATE TABLE user_cards (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            card_catalog_id INTEGER NOT NULL,
            level INTEGER NOT NULL DEFAULT 1,
            last_claim_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            acquired_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_claimed_profit FLOAT DEFAULT 0.0,
            times_claimed INTEGER DEFAULT 0,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            FOREIGN KEY (card_catalog_id) REFERENCES card_catalog (id),
            UNIQUE (user_id, card_catalog_id)
        );
        """
        create_table_if_not_exists(
            cursor, 
            "user_cards", 
            SQL_CREATE_USER_CARDS,
            ["id", "user_id", "card_catalog_id", "level", "last_claim_at", "acquired_at"]
        )

        # CardTransactions table for better tracking
        SQL_CREATE_CARD_TRANSACTIONS = """
        CREATE TABLE card_transactions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            user_card_id INTEGER,
            transaction_id INTEGER,
            amount FLOAT NOT NULL,
            type VARCHAR(20) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
            FOREIGN KEY (user_card_id) REFERENCES user_cards (id) ON DELETE SET NULL,
            FOREIGN KEY (transaction_id) REFERENCES transactions (id) ON DELETE SET NULL
        );
        """
        create_table_if_not_exists(
            cursor, 
            "card_transactions", 
            SQL_CREATE_CARD_TRANSACTIONS,
            ["id", "user_id", "user_card_id", "transaction_id", "amount", "type", "created_at"]
        )

        # 2. Check and add missing columns in existing tables

        # Users table
        add_column(cursor, "users", "last_passive_claim_at", "TIMESTAMP NULL")
        
        # Check if we need additional columns in CardCatalog
        add_column(cursor, "card_catalog", "description", "TEXT NULL")
        
        # Check for any missing columns in UserCards table
        add_column(cursor, "user_cards", "total_claimed_profit", "FLOAT DEFAULT 0.0")
        add_column(cursor, "user_cards", "times_claimed", "INTEGER DEFAULT 0")

        # 3. Explicitly check for critical columns and add them if missing
        if check_for_table(cursor, "card_catalog"):
            if not check_for_column(cursor, "card_catalog", "level_profits_json"):
                print("CRITICAL: Missing level_profits_json column in card_catalog table.")
                print("This will require data migration - recreating the table...")
                # Get existing data (if any)
                cursor.execute("SELECT * FROM card_catalog")
                existing_data = cursor.fetchall()
                
                if existing_data:
                    print(f"Found {len(existing_data)} rows of data to migrate.")
                    # Backup the table
                    cursor.execute("ALTER TABLE card_catalog RENAME TO card_catalog_backup")
                    print("Created backup of card_catalog table.")
                
                # Recreate the table with correct structure
                cursor.execute(SQL_CREATE_CARD_CATALOG)
                print("Recreated card_catalog table with correct columns.")
                
                # We can't migrate the data automatically since critical columns are missing
                if existing_data:
                    print("WARNING: You need to manually migrate data from card_catalog_backup to card_catalog")
                    print("The backup table contains data but is missing required columns.")
        
        # Similarly for user_cards table
        if check_for_table(cursor, "user_cards"):
            critical_columns = ["level", "last_claim_at", "acquired_at"]
            missing_critical = [col for col in critical_columns if not check_for_column(cursor, "user_cards", col)]
            
            if missing_critical:
                print(f"CRITICAL: Missing columns in user_cards table: {', '.join(missing_critical)}")
                print("This will require data migration - recreating the table...")
                # Get existing data (if any)
                cursor.execute("SELECT * FROM user_cards")
                existing_data = cursor.fetchall()
                
                if existing_data:
                    print(f"Found {len(existing_data)} rows of data to migrate.")
                    # Backup the table
                    cursor.execute("ALTER TABLE user_cards RENAME TO user_cards_backup")
                    print("Created backup of user_cards table.")
                
                # Recreate the table with correct structure
                cursor.execute(SQL_CREATE_USER_CARDS)
                print("Recreated user_cards table with correct columns.")
                
                if existing_data:
                    print("WARNING: You need to manually migrate data from user_cards_backup to user_cards")
                    print("The backup table contains data but is missing required columns.")

        # 4. Create required indexes for faster lookups
        
        # Indexes for UserCards
        create_index(cursor, "idx_user_cards_user_id", "user_cards", ["user_id"])
        create_index(cursor, "idx_user_cards_card_catalog_id", "user_cards", ["card_catalog_id"])
        create_index(cursor, "idx_user_cards_last_claim_at", "user_cards", ["last_claim_at"])
        
        # Indexes for CardCatalog
        create_index(cursor, "idx_card_catalog_rarity", "card_catalog", ["rarity"])
        create_index(cursor, "idx_card_catalog_created_at", "card_catalog", ["created_at"])
        
        # Indexes for CardTransactions
        create_index(cursor, "idx_card_transactions_user", "card_transactions", ["user_id"])
        create_index(cursor, "idx_card_transactions_card", "card_transactions", ["user_card_id"])
        create_index(cursor, "idx_card_transactions_transaction", "card_transactions", ["transaction_id"])

        # 5. Check and update TransactionType enum in Transaction table
        cursor.execute("SELECT sql FROM sqlite_master WHERE type='table' AND name='transactions'")
        table_sql = cursor.fetchone()
        
        # Check for card transaction types in the table
        if table_sql:
            # This is a simple detection, not a full validation of the table structure
            cursor.execute("SELECT DISTINCT type FROM transactions")
            transaction_types = [row[0] for row in cursor.fetchall()]
            
            required_card_types = ['card_profit', 'card_profit_all', 'card_upgrade', 'card_purchase']
            missing_types = []
            
            print("Checking for card transaction types...")
            for card_type in required_card_types:
                if card_type not in transaction_types:
                    print(f"Note: Transaction type '{card_type}' is not currently used in the database.")
                    missing_types.append(card_type)
                else:
                    print(f"Transaction type '{card_type}' is already in use.")
        
        # 6. Add test cards if the catalog is empty
        add_test_cards(cursor)
        
        conn.commit()
        print("Schema update completed successfully.")

    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        if conn:
            conn.rollback()
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()
            print("Database connection closed.")

if __name__ == "__main__":
    update_schema()