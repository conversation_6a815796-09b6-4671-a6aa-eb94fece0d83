/* XPM */
static char * linux_icon_chrome_stable_32_xpm[] = {
"32 32 335 2",
"  	c None",
". 	c #DF372A",
"+ 	c #E0392C",
"@ 	c #E1392D",
"# 	c #E23A2D",
"$ 	c #E13A2D",
"% 	c #E23A2E",
"& 	c #E33C2F",
"* 	c #E43B2F",
"= 	c #DD3529",
"- 	c #DE352B",
"; 	c #DF362A",
"> 	c #E0372B",
", 	c #DF372B",
"' 	c #E0382C",
") 	c #E2392C",
"! 	c #E2392D",
"~ 	c #E33B2E",
"{ 	c #E43C2F",
"] 	c #E43D30",
"^ 	c #E53E31",
"/ 	c #E73F32",
"( 	c #DD3229",
"_ 	c #DE3529",
": 	c #DE3429",
"< 	c #E1382C",
"[ 	c #E33A2D",
"} 	c #E53D30",
"| 	c #E63E31",
"1 	c #E63F31",
"2 	c #E73E32",
"3 	c #DC3228",
"4 	c #DD3428",
"5 	c #DE362A",
"6 	c #E1392C",
"7 	c #E23B2E",
"8 	c #E53C2F",
"9 	c #E73E31",
"0 	c #E84033",
"a 	c #DC3227",
"b 	c #DC3328",
"c 	c #DC3428",
"d 	c #DD3429",
"e 	c #DE352A",
"f 	c #E53E30",
"g 	c #E74032",
"h 	c #E84133",
"i 	c #DA3227",
"j 	c #DB3227",
"k 	c #DF3529",
"l 	c #E73F31",
"m 	c #E94233",
"n 	c #DA3126",
"o 	c #DB3126",
"p 	c #DC3226",
"q 	c #E0382B",
"r 	c #E63D30",
"s 	c #E74033",
"t 	c #E94134",
"u 	c #EA4235",
"v 	c #DB3026",
"w 	c #E33B2F",
"x 	c #E63F32",
"y 	c #E94234",
"z 	c #EA4234",
"A 	c #EB4234",
"B 	c #906539",
"C 	c #DA3025",
"D 	c #DA3226",
"E 	c #DB3327",
"F 	c #E76960",
"G 	c #F1A8A3",
"H 	c #FBE6E5",
"I 	c #FFFFFF",
"J 	c #FEF3D1",
"K 	c #FDE7A4",
"L 	c #FBC62A",
"M 	c #FBBE0B",
"N 	c #FCBE0A",
"O 	c #FBBD09",
"P 	c #FCBE08",
"Q 	c #FBBD07",
"R 	c #FBBC06",
"S 	c #FBBD05",
"T 	c #FCBD05",
"U 	c #FBBC04",
"V 	c #33A853",
"W 	c #3EA050",
"X 	c #D03929",
"Y 	c #DD3328",
"Z 	c #E04237",
"` 	c #F1A7A2",
" .	c #D4E5FB",
"..	c #A9CAF6",
"+.	c #B8D3F8",
"@.	c #FDE395",
"#.	c #FCC31B",
"$.	c #FBBD0A",
"%.	c #FBBE0A",
"&.	c #FBBD08",
"*.	c #FCBD07",
"=.	c #FBBC05",
"-.	c #FBBD04",
";.	c #34A753",
">.	c #7C743F",
",.	c #DC3327",
"'.	c #DF4137",
").	c #F9D9D7",
"!.	c #C6DCF9",
"~.	c #458DEC",
"{.	c #1A73E8",
"].	c #3784EB",
"^.	c #FBC21B",
"/.	c #FBBE0C",
"(.	c #FCBD08",
"_.	c #FCBD06",
":.	c #33A652",
"<.	c #32A551",
"[.	c #BB482E",
"}.	c #F0A7A1",
"|.	c #8CB9F4",
"1.	c #FCBF0E",
"2.	c #FBBD06",
"3.	c #33A752",
"4.	c #32A651",
"5.	c #31A650",
"6.	c #5B8846",
"7.	c #E14E44",
"8.	c #FDD04A",
"9.	c #FBBF0E",
"0.	c #31A551",
"a.	c #31A550",
"b.	c #31A450",
"c.	c #B04F31",
"d.	c #F2B3AE",
"e.	c #3684EB",
"f.	c #5396EE",
"g.	c #FEE497",
"h.	c #FBBF0F",
"i.	c #FBBE0D",
"j.	c #FCBF0B",
"k.	c #FBBE09",
"l.	c #30A34F",
"m.	c #2FA24E",
"n.	c #459449",
"o.	c #D13A2A",
"p.	c #F9DAD7",
"q.	c #E3EEFC",
"r.	c #FFF7E1",
"s.	c #FCC010",
"t.	c #FCC00F",
"u.	c #2FA34E",
"v.	c #2FA14E",
"w.	c #2EA14E",
"x.	c #856A3A",
"y.	c #A9CBF6",
"z.	c #FBC012",
"A.	c #FBBF11",
"B.	c #FBBF10",
"C.	c #2EA24E",
"D.	c #2EA24D",
"E.	c #2EA14D",
"F.	c #38994A",
"G.	c #D23B2A",
"H.	c #B8D3F7",
"I.	c #FCC114",
"J.	c #FCC113",
"K.	c #FBC011",
"L.	c #FCC012",
"M.	c #FCBF10",
"N.	c #FCBF0F",
"O.	c #2FA24F",
"P.	c #2DA04C",
"Q.	c #2CA04B",
"R.	c #6E763E",
"S.	c #FBE6E4",
"T.	c #D3E5FB",
"U.	c #FEF3D3",
"V.	c #FBC116",
"W.	c #FBC014",
"X.	c #FCC013",
"Y.	c #FBC013",
"Z.	c #FCC011",
"`.	c #2DA04D",
" +	c #2D9F4C",
".+	c #2C9F4B",
"++	c #2B9E4A",
"@+	c #C3C1AA",
"#+	c #FDE8A8",
"$+	c #FCC217",
"%+	c #FCC115",
"&+	c #FBC015",
"*+	c #FCC014",
"=+	c #2FA04D",
"-+	c #2C9F4C",
";+	c #2B9E4B",
">+	c #2C9E4B",
",+	c #2A9E4A",
"'+	c #2A9D4A",
")+	c #5FB577",
"!+	c #FCCA36",
"~+	c #FBC218",
"{+	c #FBC117",
"]+	c #FCC116",
"^+	c #2B9F4B",
"/+	c #2B9D4A",
"(+	c #2A9D49",
"_+	c #2A9C49",
":+	c #299B48",
"<+	c #A1D3AF",
"[+	c #8DB9F3",
"}+	c #F0E29E",
"|+	c #FCC31A",
"1+	c #FCC219",
"2+	c #FBC118",
"3+	c #FBC115",
"4+	c #299C48",
"5+	c #289B48",
"6+	c #35A153",
"7+	c #D7EBDC",
"8+	c #5396ED",
"9+	c #E4EEDA",
"0+	c #EEC42D",
"a+	c #FCC21C",
"b+	c #FBC21C",
"c+	c #FBC21A",
"d+	c #FCC218",
"e+	c #2C9E4A",
"f+	c #299B49",
"g+	c #289A47",
"h+	c #279A46",
"i+	c #34A052",
"j+	c #A0D2AE",
"k+	c #9FD1AD",
"l+	c #9CB23C",
"m+	c #FCC41F",
"n+	c #FBC31E",
"o+	c #FCC31D",
"p+	c #FCC31C",
"q+	c #FCC21A",
"r+	c #FCC016",
"s+	c #289A48",
"t+	c #279947",
"u+	c #279846",
"v+	c #269946",
"w+	c #259845",
"x+	c #41A45D",
"y+	c #ADD7B9",
"z+	c #D6EBDC",
"A+	c #E3F2E8",
"B+	c #9FD0AC",
"C+	c #5AAE71",
"D+	c #3D993E",
"E+	c #FBC320",
"F+	c #FCC31F",
"G+	c #FBC31D",
"H+	c #FCC21B",
"I+	c #299A48",
"J+	c #269745",
"K+	c #259744",
"L+	c #249644",
"M+	c #239643",
"N+	c #239443",
"O+	c #229342",
"P+	c #229241",
"Q+	c #ABB22E",
"R+	c #FCC423",
"S+	c #FCC422",
"T+	c #FCC421",
"U+	c #FDC31C",
"V+	c #289948",
"W+	c #269846",
"X+	c #269746",
"Y+	c #259745",
"Z+	c #249544",
"`+	c #239543",
" @	c #229242",
".@	c #219241",
"+@	c #589F3A",
"@@	c #FCC525",
"#@	c #FCC524",
"$@	c #FCC523",
"%@	c #FCC420",
"&@	c #FCC41E",
"*@	c #FCC31E",
"=@	c #279847",
"-@	c #259645",
";@	c #249643",
">@	c #229442",
",@	c #239342",
"'@	c #219341",
")@	c #219140",
"!@	c #E0BF2A",
"~@	c #FCC626",
"{@	c #FBC425",
"]@	c #FDC524",
"^@	c #FCC522",
"/@	c #FCC320",
"(@	c #219141",
"_@	c #73A538",
":@	c #FBC628",
"<@	c #FCC628",
"[@	c #FCC527",
"}@	c #FCC526",
"|@	c #FCC424",
"1@	c #FCC322",
"2@	c #249543",
"3@	c #229341",
"4@	c #219240",
"5@	c #209240",
"6@	c #209140",
"7@	c #3C973D",
"8@	c #E0C02E",
"9@	c #FCC62A",
"0@	c #FCC627",
"a@	c #249344",
"b@	c #209040",
"c@	c #1F903F",
"d@	c #9CAF36",
"e@	c #FBC72C",
"f@	c #FCC72C",
"g@	c #FCC72B",
"h@	c #FCC629",
"i@	c #FDC625",
"j@	c #1F8F3F",
"k@	c #3A963D",
"l@	c #FCC830",
"m@	c #FCC72E",
"n@	c #FCC72D",
"o@	c #FCC62C",
"p@	c #208F40",
"q@	c #1F8F3E",
"r@	c #A9B336",
"s@	c #FDC730",
"t@	c #FBC82E",
"                        . + @ # $ % & *                         ",
"                  = - ; > , ' ) ! % ~ { ] ^ /                   ",
"              ( _ : _ ; > ' < ) @ [ & { } } | 1 2               ",
"            3 4 4 _ 5 ; , ' 6 @ # 7 & { 8 } | 9 / 0             ",
"          a b c d e ; ; > ' < @ [ ~ ~ { 8 } f 1 g h h           ",
"        i j b c d _ k ; , ' < $ % % ~ { ] } f 9 l g h m         ",
"      n o p b c d _ e 5 q ' < @ ! ~ ~ { ] r ^ 9 g s 0 t u       ",
"    v n n j a d 4 = k ; , > ' 6 % [ w { { } | x x g h y z A     ",
"    B C D E a c = = k , F G H I I J K L M N O P Q R S S T U     ",
"  V W X n p j Y Y 5 Z ` I I  ...+. .I I @.#.$.%.P &.*.R S =.-.  ",
"  ;.;.>.D j ,.c 4 '.).I !.~.{.{.{.{.]...I J ^./.M %.O (.Q _.=.  ",
"  :.:.<.[.j b Y d }.I |.{.{.{.{.{.{.{.{.|.I @.1./.M %.%.O &.2.  ",
"3.4.5.5.6.j a Y 7.I ..{.{.{.{.{.{.{.{.{.{.!.I 8.9././.M %.O &.O ",
"0.<.a.b.b.c.b b d.I e.{.{.{.{.{.{.{.{.{.{.f.I g.h.9.9.i.j.M N k.",
"b.b.l.l.m.n.o.Y p. .{.{.{.{.{.{.{.{.{.{.{.{.q.r.s.s.t.9.i./.M j.",
"l.l.u.m.v.w.x.Y I +.{.{.{.{.{.{.{.{.{.{.{.{.y.I z.A.B.s.h.1.i.M ",
"l.l.C.m.D.E.F.G.I ..{.{.{.{.{.{.{.{.{.{.{.{.H.I I.J.K.L.B.M.N.9.",
"O.D.D.E.P.P.Q.R.S. .{.{.{.{.{.{.{.{.{.{.{.{.T.U.V.W.X.Y.L.Z.s.t.",
"C.`.P.P. +.+.+++@+I ~.{.{.{.{.{.{.{.{.{.{.].I #+$+%+&+*+I.L.z.B.",
"=+ +-+.+;+>+,+'+)+I !.{.{.{.{.{.{.{.{.{.{...I !+~+{+]+V.&+%+X.L.",
"  .+^+>+>+/+(+_+:+<+I [+{.{.{.{.{.{.{.{.[+I }+^.|+1+2+{+V.]+3+  ",
"  >+/+/+_+_+_+4+5+6+7+I ..e.{.{.{.{.8+!.I 9+0+a+b+c+1+2+d+$+V.  ",
"  e+'+_+f+f+4+g+g+h+i+j+I I  .+...q.I I k+l+m+n+o+b+p+c+q+d+r+  ",
"    _+f+:+s+s+t+t+u+v+w+x+y+z+I I A+B+C+D+E+E+F+n+G+o+#.H+c+    ",
"    _+I+s+g+u+t+v+w+J+K+L+L+M+N+N+N+O+P+Q+R+S+T+m+m+n+o+o+U+    ",
"      V+h+t+W+X+Y+K+L+L+Z+`+N+O+O+ @.@+@@@#@$@S+S+T+%@&@*@      ",
"        =@u+X+-@-@L+;@N+N+>@,@'@'@.@)@!@~@@@{@]@$@^@T+/@        ",
"          X+-@Z+;@`+N+N+,@O+O+.@(@(@_@:@<@[@}@@@#@|@1@          ",
"            -@2@`+N+>@3@O+.@4@5@6@7@8@9@9@<@0@0@}@@@            ",
"              a@,@O+.@P+(@(@(@b@c@d@e@f@g@9@9@h@i@              ",
"                   @(@5@6@b@j@c@k@l@m@n@f@o@L                   ",
"                        p@b@j@q@r@l@s@t@                        "};
