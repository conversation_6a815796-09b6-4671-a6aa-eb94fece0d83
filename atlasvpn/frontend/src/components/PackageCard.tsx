import React, { memo } from 'react';
import { VPNPackage } from '../types';
import { motion } from 'framer-motion';
import { haptics } from '../utils/haptics';
import { formatBytes, formatCurrency } from '../utils/format';
import LoadingSpinner from './LoadingSpinner';

interface PackageCardProps {
  vpnPackage: VPNPackage;
  onSelect: () => void;
  loading?: boolean;
  showTraffic?: boolean;
}

const PackageCard: React.FC<PackageCardProps> = memo(
  ({ vpnPackage, onSelect, loading = false, showTraffic = true }) => {
    const handleClick = () => {
      haptics.impact('medium');
      onSelect();
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ scale: loading ? 1 : 1.03 }}
        whileTap={{ scale: loading ? 1 : 0.98 }}
        className={`relative group cursor-pointer rounded-xl p-4 border-2 transition-all duration-200
        ${loading ? 'pointer-events-none opacity-75' : ''}`}
        onClick={!loading ? handleClick : undefined}
      >
        {/* Loading overlay */}
        {loading && (
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm rounded-xl flex items-center justify-center">
            <LoadingSpinner size="sm" />
          </div>
        )}

        {/* Background Gradient */}
        <div
          className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5 
                    group-hover:from-purple-500/10 group-hover:to-blue-500/10 
                    transition-all duration-500"
        />

        {/* Content */}
        <div className="relative z-10">
          <div className="flex justify-between items-start mb-6">
            <div>
              <h3
                className="text-xl font-bold text-white mb-1 group-hover:text-purple-300 
                         transition-colors"
              >
                {vpnPackage.name}
              </h3>
              <p className="text-white/60 text-sm">
                {vpnPackage.description || 'High-speed VPN service'}
              </p>
            </div>
            <div className="text-right">
              <div
                className="text-2xl font-bold text-white group-hover:text-purple-300 
                         transition-colors"
              >
                {formatCurrency(vpnPackage.price)}
              </div>
              <div className="text-white/60 text-sm">one-time payment</div>
            </div>
          </div>

          {/* Features */}
          {showTraffic && (
            <div className="space-y-3 mb-6">
              <div className="flex items-center space-x-3 text-white/80">
                <svg
                  className="w-5 h-5 text-purple-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span>{formatBytes(vpnPackage.data_limit)} Traffic</span>
              </div>

              <div className="flex items-center space-x-3 text-white/80">
                <svg
                  className="w-5 h-5 text-purple-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span>{vpnPackage.expire_days} Days Validity</span>
              </div>

              <div className="flex items-center space-x-3 text-white/80">
                <svg
                  className="w-5 h-5 text-purple-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064"
                  />
                </svg>
                <span>{vpnPackage.allowed_panels?.length || 'Multiple'} Available Servers</span>
              </div>
            </div>
          )}

          {/* Purchase Button */}
          <motion.button
            onClick={handleClick}
            className="w-full py-3 bg-gradient-to-r from-purple-500 to-blue-500
                   text-white font-medium rounded-lg relative overflow-hidden
                   transform transition-all duration-200 hover:shadow-lg
                   hover:shadow-purple-500/25"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <span className="relative z-10">Purchase Now</span>
            <div
              className="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 
                        opacity-0 group-hover:opacity-100 transition-opacity duration-200"
            />
          </motion.button>
        </div>
      </motion.div>
    );
  }
);

export default PackageCard;
