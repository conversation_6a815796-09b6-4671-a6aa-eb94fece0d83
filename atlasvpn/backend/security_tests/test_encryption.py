#!/usr/bin/env python
"""
Data Encryption Test
------------------
Test data encryption and secure handling of sensitive information.
"""
import logging
import re
import json
from typing import Dict, Any, List, Tuple
from test_utils import (
    print_header,
    print_success,
    print_error,
    print_warning,
    print_info,
    log_request_details,
    log_error_with_traceback,
    make_request,
    get_critical_endpoints,
    log_test_result,
    log_environment_info
)
from test_config import API_BASE_URL

logger = logging.getLogger(__name__)

def test_encryption() -> Dict[str, Any]:
    """Test data encryption and secure handling of sensitive information"""
    print_header("Testing Data Encryption")
    
    # Log environment info for debugging
    log_environment_info()
    
    # Setup results dictionary
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "vulnerabilities": [],
        "errors": []
    }
    
    # Run individual encryption tests
    try:
        # Test for TLS/HTTPS
        tls_results = test_tls()
        results["total_tests"] += tls_results["total_tests"]
        results["successful_tests"] += tls_results["successful_tests"]
        results["failed_tests"] += tls_results["failed_tests"]
        
        if "vulnerabilities" in tls_results:
            results["vulnerabilities"].extend(tls_results["vulnerabilities"])
        
        if "errors" in tls_results:
            results["errors"].extend(tls_results["errors"])
        
        # Test for sensitive data exposure
        data_exposure_results = test_data_exposure()
        results["total_tests"] += data_exposure_results["total_tests"]
        results["successful_tests"] += data_exposure_results["successful_tests"]
        results["failed_tests"] += data_exposure_results["failed_tests"]
        
        if "vulnerabilities" in data_exposure_results:
            results["vulnerabilities"].extend(data_exposure_results["vulnerabilities"])
        
        if "errors" in data_exposure_results:
            results["errors"].extend(data_exposure_results["errors"])
        
        # Test for proper password handling
        password_results = test_password_handling()
        results["total_tests"] += password_results["total_tests"]
        results["successful_tests"] += password_results["successful_tests"]
        results["failed_tests"] += password_results["failed_tests"]
        
        if "vulnerabilities" in password_results:
            results["vulnerabilities"].extend(password_results["vulnerabilities"])
        
        if "errors" in password_results:
            results["errors"].extend(password_results["errors"])
        
    except Exception as e:
        error_msg = f"Error running encryption tests: {str(e)}"
        results["errors"].append(error_msg)
        logger.error(error_msg)
        log_error_with_traceback(e, error_msg)
    
    # Calculate success rate
    success_rate = (results["successful_tests"] / results["total_tests"] * 100) if results["total_tests"] > 0 else 0
    
    # Print summary
    print_header("Data Encryption Test Summary")
    print_info(f"Total Tests Run: {results['total_tests']}")
    print_info(f"Successful Tests: {results['successful_tests']}")
    print_info(f"Failed Tests: {results['failed_tests']}")
    print_info(f"Success Rate: {success_rate:.2f}%")
    
    if results["vulnerabilities"]:
        print_warning("\nEncryption Vulnerabilities Found:")
        for vuln in results["vulnerabilities"]:
            print_warning(f"  {vuln.get('category', 'Undefined')}: {vuln.get('description', 'No description')}")
    
    if results["errors"]:
        print_warning("\nErrors Encountered:")
        for error in results["errors"]:
            print_warning(f"  {error}")
    
    return results

def test_tls() -> Dict[str, Any]:
    """Test for proper TLS/HTTPS implementation"""
    print_header("Testing TLS/HTTPS")
    
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "vulnerabilities": [],
        "errors": []
    }
    
    try:
        # Check if API is using HTTPS
        results["total_tests"] += 1
        
        if API_BASE_URL.startswith("https://"):
            results["successful_tests"] += 1
            print_success("API is using HTTPS")
        else:
            results["failed_tests"] += 1
            results["vulnerabilities"].append({
                "category": "TLS",
                "description": "API is not using HTTPS",
                "severity": "HIGH"
            })
            print_error("API is not using HTTPS")
            
        # Check for HSTS header
        success, response = make_request(f"{API_BASE_URL}/")
        
        if success:
            results["total_tests"] += 1
            
            headers = response.get("headers", {})
            hsts_header = headers.get("Strict-Transport-Security", "")
            
            if hsts_header:
                # Check if HSTS is properly configured
                has_max_age = "max-age=" in hsts_header
                max_age_value = 0
                
                if has_max_age:
                    # Extract max-age value
                    match = re.search(r"max-age=(\d+)", hsts_header)
                    if match:
                        max_age_value = int(match.group(1))
                
                has_includesubdomains = "includeSubDomains" in hsts_header
                has_preload = "preload" in hsts_header
                
                # HSTS is considered secure if max-age is at least 6 months
                secure_max_age = max_age_value >= 15768000  # 6 months in seconds
                
                if secure_max_age and has_includesubdomains:
                    results["successful_tests"] += 1
                    print_success(f"HSTS header is properly configured: {hsts_header}")
                else:
                    results["failed_tests"] += 1
                    issues = []
                    if not secure_max_age:
                        issues.append(f"max-age too low: {max_age_value}")
                    if not has_includesubdomains:
                        issues.append("missing includeSubDomains")
                    
                    results["vulnerabilities"].append({
                        "category": "TLS",
                        "description": f"HSTS header is not properly configured: {', '.join(issues)}",
                        "severity": "MEDIUM"
                    })
                    print_warning(f"HSTS header is not properly configured: {hsts_header}")
            else:
                results["failed_tests"] += 1
                results["vulnerabilities"].append({
                    "category": "TLS",
                    "description": "HSTS header is missing",
                    "severity": "MEDIUM"
                })
                print_warning("HSTS header is missing")
        
    except Exception as e:
        error_msg = f"Error testing TLS: {str(e)}"
        results["errors"].append(error_msg)
        logger.error(error_msg)
        
    return results

def test_data_exposure() -> Dict[str, Any]:
    """Test for sensitive data exposure in API responses"""
    print_header("Testing Sensitive Data Exposure")
    
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "vulnerabilities": [],
        "errors": []
    }
    
    # Patterns to look for in responses
    sensitive_patterns = [
        (r"password\":\"[^\"]+\"", "Password"),
        (r"secret\":\"[^\"]+\"", "Secret"),
        (r"token\":\"[^\"]+\"", "Token"),
        (r"api[-_]?key\":\"[^\"]+\"", "API key"),
        (r"credit[-_]?card\":\"[^\"]+\"", "Credit card"),
        (r"\d{4}[- ]?\d{4}[- ]?\d{4}[- ]?\d{4}", "Credit card number"),
        (r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", "Email address"),
        (r"\b\d{3}[-.]?\d{2}[-.]?\d{4}\b", "SSN"),
        (r"private[-_]?key", "Private key")
    ]
    
    # Get endpoints to test
    endpoints = get_critical_endpoints()
    print_info(f"Testing {len(endpoints)} endpoints for sensitive data exposure")
    
    # For each endpoint, check for sensitive data in responses
    for endpoint in endpoints:
        # Create URL for endpoint (replace placeholders)
        url = f"{API_BASE_URL}{endpoint.replace('{task_id}', '1').replace('{panel_id}', '1').replace('{subscription_url}', 'test').replace('{code}', 'test')}"
        
        print_info(f"Testing endpoint: {endpoint}")
        
        try:
            results["total_tests"] += 1
            
            # Make a GET request to the endpoint
            success, response = make_request(url)
            
            if not success:
                logger.warning(f"Could not connect to {url}")
                continue
            
            # Check the response body for sensitive data
            body = response.get("body", "")
            
            # Convert body to string if it's not already
            if not isinstance(body, str):
                body = str(body)
            
            exposed_data = []
            
            for pattern, data_type in sensitive_patterns:
                matches = re.findall(pattern, body)
                if matches:
                    exposed_data.append({
                        "type": data_type,
                        "matches": len(matches)
                    })
            
            if exposed_data:
                results["failed_tests"] += 1
                
                for data in exposed_data:
                    results["vulnerabilities"].append({
                        "category": "Data Exposure",
                        "endpoint": endpoint,
                        "description": f"Exposed {data['type']} found in response ({data['matches']} occurrences)",
                        "severity": "HIGH"
                    })
                    print_error(f"  Exposed {data['type']} found in response from {endpoint} ({data['matches']} occurrences)")
            else:
                results["successful_tests"] += 1
                print_success(f"  No sensitive data found in response from {endpoint}")
            
            # Also try with POST if it's a data submission endpoint
            if any(pattern in endpoint.lower() for pattern in ["create", "update", "add", "submit"]):
                # Make a POST request to the endpoint
                test_data = {
                    "test_field": "test_value",
                    "name": "Test User",
                    "email": "<EMAIL>"
                }
                
                success, post_response = make_request(url, method="POST", data=test_data)
                
                if not success:
                    continue
                
                # Check POST response body for sensitive data
                post_body = post_response.get("body", "")
                
                # Convert body to string if it's not already
                if not isinstance(post_body, str):
                    post_body = str(post_body)
                
                post_exposed_data = []
                
                for pattern, data_type in sensitive_patterns:
                    matches = re.findall(pattern, post_body)
                    if matches:
                        post_exposed_data.append({
                            "type": data_type,
                            "matches": len(matches)
                        })
                
                if post_exposed_data:
                    results["failed_tests"] += 1
                    
                    for data in post_exposed_data:
                        results["vulnerabilities"].append({
                            "category": "Data Exposure",
                            "endpoint": endpoint,
                            "description": f"Exposed {data['type']} found in POST response ({data['matches']} occurrences)",
                            "severity": "HIGH"
                        })
                        print_error(f"  Exposed {data['type']} found in POST response from {endpoint} ({data['matches']} occurrences)")
                else:
                    results["successful_tests"] += 1
                    print_success(f"  No sensitive data found in POST response from {endpoint}")
            
        except Exception as e:
            error_msg = f"Error testing data exposure on {endpoint}: {str(e)}"
            results["errors"].append(error_msg)
            logger.error(error_msg)
    
    return results

def test_password_handling() -> Dict[str, Any]:
    """Test for proper password handling and storage"""
    print_header("Testing Password Handling")
    
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "vulnerabilities": [],
        "errors": []
    }
    
    # Attempt to detect plain text passwords or weak hashing
    # This is a best-effort test as we can't directly inspect the database
    
    try:
        # Register endpoint - for testing password storage
        register_url = f"{API_BASE_URL}/auth/register"
        
        # Test if registration sends password confirmation emails with the password
        results["total_tests"] += 1
        
        # Generate a unique test user
        import time
        test_user = {
            "username": f"security_test_user_{int(time.time())}",
            "email": f"security_test_{int(time.time())}@example.com",
            "password": "Test@Password123!"
        }
        
        # Register the user
        success, response = make_request(register_url, method="POST", data=test_user)
        
        if success:
            # Check the response for any signs of plaintext password
            body = response.get("body", "")
            
            # Convert body to string if it's not already
            if not isinstance(body, str):
                body = str(body)
            
            if test_user["password"] in body:
                results["failed_tests"] += 1
                results["vulnerabilities"].append({
                    "category": "Password Handling",
                    "description": "Password is returned in plaintext in registration response",
                    "severity": "CRITICAL"
                })
                print_error("Password is returned in plaintext in registration response")
            else:
                results["successful_tests"] += 1
                print_success("Password is not returned in plaintext in registration response")
            
            # Check if an auth token is returned instead of credentials
            try:
                if isinstance(body, str) and body.strip():
                    body_json = json.loads(body)
                    
                    # Check for token-based authentication
                    has_token = any(key in body_json for key in ["token", "access_token", "jwt", "id_token"])
                    has_password = "password" in body_json
                    
                    if has_token and not has_password:
                        results["successful_tests"] += 1
                        print_success("Registration response uses token-based authentication")
                    elif has_password:
                        results["failed_tests"] += 1
                        results["vulnerabilities"].append({
                            "category": "Password Handling",
                            "description": "Registration response contains password field",
                            "severity": "HIGH"
                        })
                        print_error("Registration response contains password field")
            except:
                # If we can't parse the JSON, skip this check
                pass
                
        # Test password reset functionality
        reset_url = f"{API_BASE_URL}/auth/password/reset"
        
        # Check if the password reset endpoint is available
        results["total_tests"] += 1
        
        success, response = make_request(reset_url, method="GET")
        
        if success:
            status_code = response.get("status_code", 0)
            
            # Check for method not allowed (should be POST)
            if status_code in [404, 405]:
                # try POST
                reset_data = {"email": "<EMAIL>"}
                success, response = make_request(reset_url, method="POST", data=reset_data)
                
                if success:
                    status_code = response.get("status_code", 0)
            
            # If the endpoint exists (doesn't 404), check its security
            if status_code != 404:
                body = response.get("body", "")
                
                # Convert body to string if it's not already
                if not isinstance(body, str):
                    body = str(body)
                
                # Check if the response contains any password information
                contains_password = "password" in body.lower()
                
                if contains_password and any(pattern in body.lower() for pattern in ["send", "email", "temporary", "reset link", "token"]):
                    results["successful_tests"] += 1
                    print_success("Password reset functionality appears to use secure methods (email or token)")
                elif contains_password:
                    results["failed_tests"] += 1
                    results["vulnerabilities"].append({
                        "category": "Password Handling",
                        "description": "Password reset functionality may not be secure",
                        "severity": "MEDIUM"
                    })
                    print_warning("Password reset functionality may not be secure")
                else:
                    # Inconclusive - count as success but note it
                    results["successful_tests"] += 1
                    print_info("Password reset functionality found but could not determine security")
            else:
                # Endpoint not found - consider it neutral
                print_info("Password reset endpoint not found or not enabled")
                
        # Check password storage by attempting timing attacks
        # This is a simplistic test and may not be accurate
        results["total_tests"] += 1
        
        login_url = f"{API_BASE_URL}/auth/login"
        
        # Try login with different password lengths
        import time
        
        timing_results = []
        
        for length in [5, 10, 20, 30]:
            password = "A" * length
            
            login_data = {
                "username": "admin",  # Try a common username
                "password": password
            }
            
            start_time = time.time()
            success, response = make_request(login_url, method="POST", data=login_data)
            end_time = time.time()
            
            timing_results.append((length, end_time - start_time))
        
        # If timing varies significantly with password length, it might indicate
        # vulnerable password comparison (e.g., character-by-character)
        timing_variation = max(t[1] for t in timing_results) - min(t[1] for t in timing_results)
        
        if timing_variation > 0.1:  # Arbitrary threshold
            results["failed_tests"] += 1
            results["vulnerabilities"].append({
                "category": "Password Handling",
                "description": "Possible timing attack vulnerability in password verification",
                "severity": "MEDIUM"
            })
            print_warning("Possible timing attack vulnerability in password verification")
        else:
            results["successful_tests"] += 1
            print_success("No obvious timing attack vulnerability detected")
            
    except Exception as e:
        error_msg = f"Error testing password handling: {str(e)}"
        results["errors"].append(error_msg)
        logger.error(error_msg)
    
    return results

if __name__ == "__main__":
    test_encryption() 