<template>
  <div 
    class="vpn-package-card group relative overflow-hidden rounded-xl border transition-all duration-300 hover:scale-[1.02] cursor-pointer"
    :class="[
      isSelected ? 'border-purple-500/50 bg-purple-500/10' : 'border-gray-700 bg-gray-900/50 hover:border-purple-500/30',
      { 'opacity-60': !canAfford }
    ]"
    @click="handleSelect"
  >
    <!-- Popular Badge -->
    <div v-if="isPopular" class="absolute top-3 right-3 z-10">
      <div class="bg-gradient-to-r from-purple-500 to-blue-500 text-white text-xs font-bold px-2 py-1 rounded-full">
        POPULAR
      </div>
    </div>

    <!-- Background Glow -->
    <div 
      class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      :class="isSelected ? 'bg-purple-500/5' : 'bg-purple-500/5'"
    />

    <!-- Corner Accent -->
    <div class="absolute top-0 left-0 w-8 h-8 opacity-50">
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
        <path d="M 0 12 V 0 H 12" stroke="#8b5cf6" stroke-width="1" />
        <path d="M 0 8 V 0 H 8" stroke="#8b5cf6" stroke-width="2" />
      </svg>
    </div>

    <div class="relative p-6">
      <!-- Header -->
      <div class="mb-4">
        <div class="flex items-center justify-between mb-2">
          <h3 class="text-xl font-bold text-white">{{ vpnPackage.name }}</h3>
          <div class="text-2xl font-bold text-purple-400">
            ${{ vpnPackage.price.toFixed(2) }}
          </div>
        </div>
        <p class="text-gray-400 text-sm">{{ vpnPackage.description }}</p>
      </div>

      <!-- Features -->
      <div class="space-y-3 mb-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Icon name="i-heroicons-cloud-arrow-down" class="w-4 h-4 text-blue-400" />
            <span class="text-gray-300 text-sm">Data Limit</span>
          </div>
          <span class="text-white font-medium">{{ formatDataLimit(vpnPackage.data_limit) }}</span>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Icon name="i-heroicons-calendar-days" class="w-4 h-4 text-green-400" />
            <span class="text-gray-300 text-sm">Duration</span>
          </div>
          <span class="text-white font-medium">{{ vpnPackage.expire_days }} days</span>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Icon name="i-heroicons-server" class="w-4 h-4 text-purple-400" />
            <span class="text-gray-300 text-sm">Servers</span>
          </div>
          <span class="text-white font-medium">{{ vpnPackage.allowed_panels.length }} locations</span>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Icon name="i-heroicons-bolt" class="w-4 h-4 text-yellow-400" />
            <span class="text-gray-300 text-sm">Speed</span>
          </div>
          <span class="text-white font-medium">{{ getSpeedLabel() }}</span>
        </div>
      </div>

      <!-- Value Proposition -->
      <div class="mb-6">
        <div class="bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-lg p-3 border border-purple-500/20">
          <div class="flex items-center justify-between">
            <span class="text-purple-300 text-sm">Price per day</span>
            <span class="text-white font-bold">
              ${{ (vpnPackage.price / vpnPackage.expire_days).toFixed(2) }}
            </span>
          </div>
        </div>
      </div>

      <!-- Server Locations Preview -->
      <div v-if="vpnPackage.allowed_panels.length > 0" class="mb-6">
        <div class="text-sm text-gray-400 mb-2">Available Locations:</div>
        <div class="flex flex-wrap gap-1">
          <div
            v-for="panel in vpnPackage.allowed_panels.slice(0, 3)"
            :key="panel.id"
            class="px-2 py-1 bg-gray-800 rounded text-xs text-gray-300"
          >
            {{ getLocationName(panel) }}
          </div>
          <div
            v-if="vpnPackage.allowed_panels.length > 3"
            class="px-2 py-1 bg-gray-800 rounded text-xs text-gray-300"
          >
            +{{ vpnPackage.allowed_panels.length - 3 }} more
          </div>
        </div>
      </div>

      <!-- Action Button -->
      <UButton
        @click.stop="handlePurchase"
        :loading="isLoading"
        :disabled="!canAfford"
        color="primary"
        class="w-full"
        size="lg"
      >
        <template #leading>
          <Icon name="i-heroicons-shopping-cart" class="w-5 h-5" />
        </template>
        {{ canAfford ? 'Purchase Package' : 'Insufficient Balance' }}
      </UButton>

      <!-- Insufficient Balance Warning -->
      <div v-if="!canAfford" class="mt-3 text-center">
        <p class="text-red-400 text-xs">
          Need ${{ (vpnPackage.price - walletBalance).toFixed(2) }} more
        </p>
        <UButton
          @click.stop="navigateTo('/dashboard/wallet')"
          variant="ghost"
          size="xs"
          class="mt-1"
        >
          Top Up Wallet
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { VPNPackage } from '~/types'

interface Props {
  vpnPackage: VPNPackage
  isSelected?: boolean
  isPopular?: boolean
  isLoading?: boolean
  walletBalance: number
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false,
  isPopular: false,
  isLoading: false
})

const emit = defineEmits<{
  select: [pkg: VPNPackage]
  purchase: [pkg: VPNPackage]
}>()

const canAfford = computed(() => props.walletBalance >= props.vpnPackage.price)

const handleSelect = () => {
  emit('select', props.vpnPackage)
}

const handlePurchase = () => {
  if (canAfford.value && !props.isLoading) {
    emit('purchase', props.vpnPackage)
  }
}

const formatDataLimit = (bytes: number) => {
  if (bytes === 0) return 'Unlimited'
  const gb = bytes / (1024 * 1024 * 1024)
  return `${gb.toFixed(0)} GB`
}

const getSpeedLabel = () => {
  // Simple speed categorization based on price
  if (props.vpnPackage.price < 10) return 'Standard'
  if (props.vpnPackage.price < 25) return 'High Speed'
  return 'Ultra Fast'
}

const getLocationName = (panel: any) => {
  // Extract location from panel name or use a default
  return panel.location || panel.name.split(' ')[0] || 'Server'
}
</script>

<style scoped>
.vpn-package-card {
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
}
</style>
