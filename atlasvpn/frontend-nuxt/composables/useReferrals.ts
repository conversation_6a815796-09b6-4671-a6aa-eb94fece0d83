import type { ReferralInfo, ReferredUser, ReferralReward } from '~/types'

/**
 * Modern referrals composable using Nuxt 4 data fetching patterns
 * Optimized for Telegram Mini App with proper caching and error handling
 */
export const useReferrals = () => {
  // Default fetch options optimized for Telegram Mini App
  const defaultOptions = {
    server: false, // Client-side only for Telegram Mini App
    retry: 3,
    retryDelay: 1000,
    timeout: 15000,
    onRequestError({ error }: { error: any }) {
      console.error('[Referrals] Request error:', error)
    },
    onResponseError({ response }: { response: any }) {
      console.error('[Referrals] Response error:', response.status, response.statusText)
    }
  }

  // Referral info with modern useFetch
  const { 
    data: referralInfo, 
    pending: isLoadingInfo, 
    error: infoError, 
    refresh: refreshInfo 
  } = useFetch<ReferralInfo>('/api/referrals/info', {
    ...defaultOptions,
    key: 'referral-info',
    default: () => ({
      referral_code: '',
      total_referrals: 0,
      total_earnings: 0,
      referral_link: '',
      commission_rate: 0
    }),
    transform: (data: any) => data?.data || data
  })

  // Referred users with modern useFetch
  const { 
    data: referredUsers, 
    pending: isLoadingUsers, 
    error: usersError, 
    refresh: refreshUsers 
  } = useFetch<ReferredUser[]>('/api/referrals/users', {
    ...defaultOptions,
    key: 'referred-users',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // Referral rewards with modern useFetch
  const { 
    data: referralRewards, 
    pending: isLoadingRewards, 
    error: rewardsError, 
    refresh: refreshRewards 
  } = useFetch<ReferralReward[]>('/api/referrals/rewards', {
    ...defaultOptions,
    key: 'referral-rewards',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // Generate referral link
  const generateReferralLink = async (): Promise<string> => {
    try {
      const response = await $fetch<{ data: { referral_link: string } }>('/api/referrals/generate-link', {
        method: 'POST'
      })
      
      // Refresh referral info to get updated data
      await refreshInfo()
      
      return response.data.referral_link
    } catch (error: any) {
      console.error('[Referrals] Failed to generate referral link:', error)
      throw error
    }
  }

  // Claim referral reward
  const claimReferralReward = async (rewardId: number): Promise<ReferralReward> => {
    try {
      const response = await $fetch<{ data: ReferralReward }>(`/api/referrals/rewards/${rewardId}/claim`, {
        method: 'POST'
      })
      
      // Refresh rewards to get updated data
      await refreshRewards()
      
      return response.data
    } catch (error: any) {
      console.error('[Referrals] Failed to claim referral reward:', error)
      throw error
    }
  }

  // Share referral link via Telegram
  const shareReferralLink = async (message?: string): Promise<boolean> => {
    const { openLink } = useTelegram()
    
    if (!referralInfo.value?.referral_link) {
      throw new Error('No referral link available')
    }

    const shareText = message || `Join AtlasVPN and get exclusive rewards! Use my referral link: ${referralInfo.value.referral_link}`
    const telegramShareUrl = `https://t.me/share/url?url=${encodeURIComponent(referralInfo.value.referral_link)}&text=${encodeURIComponent(shareText)}`
    
    try {
      openLink(telegramShareUrl)
      return true
    } catch (error: any) {
      console.error('[Referrals] Failed to share referral link:', error)
      return false
    }
  }

  // Copy referral link to clipboard
  const copyReferralLink = async (): Promise<boolean> => {
    if (!referralInfo.value?.referral_link) {
      throw new Error('No referral link available')
    }

    try {
      await navigator.clipboard.writeText(referralInfo.value.referral_link)
      return true
    } catch (error: any) {
      console.error('[Referrals] Failed to copy referral link:', error)
      // Fallback for older browsers
      try {
        const textArea = document.createElement('textarea')
        textArea.value = referralInfo.value.referral_link
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        return true
      } catch (fallbackError) {
        console.error('[Referrals] Fallback copy failed:', fallbackError)
        return false
      }
    }
  }

  // Refresh all referral data
  const refreshAllReferrals = async () => {
    await Promise.all([
      refreshInfo(),
      refreshUsers(),
      refreshRewards()
    ])
  }

  // Computed properties
  const totalEarnings = computed(() => referralInfo.value?.total_earnings || 0)

  const totalReferrals = computed(() => referralInfo.value?.total_referrals || 0)

  const activeReferrals = computed(() => 
    referredUsers.value?.filter(user => user.is_active) || []
  )

  const pendingRewards = computed(() => 
    referralRewards.value?.filter(reward => !reward.is_claimed) || []
  )

  const claimedRewards = computed(() => 
    referralRewards.value?.filter(reward => reward.is_claimed) || []
  )

  const totalPendingAmount = computed(() => 
    pendingRewards.value.reduce((total, reward) => total + reward.amount, 0)
  )

  const totalClaimedAmount = computed(() => 
    claimedRewards.value.reduce((total, reward) => total + reward.amount, 0)
  )

  const referralStats = computed(() => ({
    total: totalReferrals.value,
    active: activeReferrals.value.length,
    inactive: totalReferrals.value - activeReferrals.value.length,
    earnings: totalEarnings.value,
    pendingRewards: pendingRewards.value.length,
    pendingAmount: totalPendingAmount.value
  }))

  const recentReferrals = computed(() => 
    referredUsers.value
      ?.sort((a, b) => new Date(b.joined_at).getTime() - new Date(a.joined_at).getTime())
      .slice(0, 10) || []
  )

  const topEarningReferrals = computed(() => 
    referredUsers.value
      ?.sort((a, b) => (b.total_spent || 0) - (a.total_spent || 0))
      .slice(0, 5) || []
  )

  // Utility functions
  const formatEarnings = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getCommissionAmount = (userSpent: number): number => {
    const rate = referralInfo.value?.commission_rate || 0
    return userSpent * (rate / 100)
  }

  const getReferralTier = (referralCount: number): string => {
    if (referralCount >= 100) return 'Diamond'
    if (referralCount >= 50) return 'Platinum'
    if (referralCount >= 25) return 'Gold'
    if (referralCount >= 10) return 'Silver'
    if (referralCount >= 5) return 'Bronze'
    return 'Starter'
  }

  const getDaysAgo = (dateString: string): number => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = now.getTime() - date.getTime()
    return Math.floor(diffTime / (1000 * 60 * 60 * 24))
  }

  return {
    // State (reactive from useFetch)
    referralInfo: readonly(referralInfo),
    referredUsers: readonly(referredUsers),
    referralRewards: readonly(referralRewards),
    isLoadingInfo: readonly(isLoadingInfo),
    isLoadingUsers: readonly(isLoadingUsers),
    isLoadingRewards: readonly(isLoadingRewards),
    infoError: readonly(infoError),
    usersError: readonly(usersError),
    rewardsError: readonly(rewardsError),

    // Actions
    refreshInfo,
    refreshUsers,
    refreshRewards,
    refreshAllReferrals,
    generateReferralLink,
    claimReferralReward,
    shareReferralLink,
    copyReferralLink,

    // Computed
    totalEarnings,
    totalReferrals,
    activeReferrals,
    pendingRewards,
    claimedRewards,
    totalPendingAmount,
    totalClaimedAmount,
    referralStats,
    recentReferrals,
    topEarningReferrals,

    // Utilities
    formatEarnings,
    getCommissionAmount,
    getReferralTier,
    getDaysAgo
  }
}
