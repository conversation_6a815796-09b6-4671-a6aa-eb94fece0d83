<template>
  <div class="dashboard-layout min-h-screen bg-atlas-black text-white telegram-safe-area">
    <!-- Ultra-Deep Black Background Effects -->
    <div class="fixed inset-0 overflow-hidden pointer-events-none">
      <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-atlas-purple-500/10 rounded-full blur-3xl glow-purple"></div>
      <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-atlas-cyan/8 rounded-full blur-3xl glow-emerald"></div>
      <div class="absolute top-3/4 right-1/3 w-64 h-64 bg-atlas-gold/5 rounded-full blur-2xl"></div>
    </div>

    <!-- Header -->
    <header class="relative z-10 bg-atlas-black/80 backdrop-blur-md border-b border-atlas-purple-800/30 safe-area-top">
      <div class="container-responsive py-3">
        <div class="flex items-center justify-between">
          <!-- Logo/Brand -->
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded-lg bg-gradient-to-r from-atlas-purple-500 to-atlas-purple-600 flex items-center justify-center glow-purple">
              <Icon name="i-heroicons-shield-check" class="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 class="text-lg font-bold text-atlas-gold">AtlasVPN</h1>
              <p class="text-xs text-atlas-purple-300">{{ currentTime }}</p>
            </div>
          </div>

          <!-- User Menu -->
          <div class="flex items-center space-x-3">
            <!-- Notifications -->
            <UButton
              @click="showNotifications = !showNotifications"
              variant="ghost"
              size="sm"
              icon="i-heroicons-bell"
              class="relative"
            >
              <span v-if="unreadNotifications > 0" class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs flex items-center justify-center">
                {{ unreadNotifications }}
              </span>
            </UButton>

            <!-- User Profile -->
            <UDropdown :items="userMenuItems">
              <UButton
                variant="ghost"
                size="sm"
                class="flex items-center space-x-2"
              >
                <div class="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center">
                  <span class="text-white font-medium text-sm">{{ userInitials }}</span>
                </div>
                <Icon name="i-heroicons-chevron-down" class="w-4 h-4" />
              </UButton>
            </UDropdown>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="relative z-10 flex-1 pb-20 safe-area-bottom">
      <div class="container-responsive py-6 space-y-responsive">
        <slot />
      </div>
    </main>

    <!-- Bottom Navigation -->
    <BottomNavigation />

    <!-- Notifications Dropdown -->
    <div v-if="showNotifications" class="fixed top-16 right-4 w-80 max-w-[calc(100vw-2rem)] z-50">
      <div class="bg-gray-900 border border-gray-700 rounded-lg shadow-xl">
        <div class="p-4 border-b border-gray-700">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold text-white">Notifications</h3>
            <UButton
              @click="showNotifications = false"
              variant="ghost"
              size="sm"
              icon="i-heroicons-x-mark"
            />
          </div>
        </div>
        
        <div class="max-h-96 overflow-y-auto">
          <div v-if="notifications.length === 0" class="p-8 text-center">
            <Icon name="i-heroicons-bell-slash" class="w-12 h-12 mx-auto mb-2 text-gray-600" />
            <p class="text-gray-400">No notifications</p>
          </div>
          
          <div v-else class="divide-y divide-gray-700">
            <div
              v-for="notification in notifications"
              :key="notification.id"
              class="p-4 hover:bg-gray-800 transition-colors"
            >
              <div class="flex items-start space-x-3">
                <div class="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                <div class="flex-1">
                  <h4 class="text-white font-medium">{{ notification.title }}</h4>
                  <p class="text-gray-400 text-sm">{{ notification.message }}</p>
                  <p class="text-gray-500 text-xs mt-1">{{ formatNotificationTime(notification.created_at) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Global Loading Overlay -->
    <div v-if="globalLoading" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
      <div class="text-center">
        <div class="loading-spinner w-12 h-12 mx-auto mb-4"></div>
        <p class="text-white">Loading...</p>
      </div>
    </div>

    <!-- Toast Notifications -->
    <div class="fixed top-4 right-4 z-50 space-y-2">
      <div
        v-for="toast in activeToasts"
        :key="toast.id"
        class="max-w-sm"
      >
        <UAlert
          :title="toast.title"
          :description="toast.message"
          :color="getToastColor(toast.type)"
          variant="solid"
          :close-button="{ icon: 'i-heroicons-x-mark-20-solid', color: 'white', variant: 'link' }"
          @close="removeToast(toast.id)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { format } from 'date-fns'

const route = useRoute()
const { logout } = useAuth()
const userStore = useUserStore()
const uiStore = useUiStore()

// Computed properties
const userInitials = computed(() => userStore.userInitials)
const globalLoading = computed(() => uiStore.globalLoading)
const activeToasts = computed(() => uiStore.activeToasts)

// Local state
const showNotifications = ref(false)
const unreadNotifications = ref(0)
const notifications = ref([])

// Navigation items
const navItems = [
  {
    path: '/dashboard/home',
    label: 'Home',
    icon: 'i-heroicons-home'
  },
  {
    path: '/dashboard/earn',
    label: 'Earn',
    icon: 'i-heroicons-currency-dollar'
  },
  {
    path: '/dashboard/premium',
    label: 'Premium',
    icon: 'i-heroicons-star'
  },
  {
    path: '/dashboard/wallet',
    label: 'Wallet',
    icon: 'i-heroicons-wallet'
  },
  {
    path: '/dashboard/friends',
    label: 'Friends',
    icon: 'i-heroicons-user-group'
  }
]

// User menu items
const userMenuItems = [
  [{
    label: 'Profile',
    icon: 'i-heroicons-user',
    click: () => navigateTo('/dashboard/profile')
  }],
  [{
    label: 'Settings',
    icon: 'i-heroicons-cog-6-tooth',
    click: () => navigateTo('/dashboard/settings')
  }],
  [{
    label: 'AtlasVerse',
    icon: 'i-heroicons-cube',
    click: () => navigateTo('/verse')
  }],
  [{
    label: 'Logout',
    icon: 'i-heroicons-arrow-right-on-rectangle',
    click: handleLogout
  }]
]

// Methods
const isActiveRoute = (path: string) => {
  return route.path === path || (path === '/dashboard/home' && route.path === '/dashboard')
}

const handleLogout = async () => {
  try {
    await logout()
  } catch (error) {
    console.error('Logout failed:', error)
  }
}

const removeToast = (id: string) => {
  uiStore.removeToast(id)
}

const getToastColor = (type: string) => {
  const colors: Record<string, string> = {
    success: 'green',
    error: 'red',
    warning: 'yellow',
    info: 'blue'
  }
  return colors[type] || 'blue'
}

const formatNotificationTime = (dateString: string) => {
  return format(new Date(dateString), 'MMM d, HH:mm')
}

// Close notifications when clicking outside
onClickOutside(showNotifications, () => {
  showNotifications.value = false
})

// Initialize theme
onMounted(() => {
  uiStore.initializeTheme()
})
</script>

<style scoped>
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  color: #9ca3af;
}

.nav-item:hover {
  color: white;
}

.nav-item.active {
  color: #a855f7;
}

.dashboard-layout {
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
}
</style>
