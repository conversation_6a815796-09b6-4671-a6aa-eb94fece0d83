from fastapi import APIRouter, Depends, HTTPException, Query, WebSocket, WebSocketDisconnect, WebSocketException, Request
from fastapi.responses import JSONResponse
from sqlalchemy import select, or_, and_, desc, func, update, case
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, List, Optional, Any, ClassVar
from datetime import datetime, timedelta, timezone
import logging
import json
import uuid
import random
import string
from slowapi import Limiter
from slowapi.util import get_remote_address
import asyncio
from slowapi.errors import RateLimitExceeded
import redis.asyncio as redis # Import async redis client
import time

from database import get_async_db, get_redis_dependency
# Removed WebSocketToken model import
from models import User, ChatMessage
from schemas import ChatMessageCreate

# Setup logging
logger = logging.getLogger(__name__)

router = APIRouter(tags=["websocket"], prefix="/ws")

# Get limiter instance from app state (or define a new one if needed)
# This assumes limiter is added to app state in main.py: app.state.limiter
# limiter: Limiter = Depends(lambda request: request.app.state.limiter) # Use dependency injection if possible

# Store active connections: user_id -> WebSocket
active_connections: Dict[int, WebSocket] = {}

# Connection Manager
class ConnectionManager:
    """
    WebSocket connection manager
    Handles active WebSocket connections and broadcasting messages
    """
    def __init__(self):
        self.active_connections: Dict[int, WebSocket] = {}
    
    def add_connection(self, user_id: int, websocket: WebSocket):
        """Add a new connection for a user"""
        self.active_connections[user_id] = websocket
        
    def remove_connection(self, user_id: int):
        """Remove a user's connection"""
        self.active_connections.pop(user_id, None)
        
    async def send_personal_message(self, message: Any, user_id: int) -> bool:
        """Send a message (JSON or text) to a specific user"""
        connection = self.active_connections.get(user_id)
        if connection:
            try:
                if isinstance(message, str):
                    await connection.send_text(message)
                else: # Assume dict/list for JSON
                    await connection.send_json(message)
                return True
            except WebSocketDisconnect:
                logger.info(f"User {mask_user_id(user_id)} disconnected during send_personal_message.")
                self.remove_connection(user_id)
            except Exception as e:
                logger.error(f"Error sending message to user {mask_user_id(user_id)}: {str(e)}")
                # Connection might be stale, remove it
                self.remove_connection(user_id)
        return False
    
    async def broadcast(self, message: Any, exclude_user_id: Optional[int] = None):
        """Broadcast a message (JSON or text) to all connected clients except excluded user"""
        disconnected_users = []
        
        # Create a copy of the keys to iterate over, allowing removal during iteration
        user_ids = list(self.active_connections.keys())

        for user_id in user_ids:
            if exclude_user_id is not None and user_id == exclude_user_id:
                continue
                
            connection = self.active_connections.get(user_id)
            if not connection: # Should not happen with key iteration, but safety check
                continue

            try:
                if isinstance(message, str):
                    await connection.send_text(message)
                else: # Assume dict/list for JSON
                    await connection.send_json(message)
            except WebSocketDisconnect:
                 logger.info(f"User {mask_user_id(user_id)} disconnected during broadcast.")
                 disconnected_users.append(user_id)
            except Exception as e:
                logger.error(f"Error broadcasting to user {mask_user_id(user_id)}: {str(e)}")
                disconnected_users.append(user_id)
        
        # Clean up stale connections
        for user_id in disconnected_users:
            self.remove_connection(user_id)
            
    async def get_online_count(self) -> int:
        """Get number of online users"""
        return len(self.active_connections)
        
    async def get_online_users(self) -> List[int]:
        """Get list of online user IDs"""
        return list(self.active_connections.keys())

# Create a single instance of the connection manager
connection_manager = ConnectionManager()

# --- REVISED: Validate one-time WebSocket token using Redis ---
async def validate_one_time_token_redis(
    token: str, 
    redis_client: redis.Redis, 
    db: AsyncSession # Keep DB session to fetch user details
) -> Optional[User]:
    """
    Validate a one-time WebSocket token from Redis, delete it, and return the user.
    """
    redis_key = f"ws_token:{token}"
    logger.info(f"[WebSocket Auth] Attempting to validate one-time token from Redis: {redis_key[:18]}...")
    try:
        # Use WATCH/MULTI for atomic GET and DEL (optional but safer against race conditions)
        # More simply, just GET then DEL if exists
        user_id_bytes = await redis_client.get(redis_key)
        
        if not user_id_bytes:
            logger.warning(f"[WebSocket Auth] One-time token not found or expired in Redis: {redis_key[:18]}...")
            return None
        
        # Decode user_id and try to delete the token immediately
        user_id_str = user_id_bytes.decode()
        deleted_count = await redis_client.delete(redis_key)
        
        if deleted_count == 0:
            # This could happen in a race condition if another process used the token
            # between GET and DEL without using WATCH/MULTI.
            logger.warning(f"[WebSocket Auth] Token {redis_key[:18]}... was found but deleted before validation completed (race condition?)")
            return None
        
        logger.info(f"[WebSocket Auth] One-time token {redis_key[:18]}... successfully consumed from Redis for user_id: {user_id_str}")
        
        try:
            user_id = int(user_id_str)
        except ValueError:
            logger.error(f"[WebSocket Auth] Invalid user_id stored in Redis for token {redis_key[:18]}...: {user_id_str}")
            return None
        
        # Fetch the associated user
        user_result = await db.execute(select(User).where(User.id == user_id))
        user = user_result.scalar_one_or_none()
        
        if user:
            if not user.is_active:
                logger.warning(f"[WebSocket Auth] User {mask_user_id(user.id)} ({user.username}) associated with consumed token {redis_key[:18]}... is inactive.")
                return None
            logger.info(f"[WebSocket Auth] Redis token validated successfully for user: {user.username} (ID: {mask_user_id(user.id)})" )
            return user
        else:
            logger.warning(f"[WebSocket Auth] User with ID {mask_user_id(user_id)} (from consumed token {redis_key[:18]}...) not found in DB.")
            return None
    except Exception as e:
        logger.error(f"[WebSocket Auth] Authentication error: {str(e)}", exc_info=True)
        return None
# --- END REVISED ---

@router.websocket("/chat/{one_time_token}")
async def websocket_endpoint(
    websocket: WebSocket,
    one_time_token: str,
    db: AsyncSession = Depends(get_async_db),
    redis_client: redis.Redis = Depends(get_redis_dependency), # Use correct Redis dependency
):
    """
    WebSocket endpoint for real-time chat communications.

    Authentication is performed using a short-lived, one-time token
    passed in the URL path. This token is obtained via a separate REST endpoint.
    
    Note: Online status tracking is disabled for privacy reasons. The endpoint
    still tracks connections internally but does not broadcast user status.
    """
    websocket_id = f"ws_{uuid.uuid4()}" # Use UUID for better uniqueness
    
    user: Optional[User] = None # Define user outside try block
    user_id: Optional[int] = None  # Store user_id separately to avoid ORM access later
    username: Optional[str] = None  # Store username to avoid ORM access later
    
    # Create a mock Request object for rate limiting
    mock_request = Request(scope={
        "type": "http",
        "method": "GET",
        "path": f"/ws/chat/{one_time_token}",
        "headers": []
    })

    # Track resources to clean up in finally block
    db_session_needs_cleanup = True
    
    try:
        # 1. Authenticate user using the one-time token
        user = await validate_one_time_token_redis(one_time_token, redis_client, db)
        
        if not user:
            logger.warning(f"[WebSocket {websocket_id}] Authentication failed using one-time token, rejecting connection")
            # Accept briefly to send close reason, standard practice
            await websocket.accept() 
            await websocket.close(code=1008, reason="Authentication failed")
            return

        # Ensure user has a username
        if not user.username:
            # Generate a default username if not set
            user.username = f"User_{user.id}"
            db.add(user)
            await db.commit()
            logger.info(f"[WebSocket {websocket_id}] Generated default username for user {user.id}")

        # Extract user properties immediately to avoid ORM access later
        # This is critical to prevent lazy loading issues in async operations
        user_id = user.id
        username = user.username or f"User_{user_id}"
        
        logger.info(f"[WebSocket {websocket_id}] Authenticated user ID: {mask_user_id(user_id)}, Username: {username}")

        # 2. User is authenticated, accept the connection fully
        await websocket.accept()
        logger.info(f"[WebSocket {websocket_id}] Connection accepted for user {mask_user_id(user_id)}")
        
        # 3. Register connection
        connection_manager.add_connection(user_id, websocket)
        
        # 4. Send initial data (conversations, maybe recent messages)
        await send_conversations(user_id, websocket, db) # Send initial conversations

        # Simple ping/pong for connection keep-alive
        IDLE_TIMEOUT = 240  # 4 minutes
        PING_TIMEOUT = 60   # 1 minute
        
        last_activity = time.time()
        ping_sent_at = None
        waiting_for_pong = False
        
        # 5. Main message processing loop
        while True:
            try:
                # Set timeout based on state
                if waiting_for_pong:
                    # Shorter timeout when waiting for pong response
                    timeout = max(1, PING_TIMEOUT - (time.time() - ping_sent_at))
                else:
                    # Regular timeout based on idle time
                    timeout = max(1, IDLE_TIMEOUT - (time.time() - last_activity))
                
                # Wait for messages with timeout, use receive_json if expecting JSON
                receive_task = asyncio.create_task(websocket.receive_text())
                done, pending = await asyncio.wait(
                    [receive_task], 
                    timeout=timeout,
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # Cancel any pending tasks
                for task in pending:
                    task.cancel()
                
                if receive_task in done:
                    # Message received
                    data_raw = await receive_task
                    data = json.loads(data_raw)
                    last_activity = time.time()
                    waiting_for_pong = False
                    
                    # Handle pong responses
                    if data.get("type") == "pong":
                        logger.debug(f"[WebSocket {websocket_id}] Received pong from user {mask_user_id(user_id)}")
                        continue
                    
                    # Process normal message
                    await process_websocket_message(data, user, websocket, mock_request, db)
                
                else:
                    # Timeout reached
                    current_time = time.time()
                    
                    if waiting_for_pong:
                        # Pong response timeout - close connection
                        logger.warning(f"[WebSocket {websocket_id}] Ping timeout for user {mask_user_id(user_id)}, closing connection")
                        await websocket.close(code=1001, reason="Ping timeout")
                        break
                    
                    # Send ping to check if client is still alive
                    logger.debug(f"[WebSocket {websocket_id}] Sending ping to user {mask_user_id(user_id)}")
                    await websocket.send_json({"type": "ping", "ts": current_time})
                    ping_sent_at = current_time
                    waiting_for_pong = True
                    
            except asyncio.CancelledError:
                logger.info(f"[WebSocket {websocket_id}] WebSocket task cancelled for user {mask_user_id(user_id)}")
                break
                
            except WebSocketDisconnect:
                logger.info(f"[WebSocket {websocket_id}] Client disconnected normally")
                break # Exit loop on disconnect
                
            except json.JSONDecodeError:
                 logger.warning(f"[WebSocket {websocket_id}] Received invalid JSON from user {mask_user_id(user_id)}")
                 await websocket.send_json({"type": "error", "message": "Invalid JSON format"})
                 
            except RateLimitExceeded as e:
                 logger.warning(f"[WebSocket {websocket_id}] Rate limit exceeded for user {mask_user_id(user_id)}: {e.detail}")
                 await websocket.send_json({
                    "type": "error", 
                    "message": "Rate limit exceeded. Please wait.",
                    "detail": e.detail
                 })
                 
            except Exception as e:
                # Catch other unexpected errors during message processing
                logger.error(f"[WebSocket {websocket_id}] Error processing message for user {mask_user_id(user_id)}: {str(e)}")
                try:
                    # Try to inform the client
                    await websocket.send_json({"type": "error", "message": "Internal server error"})
                except:
                    logger.error(f"[WebSocket {websocket_id}] Failed to send error message to client")
                # For severe errors, break the loop
                break

    except Exception as e:
        # Catch errors during initial connection setup/authentication
        logger.error(f"[WebSocket {websocket_id}] Unexpected error during connection: {str(e)}")
        # Ensure connection is closed if accept() was called partially
        try:
            await websocket.close(code=1011, reason="Internal server error")
        except: 
            pass # Ignore errors during close after another error

    finally:
        # Final cleanup: Ensure all resources are properly released
        
        # 1. Remove from connection manager
        if user_id is not None:
            logger.info(f"[WebSocket {websocket_id}] Removing connection for user {mask_user_id(user_id)}")
            connection_manager.remove_connection(user_id)
                
        # 2. Close DB session if needed
        if db_session_needs_cleanup:
            try:
                logger.debug(f"[WebSocket {websocket_id}] Cleaning up DB session")
                await db.close()
                db_session_needs_cleanup = False
            except Exception as e:
                logger.error(f"[WebSocket {websocket_id}] Error closing DB session: {e}")
        
        # 3. Ensure WebSocket is closed
        try:
            logger.debug(f"[WebSocket {websocket_id}] Ensuring WebSocket is closed")
            await websocket.close()
        except:
            pass
            
        logger.info(f"[WebSocket {websocket_id}] Connection cleanup complete")


async def process_chat_message(
    message_data: Dict[str, Any],
    user: User,
    websocket: WebSocket,
    request: Request,
    db: AsyncSession
) -> None:
    """
    Process and store a chat message, including rate limiting.
    """
    websocket_id = getattr(websocket, 'id', 'unknown_ws_id')
    
    # Extract user data early to avoid ORM access issues later
    # This is critical to prevent lazy loading and MissingGreenlet errors
    user_id = user.id
    username = user.username or f"User_{user_id}"
    
    try:
        # Apply rate limit before processing - with safety check for mock requests
        try:
            if hasattr(request, 'app') and hasattr(request.app, 'state') and hasattr(request.app.state, 'limiter'):
                limiter: Limiter = request.app.state.limiter
                # Use user ID for rate limiting scope
                await limiter.hit(f"websocket_message:{user_id}", request)
            else:
                logger.debug(f"[WebSocket {websocket_id}] Skipping rate limiting for WebSocket message")
        except Exception as e:
            logger.warning(f"[WebSocket {websocket_id}] Rate limiting error: {str(e)}")
            # Continue processing even if rate limiting fails

        # Extract message content and receiver_id
        content = message_data.get("content", "").strip()
        receiver_id_str = message_data.get("receiver_id") # Can be null for public
        
        # Validate receiver ID if present
        receiver_id: Optional[int] = None
        receiver_username: Optional[str] = None 
        if receiver_id_str is not None:
            try:
                receiver_id = int(receiver_id_str)
                if receiver_id == user_id:
                     logger.warning(f"[WebSocket {websocket_id}] User {mask_user_id(user_id)} tried sending to self.")
                     await websocket.send_json({"type": "error", "message": "Cannot send message to yourself."})
                     return
                # Check if receiver exists - fixed: use execute + scalar instead of get for async
                receiver_query = await db.execute(select(User).where(User.id == receiver_id))
                receiver = receiver_query.scalar_one_or_none()
                if not receiver:
                     logger.warning(f"[WebSocket {websocket_id}] Receiver ID {receiver_id} not found.")
                     await websocket.send_json({"type": "error", "message": "Receiver user not found."})
                     return
                
                # Extract receiver username early to avoid ORM access later
                receiver_username = receiver.username or f"User_{receiver_id}"
            except (ValueError, TypeError):
                 logger.warning(f"[WebSocket {websocket_id}] Invalid receiver_id format: {receiver_id_str}")
                 await websocket.send_json({"type": "error", "message": "Invalid receiver ID format."})
                 return

        # Basic content validation
        if not content:
            logger.warning(f"[WebSocket {websocket_id}] Message content empty")
            await websocket.send_json({"type": "error", "message": "Message cannot be empty."})
            return
            
        if len(content) > 1000:
            logger.warning(f"[WebSocket {websocket_id}] Message content too long: {len(content)} chars")
            await websocket.send_json({"type": "error", "message": "Message must be 1-1000 characters."})
            return
            
        # Sanitize message content for security
        sanitized_content = sanitize_message_content(content)
        
        # Determine if this is a public or private message
        is_public_message = receiver_id is None
        
        # Debug log for message type
        logger.info(f"[WebSocket {websocket_id}] Processing {is_public_message and 'PUBLIC' or 'PRIVATE'} message from {mask_user_id(user_id)}")
        
        # Determine conversation ID for private messages
        conversation_id: Optional[str] = None
        if not is_public_message and receiver_id is not None:
             conversation_id = await get_or_create_conversation_id(db, user_id, receiver_id)
             logger.debug(f"[WebSocket {websocket_id}] Using conversation_id: {conversation_id}")

        # Create and store the message
        msg = ChatMessage(
            sender_id=user_id,
            receiver_id=receiver_id,
            content=sanitized_content,
            sender_nickname=username,
            conversation_id=conversation_id,
            is_public=is_public_message,
            is_read=False  # Explicitly set new messages as unread
        )
        db.add(msg)
        await db.commit()
        await db.refresh(msg)
        
        # Get sender's avatar URL from User record if available
        user_query = await db.execute(select(User.telegram_photo_url).where(User.id == user_id))
        avatar_url = user_query.scalar_one_or_none()
        
        # Prepare message data for broadcasting/sending
        message_out = {
            "type": "chat_message",
            "message": {
                "id": msg.id,
                "sender_id": str(msg.sender_id),
                "sender_nickname": username,
                "sender_avatar": avatar_url,
                "receiver_id": str(msg.receiver_id) if msg.receiver_id else None,
                "receiver_nickname": receiver_username,
                "content": msg.content,
                "created_at": msg.created_at.isoformat() + "Z",
                "is_public": msg.is_public,
                "is_read": msg.is_read,  # Include read status
                "conversation_id": msg.conversation_id
            }
        }
        
        # Handle message routing
        if is_public_message:
            # PUBLIC MESSAGE - broadcast to all connected users
            logger.info(f"[WebSocket {websocket_id}] Broadcasting public message ID {msg.id} from {mask_user_id(user_id)}")
            await connection_manager.broadcast(message_out)
        else:
            # PRIVATE MESSAGE - send ONLY to sender and receiver
            logger.info(f"[WebSocket {websocket_id}] Sending private message ID {msg.id} from {mask_user_id(user_id)} to {mask_user_id(receiver_id)}")
            
            # Send to recipient only if they exist and are online
            if receiver_id is not None:
                sent_to_recipient = await connection_manager.send_personal_message(message_out, receiver_id)
                
                # Always send a copy back to the sender
                await connection_manager.send_personal_message(message_out, user_id)
                
                if not sent_to_recipient:
                    logger.info(f"[WebSocket {websocket_id}] Recipient {mask_user_id(receiver_id)} is offline, message delivered when they connect")

    except RateLimitExceeded as e:
         raise e 
    except Exception as e:
        logger.error(f"[WebSocket {websocket_id}] Error processing chat message: {str(e)}", exc_info=True)
        try:
            await websocket.send_json({
                "type": "error",
                "message": "Failed to send message due to server error."
            })
        except:
            pass # Ignore if we can't even send the error

def sanitize_message_content(content: str) -> str:
    """
    Sanitize message content while preserving markdown image syntax and URLs.
    Protects against XSS and script injection.
    """
    import re
    from html import escape
    
    # First escape all HTML entities to prevent XSS
    escaped_content = escape(content)
    
    # Define regex patterns for permitted content
    image_md_pattern = r'!\[(.*?)\]\((https?://[^\s<>"]+?)\)'  # ![alt](url)
    url_pattern = r'https?://[^\s<>"]+?(?=\s|$)'  # URLs ending with space or end of line
    
    # Function to process and validate image markdown
    def validate_image_md(match):
        alt_text = match.group(1)
        url = match.group(2)
        
        # Validate URL is a common image format
        if re.search(r'\.(gif|jpe?g|png|webp)$', url.lower()):
            return f'![{alt_text}]({url})'
        else:
            # If not a valid image URL, sanitize as regular text
            return f'![{alt_text}]({url})'  # Keep as is but it was already HTML escaped
    
    # Find and preserve image markdown
    result = re.sub(image_md_pattern, validate_image_md, escaped_content)
    
    # Handle URLs - find URLs and make them clickable, but sanitized
    def replace_url(match):
        url = match.group(0)
        # Simple validation - keep domain limited to common domains for images/GIFs
        if re.search(r'(giphy\.com|imgur\.com|tenor\.com|media\.giphy\.com)', url.lower()):
            return url  # Return the unescaped URL to preserve it
        return url  # Return URL as-is (already HTML escaped)
    
    # Replace URLs with validated ones
    result = re.sub(url_pattern, replace_url, result)
    
    return result

async def process_websocket_message(
    data: Dict[str, Any],
    user: User,
    websocket: WebSocket,
    request: Request, # Correctly defined
    db: AsyncSession
):
    """
    Process incoming WebSocket messages based on their type.
    """
    message_type = data.get("type")
    
    # Extract user_id early to avoid accessing user.id in places that might cause
    # lazy loading which creates a MissingGreenlet error with SQLAlchemy async
    user_id = user.id
    
    try:
        # Handling different message types
        if message_type == "chat_message":
            await process_chat_message(data, user, websocket, request, db)
        
        elif message_type == "get_messages":
            chat_id = data.get("chat_id")
            if chat_id is None:
                # If chat_id is None, get public messages
                messages = await get_public_messages(db, limit=50)
            else:
                # Otherwise, get private messages
                messages = await get_private_messages(db, user_id, chat_id, limit=50)
                
            # Format messages for response
            messages_response = []
            for msg in messages:
                messages_response.append(message_to_dict(msg))
                
            # Send messages to client
            await websocket.send_json({
                "type": "messages",
                "messages": messages_response
            })
            
        elif message_type == "get_conversations":
            # Use the send_conversations function directly instead of get_user_conversations
            await send_conversations(user_id, websocket, db)
            
        elif message_type == "ping":
            # Respond to ping with pong to keep connection alive
            logger.debug(f"Received ping from user {mask_user_id(user_id)}, sending pong")
            await websocket.send_json({"type": "pong"})
            
        elif message_type == "pong":
            # Client acknowledges our ping
            logger.debug(f"Received pong from user {mask_user_id(user_id)}")
            
        elif message_type == "mark_read":
            # Mark messages as read
            conversation_id = data.get("conversation_id")
            if conversation_id:
                # Get modified messages and sender ID
                message_ids, sender_id = await mark_messages_as_read(db, user_id, conversation_id)
                
                # Send confirmation to the client who marked messages as read
                await websocket.send_json({
                    "type": "mark_read_response",
                    "success": True,
                    "conversation_id": conversation_id,
                    "message_ids": message_ids,
                    "user_id": user_id
                })
                
                # Notify the original sender that their messages were read
                if sender_id and message_ids:
                    notification = {
                        "type": "messages_read_by_recipient",
                        "message_ids": message_ids,
                        "conversation_id": conversation_id,
                        "read_by_user_id": user_id
                    }
                    
                    # Try to notify the sender if they're online
                    await connection_manager.send_personal_message(notification, sender_id)
                    logger.debug(f"Notified user {mask_user_id(sender_id)} that their messages were read by {mask_user_id(user_id)}")
                
                # Refresh conversations to update unread counts
                await send_conversations(user_id, websocket, db)
                
        else:
            logger.warning(f"Unknown message type: {message_type}")
            await websocket.send_json({
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            })
    except Exception as e:
        logger.error(f"Error processing '{message_type}' message: {str(e)}", exc_info=True)
        try:
            await websocket.send_json({
                "type": "error",
                "message": "Internal server error"
            })
        except Exception:
            logger.error("Failed to send error response", exc_info=True)


async def send_conversations(user_id: int, websocket: WebSocket, db: AsyncSession):
    """Fetch and send the list of conversations to a user."""
    try:
        # Get all private messages involving this user
        sent_query = select(ChatMessage.receiver_id, 
                           ChatMessage.created_at).where(
            ChatMessage.sender_id == user_id,
            ChatMessage.is_public == False
        )
        
        received_query = select(ChatMessage.sender_id, 
                               ChatMessage.created_at).where(
            ChatMessage.receiver_id == user_id,
            ChatMessage.is_public == False
        )
        
        # Execute queries
        sent_result = await db.execute(sent_query)
        received_result = await db.execute(received_query)
        
        # Get results
        sent_messages = sent_result.all()
        received_messages = received_result.all()
        
        # Process messages to find unique conversation partners
        partners = {}
        
        # Process sent messages
        for receiver_id, created_at in sent_messages:
            if receiver_id not in partners:
                partners[receiver_id] = {"last_message_at": created_at}
            elif created_at > partners[receiver_id]["last_message_at"]:
                partners[receiver_id]["last_message_at"] = created_at
        
        # Process received messages
        for sender_id, created_at in received_messages:
            if sender_id not in partners:
                partners[sender_id] = {"last_message_at": created_at}
            elif created_at > partners[sender_id]["last_message_at"]:
                partners[sender_id]["last_message_at"] = created_at
        
        if not partners:
            await websocket.send_json({"type": "conversations", "conversations": []})
            return

        partner_ids = list(partners.keys())

        # Get partner usernames from User table
        user_result = await db.execute(
            select(User.id, User.username, User.telegram_photo_url)
            .where(User.id.in_(partner_ids))
        )
        
        # Create dictionaries for usernames and avatars
        usernames = {}
        avatars = {}
        for user_id, username, avatar_url in user_result:
            usernames[user_id] = username or f"User_{user_id}"
            avatars[user_id] = avatar_url

        # Count unread messages for each partner in a separate query
        unread_counts = {}
        for partner_id in partner_ids:
            unread_query = select(func.count()).where(
                ChatMessage.sender_id == partner_id,
                ChatMessage.receiver_id == user_id,
                ChatMessage.is_read == False,
                ChatMessage.is_public == False
            )
            unread_result = await db.execute(unread_query)
            unread_counts[partner_id] = unread_result.scalar() or 0

        # Build conversation list
        conversations = []
        for partner_id, data in partners.items():
            # Always use real username from User table
            username = usernames.get(partner_id, f"User_{partner_id}")
            
            conversations.append({
                "id": partner_id,  # Keep ID as number for internal use
                "username": username,  # Use real username consistently
                "avatar_url": avatars.get(partner_id),
                "last_message_at": data["last_message_at"].isoformat() + "Z" if data["last_message_at"] else None,
                "unread_count": unread_counts.get(partner_id, 0),
                "is_online": False  # Always set to false for privacy
            })
            
        # Sort by last message time (most recent first)
        conversations.sort(
            key=lambda x: x.get("last_message_at") or "0000-00-00T00:00:00Z", 
            reverse=True
        )
        
        # Add public chat conversation (always online)
        try:
            # Get last public message time
            public_msg_query = select(func.max(ChatMessage.created_at)).where(ChatMessage.is_public == True)
            public_result = await db.execute(public_msg_query)
            last_public_msg_time = public_result.scalar_one_or_none()
            
            # Add public chat to conversation list
            if last_public_msg_time:
                conversations.append({
                    "id": 0,  # Special ID for public chat
                    "username": "Public Chat", 
                    "avatar_url": None,
                    "last_message_at": last_public_msg_time.isoformat() + "Z",
                    "unread_count": 0,  # No unread tracking for public chat
                    "is_online": True,  # Public chat is always shown as online
                    "is_public": True
                })
        except Exception as e:
            logger.error(f"Error adding public chat to conversations: {str(e)}")
            
        # Send to client
        await websocket.send_json({
            "type": "conversations",
            "conversations": conversations
        })
        
    except Exception as e:
        logger.error(f"Error sending conversations for user {mask_user_id(user_id)}: {str(e)}")
        await websocket.send_json({
            "type": "error",
            "message": "Error retrieving conversations"
        })

# --- Helper functions for websocket module ---
# (These could potentially be moved to a shared utils file)
async def get_or_create_conversation_id(db: AsyncSession, user1_id: int, user2_id: int) -> str:
    """Get or create a unique conversation ID for two users."""
    # Ensure consistent ordering
    if user1_id > user2_id:
        user1_id, user2_id = user2_id, user1_id
    
    # Simple concatenation for ID (consider hashing for very large scale)
    # In a real app, might use a separate Conversation table
    conversation_id = f"conv_{user1_id}_{user2_id}"
    logger.debug(f"Generated conversation ID: {conversation_id} for users {mask_user_id(user1_id)} and {mask_user_id(user2_id)}")
    return conversation_id

async def mark_messages_as_read(db: AsyncSession, user_id: int, conversation_id: str) -> List[int]:
    """Mark all messages in a conversation as read for a specific user and return sender IDs"""
    query = select(ChatMessage).where(
        and_(
            ChatMessage.conversation_id == conversation_id,
            ChatMessage.receiver_id == user_id,
            ChatMessage.is_read == False
        )
    )
    result = await db.execute(query)
    messages = result.scalars().all()
    
    # Track message IDs and sender IDs for notifications
    message_ids = []
    sender_id = None
    
    for message in messages:
        message.is_read = True
        message_ids.append(message.id)
        # Store the sender ID (should be the same for all messages in this query)
        if sender_id is None and message.sender_id != user_id:
            sender_id = message.sender_id
    
    await db.commit()
    
    # Return list of message IDs and the sender ID
    return message_ids, sender_id

def message_to_dict(msg: ChatMessage) -> dict:
    """
    Convert a ChatMessage model to a standardized dictionary format for JSON response.
    Includes string IDs instead of integers to mask actual user IDs.
    """
    return {
        "id": msg.id,
        "sender_id": str(msg.sender_id),  # Convert to string to mask actual ID
        "receiver_id": str(msg.receiver_id) if msg.receiver_id else None,  # Convert to string
        "content": msg.content,
        "created_at": msg.created_at.isoformat() + "Z",
        "is_public": msg.is_public,
        "is_read": msg.is_read,  # Include read status for read receipts
        "sender_nickname": msg.sender_nickname or f"User_{msg.sender_id}",  # Use real username or fallback
        "sender_avatar": msg.sender_avatar,  # Include avatar URL
        "conversation_id": msg.conversation_id  # Include conversation ID if available
    }

async def get_public_messages(db: AsyncSession, limit: int = 50) -> List[ChatMessage]:
    """
    Get the most recent public messages.
    """
    query = select(ChatMessage).where(
        ChatMessage.is_public == True
    ).order_by(
        desc(ChatMessage.created_at)
    ).limit(limit)
    
    result = await db.execute(query)
    messages_db = result.scalars().all()
    
    # Get all the sender IDs to fetch avatars and usernames in one query
    sender_ids = list(set(msg.sender_id for msg in messages_db))
    
    # Fetch avatar URLs and usernames for all senders in one query
    if sender_ids:
        avatar_query = await db.execute(
            select(User.id, User.telegram_photo_url, User.username)
            .where(User.id.in_(sender_ids))
        )
        
        # Create lookup dictionaries
        avatars = {}
        usernames = {}
        for user_id, photo_url, username in avatar_query:
            avatars[user_id] = photo_url
            usernames[user_id] = username or f"User_{user_id}"
        
        # Populate message objects with sender info
        for msg in messages_db:
            msg.sender_avatar = avatars.get(msg.sender_id)
            msg.sender_nickname = usernames.get(msg.sender_id, f"User_{msg.sender_id}")
    
    # Sort chronologically (oldest first)
    return sorted(messages_db, key=lambda x: x.created_at)

async def get_private_messages(db: AsyncSession, user_id: int, chat_partner_id: int, limit: int = 50) -> List[ChatMessage]:
    """
    Get private messages between two users.
    """
    # Ensure chat_partner_id is an integer
    try:
        chat_partner_id = int(chat_partner_id)
    except (ValueError, TypeError):
        logger.error(f"Invalid chat_partner_id: {chat_partner_id}")
        return []
    
    # Get or create conversation ID
    conversation_id = await get_or_create_conversation_id(db, user_id, chat_partner_id)
    logger.info(f"Retrieving messages for conversation: {conversation_id} between users {mask_user_id(user_id)} and {mask_user_id(chat_partner_id)}")
    
    # Mark messages as read
    await mark_messages_as_read(db, user_id, conversation_id)
    
    # Get messages for this conversation
    query = select(ChatMessage).where(
        ChatMessage.conversation_id == conversation_id
    ).order_by(
        desc(ChatMessage.created_at)
    ).limit(limit)
    
    result = await db.execute(query)
    messages_db = result.scalars().all()
    
    logger.info(f"Found {len(messages_db)} messages for conversation: {conversation_id}")
    
    # Get all the sender IDs to fetch avatars and usernames in one query
    sender_ids = list(set(msg.sender_id for msg in messages_db))
    
    # Fetch avatar URLs and usernames for all senders in one query
    if sender_ids:
        avatar_query = await db.execute(
            select(User.id, User.telegram_photo_url, User.username)
            .where(User.id.in_(sender_ids))
        )
        
        # Create lookup dictionaries
        avatars = {}
        usernames = {}
        for user_id, photo_url, username in avatar_query:
            avatars[user_id] = photo_url
            usernames[user_id] = username or f"User_{user_id}"
        
        # Populate message objects with sender info
        for msg in messages_db:
            msg.sender_avatar = avatars.get(msg.sender_id)
            msg.sender_nickname = usernames.get(msg.sender_id, f"User_{msg.sender_id}")
    
    # Sort chronologically (oldest first)
    return sorted(messages_db, key=lambda x: x.created_at)

# Helper function to mask user IDs in logs
def mask_user_id(user_id: int) -> str:
    """Mask a user ID for secure logging"""
    if user_id is None:
        return "None"
    user_id_str = str(user_id)
    if len(user_id_str) <= 2:
        return f"user_{user_id_str[-1]}*"
    return f"user_{user_id_str[-2:]}*"

# Use this helper throughout the file for log messages
# For example:
# logger.info(f"[WebSocket {websocket_id}] User {mask_user_id(user_id)} marked as offline")