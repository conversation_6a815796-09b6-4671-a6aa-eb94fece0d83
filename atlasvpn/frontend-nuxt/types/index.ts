// Telegram WebApp types
export interface WebAppUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  is_bot?: boolean;
}

export interface WebAppInitData {
  query_id: string;
  user: WebAppUser;
  auth_date: string;
  hash: string;
}

export interface TelegramWebApp {
  initData: string;
  initDataUnsafe: WebAppInitData;
  platform: string;
  colorScheme: string;
  themeParams: {
    bg_color: string;
    text_color: string;
    hint_color: string;
    link_color: string;
    button_color: string;
    button_text_color: string;
    secondary_bg_color: string;
  };
  isExpanded: boolean;
  viewportHeight: number;
  viewportStableHeight: number;
  headerColor: string;
  backgroundColor: string;
  BackButton: {
    show: () => void;
    hide: () => void;
    onClick: (callback: () => void) => void;
    offClick: (callback: () => void) => void;
    isVisible: boolean;
  };
  MainButton: {
    text: string;
    color: string;
    textColor: string;
    isVisible: boolean;
    isActive: boolean;
    isProgressVisible: boolean;
    setText: (text: string) => void;
    onClick: (callback: () => void) => void;
    offClick: (callback: () => void) => void;
    show: () => void;
    hide: () => void;
    enable: () => void;
    disable: () => void;
    showProgress: (leaveActive?: boolean) => void;
    hideProgress: () => void;
    setParams: (params: {
      text?: string;
      color?: string;
      text_color?: string;
      is_active?: boolean;
      is_visible?: boolean;
    }) => void;
  };
  ready: () => void;
  expand: () => void;
  close: () => void;
  enableClosingConfirmation: () => void;
  disableClosingConfirmation: () => void;
  isClosingConfirmationEnabled: boolean;
  onEvent: (eventType: string, eventHandler: () => void) => void;
  offEvent: (eventType: string, eventHandler: () => void) => void;
  sendData: (data: string) => void;
  showPopup: (params: {
    title?: string;
    message: string;
    buttons?: {
      id: string;
      type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive';
      text: string;
    }[];
  }) => Promise<{ button_id: string }>;
  showAlert: (message: string) => void;
  showConfirm: (message: string) => Promise<boolean>;
  openLink: (url: string) => void;
}

declare global {
  interface Window {
    Telegram?: {
      WebApp: TelegramWebApp;
    };
    telegramReady?: boolean;
    hideLoadingScreen?: () => void;
    telegramError?: any;
  }
}

// User and Role types
export enum Role {
  admin = 'admin',
  reseller = 'reseller',
  user = 'user',
}

export interface User {
  id: number;
  username: string;
  email?: string;
  role: 'admin' | 'reseller' | 'user';
  telegram_id?: string;
  isNewUser?: number;
  wallet_balance: number;
  first_name?: string;
  last_name?: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  telegram_photo_url?: string;
  referral_code: string;
  referred_by?: number;
  referred_by_username?: string;
  discount_percent: number;
  vpn_subscriptions: VPNSubscription[];
  device_platform?: string;
  device_id?: string;
  last_login?: string;
}

// VPN related types
export interface MarzbanSystemStats {
  total_users: number;
  active_users: number;
  total_bandwidth: number;
  uptime: number;
  version: string;
  mem_total: number;
  mem_used: number;
  cpu_usage: number;
}

export interface MarzbanPanel {
  id: number;
  name: string;
  api_url: string;
  admin_username: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
  stats?: MarzbanSystemStats;
}

export interface VPNPackage {
  id: number;
  name: string;
  description: string;
  price: number;
  data_limit: number;
  expire_days: number;
  allowed_panels: MarzbanPanel[];
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface VPNSubscription {
  id: number;
  user_id: number;
  package_id: number;
  marzban_username: string;
  subscription_url: string;
  data_limit: number;
  data_used: number;
  expires_at: string;
  created_at: string;
  updated_at: string;
  package: VPNPackage;
  user: User;
  is_active: boolean;
}

// Transaction types
export enum TransactionType {
  subscription_purchase = 'subscription_purchase',
  subscription_renewal = 'subscription_renewal',
  wallet_topup = 'wallet_topup',
  referral_commission = 'referral_commission',
  admin_adjustment = 'admin_adjustment',
  card_profit_claim = 'card_profit_claim',
  card_level_up = 'card_level_up',
  card_purchase = 'card_purchase',
}

export interface Transaction {
  id: number;
  user_id: number;
  type: TransactionType;
  amount: number;
  created_at: string;
  status: 'pending' | 'completed' | 'failed';
  description: string;
  balance_after: number;
  reseller_id?: number;
  subscription_id?: number;
  package_name?: string;
  package_price?: number;
  package_data_limit?: number;
  package_expire_days?: number;
}

// API Response types
export interface ApiResponse<T = any> {
  data: T;
  message?: string;
  error?: string;
  detail?: string;
  csrf_token?: string;
}

// Dashboard types
export interface DashboardStats {
  active_subscriptions: number;
  total_data_used: number;
  total_data_limit: number;
  wallet_balance: number;
  total_accumulated_card_profit?: number;
  total_passive_hourly_income?: number;
}

// Tab Types
export type TabId =
  | 'home'
  | 'services'
  | 'packages'
  | 'wallet'
  | 'friends'
  | 'earn'
  | 'cards'
  | 'transactions'
  | 'premium'
  | 'verse';

// Authentication types
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface TelegramLoginParams {
  init_data: string;
  auth_method: 'telegram';
}

export interface VerifySessionData {
  status: string;
  data?: any;
}

export interface TelegramData {
  telegram_id: string;
  username: string | undefined;
  first_name: string | undefined;
  last_name: string | undefined;
  photo_url: string | undefined;
}

export interface VerifiedData {
  initData: string;
  telegramData: TelegramData;
  timestamp: number;
}

// Task types
export interface Task {
  id: number;
  title: string;
  description: string;
  type: 'telegram' | 'social' | 'referral' | 'daily' | 'special';
  status: 'available' | 'pending' | 'completed' | 'failed';
  reward_amount: number;
  reward_type: 'coins' | 'premium_days' | 'special';
  requirements?: Record<string, any>;
  is_claimed: boolean;
  completed_at?: string;
  claimed_at?: string;
  expires_at?: string;
  avg_completion_time?: number | null;
  difficulty: 'easy' | 'medium' | 'hard';
  category: string;
  icon_url?: string;
  action_url?: string;
  verification_required: boolean;
  max_completions?: number;
  current_completions: number;
  created_at: string;
  updated_at: string;
}

export interface TaskCompletion {
  task_id: number;
  user_id: number;
  completed_at: string;
  reward_amount: number;
  status: 'completed' | 'verified' | 'failed';
  verification_data?: Record<string, any>;
}

export interface DailyTaskStreak {
  user_id: number;
  current_streak: number;
  longest_streak: number;
  last_checkin_date?: string;
  total_checkins: number;
  streak_rewards_claimed: number[];
  next_reward_day: number;
  next_reward_amount: number;
}

export interface DailyCheckInResponse {
  success: boolean;
  reward_amount: number;
  new_streak: number;
  bonus_applied: boolean;
  message: string;
}

export interface TaskVerificationResult {
  success: boolean;
  verified: boolean;
  message: string;
  reward_amount?: number;
}

export interface CreateTaskData {
  title: string;
  description: string;
  type: Task['type'];
  reward_amount: number;
  reward_type: Task['reward_type'];
  requirements?: Record<string, any>;
  expires_at?: string;
  difficulty: Task['difficulty'];
  category: string;
  icon_url?: string;
  action_url?: string;
  verification_required: boolean;
  max_completions?: number;
}

export interface UpdateTaskData extends Partial<CreateTaskData> {
  id: number;
}

export interface TaskAnalytics {
  total_tasks: number;
  completed_tasks: number;
  completion_rate: number;
  total_rewards_distributed: number;
  average_completion_time: number;
  popular_categories: Array<{
    category: string;
    count: number;
  }>;
}
