import type { VPNPackage, VPNSubscription, MarzbanPanel } from '~/types'

/**
 * Modern VPN composable using Nuxt 4 data fetching patterns
 * Optimized for Telegram Mini App with proper caching and error handling
 */
export const useVPN = () => {
  // Default fetch options optimized for Telegram Mini App
  const defaultOptions = {
    server: false, // Client-side only for Telegram Mini App
    retry: 3,
    retryDelay: 1000,
    timeout: 15000,
    onRequestError({ error }: { error: any }) {
      console.error('[VPN] Request error:', error)
    },
    onResponseError({ response }: { response: any }) {
      console.error('[VPN] Response error:', response.status, response.statusText)
    }
  }

  // Purchase state (local state for UI feedback)
  const isPurchasing = ref(false)
  const purchaseError = ref<string | null>(null)

  // VPN packages with modern useFetch
  const {
    data: packages,
    pending: isLoadingPackages,
    error: packagesError,
    refresh: refreshPackages
  } = useFetch<VPNPackage[]>('/api/vpn/packages', {
    ...defaultOptions,
    key: 'vpn-packages',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // User subscriptions with modern useFetch
  const {
    data: subscriptions,
    pending: isLoadingSubscriptions,
    error: subscriptionsError,
    refresh: refreshSubscriptions
  } = useFetch<VPNSubscription[]>('/api/vpn/subscriptions', {
    ...defaultOptions,
    key: 'vpn-subscriptions',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // Marzban panels with modern useFetch
  const {
    data: panels,
    pending: isLoadingPanels,
    error: panelsError,
    refresh: refreshPanels
  } = useFetch<MarzbanPanel[]>('/api/vpn/panels', {
    ...defaultOptions,
    key: 'vpn-panels',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // Purchase VPN package with modern $fetch
  const purchasePackage = async (packageId: number, panelId?: number): Promise<VPNSubscription> => {
    if (isPurchasing.value) throw new Error('Purchase already in progress')

    isPurchasing.value = true
    purchaseError.value = null

    try {
      const response = await $fetch<{ data: VPNSubscription }>('/api/vpn/purchase', {
        method: 'POST',
        body: {
          package_id: packageId,
          panel_id: panelId
        }
      })

      // Refresh subscriptions after purchase
      await refreshSubscriptions()

      return response.data
    } catch (error: any) {
      console.error('[VPN] Failed to purchase package:', error)
      purchaseError.value = error.message || 'Failed to purchase VPN package'
      throw error
    } finally {
      isPurchasing.value = false
    }
  }

  // Renew subscription with modern $fetch
  const renewSubscription = async (subscriptionId: number): Promise<VPNSubscription> => {
    try {
      const response = await $fetch<{ data: VPNSubscription }>(`/api/vpn/subscriptions/${subscriptionId}/renew`, {
        method: 'POST'
      })

      // Refresh subscriptions to get updated data
      await refreshSubscriptions()

      return response.data
    } catch (error: any) {
      console.error('[VPN] Failed to renew subscription:', error)
      throw error
    }
  }

  // Get subscription details with modern $fetch
  const getSubscriptionDetails = async (subscriptionId: number): Promise<VPNSubscription> => {
    try {
      const response = await $fetch<{ data: VPNSubscription }>(`/api/vpn/subscriptions/${subscriptionId}`)
      return response.data
    } catch (error: any) {
      console.error('[VPN] Failed to get subscription details:', error)
      throw error
    }
  }

  // Cancel subscription with modern $fetch
  const cancelSubscription = async (subscriptionId: number): Promise<void> => {
    try {
      await $fetch(`/api/vpn/subscriptions/${subscriptionId}`, {
        method: 'DELETE'
      })

      // Refresh subscriptions to get updated data
      await refreshSubscriptions()
    } catch (error: any) {
      console.error('[VPN] Failed to cancel subscription:', error)
      throw error
    }
  }

  // Refresh all VPN data
  const refreshVPNData = async () => {
    await Promise.all([
      refreshPackages(),
      refreshSubscriptions(),
      refreshPanels()
    ])
  }

  // Computed properties
  const activeSubscriptions = computed(() => 
    subscriptions.value.filter(sub => sub.is_active && new Date(sub.expires_at) > new Date())
  )

  const expiredSubscriptions = computed(() => 
    subscriptions.value.filter(sub => !sub.is_active || new Date(sub.expires_at) <= new Date())
  )

  const totalDataUsed = computed(() => 
    subscriptions.value.reduce((total, sub) => total + sub.data_used, 0)
  )

  const totalDataLimit = computed(() => 
    activeSubscriptions.value.reduce((total, sub) => total + sub.data_limit, 0)
  )

  const dataUsagePercentage = computed(() => {
    if (totalDataLimit.value === 0) return 0
    return Math.min((totalDataUsed.value / totalDataLimit.value) * 100, 100)
  })

  const hasActiveSubscription = computed(() => 
    activeSubscriptions.value.length > 0
  )

  const nextExpiringSubscription = computed(() => {
    if (activeSubscriptions.value.length === 0) return null
    
    return activeSubscriptions.value.reduce((earliest, current) => {
      return new Date(current.expires_at) < new Date(earliest.expires_at) ? current : earliest
    })
  })

  const availablePanels = computed(() => 
    panels.value.filter(panel => panel.is_active)
  )

  const formatDataUsage = (bytes: number): string => {
    if (bytes === 0) return '0 GB'
    const gb = bytes / (1024 * 1024 * 1024)
    return `${gb.toFixed(2)} GB`
  }

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price)
  }

  const getDaysUntilExpiry = (expiresAt: string): number => {
    const now = new Date()
    const expiry = new Date(expiresAt)
    const diffTime = expiry.getTime() - now.getTime()
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  return {
    // State
    packages: readonly(packages),
    subscriptions: readonly(subscriptions),
    panels: readonly(panels),
    isLoadingPackages: readonly(isLoadingPackages),
    isLoadingSubscriptions: readonly(isLoadingSubscriptions),
    isLoadingPanels: readonly(isLoadingPanels),
    packagesError: readonly(packagesError),
    subscriptionsError: readonly(subscriptionsError),
    panelsError: readonly(panelsError),
    isPurchasing: readonly(isPurchasing),
    purchaseError: readonly(purchaseError),

    // Actions
    refreshPackages,
    refreshSubscriptions,
    refreshPanels,
    refreshVPNData,
    purchasePackage,
    renewSubscription,
    getSubscriptionDetails,
    cancelSubscription,

    // Computed
    activeSubscriptions,
    expiredSubscriptions,
    totalDataUsed,
    totalDataLimit,
    dataUsagePercentage,
    hasActiveSubscription,
    nextExpiringSubscription,
    availablePanels,

    // Utilities
    formatDataUsage,
    formatPrice,
    getDaysUntilExpiry
  }
}
