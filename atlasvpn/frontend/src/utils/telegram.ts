import { useEffect, useState, useCallback } from 'react';
import type {
  TelegramWebApp,
  WebAppInitData,
  PreparedInlineMessage,
  WebAppUser,
  WebAppEventType,
} from '../types/telegram';
import { secureTelegramStorage } from './secureTelegramStorage';

// --- Core WebApp Access ---
export const tg = window.Telegram?.WebApp as TelegramWebApp | undefined;

// Safe WebApp access function to use throughout the codebase
const getWebApp = () => window.Telegram?.WebApp as TelegramWebApp | undefined;

// --- App Visibility Management ---
// Global state for tracking app visibility
let isAppVisible = true;
let isAppMinimized = false;
let lastActiveTime = Date.now();
let visibilityChangeCallbacks: Function[] = [];

// Constants for Telegram events
const EVENTS = {
  VIEWPORT_CHANGED: 'viewportChanged' as WebAppEventType,
  THEME_CHANGED: 'themeChanged' as WebAppEventType,
  MAIN_BUTTON_CLICKED: 'mainButtonClicked' as WebAppEventType,
  ACTIVATED: 'activated' as WebAppEventType,
  DEACTIVATED: 'deactivated' as WebAppEventType,
};

// Session storage keys for state persistence
const SESSION_STATE_KEY = 'telegramMiniAppState';
const APP_DEACTIVATED_FLAG_KEY = 'telegramMiniAppDeactivated';

// --- Core Initialization & Context ---
/**
 * Initialize Telegram WebApp integration
 * @returns The WebApp instance or false if not available
 */
export async function initTelegramWebApp() {
  const webapp = getWebApp();
  if (!webapp) {
    console.warn('Telegram WebApp not available - running in browser mode');
    // Attempt to restore state even in browser mode for testing
    await restoreMiniAppState();
    return false;
  }

  try {
    // Configure Telegram app
    webapp.enableClosingConfirmation();
    webapp.expand();

    // Ensure MainButton is hidden by default
    if (webapp.MainButton && webapp.MainButton.isVisible) {
      webapp.MainButton.hide();
    }

    // Store referral code if present
    const referralCode = getReferralCode();
    if (referralCode) {
      await secureTelegramStorage.setReferralCode(referralCode);
    }

    // Apply Telegram theme colors
    applyTelegramThemeToDOM();

    // Setup event listeners for visibility detection
    webapp.onEvent(EVENTS.VIEWPORT_CHANGED, handleViewportChange);
    document.addEventListener('visibilitychange', handleDocumentVisibilityChange);
    webapp.onEvent(EVENTS.THEME_CHANGED, handleThemeChange);
    webapp.onEvent(EVENTS.ACTIVATED, handleAppActivated);
    webapp.onEvent(EVENTS.DEACTIVATED, handleAppDeactivated);

    // Start heartbeat for background detection
    startHeartbeat();

    // Attempt to restore state on initial load
    await restoreMiniAppState();

    // Tell Telegram the app is ready
    webapp.ready();
    console.log('Telegram WebApp initialized');

    return webapp;
  } catch (error) {
    console.error('Error initializing Telegram WebApp:', error);
    return false;
  }
}

/**
 * Validate the Telegram context and user data
 * @throws Error if not in Telegram or missing data
 */
export function validateTelegramContext() {
  const webapp = getWebApp();
  if (!webapp) throw new Error('Please open this application in Telegram');

  const initData = webapp.initData;
  if (!initData) throw new Error('No init data available');

  const user = webapp.initDataUnsafe.user;
  if (!user) throw new Error('No user data available');

  return { webapp, initData, user };
}

// --- Visibility Management Core Functions ---
/**
 * Handles changes in the Telegram viewport (can indicate minimization)
 */
function handleViewportChange() {
  const webapp = getWebApp();
  if (!webapp) return;

  const currentTime = Date.now();
  const viewportHeight = webapp.viewportHeight;

  // Detect minimization based on viewport height
  const wasMinimized = isAppMinimized;
  isAppMinimized = viewportHeight < 100;

  if (isAppMinimized !== wasMinimized) {
    console.log(
      `Telegram WebApp ${isAppMinimized ? 'minimized' : 'restored'} (viewport height: ${viewportHeight}px)`
    );
    notifyVisibilityChange(!isAppMinimized);
  }

  lastActiveTime = currentTime;
}

/**
 * Handles document visibility changes (works in both Telegram and browser)
 */
function handleDocumentVisibilityChange() {
  const wasVisible = isAppVisible;
  isAppVisible = document.visibilityState === 'visible';

  if (isAppVisible !== wasVisible) {
    console.log(`App ${isAppVisible ? 'visible' : 'hidden'} (document visibility change)`);
    notifyVisibilityChange(isAppVisible);
  }

  if (isAppVisible) {
    lastActiveTime = Date.now();
  }
}

/**
 * Handles theme changes from Telegram
 */
function handleThemeChange() {
  applyTelegramThemeToDOM();
  console.log('Telegram theme updated');
}

/**
 * Handles Telegram Mini App activation
 */
function handleAppActivated() {
  console.log('Telegram Mini App Activated');
  isAppVisible = true; // Assume it becomes visible
  isAppMinimized = false; // Assume it's not minimized
  lastActiveTime = Date.now();

  restoreMiniAppState();
  notifyVisibilityChange(true); // Notify general visibility system

  // Potentially trigger data refreshes needed on activation
  // This could call a function from your stores, e.g., useAuthStore.getState().refreshAuthIfStale();
  // Or trigger specific data fetches from dashboardStore, etc.
  // Example: useDashboardStore.getState().fetchStats(true); // silent refresh
}

/**
 * Handles Telegram Mini App deactivation
 */
async function handleAppDeactivated() {
  console.log('Telegram Mini App Deactivated');
  isAppVisible = false; // Assume it becomes hidden/inactive

  await persistMiniAppState();
  await secureTelegramStorage.setAppDeactivatedFlag(true);

  notifyVisibilityChange(false); // Notify general visibility system

  // Pause any critical operations, like game loops or frequent polling
}

/**
 * Persists minimal app state to session storage.
 */
async function persistMiniAppState() {
  try {
    const stateToSave = {
      // Example: current path, active tab, scroll positions
      lastHref: window.location.href, // Save the current URL
      // activeDashboardTab: useDashboardStore.getState().activeTab, // If you have such state
      // Add other relevant small pieces of UI state
    };
    await secureTelegramStorage.persistSessionState(stateToSave);
    console.log('Mini App state persisted securely:', stateToSave);
  } catch (error) {
    console.error('Error persisting Mini App state:', error);
  }
}

/**
 * Restores minimal app state from session storage.
 */
async function restoreMiniAppState() {
  try {
    const appWasDeactivated = await secureTelegramStorage.wasAppDeactivated();
    const storedState = await secureTelegramStorage.restoreSessionState();

    if (appWasDeactivated && storedState) {
      console.log('Restoring Mini App state - last known href:', storedState.lastHref);
      // The browser will likely have already loaded to storedState.lastHref if a full reload happened.
      // Additional restoration logic for other UI elements can go here.
      // Example: if (storedState.activeDashboardTab) { useDashboardStore.getState().setActiveTab(storedState.activeDashboardTab); }

      // Important: Clear the flag and state so it doesn't interfere with subsequent fresh loads
      secureTelegramStorage.clearSessionState();
    } else {
      console.log('No Mini App state to restore or app was not properly deactivated.');
    }
  } catch (error) {
    console.error('Error restoring Mini App state:', error);
    secureTelegramStorage.clearSessionState();
  }
}

/**
 * Starts a heartbeat timer to detect when app is backgrounded
 */
function startHeartbeat() {
  // Check every 2 seconds if the app has been active
  setInterval(() => {
    const currentTime = Date.now();
    const inactiveTime = currentTime - lastActiveTime;

    // Consider app backgrounded if inactive for more than 5 seconds
    if (inactiveTime > 5000 && isAppVisible && !isAppMinimized) {
      isAppMinimized = true;
      console.log('App likely backgrounded (detected by heartbeat)');
      notifyVisibilityChange(false);
    }

    // If document is visible again, update the active time
    if (document.visibilityState === 'visible') {
      lastActiveTime = currentTime;

      // If we marked it as minimized but visibility is now active, update state
      if (isAppMinimized) {
        isAppMinimized = false;
        console.log('App likely restored (detected by heartbeat)');
        notifyVisibilityChange(true);
      }
    }
  }, 2000);
}

/**
 * Notify all registered callbacks about visibility changes
 */
function notifyVisibilityChange(isVisible: boolean) {
  visibilityChangeCallbacks.forEach(callback => {
    try {
      callback(isVisible);
    } catch (e) {
      console.error('Error in visibility change callback:', e);
    }
  });
}

/**
 * Apply Telegram theme colors to DOM
 */
function applyTelegramThemeToDOM() {
  const webapp = getWebApp();
  if (!webapp) return;

  try {
    // Apply theme colors via CSS variables
    document.documentElement.style.setProperty('--tg-theme-bg-color', webapp.backgroundColor || '');
    document.documentElement.style.setProperty(
      '--tg-theme-text-color',
      webapp.themeParams.text_color || ''
    );
    document.documentElement.style.setProperty(
      '--tg-theme-hint-color',
      webapp.themeParams.hint_color || ''
    );
    document.documentElement.style.setProperty(
      '--tg-theme-link-color',
      webapp.themeParams.link_color || ''
    );
    document.documentElement.style.setProperty(
      '--tg-theme-button-color',
      webapp.themeParams.button_color || ''
    );
    document.documentElement.style.setProperty(
      '--tg-theme-button-text-color',
      webapp.themeParams.button_text_color || ''
    );
  } catch (e) {
    console.error('Error applying Telegram theme:', e);
  }
}

// --- Public Visibility API ---
/**
 * Register a callback for app visibility changes
 * @param callback Function to call when visibility changes, receives isVisible boolean
 * @returns Function to unregister the callback
 */
export function onAppVisibilityChange(callback: (isVisible: boolean) => void): () => void {
  visibilityChangeCallbacks.push(callback);

  // Return function to remove the callback
  return () => {
    visibilityChangeCallbacks = visibilityChangeCallbacks.filter(cb => cb !== callback);
  };
}

/**
 * Check if app is currently active/visible
 * @returns Boolean indicating if app is currently visible to user
 */
export function isAppActive(): boolean {
  return isAppVisible && !isAppMinimized;
}

/**
 * React hook to track app visibility
 * @returns [isVisible, isMinimized] - Current app visibility state
 */
export function useAppVisibility(): [boolean, boolean] {
  const [isVisible, setIsVisible] = useState(isAppVisible);
  const [isMinimized, setIsMinimized] = useState(isAppMinimized);

  useEffect(() => {
    // Update state immediately with current values
    setIsVisible(isAppVisible);
    setIsMinimized(isAppMinimized);

    // Register for visibility change notifications
    const unsubscribe = onAppVisibilityChange(isVisible => {
      setIsVisible(isVisible);
      setIsMinimized(!isVisible);
    });

    return unsubscribe;
  }, []);

  return [isVisible, isMinimized];
}

// --- Theme & UI Helpers ---
/**
 * Get Telegram theme colors
 */
export function getTelegramTheme() {
  const webapp = getWebApp();
  if (!webapp) {
    return {
      backgroundColor: '#1a1b1e',
      textColor: '#ffffff',
      buttonColor: '#2481cc',
      buttonTextColor: '#ffffff',
      hintColor: '#686870',
      linkColor: '#62a0ea',
    };
  }

  return {
    backgroundColor: webapp.themeParams.bg_color,
    textColor: webapp.themeParams.text_color,
    buttonColor: webapp.themeParams.button_color,
    buttonTextColor: webapp.themeParams.button_text_color,
    hintColor: webapp.themeParams.hint_color,
    linkColor: webapp.themeParams.link_color,
  };
}

/**
 * Hook for tracking Telegram theme changes
 */
export function useTelegramTheme() {
  const [theme, setTheme] = useState('light');

  useEffect(() => {
    const webapp = getWebApp();
    if (!webapp) return;

    const handleThemeChange = () => {
      setTheme(webapp.colorScheme || 'light');
    };

    webapp.onEvent(EVENTS.THEME_CHANGED, handleThemeChange);
    handleThemeChange(); // Set initial value

    return () => {
      webapp.offEvent(EVENTS.THEME_CHANGED, handleThemeChange);
    };
  }, []);

  return theme;
}

// --- Function to get current safe area insets ---
export interface SafeAreaInsets {
  top: number;
  right: number;
  bottom: number;
  left: number;
}

const defaultInsets: SafeAreaInsets = { top: 0, right: 0, bottom: 0, left: 0 };

export const getSafeAreaInsets = (): SafeAreaInsets => {
  const webapp = getWebApp();
  if (!webapp) return defaultInsets;

  try {
    // First try safeAreaInset (preferred for UI elements)
    if ('safeAreaInset' in webapp) {
      const rawInsets = (webapp.safeAreaInset as Partial<SafeAreaInsets>) || {};
      return {
        top: typeof rawInsets.top === 'number' ? rawInsets.top : 0,
        right: typeof rawInsets.right === 'number' ? rawInsets.right : 0,
        bottom: typeof rawInsets.bottom === 'number' ? rawInsets.bottom : 0,
        left: typeof rawInsets.left === 'number' ? rawInsets.left : 0,
      };
    }
    return defaultInsets;
  } catch (error) {
    console.error('Error getting Telegram safe area insets:', error);
    return defaultInsets;
  }
};

export const getContentSafeAreaInsets = (): SafeAreaInsets => {
  const webapp = getWebApp();
  if (!webapp) return defaultInsets;

  try {
    // Content safe area is used for content positioning (may differ from UI safe area)
    if ('contentSafeAreaInset' in webapp) {
      const rawInsets = (webapp.contentSafeAreaInset as Partial<SafeAreaInsets>) || {};
      return {
        top: typeof rawInsets.top === 'number' ? rawInsets.top : 0,
        right: typeof rawInsets.right === 'number' ? rawInsets.right : 0,
        bottom: typeof rawInsets.bottom === 'number' ? rawInsets.bottom : 0,
        left: typeof rawInsets.left === 'number' ? rawInsets.left : 0,
      };
    }
    // Fall back to regular safe area if content-specific one isn't available
    return getSafeAreaInsets();
  } catch (error) {
    console.error('Error getting Telegram content safe area insets:', error);
    return defaultInsets;
  }
};

// Hook to get and react to safe area changes
export const useTelegramSafeArea = (): SafeAreaInsets => {
  const [insets, setInsets] = useState<SafeAreaInsets>(getSafeAreaInsets);

  useEffect(() => {
    const webapp = getWebApp();
    if (!webapp) return;

    const handleSafeAreaChange = () => {
      setInsets(getSafeAreaInsets());

      // Also update CSS variables for global use
      const safeArea = getSafeAreaInsets();
      document.documentElement.style.setProperty('--safe-area-top', `${safeArea.top}px`);
      document.documentElement.style.setProperty('--safe-area-right', `${safeArea.right}px`);
      document.documentElement.style.setProperty('--safe-area-bottom', `${safeArea.bottom}px`);
      document.documentElement.style.setProperty('--safe-area-left', `${safeArea.left}px`);

      const contentSafeArea = getContentSafeAreaInsets();
      document.documentElement.style.setProperty(
        '--content-safe-area-top',
        `${contentSafeArea.top}px`
      );
      document.documentElement.style.setProperty(
        '--content-safe-area-right',
        `${contentSafeArea.right}px`
      );
      document.documentElement.style.setProperty(
        '--content-safe-area-bottom',
        `${contentSafeArea.bottom}px`
      );
      document.documentElement.style.setProperty(
        '--content-safe-area-left',
        `${contentSafeArea.left}px`
      );
    };

    // Initial check and event subscription
    handleSafeAreaChange();
    try {
      // Always listen for viewportChanged as it's the most compatible event
      webapp.onEvent('viewportChanged', handleSafeAreaChange);

      // Try to listen for safe area events if they exist
      // Note: These might not be in the type definitions but could be available at runtime
      // We use a type assertion to appease TypeScript
      try {
        (webapp.onEvent as any)('safeAreaChanged', handleSafeAreaChange);
      } catch (e) {
        // Silently ignore if event is not supported
      }

      try {
        (webapp.onEvent as any)('contentSafeAreaChanged', handleSafeAreaChange);
      } catch (e) {
        // Silently ignore if event is not supported
      }

      return () => {
        webapp.offEvent('viewportChanged', handleSafeAreaChange);

        // Clean up the other event listeners
        try {
          (webapp.offEvent as any)('safeAreaChanged', handleSafeAreaChange);
        } catch (e) {
          // Silently ignore if event is not supported
        }

        try {
          (webapp.offEvent as any)('contentSafeAreaChanged', handleSafeAreaChange);
        } catch (e) {
          // Silently ignore if event is not supported
        }
      };
    } catch (error) {
      console.error('Error setting up Telegram safe area events:', error);
      return () => {};
    }
  }, []);

  return insets;
};

// --- Viewport Helpers ---
export const getViewportHeight = (): number => {
  const webapp = getWebApp();
  if (!webapp) return window.innerHeight;

  try {
    return webapp.viewportStableHeight || webapp.viewportHeight || window.innerHeight;
  } catch (error) {
    console.error('Error getting Telegram viewport height:', error);
    return window.innerHeight;
  }
};

export const useTelegramViewport = () => {
  useEffect(() => {
    const webapp = getWebApp();
    if (!webapp) return;

    const handleViewportChange = () => {
      try {
        if (!webapp.isExpanded) {
          webapp.expand();
        }
      } catch (error) {
        console.error('Error expanding Telegram viewport:', error);
      }
    };

    try {
      handleViewportChange();
      webapp.onEvent(EVENTS.VIEWPORT_CHANGED, handleViewportChange);

      return () => {
        webapp.offEvent(EVENTS.VIEWPORT_CHANGED, handleViewportChange);
      };
    } catch (error) {
      console.error('Error setting up Telegram viewport events:', error);
      return () => {};
    }
  }, []);
};

// --- User Info Helpers ---
export const getUserInfo = (): WebAppUser | null => {
  return getWebApp()?.initDataUnsafe?.user || null;
};

export const getUserId = (): number | null => {
  return getUserInfo()?.id || null;
};

export const isUserPremium = (): boolean => {
  return getUserInfo()?.is_premium || false;
};

export const isUserBot = (): boolean => {
  return getUserInfo()?.is_bot || false;
};

// --- Platform & Device Info ---
export const getPlatform = (): string => {
  return getWebApp()?.platform || 'unknown';
};

export function getDeviceInfo() {
  const webapp = getWebApp();
  if (!webapp) return null;

  // Create a unique device identifier
  const uniqueId = [webapp.platform, webapp.initDataUnsafe?.user?.id, webapp.colorScheme]
    .filter(Boolean)
    .join('_');

  return {
    platform: webapp.platform,
    deviceId: uniqueId,
  };
}

// --- UI Interactions ---
/**
 * Close the Telegram WebApp
 */
export const closeTelegramWebApp = () => {
  getWebApp()?.close();
};

export const expandTelegramWebApp = () => {
  const webapp = getWebApp();
  if (webapp && !webapp.isExpanded) {
    webapp.expand();
  }
};

/**
 * Shows alert using Telegram's native UI or browser fallback
 */
export const showAlert = (message: string, callback?: () => void) => {
  const webapp = getWebApp();
  if (webapp) {
    try {
      webapp.showAlert(message);
      if (callback) callback();
    } catch (e) {
      console.error('Error showing Telegram alert:', e);
      alert(message);
      if (callback) callback();
    }
  } else {
    alert(message);
    if (callback) callback();
  }
};

/**
 * Shows confirmation dialog using Telegram's native UI or browser fallback
 */
export const showConfirm = (message: string, callback: (confirmed: boolean) => void) => {
  const webapp = getWebApp();
  if (webapp) {
    try {
      webapp.showConfirm(message).then(callback);
    } catch (e) {
      console.error('Error showing Telegram confirm:', e);
      const result = confirm(message);
      callback(result);
    }
  } else {
    const result = confirm(message);
    callback(result);
  }
};

export const showPopup = (params: {
  title?: string;
  message: string;
  buttons?: Array<{
    id: string;
    type?: 'ok' | 'close' | 'cancel' | 'destructive';
    text: string;
  }>;
}) => {
  getWebApp()?.showPopup(params);
};

// --- Swipe Control ---
export const enableVerticalSwipes = () => {
  getWebApp()?.enableVerticalSwipes();
};

export const disableVerticalSwipes = () => {
  getWebApp()?.disableVerticalSwipes();
};

// --- Chat UI Optimizations ---
export const optimizeChatUI = () => {
  const webapp = getWebApp();
  if (!webapp) return;

  // Disable vertical swipes
  webapp.disableVerticalSwipes();

  // Ensure full expansion
  webapp.expand();

  // Apply viewport height adjustments
  if (webapp.ViewportManager && webapp.ViewportManager.isStable) {
    const viewportHeight = webapp.viewportStableHeight || webapp.viewportHeight;
    document.documentElement.style.setProperty('--tg-viewport-height', `${viewportHeight}px`);
  }
};

export const getChatUIConfig = () => {
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );

  return {
    fontSize: isMobile ? '12px' : '14px',
    messageSpacing: isMobile ? '4px' : '6px',
    compactUI: isMobile,
    useTelegramTheme: true,
    fullscreenChat: true,
  };
};

// --- Navigation Controls ---
export const configureBackButton = (
  callback: () => void = () => window.history.back()
): (() => void) | undefined => {
  const webapp = getWebApp();
  if (!webapp || !webapp.BackButton) return;

  // Make sure button is visible
  webapp.BackButton.show();

  // Set new handler (we don't need to clear existing ones since Telegram handles this)
  webapp.BackButton.onClick(callback);

  return () => {
    if (webapp.BackButton) {
      webapp.BackButton.offClick(callback);
      webapp.BackButton.hide();
    }
  };
};

// Enhanced back button with more control
export const setupBackButtonHandler = (options: {
  isVisible?: boolean;
  onBack?: () => void;
  skipHistoryManipulation?: boolean;
}) => {
  const webapp = getWebApp();
  if (!webapp || !webapp.BackButton) return () => {};

  const {
    isVisible = true,
    onBack = () => window.history.back(),
    skipHistoryManipulation = false,
  } = options;

  // If we want to handle back navigation entirely in our app
  if (skipHistoryManipulation) {
    // Prevent browser history navigation
    const preventNavigation = (e: PopStateEvent) => {
      e.preventDefault();
      onBack();
      return false;
    };

    window.addEventListener('popstate', preventNavigation);
  }

  // Configure visible state
  if (isVisible) {
    webapp.BackButton.show();
  } else {
    webapp.BackButton.hide();
  }

  // Set up handler - Telegram manages the handlers internally
  webapp.BackButton.onClick(onBack);

  // Return cleanup function
  return () => {
    if (skipHistoryManipulation) {
      window.removeEventListener('popstate', onBack);
    }

    if (webapp.BackButton) {
      webapp.BackButton.offClick(onBack);
      webapp.BackButton.hide();
    }
  };
};

// --- Chat Style & Navigation ---
export const getTelegramChatStyles = () => {
  const theme = getTelegramTheme();

  return {
    fontSizes: {
      tiny: '10px',
      xs: '11px',
      sm: '12px',
      md: '14px',
      lg: '16px',
    },
    spacing: {
      xxs: '2px',
      xs: '4px',
      sm: '6px',
      md: '8px',
      lg: '12px',
      xl: '16px',
    },
    colors: {
      ...theme,
      messageBg: theme.backgroundColor === '#ffffff' ? '#f0f2f5' : '#2b2c2f',
      messageBgOwn: theme.buttonColor || '#2481cc',
      messageText: theme.textColor,
      messageTextOwn: '#ffffff',
      chatInputBg: theme.backgroundColor === '#ffffff' ? '#f0f2f5' : '#2b2c2f',
    },
    borderRadius: {
      sm: '8px',
      md: '12px',
      lg: '16px',
      xl: '20px',
    },
  };
};

export const navigateChatHistory = (direction: 'forward' | 'back') => {
  if (direction === 'back') {
    window.history.back();
  } else {
    window.history.forward();
  }
};

export const handleChatTransition = (isEntering: boolean) => {
  const webapp = getWebApp();
  if (!webapp) return;

  if (isEntering) {
    optimizeChatUI();
    webapp.BackButton.show();
  } else {
    webapp.enableVerticalSwipes();
    webapp.BackButton.hide();
  }
};

// --- Sharing & Social ---
interface ShareOptions {
  title?: string;
  text: string;
  url?: string;
  preview?: boolean;
  formatAs?: 'HTML' | 'MarkdownV2';
  preferLargeMedia?: boolean;
  showAboveText?: boolean;
}

export const shareInTelegram = async (options: ShareOptions): Promise<boolean> => {
  const webapp = getWebApp();
  if (!webapp) {
    console.error('Telegram WebApp is not available');
    return false;
  }

  try {
    // Format the message text
    let messageText = options.text;
    if (options.url) {
      messageText = `${options.text}\n\n${options.url}`;
    }

    // Prepare the message with rich formatting
    const preparedMessage: PreparedInlineMessage = {
      message_text: messageText,
      parse_mode: options.formatAs,
      link_preview_options: {
        is_disabled: !options.preview,
        prefer_large_media: options.preferLargeMedia,
        show_above_text: options.showAboveText,
        url: options.url,
      },
    };

    // Add entities for URL if present
    if (options.url) {
      preparedMessage.entities = [
        {
          type: 'text_link',
          url: options.url,
          offset: options.text.length + 2,
          length: options.url.length,
        },
      ];
    }

    // Try sharing via shareMessage first
    const shared = await webapp.shareMessage(preparedMessage);

    if (!shared && webapp.ShareManager?.shareData) {
      // Fallback to legacy sharing if available
      return await webapp.ShareManager.shareData({
        text: messageText,
        url: options.url,
        title: options.title,
      });
    }

    return shared;
  } catch (error) {
    console.error('Error sharing in Telegram:', error);

    // Show user-friendly error
    showPopup({
      title: 'Sharing Failed',
      message: 'Unable to share content. Please try again.',
      buttons: [{ id: 'ok', type: 'ok', text: 'OK' }],
    });

    return false;
  }
};

export const shareWithSpecificChats = async (
  message: PreparedInlineMessage,
  chatTypes: Array<'users' | 'bots' | 'groups' | 'channels'>
): Promise<boolean> => {
  const webapp = getWebApp();
  if (!webapp) return false;

  try {
    webapp.switchInlineQuery(JSON.stringify(message), chatTypes);
    return true;
  } catch (error) {
    console.error('Error sharing with specific chats:', error);
    return false;
  }
};

export const formatTelegramText = (text: string, format: 'HTML' | 'MarkdownV2'): string => {
  if (format === 'HTML') {
    return text.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
  }

  // MarkdownV2 escaping
  return text.replace(/[_*[\]()~`>#+\-=|{}.!]/g, '\\$&');
};

// --- Referral System ---
export function getReferralCode(): string | null {
  const webapp = getWebApp();
  if (!webapp) return null;

  try {
    const startParam = (webapp.initDataUnsafe as any)?.start_param;

    if (startParam && typeof startParam === 'string' && startParam.startsWith('ref_')) {
      return startParam.substring(4);
    }

    return null;
  } catch (error) {
    console.error('Error accessing referral code from initDataUnsafe:', error);
    return null;
  }
}

export function generateReferralLink(code: string): string {
  const botUsername = import.meta.env.VITE_TELEGRAM_BOT_USERNAME || 'your_bot_username';
  return `https://t.me/${botUsername}?startapp=ref_${code}`;
}

// --- Feature Detection ---
export const isTelegramWebApp = !!window.Telegram?.WebApp;

export const isVersionAtLeast = (version: string): boolean => {
  const webapp = getWebApp();
  if (!webapp) return false;

  try {
    return webapp.isVersionAtLeast(version);
  } catch (e) {
    console.error('Error checking Telegram version:', e);
    return false;
  }
};
