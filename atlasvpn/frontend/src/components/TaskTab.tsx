import React, { useState, useMemo, useRef, useEffect, useCallback, useId } from 'react';
import { Task, TaskType, TaskStatus, RewardType, DailyTaskStreak } from '../types/task';
import { motion, AnimatePresence } from 'framer-motion';
import {
  <PERSON>a<PERSON><PERSON><PERSON>,
  <PERSON>aCheck,
  FaTelegram,
  FaYoutube,
  FaInstagram,
  FaTwitter,
  FaGlobe,
  FaUserFriends,
  FaClock,
  FaGift,
  FaCalendarCheck,
  FaCoins,
  FaFilter,
  FaEye,
} from 'react-icons/fa';
import { DailyStreakCard } from './DailyStreakCard';
import { toast } from 'react-hot-toast';
import { haptics } from '../utils/haptics';
import Section from './layout/Section';
import LoadingSpinner from './LoadingSpinner';
import {
  useClaimRewardMutation,
  useStartTaskMutation,
  useVerifyTaskMutation,
} from '../hooks/taskHooks';
import ReferralTaskCard from './ReferralTaskCard';

interface TaskTabProps {
  tasks: Task[];
  loading: boolean;
  onTaskSelect: (task: Task) => void;
}

interface TaskGroup {
  title: string;
  types: TaskType[];
  tasks: Task[];
  isDailyGroup?: boolean;
}

type FilterOption = 'all' | 'active' | 'pending' | 'completed' | 'claimed';

export const TaskTabComponent: React.FC<TaskTabProps> = ({ tasks, loading, onTaskSelect }) => {
  const [filter, setFilter] = useState<FilterOption>('all');
  const [showFilterMenu, setShowFilterMenu] = useState(false);

  const [currentStreak, setCurrentStreak] = useState<number>(0);
  const [claimingTaskId, setClaimingTaskId] = useState<string | null>(null);
  const [hasError, setHasError] = useState<boolean>(false);

  const filterMenuRef = useRef<HTMLDivElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const dailyTaskSelectedRef = useRef<boolean>(false);
  const filterGradientId = useId();

  const claimRewardMutation = useClaimRewardMutation();
  const startTaskMutation = useStartTaskMutation();
  const verifyTaskMutation = useVerifyTaskMutation();

  // Find the first active daily check-in task ID reliably (as number)
  const dailyTaskIdNum = useMemo(() => {
    const dailyTask = (tasks ?? []).find(
      task => task && task.type === TaskType.DAILY_CHECKIN && task.is_active
    );
    return dailyTask?.id; // Keep as number
  }, [tasks]);

  // Error boundary component
  class ErrorBoundary extends React.Component<
    { children: React.ReactNode },
    { hasError: boolean }
  > {
    constructor(props: { children: React.ReactNode }) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError() {
      return { hasError: true };
    }

    componentDidCatch(error: Error) {
      console.error('TaskTab error caught:', error);
    }

    render() {
      if (this.state.hasError) {
        return (
          <div className="flex flex-col items-center justify-center p-8 text-center text-white/70">
            <div className="mb-4 text-red-400 text-3xl">⚠️</div>
            <h3 className="mb-2 text-lg">Something went wrong</h3>
            <p className="mb-4 text-sm text-white/50">
              The task section could not be loaded correctly
            </p>
            <button
              onClick={() => {
                this.setState({ hasError: false });
                setHasError(false);
                window.location.reload();
              }}
              className="px-4 py-2 bg-purple-600 rounded-md text-white text-sm hover:bg-purple-700"
            >
              Refresh Page
            </button>
          </div>
        );
      }
      return this.props.children;
    }
  }

  useEffect(() => {
    // Create a new AbortController for this component
    abortControllerRef.current = new AbortController();
    dailyTaskSelectedRef.current = false;

    const handleClickOutside = (event: MouseEvent) => {
      if (filterMenuRef.current && !filterMenuRef.current.contains(event.target as Node)) {
        setShowFilterMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      // Abort any ongoing fetch requests when component unmounts
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      dailyTaskSelectedRef.current = false;
    };
  }, []);

  const handleStreakUpdate = useCallback((streak: any) => {
    if (streak && streak.currentStreak !== undefined) {
      setCurrentStreak(streak.currentStreak);
    }
  }, []);

  const groupIcons: Record<string, React.ReactNode> = useMemo(
    () => ({
      'Daily Tasks': <FaCalendarCheck className="text-purple-400" />,
      'Join Telegram': <FaTelegram className="text-blue-400" />,
      'View & Engage': <FaYoutube className="text-red-500" />,
      'Follow & Connect': <FaUserFriends className="text-pink-500" />,
      'Visit & Explore': <FaGlobe className="text-green-400" />,
      'Invite Friends': <FaUserFriends className="text-purple-400" />,
    }),
    []
  );

  const taskGroups = useMemo(() => {
    try {
      const validTasks = (tasks ?? []).filter((task): task is Task => !!task);

      let filteredTasks = validTasks;
      if (filter !== 'all') {
        if (filter === 'active') {
          filteredTasks = filteredTasks.filter(
            task => task.status === TaskStatus.ACTIVE && !task.is_claimed
          );
        } else if (filter === 'pending') {
          filteredTasks = filteredTasks.filter(
            task => task.status === TaskStatus.PENDING && !task.is_claimed
          );
        } else if (filter === 'completed') {
          filteredTasks = filteredTasks.filter(
            task => task.status === TaskStatus.COMPLETED && !task.is_claimed
          );
        } else if (filter === 'claimed') {
          filteredTasks = filteredTasks.filter(task => task.is_claimed);
        }
      } else {
        filteredTasks = filteredTasks.filter(task => !task.is_claimed);
      }

      const groups: TaskGroup[] = [
        {
          title: 'Daily Tasks',
          types: [TaskType.DAILY_CHECKIN],
          tasks: [],
          isDailyGroup: true,
        },
        {
          title: 'Join Telegram',
          types: [TaskType.TELEGRAM_CHANNEL],
          tasks: [],
        },
        {
          title: 'View & Engage',
          types: [TaskType.YOUTUBE_VIEW, TaskType.INSTAGRAM_VIEW],
          tasks: [],
        },
        {
          title: 'Follow & Connect',
          types: [TaskType.INSTAGRAM_FOLLOW, TaskType.TWITTER_FOLLOW],
          tasks: [],
        },
        {
          title: 'Visit & Explore',
          types: [TaskType.WEBSITE_VISIT],
          tasks: [],
        },
        {
          title: 'Invite Friends',
          types: [TaskType.REFERRAL],
          tasks: [],
        },
      ];

      const dailyTasks = validTasks.filter(task => {
        if (filter === 'all') return task.type === TaskType.DAILY_CHECKIN && !task.is_claimed;
        if (filter === 'active')
          return (
            task.type === TaskType.DAILY_CHECKIN &&
            task.status === TaskStatus.ACTIVE &&
            !task.is_claimed
          );
        if (filter === 'pending')
          return (
            task.type === TaskType.DAILY_CHECKIN &&
            task.status === TaskStatus.PENDING &&
            !task.is_claimed
          );
        if (filter === 'completed')
          return (
            task.type === TaskType.DAILY_CHECKIN &&
            task.status === TaskStatus.COMPLETED &&
            !task.is_claimed
          );
        if (filter === 'claimed') return task.type === TaskType.DAILY_CHECKIN && task.is_claimed;

        return task.type === TaskType.DAILY_CHECKIN;
      });

      groups[0].tasks = dailyTasks;

      filteredTasks.forEach(task => {
        if (task.type !== TaskType.DAILY_CHECKIN) {
          const group = groups.find(g => g.types.includes(task.type));
          if (group) {
            group.tasks.push(task);
          }
        }
      });

      groups.forEach(group => {
        group.tasks.sort((a, b) => {
          if (
            a.status === TaskStatus.COMPLETED &&
            !a.is_claimed &&
            b.status !== TaskStatus.COMPLETED
          )
            return -1;
          if (
            b.status === TaskStatus.COMPLETED &&
            !b.is_claimed &&
            a.status !== TaskStatus.COMPLETED
          )
            return 1;
          if (a.status === TaskStatus.PENDING && b.status !== TaskStatus.PENDING) return -1;
          if (b.status === TaskStatus.PENDING && a.status !== TaskStatus.PENDING) return 1;
          return 0;
        });
      });

      // Ensure the daily task group always exists, even if no tasks match the filter
      const dailyGroupIndex = groups.findIndex(g => g.isDailyGroup);
      if (dailyGroupIndex === -1) {
        groups.unshift({
          title: 'Daily Tasks',
          types: [TaskType.DAILY_CHECKIN],
          tasks: [], // Initially empty, tasks might be filtered out
          isDailyGroup: true,
        });
      } else {
        // If it exists, ensure it contains the filtered daily tasks
        const dailyTasksFiltered = filteredTasks.filter(
          task => task.type === TaskType.DAILY_CHECKIN
        );
        groups[dailyGroupIndex].tasks = dailyTasksFiltered;
      }

      return groups.filter(group => group.tasks.length > 0 || group.isDailyGroup);
    } catch (error) {
      console.error('Error processing task groups:', error);
      setHasError(true);
      return [];
    }
  }, [tasks, filter]);

  const taskCounts = useMemo(() => {
    try {
      return {
        all: (tasks ?? []).filter(t => t && !t.is_claimed).length,
        active: (tasks ?? []).filter(t => t && t.status === TaskStatus.ACTIVE && !t.is_claimed)
          .length,
        pending: (tasks ?? []).filter(t => t && t.status === TaskStatus.PENDING && !t.is_claimed)
          .length,
        completed: (tasks ?? []).filter(
          t => t && t.status === TaskStatus.COMPLETED && !t.is_claimed
        ).length,
        claimed: (tasks ?? []).filter(t => t && t.is_claimed).length,
      };
    } catch (error) {
      console.error('Error calculating task counts:', error);
      return { all: 0, active: 0, pending: 0, completed: 0, claimed: 0 };
    }
  }, [tasks]);

  const handleClaim = async (e: React.MouseEvent, taskIdStr: string) => {
    e.stopPropagation();
    if (claimingTaskId) return;
    setClaimingTaskId(taskIdStr); // Keep claimingTaskId as string for state mgmt
    haptics.impact('medium');

    try {
      // Convert taskIdStr back to number for the store call
      const taskIdNum = parseInt(taskIdStr, 10);
      if (isNaN(taskIdNum)) {
        console.error('Invalid task ID for claim:', taskIdStr);
        toast.error('Failed to claim reward: Invalid ID');
        setClaimingTaskId(null);
        return;
      }
      await claimRewardMutation.mutateAsync(taskIdNum); // Pass number to claimReward
      // toast.success('Reward claimed!'); // Toast moved to store
    } catch (error: any) {
      if (error.name === 'AbortError') {
        console.log('Claim request aborted');
      } else {
        console.error('Claim failed:', error);
        toast.error(error?.response?.data?.detail || 'Failed to claim reward.');
      }
    } finally {
      setClaimingTaskId(null);
    }
  };

  const handleTaskClick = (task: Task) => {
    // If the task is a referral task, don't attempt to "start" it.
    // Its progress is handled automatically by the backend.
    // Just call onTaskSelect to navigate/show details.
    if (task.type === TaskType.REFERRAL) {
      console.log(`Referral task clicked (ID: ${task.id}), calling onTaskSelect.`);
      onTaskSelect(task);
      haptics.impact('light');
      return; // Stop further processing for referral tasks
    }

    // Original logic for non-referral tasks (e.g., daily check-in)
    if (task.type === TaskType.DAILY_CHECKIN && dailyTaskSelectedRef.current) {
      console.log('Daily task already selected, skipping.');
      return; // Avoid re-selecting daily task immediately
    }

    if (task.type === TaskType.DAILY_CHECKIN) {
      dailyTaskSelectedRef.current = true;
    }

    // Potentially call startTask here ONLY if needed for other task types
    // based on their status, BUT onTaskSelect likely handles navigation
    // to a detail view where a start button might exist.
    // For now, let's assume onTaskSelect handles the primary action.
    console.log(
      `Non-referral task clicked (ID: ${task.id}, Type: ${task.type}), calling onTaskSelect.`
    );
    onTaskSelect(task);
    haptics.impact('light');

    // Example: If you needed to auto-start other tasks on click:
    // const { startTask } = useTaskStore.getState();
    // if (task.status === TaskStatus.ACTIVE) { // Or some other condition
    //   console.log(`Attempting to auto-start task ${task.id}`);
    //   startTask(task.id).catch(err => console.error(`Failed to auto-start task ${task.id}`, err));
    // }
  };

  const filterOptions = useMemo(
    () => [
      { id: 'all', label: 'All Tasks', count: taskCounts.all },
      { id: 'active', label: 'Available', count: taskCounts.active },
      { id: 'pending', label: 'In Progress', count: taskCounts.pending },
      { id: 'completed', label: 'Ready to Claim', count: taskCounts.completed },
      { id: 'claimed', label: 'Claimed Tasks', count: taskCounts.claimed },
    ],
    [tasks]
  );

  const filterLabel = useMemo(() => {
    return filterOptions.find(opt => opt.id === filter)?.label || 'All Tasks';
  }, [filter, filterOptions]);

  if (hasError) {
    // If ErrorBoundary didn't catch it, show a simpler error
    return <div>Error loading tasks. Please refresh.</div>;
  }

  return (
    <ErrorBoundary>
      <div>
        <div className="flex justify-end mb-4 relative" ref={filterMenuRef}>
          <button
            onClick={() => setShowFilterMenu(!showFilterMenu)}
            className="flex items-center gap-1.5 text-xs px-3 py-1.5 rounded-md bg-white/5 hover:bg-white/10 text-white/80 hover:text-white transition-colors border border-white/10"
          >
            <FaFilter className="text-purple-400" /> Filter: {filterLabel}
          </button>
          <AnimatePresence>
            {showFilterMenu && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="absolute top-full right-0 mt-1 w-48 bg-gradient-to-br from-black/80 via-base-900/70 to-base-900/90 backdrop-blur-md rounded-lg border border-purple-500/20 shadow-lg shadow-purple-500/20 z-20 overflow-hidden"
              >
                {/* Filter Glow */}
                <div className="absolute inset-0 flex items-center justify-center overflow-hidden pointer-events-none -z-10">
                  <svg
                    className="absolute h-48 w-48 opacity-15"
                    viewBox="0 0 100 100"
                    preserveAspectRatio="xMidYMid slice"
                  >
                    <defs>
                      <radialGradient id={filterGradientId} cx="50%" cy="50%" r="50%">
                        <stop offset="0%" stopColor="#a855f7" stopOpacity="0.6" />
                        <stop offset="100%" stopColor="#a855f7" stopOpacity="0" />
                      </radialGradient>
                    </defs>
                    <circle cx="50" cy="50" r="50" fill={`url(#${filterGradientId})`} />
                  </svg>
                </div>
                {/* Filter Options */}
                <div className="relative z-10">
                  {filterOptions.map(opt => (
                    <button
                      key={opt.id}
                      onClick={() => {
                        setFilter(opt.id as FilterOption);
                        setShowFilterMenu(false);
                      }}
                      className={`w-full text-left px-3 py-2 text-xs flex items-center justify-between ${filter === opt.id ? 'bg-purple-500/20 text-purple-200' : 'text-white/70 hover:bg-white/10 hover:text-white'} transition-colors`}
                    >
                      {opt.label}
                      <span
                        className={`px-1.5 py-0.5 rounded text-[9px] ${filter === opt.id ? 'bg-purple-600 text-white' : 'bg-white/10 text-white/60'}`}
                      >
                        {opt.count}
                      </span>
                    </button>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {loading && taskGroups.length === 0 ? (
          <div className="text-center py-12">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <div>
            {taskGroups.map(group =>
              group.tasks.length > 0 || group.isDailyGroup ? (
                <Section key={group.title} title={group.title} className="!mb-4">
                  {group.isDailyGroup ? (
                    dailyTaskIdNum !== undefined ? (
                      <DailyStreakCard
                        taskId={dailyTaskIdNum} // Pass number taskId
                        onStreakUpdate={handleStreakUpdate}
                      />
                    ) : (
                      <div className="text-center py-4 text-white/50 text-sm">
                        Daily check-in task not available.
                      </div>
                    )
                  ) : (
                    <div className="space-y-3">
                      {/* REFERRAL TASKS - Always at top, but only if there are any */}
                      {group.title === 'Referral Tasks' && group.tasks.length > 0 && (
                        <div className="space-y-3">
                          {group.tasks.map(task => (
                            <ReferralTaskCard
                              key={task.id}
                              task={task}
                              onClick={() => handleTaskClick(task)}
                              onClaim={
                                task.status === TaskStatus.COMPLETED && !task.is_claimed
                                  ? (e: React.MouseEvent) => handleClaim(e, task.id.toString())
                                  : undefined
                              }
                              isClaimingTask={claimingTaskId === task.id.toString()}
                            />
                          ))}
                        </div>
                      )}
                      {group.tasks.map(task =>
                        task.type === TaskType.REFERRAL ? (
                          <ReferralTaskCard
                            key={task.id}
                            task={task}
                            onClick={() => handleTaskClick(task)}
                            onClaim={
                              task.status === TaskStatus.COMPLETED && !task.is_claimed
                                ? (e: React.MouseEvent) => handleClaim(e, task.id.toString())
                                : undefined
                            }
                            isClaimingTask={claimingTaskId === task.id.toString()}
                          />
                        ) : (
                          <TaskCard
                            key={task.id}
                            task={task}
                            onClick={() => handleTaskClick(task)}
                            onClaim={
                              task.status === TaskStatus.COMPLETED && !task.is_claimed
                                ? (e: React.MouseEvent) => handleClaim(e, task.id.toString())
                                : undefined
                            }
                            isClaimingTask={claimingTaskId === task.id.toString()}
                          />
                        )
                      )}
                    </div>
                  )}
                </Section>
              ) : null
            )}
          </div>
        )}
      </div>
    </ErrorBoundary>
  );
};

interface FilterOptionProps {
  icon: React.ReactNode;
  label: string;
  count: number;
  active: boolean;
  onClick: () => void;
}

const FilterOption: React.FC<FilterOptionProps> = ({ icon, label, count, active, onClick }) => {
  return (
    <button
      onClick={onClick}
      className={`w-full px-2 py-1.5 rounded-md flex items-center justify-between mb-1 transition-colors ${
        active ? 'bg-purple-600/30 text-white' : 'text-white/70 hover:bg-white/5'
      }`}
    >
      <div className="flex items-center gap-2">
        <span className="text-xs opacity-80">{icon}</span>
        <span className="text-xs">{label}</span>
      </div>
      <span
        className={`text-xs px-1.5 py-0.5 rounded-full ${active ? 'bg-white/20' : 'bg-white/10'}`}
      >
        {count}
      </span>
    </button>
  );
};

interface TaskCardProps {
  task: Task;
  onClick: () => void;
  onClaim?: (e: React.MouseEvent) => void;
  isClaimingTask?: boolean;
}

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onClick,
  onClaim,
  isClaimingTask = false,
}) => {
  const cardId = useId(); // Unique ID for SVG gradient per card
  const { glowColor, shadowClass, borderClass } = getGlowStyle(task);

  if (task.type === TaskType.REFERRAL) {
    return (
      <ReferralTaskCard
        task={task}
        onClick={onClick}
        onClaim={onClaim}
        isClaimingTask={isClaimingTask}
      />
    );
  }

  const getTaskIcon = () => {
    switch (task.type) {
      case TaskType.TELEGRAM_CHANNEL:
        return <FaTelegram className="text-blue-400" />;
      case TaskType.YOUTUBE_VIEW:
        return <FaYoutube className="text-red-500" />;
      case TaskType.INSTAGRAM_FOLLOW:
      case TaskType.INSTAGRAM_VIEW:
        return <FaInstagram className="text-pink-500" />;
      case TaskType.TWITTER_FOLLOW:
        return <FaTwitter className="text-blue-400" />;
      case TaskType.WEBSITE_VISIT:
        return <FaGlobe className="text-green-400" />;
      case TaskType.DAILY_CHECKIN:
        return <FaCalendarCheck className="text-purple-400" />;
      default:
        return null;
    }
  };

  const isInCooldown =
    task.status === TaskStatus.PENDING &&
    task.verification_attempts !== undefined &&
    task.verification_attempts > 0 &&
    task.social_task?.verification_interval !== undefined;

  const isClaimable = task.status === TaskStatus.COMPLETED && !task.is_claimed;

  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
      className={`relative bg-gradient-to-br from-black/70 via-base-900/60 to-base-900/80 rounded-xl overflow-hidden backdrop-blur-sm shadow-lg ${shadowClass} ${borderClass} border cursor-pointer h-full flex flex-col min-h-[130px]`}
      onClick={onClick}
    >
      {/* Centered Glow */}
      <div className="absolute inset-0 flex items-center justify-center overflow-hidden pointer-events-none -z-10">
        <svg
          className="absolute h-48 w-48 opacity-15"
          viewBox="0 0 100 100"
          preserveAspectRatio="xMidYMid slice"
        >
          <defs>
            <radialGradient id={cardId} cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor={glowColor} stopOpacity="0.6" />
              <stop offset="100%" stopColor={glowColor} stopOpacity="0" />
            </radialGradient>
          </defs>
          <circle cx="50" cy="50" r="50" fill={`url(#${cardId})`} />
        </svg>
      </div>

      {/* Content Area */}
      <div className="relative flex flex-col h-full flex-1 p-3">
        {/* Status Badge - More integrated */}
        <div className="absolute top-2 right-2 z-10">
          {isClaimable ? (
            <div className="bg-green-500/80 text-white text-[10px] font-medium px-1.5 py-0.5 rounded">
              Ready
            </div>
          ) : task.status === TaskStatus.PENDING ? (
            <div className="bg-yellow-500/80 text-black text-[10px] font-medium px-1.5 py-0.5 rounded">
              In Progress
            </div>
          ) : task.is_claimed ? (
            <div className="bg-purple-500/80 text-white text-[10px] font-medium px-1.5 py-0.5 rounded">
              Claimed
            </div>
          ) : null}
        </div>

        {isInCooldown && (
          <div className="absolute bottom-2 right-2 m-1.5 z-10">
            <div className="text-xs bg-yellow-500/20 text-yellow-300 p-1 rounded-full shadow-md">
              <FaClock size={10} />
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="flex items-center gap-2 mb-1.5">
          {' '}
          {/* Adjusted spacing */}
          <div className="text-xl p-1 bg-white/5 rounded-md">{getTaskIcon()}</div>
          <h3 className="font-semibold text-sm text-white truncate">{task.name}</h3> {/* Bolder */}
        </div>

        <p className="text-xs text-white/60 mb-2 line-clamp-2 flex-grow">{task.description}</p>

        {/* Footer Info */}
        <div className="mt-auto flex items-center justify-between pt-1">
          {' '}
          {/* Added padding top */}
          <div className="flex items-center gap-1 text-white/80 text-xs font-medium">
            {' '}
            {/* Bolder reward text */}
            <FaGift className="text-purple-400" size={12} />
            <span>
              {task.reward_value} {task.reward_type.split('_')[0]}
            </span>
          </div>
          {task.avg_completion_time !== undefined && task.avg_completion_time !== null && (
            <div className="flex items-center gap-1 text-white/60 text-xs">
              <FaClock size={10} />
              <span>~{Math.floor(task.avg_completion_time / 60)}m</span>
            </div>
          )}
        </div>

        {isClaimable && onClaim && (
          <button
            onClick={onClaim}
            disabled={isClaimingTask}
            className={`mt-2 w-full px-3 py-1.5 rounded-md text-sm font-medium flex items-center justify-center gap-1
              ${
                isClaimingTask
                  ? 'bg-green-500/50 text-white/70 cursor-wait'
                  : 'bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700'
              }`}
          >
            {isClaimingTask ? (
              <>
                <FaSpinner className="animate-spin" size={12} />
                <span>Claiming...</span>
              </>
            ) : (
              <>
                <FaCoins size={12} />
                <span>Claim Reward</span>
              </>
            )}
          </button>
        )}
      </div>
    </motion.div>
  );
};

const getRewardIcon = (rewardType: RewardType) => {
  switch (rewardType) {
    case RewardType.WALLET_BONUS:
      return '💰';
    case RewardType.DATA_BONUS:
      return '📊';
    case RewardType.DAYS_BONUS:
      return '📆';
    default:
      return '🎁';
  }
};

const getTaskStatus = (task: Task): string => {
  if (task.is_claimed) return 'Claimed';
  if (task.status === TaskStatus.COMPLETED) return 'Completed';
  if (task.status === TaskStatus.PENDING) return 'In Progress';
  return 'Start Task';
};

const getStatusStyle = (task: Task): string => {
  if (task.is_claimed) return 'bg-purple-500/20 text-purple-300';
  if (task.status === TaskStatus.COMPLETED) return 'bg-green-500/20 text-green-300';
  if (task.status === TaskStatus.PENDING) return 'bg-yellow-500/20 text-yellow-300';
  return 'bg-white/10 text-white/80';
};

// Updated status text function
const getTaskStatusText = (task: Task): string => {
  if (task.is_claimed) return 'Claimed';
  if (task.status === TaskStatus.COMPLETED) return 'Ready to Claim';
  if (task.status === TaskStatus.PENDING) return 'In Progress';
  if (task.status === TaskStatus.ACTIVE) return 'Available';
  return 'View Task'; // Default/fallback
};

// Updated status styling function
const getStatusStyleClasses = (task: Task): string => {
  if (task.is_claimed) return 'bg-purple-500/30 text-purple-300 border border-purple-500/40';
  if (task.status === TaskStatus.COMPLETED)
    return 'bg-green-500/30 text-green-300 border border-green-500/40';
  if (task.status === TaskStatus.PENDING)
    return 'bg-yellow-500/30 text-yellow-300 border border-yellow-500/40';
  return 'bg-slate-500/20 text-slate-300 border border-slate-500/30'; // Default for Active/Other
};

// Helper function for TaskCard styling (now in module scope)
const getGlowStyle = (
  task: Task
): { glowColor: string; shadowClass: string; borderClass: string } => {
  if (task.is_claimed) {
    return {
      glowColor: '#a855f7',
      shadowClass: 'shadow-purple-500/20',
      borderClass: 'border-purple-500/30',
    }; // Purple for claimed
  }
  switch (task.status) {
    case TaskStatus.COMPLETED:
      return {
        glowColor: '#22c55e',
        shadowClass: 'shadow-green-500/20',
        borderClass: 'border-green-500/40',
      }; // Green for claimable
    case TaskStatus.PENDING:
      return {
        glowColor: '#eab308',
        shadowClass: 'shadow-yellow-500/20',
        borderClass: 'border-yellow-500/40',
      }; // Yellow for pending
    case TaskStatus.ACTIVE:
    default:
      // Use a more subtle glow for default active tasks
      return {
        glowColor: '#475569',
        shadowClass: 'shadow-slate-600/15',
        borderClass: 'border-slate-500/20',
      }; // Slate/Neutral for active/default
  }
};
