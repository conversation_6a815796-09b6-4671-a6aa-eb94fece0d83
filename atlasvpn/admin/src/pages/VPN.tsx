import { useState } from 'react'
import { useQ<PERSON>y, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Server, Plus, Edit, Search, Users, DollarSign, Activity, Wifi } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface VPNPackage {
  id: number
  name: string
  data_limit: number
  expire_days: number
  price: number
  description?: string
  is_active: boolean
  created_at: string
  subscription_count: number
}

interface MarzbanPanel {
  id: number
  name: string
  api_url: string
  admin_username: string
  is_active: boolean
  created_at: string
  status: 'online' | 'offline' | 'error'
  user_count: number
  last_check: string
}

interface VPNSubscription {
  id: number
  user_id: number
  username: string
  package_name: string
  marzban_username: string
  created_at: string
  expires_at: string
  data_limit: number
  data_used: number
  is_active: boolean
  subscription_url: string
}

interface VPNStats {
  total_packages: number
  active_packages: number
  total_panels: number
  online_panels: number
  total_subscriptions: number
  active_subscriptions: number
  total_revenue: number
  data_usage_gb: number
}

interface CreatePackageData {
  name: string
  data_limit: number
  expire_days: number
  price: number
  description?: string
}

interface CreatePanelData {
  name: string
  api_url: string
  admin_username: string
  admin_password: string
}

export function VPN() {
  const [searchTerm, setSearchTerm] = useState('')
  const [packageDialogOpen, setPackageDialogOpen] = useState(false)
  const [panelDialogOpen, setPanelDialogOpen] = useState(false)
  
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const { data: vpnStats, isLoading: isStatsLoading } = useQuery<VPNStats>({
    queryKey: ['vpn-stats'],
    queryFn: () => api.get('/admin/vpn/stats').then(res => res.data)
  })

  const { data: packages, isLoading: isPackagesLoading } = useQuery<VPNPackage[]>({
    queryKey: ['vpn-packages'],
    queryFn: () => api.get('/admin/vpn/packages').then(res => res.data)
  })

  const { data: panels, isLoading: isPanelsLoading } = useQuery<MarzbanPanel[]>({
    queryKey: ['marzban-panels'],
    queryFn: () => api.get('/admin/vpn/panels').then(res => res.data)
  })

  const { data: subscriptions } = useQuery<VPNSubscription[]>({
    queryKey: ['vpn-subscriptions'],
    queryFn: () => api.get('/admin/vpn/subscriptions').then(res => res.data)
  })

  const createPackageMutation = useMutation({
    mutationFn: (packageData: CreatePackageData) => api.post('/admin/vpn/packages', packageData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vpn-packages'] })
      queryClient.invalidateQueries({ queryKey: ['vpn-stats'] })
      setPackageDialogOpen(false)
      toast({
        title: "Success",
        description: "VPN package created successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to create package",
        variant: "destructive",
      })
    }
  })

  const createPanelMutation = useMutation({
    mutationFn: (panelData: CreatePanelData) => api.post('/admin/vpn/panels', panelData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['marzban-panels'] })
      queryClient.invalidateQueries({ queryKey: ['vpn-stats'] })
      setPanelDialogOpen(false)
      toast({
        title: "Success",
        description: "Marzban panel added successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to add panel",
        variant: "destructive",
      })
    }
  })

  const togglePackageMutation = useMutation({
    mutationFn: (packageId: number) => api.post(`/admin/vpn/packages/${packageId}/toggle`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['vpn-packages'] })
      toast({
        title: "Success",
        description: "Package status updated successfully",
      })
    }
  })

  const testPanelMutation = useMutation({
    mutationFn: (panelId: number) => api.post(`/admin/vpn/panels/${panelId}/test`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['marzban-panels'] })
      toast({
        title: "Success",
        description: "Panel connection tested successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Panel connection failed",
        variant: "destructive",
      })
    }
  })

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-800'
      case 'offline': return 'bg-red-100 text-red-800'
      case 'error': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const StatCard = ({ 
    title, 
    value, 
    description, 
    icon: Icon, 
    isLoading,
    variant = 'default'
  }: { 
    title: string
    value: string | number
    description: string
    icon: any
    isLoading: boolean
    variant?: 'default' | 'success' | 'warning'
  }) => (
    <Card className={variant === 'success' ? 'border-green-200' : variant === 'warning' ? 'border-yellow-200' : ''}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 ${variant === 'success' ? 'text-green-600' : variant === 'warning' ? 'text-yellow-600' : 'text-muted-foreground'}`} />
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-4 w-32" />
          </div>
        ) : (
          <>
            <div className={`text-2xl font-bold ${variant === 'success' ? 'text-green-700' : variant === 'warning' ? 'text-yellow-700' : ''}`}>
              {value}
            </div>
            <p className="text-xs text-muted-foreground">{description}</p>
          </>
        )}
      </CardContent>
    </Card>
  )

  const CreatePackageDialog = () => {
    const [formData, setFormData] = useState<CreatePackageData>({
      name: '',
      data_limit: 0,
      expire_days: 30,
      price: 0,
      description: ''
    })

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      createPackageMutation.mutate(formData)
    }

    return (
      <Dialog open={packageDialogOpen} onOpenChange={setPackageDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create VPN Package</DialogTitle>
            <DialogDescription>
              Add a new VPN package to the system
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="name">Package Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                required
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="data_limit">Data Limit (GB)</Label>
                <Input
                  id="data_limit"
                  type="number"
                  min="0"
                  value={formData.data_limit}
                  onChange={(e) => setFormData({...formData, data_limit: parseInt(e.target.value) || 0})}
                  required
                />
              </div>
              <div>
                <Label htmlFor="expire_days">Expire Days</Label>
                <Input
                  id="expire_days"
                  type="number"
                  min="1"
                  value={formData.expire_days}
                  onChange={(e) => setFormData({...formData, expire_days: parseInt(e.target.value) || 30})}
                  required
                />
              </div>
            </div>
            <div>
              <Label htmlFor="price">Price ($)</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={(e) => setFormData({...formData, price: parseFloat(e.target.value) || 0})}
                required
              />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
              />
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setPackageDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={createPackageMutation.isPending}>
                {createPackageMutation.isPending ? 'Creating...' : 'Create Package'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    )
  }

  const CreatePanelDialog = () => {
    const [formData, setFormData] = useState<CreatePanelData>({
      name: '',
      api_url: '',
      admin_username: '',
      admin_password: ''
    })

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      createPanelMutation.mutate(formData)
    }

    return (
      <Dialog open={panelDialogOpen} onOpenChange={setPanelDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Add Marzban Panel</DialogTitle>
            <DialogDescription>
              Connect a new Marzban panel to the system
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="panel_name">Panel Name</Label>
              <Input
                id="panel_name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                required
              />
            </div>
            <div>
              <Label htmlFor="api_url">API URL</Label>
              <Input
                id="api_url"
                type="url"
                value={formData.api_url}
                onChange={(e) => setFormData({...formData, api_url: e.target.value})}
                placeholder="https://panel.example.com"
                required
              />
            </div>
            <div>
              <Label htmlFor="admin_username">Admin Username</Label>
              <Input
                id="admin_username"
                value={formData.admin_username}
                onChange={(e) => setFormData({...formData, admin_username: e.target.value})}
                required
              />
            </div>
            <div>
              <Label htmlFor="admin_password">Admin Password</Label>
              <Input
                id="admin_password"
                type="password"
                value={formData.admin_password}
                onChange={(e) => setFormData({...formData, admin_password: e.target.value})}
                required
              />
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setPanelDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={createPanelMutation.isPending}>
                {createPanelMutation.isPending ? 'Adding...' : 'Add Panel'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    )
  }

  if (isStatsLoading || isPackagesLoading || isPanelsLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">VPN Management</h1>
          <p className="text-gray-600">Manage VPN packages, panels, and subscriptions</p>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">VPN Management</h1>
          <p className="text-gray-600">Manage VPN packages, panels, and subscriptions</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setPackageDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Package
          </Button>
          <Button variant="outline" onClick={() => setPanelDialogOpen(true)}>
            <Server className="h-4 w-4 mr-2" />
            Add Panel
          </Button>
        </div>
      </div>

      {/* VPN Statistics */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">VPN System Overview</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Packages"
            value={vpnStats?.total_packages || 0}
            description={`${vpnStats?.active_packages || 0} active`}
            icon={Server}
            isLoading={isStatsLoading}
          />
          <StatCard
            title="Marzban Panels"
            value={vpnStats?.total_panels || 0}
            description={`${vpnStats?.online_panels || 0} online`}
            icon={Wifi}
            isLoading={isStatsLoading}
            variant={vpnStats?.online_panels === vpnStats?.total_panels ? 'success' : 'warning'}
          />
          <StatCard
            title="Active Subscriptions"
            value={vpnStats?.active_subscriptions || 0}
            description={`${vpnStats?.total_subscriptions || 0} total`}
            icon={Users}
            isLoading={isStatsLoading}
          />
          <StatCard
            title="Total Revenue"
            value={`$${vpnStats?.total_revenue?.toFixed(2) || '0.00'}`}
            description={`${vpnStats?.data_usage_gb?.toFixed(1) || '0'} GB used`}
            icon={DollarSign}
            isLoading={isStatsLoading}
          />
        </div>
      </div>

      {/* Tabs for different views */}
      <Tabs defaultValue="packages" className="space-y-4">
        <TabsList>
          <TabsTrigger value="packages">VPN Packages</TabsTrigger>
          <TabsTrigger value="panels">Marzban Panels</TabsTrigger>
          <TabsTrigger value="subscriptions">Subscriptions</TabsTrigger>
        </TabsList>

        <TabsContent value="packages" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>VPN Packages ({packages?.length || 0})</CardTitle>
              <CardDescription>Manage VPN service packages and pricing</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Package</TableHead>
                    <TableHead>Data Limit</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Price</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Subscriptions</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {packages?.map((pkg) => (
                    <TableRow key={pkg.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{pkg.name}</div>
                          <div className="text-sm text-gray-600 max-w-xs truncate">
                            {pkg.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {pkg.data_limit === 0 ? 'Unlimited' : formatBytes(pkg.data_limit * 1024 * 1024 * 1024)}
                      </TableCell>
                      <TableCell>{pkg.expire_days} days</TableCell>
                      <TableCell>${pkg.price.toFixed(2)}</TableCell>
                      <TableCell>
                        <Badge variant={pkg.is_active ? 'default' : 'secondary'}>
                          {pkg.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{pkg.subscription_count}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => togglePackageMutation.mutate(pkg.id)}
                          >
                            {pkg.is_active ? 'Disable' : 'Enable'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedPackage(pkg)
                              setEditPackageDialogOpen(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="panels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Marzban Panels ({panels?.length || 0})</CardTitle>
              <CardDescription>Manage connected Marzban panel servers</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Panel</TableHead>
                    <TableHead>API URL</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Users</TableHead>
                    <TableHead>Last Check</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {panels?.map((panel) => (
                    <TableRow key={panel.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{panel.name}</div>
                          <div className="text-sm text-gray-600">{panel.admin_username}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                          {panel.api_url}
                        </code>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(panel.status)}>
                          {panel.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{panel.user_count}</Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(panel.last_check).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => testPanelMutation.mutate(panel.id)}
                            disabled={testPanelMutation.isPending}
                          >
                            <Activity className="h-4 w-4 mr-2" />
                            Test
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedPanel(panel)
                              setEditPanelDialogOpen(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subscriptions" className="space-y-4">
          <Card>
            <CardContent className="p-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search subscriptions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Active Subscriptions ({subscriptions?.length || 0})</CardTitle>
              <CardDescription>Monitor user VPN subscriptions and usage</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Package</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Data Usage</TableHead>
                    <TableHead>Expires</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {subscriptions?.filter(sub => 
                    sub.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    sub.package_name.toLowerCase().includes(searchTerm.toLowerCase())
                  ).map((subscription) => (
                    <TableRow key={subscription.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{subscription.username}</div>
                          <div className="text-sm text-gray-600">{subscription.marzban_username}</div>
                        </div>
                      </TableCell>
                      <TableCell>{subscription.package_name}</TableCell>
                      <TableCell>
                        <Badge variant={subscription.is_active ? 'default' : 'secondary'}>
                          {subscription.is_active ? 'Active' : 'Expired'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {formatBytes(subscription.data_used)} / {formatBytes(subscription.data_limit)}
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${Math.min(100, (subscription.data_used / subscription.data_limit) * 100)}%` }}
                            />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(subscription.expires_at).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              navigator.clipboard.writeText(subscription.subscription_url)
                              toast({
                                title: "Copied",
                                description: "Subscription URL copied to clipboard",
                              })
                            }}
                          >
                            Copy URL
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <CreatePackageDialog />
      <CreatePanelDialog />
    </div>
  )
} 