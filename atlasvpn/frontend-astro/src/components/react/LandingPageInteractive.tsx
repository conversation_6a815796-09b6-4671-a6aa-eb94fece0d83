import React, { useEffect, useState, useCallback, useMemo, memo } from 'react';
import LoadingSpinner from './LoadingSpinner';
import { useTelegramLoginMutation, useCurrentUserQuery } from '../../api/hooks';
import { getTelegramData, initializeTelegramWebApp, isTelegramWebApp, triggerHapticFeedback } from '../../utils/telegram';
import { useToast } from './ToastProvider';

const LandingPageInteractive = memo(() => {
  // Navigation function using window.location.href
  const navigate = (path: string) => {
    if (typeof window !== 'undefined') {
      window.location.href = path;
    }
  };
  const { showToast } = useToast();
  const telegramLoginMutation = useTelegramLoginMutation();
  const { data: currentUser, isLoading: isAuthLoading } = useCurrentUserQuery();
  const [isTelegramReady, setIsTelegramReady] = useState(false);
  const [isNonTelegramEnvironment, setIsNonTelegramEnvironment] = useState(false);
  const [initializationAttempts, setInitializationAttempts] = useState(0);
  const [showInitialLoading, setShowInitialLoading] = useState(true);
  const [showTelegramInitializing, setShowTelegramInitializing] = useState(false);
  const [isInTelegram, setIsInTelegram] = useState(false);

  useEffect(() => {
    const checkTelegram = () => {
      const inTelegram = !!window.Telegram?.WebApp?.initData;
      setIsInTelegram(inTelegram);
      if (!inTelegram) {
        showToast('Please open this app in Telegram', 'warning');
      }
    };
    checkTelegram();
  }, []);

  // Check if already authenticated - optimized with early return
  useEffect(() => {
    if (currentUser && !isAuthLoading) {
      console.log('[LandingPage] User already authenticated, redirecting');
      navigate('/dashboard');
    }
  }, [currentUser, isAuthLoading]);

  // Optimized Telegram WebApp initialization
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;
    
    const initializeTelegram = async () => {
      try {
        if (typeof window === 'undefined') return;
        
        console.log('[LandingPage] Starting Telegram initialization...');
        setShowTelegramInitializing(true);
        
        // Check if we're in a Telegram environment
        if (!isTelegramWebApp()) {
          console.log('[LandingPage] Not in Telegram environment');
          setIsNonTelegramEnvironment(true);
          setShowTelegramInitializing(false);
          setShowInitialLoading(false);
          return;
        }
        
        // Initialize Telegram WebApp
        const webApp = await initializeTelegramWebApp();
        
        if (webApp) {
          console.log('[LandingPage] Telegram WebApp initialized successfully');
          setIsTelegramReady(true);
          setIsNonTelegramEnvironment(false);
        } else {
          console.warn('[LandingPage] Failed to initialize Telegram WebApp');
          setIsNonTelegramEnvironment(true);
        }
        
      } catch (error) {
        console.error('[LandingPage] Telegram initialization error:', error);
        setIsNonTelegramEnvironment(true);
      } finally {
        setShowTelegramInitializing(false);
        setShowInitialLoading(false);
        setInitializationAttempts(prev => prev + 1);
      }
    };

    // Initial loading delay
    timeoutId = setTimeout(() => {
      setShowInitialLoading(false);
      initializeTelegram();
    }, 1500);

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, []);

  const handleTelegramLogin = useCallback(async () => {
    if (!isInTelegram) {
      showToast('Please open this app in Telegram to login', 'error');
      return;
    }
    try {
      triggerHapticFeedback('light');
      
      if (isNonTelegramEnvironment) {
        showToast('This app must be opened in Telegram to login', 'error');
        return;
      }

      const telegramData = getTelegramData();
      if (!telegramData?.initData) {
        showToast('Please open this app in Telegram to login', 'error');
        return;
      }

      console.log('[LandingPage] Starting Telegram login...');
      
      const result = await telegramLoginMutation.mutateAsync({
        init_data: telegramData.initData,
        auth_method: 'telegram'
      });

      if (result.isNewUser) {
        console.log('[LandingPage] New user, redirecting to setup');
        triggerHapticFeedback('success');
        navigate('/new-user-setup');
      } else if (result.user) {
        console.log('[LandingPage] Existing user, redirecting to dashboard');
        triggerHapticFeedback('success');
        navigate('/dashboard');
      }
    } catch (error: any) {
      console.error('[LandingPage] Login failed:', error);
      triggerHapticFeedback('error');
      
      // Show specific error messages
      const errorMessage = error?.message || 'Login failed. Please try again.';
      showToast(errorMessage, 'error');
    }
  }, [isNonTelegramEnvironment, telegramLoginMutation, showToast, navigate]);

  const handleCreateAccount = useCallback(() => {
    if (!isInTelegram) {
      showToast('Please open this app in Telegram to create an account', 'error');
      return;
    }
    if (isNonTelegramEnvironment) {
      showToast('This app must be opened in Telegram to create an account', 'error');
      return;
    }

    const telegramData = getTelegramData();
    if (!telegramData?.initData) {
      showToast('Please open this app in Telegram to create an account', 'error');
      return;
    }
    
    triggerHapticFeedback('light');
    navigate('/new-user-setup');
  }, [isNonTelegramEnvironment, showToast, navigate]);

  const handleOpenInTelegram = useCallback(() => {
    const telegramUrl = 'https://t.me/VIPVerseBot/app';
    window.open(telegramUrl, '_blank');
  }, []);

  // Loading state component
  const LoadingComponent = useMemo(() => (
    <div
      className="flex flex-col items-center justify-center text-center p-5 animate-fade-in"
    >
      <div className="text-[3.5rem] mb-6 animate-[floatIcon_3s_ease-in-out_infinite]">🚀</div>
      <h1 className="text-[2.25rem] font-bold text-gray-100 mb-3 tracking-tight">VIPVerse</h1>
      <p className="text-base text-zinc-400 mb-8">Welcome to the future</p>
      <LoadingSpinner size="lg" />
      <p className="text-[0.95rem] text-purple-500 font-medium animate-[pulseText_1.8s_infinite_alternate] mt-4">
        {showInitialLoading ? 'Verifying session...' : 'Initializing Telegram...'}
      </p>
    </div>
  ), [showInitialLoading]);

  if (showInitialLoading || showTelegramInitializing) {
    return LoadingComponent;
  }

  // Non-Telegram environment component
  if (isNonTelegramEnvironment) {
    return (
      <div
        className="max-w-md w-full bg-gray-800 rounded-2xl p-8 text-center animate-fade-in"
      >
        <div className="text-4xl mb-6">⚠️</div>
        
        <h1 className="text-2xl font-bold text-white mb-4">
          Telegram Required
        </h1>
        
        <p className="text-gray-300 mb-6">
          VIPVerse is a Telegram Mini App and must be opened within Telegram to function properly.
        </p>

        <div className="space-y-4">
          <button
            onClick={handleOpenInTelegram}
            className="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors hover:scale-105 active:scale-95"
          >
            Open in Telegram
          </button>

          {import.meta.env.DEV && (
            <button
              onClick={() => {
                setIsNonTelegramEnvironment(false);
                setIsTelegramReady(true);
              }}
              className="w-full py-2 px-4 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded-lg transition-colors hover:scale-105 active:scale-95"
            >
              Continue Anyway (Dev Mode)
            </button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="relative max-w-lg w-full">
      {/* Cyberpunk Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 via-purple-500/10 to-pink-500/10 rounded-3xl blur-xl animate-pulse-slow"></div>
      <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/5 via-transparent to-red-500/5 rounded-3xl"></div>
      
      {/* Main Container */}
      <div className="relative bg-black/90 backdrop-blur-xl rounded-3xl p-8 text-center shadow-2xl border border-cyan-500/20 animate-fade-in overflow-hidden">
        {/* Animated Grid Background */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '20px 20px'
          }}></div>
        </div>
        
        {/* Glowing Orb */}
        <div className="relative mb-8">
          <div className="w-24 h-24 mx-auto relative">
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-400 via-purple-500 to-pink-500 rounded-full animate-spin" style={{ animationDuration: '8s' }}></div>
            <div className="absolute inset-1 bg-black rounded-full flex items-center justify-center">
              <div className="text-3xl animate-pulse">🛡️</div>
            </div>
          </div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-cyan-500/20 rounded-full blur-xl animate-pulse"></div>
        </div>
        
        {/* Title with Gaming Font */}
        <h1 className="text-4xl font-bold text-transparent bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text mb-4" style={{ fontFamily: 'Exo 2, Rajdhani, sans-serif' }}>
          VIPVerse
        </h1>
        
        {/* Subtitle */}
        <div className="mb-2">
          <span className="text-cyan-400 font-semibold text-lg" style={{ fontFamily: 'Rajdhani, sans-serif' }}>NEURAL PRIVACY PROTOCOL</span>
        </div>
        
        <p className="text-gray-300/80 mb-8 text-sm leading-relaxed">
          <span className="text-purple-400">●</span> Military-Grade Encryption <span className="text-cyan-400">●</span> Zero-Log Architecture <span className="text-pink-400">●</span> Quantum-Safe Security
        </p>

        {/* Action Buttons */}
        <div className="space-y-4 relative">
          {/* Primary Login Button */}
          <button
            onClick={handleTelegramLogin}
            disabled={telegramLoginMutation.isPending || !isTelegramReady || isAuthLoading}
            className="group relative w-full py-4 px-6 bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500 hover:from-cyan-400 hover:via-purple-400 hover:to-pink-400 text-black font-bold rounded-2xl shadow-2xl transition-all duration-300 ease-in-out disabled:opacity-60 disabled:cursor-not-allowed focus:outline-none hover:scale-105 active:scale-95 overflow-hidden"
            style={{ fontFamily: 'Exo 2, sans-serif' }}
          >
            {/* Button Glow Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/50 via-purple-500/50 to-pink-500/50 blur-xl group-hover:blur-2xl transition-all duration-300"></div>
            
            {/* Button Content */}
            <div className="relative flex items-center justify-center space-x-3">
              {telegramLoginMutation.isPending ? (
                <>
                  <LoadingSpinner size="sm" />
                  <span className="text-lg">NEURAL SYNC...</span>
                </>
              ) : !isTelegramReady ? (
                <>
                  <div className="w-5 h-5 border-2 border-black border-t-transparent rounded-full animate-spin"></div>
                  <span className="text-lg">INITIALIZING...</span>
                </>
              ) : (
                <>
                  <span className="text-2xl">⚡</span>
                  <span className="text-lg">ENTER VIPVERSE</span>
                </>
              )}
            </div>
          </button>

          {/* Secondary Create Account Button */}
          <button
            onClick={handleCreateAccount}
            disabled={!isTelegramReady || isAuthLoading}
            className="group relative w-full py-3 px-6 bg-black/50 hover:bg-black/70 border-2 border-cyan-500/50 hover:border-cyan-400 text-cyan-400 hover:text-cyan-300 font-semibold rounded-2xl shadow-lg transition-all duration-300 ease-in-out disabled:opacity-60 disabled:cursor-not-allowed focus:outline-none hover:scale-105 active:scale-95 backdrop-blur-sm"
            style={{ fontFamily: 'Rajdhani, sans-serif' }}
          >
            <div className="flex items-center justify-center space-x-2">
              <span className="text-lg">🔐</span>
              <span className="text-lg font-bold">CREATE NEURAL ID</span>
            </div>
            
            {/* Hover Glow */}
            <div className="absolute inset-0 bg-cyan-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </button>
        </div>

        <div className="mt-8 pt-6 border-t border-purple-700/20">
          <p className="text-xs text-gray-400/70">
            Secure • Anonymous • Premium
          </p>
        </div>

        {/* Debug info in development */}
        {import.meta.env.DEV && (
          <div className="mt-6 p-3 bg-black/30 rounded-lg text-left text-xs text-gray-500 overflow-x-auto">
            <p><strong>Dev Info:</strong></p>
            <p>TG WebApp: {String(!!window.Telegram?.WebApp)}</p>
            <p>TG Ready: {String(isTelegramReady)}</p>
            <p>TG InitData: {(window.Telegram?.WebApp?.initData?.length || 0) > 0 ? 'Available' : 'Missing'}</p>
            <p>Non-TG Env: {String(isNonTelegramEnvironment)}</p>
            <p>Auth Loading: {String(isAuthLoading)}</p>
            <p>Attempts: {initializationAttempts}</p>
          </div>
        )}
      </div>
    </div>
  );
});

LandingPageInteractive.displayName = 'LandingPageInteractive';

export default LandingPageInteractive;
