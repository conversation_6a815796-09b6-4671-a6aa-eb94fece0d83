from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, or_, desc, asc, and_
from sqlalchemy.orm import selectinload
from sqlalchemy.exc import SQLAlchemyError
from typing import Dict, Optional, List, Any
from datetime import datetime, timedelta, timezone
import logging
import secrets
import string
import csv
import io

from database import get_async_db
from models import (
    User, Role, VPNSubscription, Transaction, TransactionType, 
    UserCard, TaskCompletion, Task, DailyTaskStreak, ChatMessage,
    TaskStatus, CardCatalog, SecurityLog, OnlineUser
)
from schemas import (
    UserCreate, UserOut, AdminUserUpdate, UserVPNUpdate
)
from auth import get_current_admin, get_password_hash

router = APIRouter(
    prefix="/users",
    tags=["admin-users"]
)

logger = logging.getLogger(__name__)

@router.get("/", response_model=List[UserOut])
async def get_users(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    sort: str = "newest"
):
    """Get all users with proper sorting"""
    stmt = select(User).options(
        selectinload(User.user_cards).selectinload(UserCard.card_catalog),
        selectinload(User.vpn_subscriptions).options(
            selectinload(VPNSubscription.package)
        ),
        selectinload(User.task_completions).selectinload(TaskCompletion.task),
        selectinload(User.daily_streak)
    )
    if sort == "newest":
        stmt = stmt.order_by(User.created_at.desc())
    elif sort == "oldest":
        stmt = stmt.order_by(User.created_at.asc())
    elif sort == "balance-high":
        stmt = stmt.order_by(User.wallet_balance.desc())
    elif sort == "balance-low":
        stmt = stmt.order_by(User.wallet_balance.asc())
    
    result = await db.execute(stmt)
    users = result.scalars().all()
    
    # Ensure dates are properly formatted in ISO format
    for user in users:
        if user.created_at:
            user.created_at = user.created_at.isoformat()
    
    return users

@router.post("/", response_model=UserOut)
async def create_user_admin(
    user: UserCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    logger.info(f"Creating user with data: {user.dict()}")
    try:
        # Check if username or email already exists
        stmt = select(User).filter(
            or_(User.username == user.username, User.email == user.email)
        )
        result = await db.execute(stmt)
        existing_user = result.scalar_one_or_none()
        
        if existing_user:
            raise HTTPException(
                status_code=400,
                detail="Username or email already registered"
            )

        # Generate unique referral code
        while True:
            referral_code = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(8))
            ref_stmt = select(User).filter(User.referral_code == referral_code)
            ref_result = await db.execute(ref_stmt)
            existing = ref_result.scalar_one_or_none()
            if not existing:
                break

        # Create new user with UTC timestamp
        user_data = user.dict(exclude={'password'})
        new_user = User(
            **user_data,
            created_at=datetime.now(timezone.utc),
            hashed_password=get_password_hash(user.password),
            referral_code=referral_code
        )
        db.add(new_user)
        await db.commit()
        await db.refresh(new_user)
        
        # Format the created_at time in ISO format with timezone
        new_user.created_at = new_user.created_at.isoformat()
        return new_user

    except SQLAlchemyError as e:
        await db.rollback()
        logger.error(f"Database error while creating user: {str(e)}")
        raise HTTPException(
            status_code=400,
            detail=f"Database error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error creating user: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create user: {str(e)}"
        )

@router.get("/{user_id}", response_model=UserOut)
async def get_user_details(
    user_id: int,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Get detailed information about a specific user"""
    stmt = select(User).where(User.id == user_id).options(
        selectinload(User.user_cards).selectinload(UserCard.card_catalog),
        selectinload(User.vpn_subscriptions).options(
            selectinload(VPNSubscription.package)
        ),
        selectinload(User.task_completions).selectinload(TaskCompletion.task),
        selectinload(User.daily_streak)
    )
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    return user

@router.put("/{user_id}", response_model=UserOut)
async def update_user(
    user_id: int,
    user_update: AdminUserUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update user details"""
    try:
        logger.info(f"Received update request for user {user_id}")
        logger.info(f"Update data received: {user_update.dict()}")
        
        # Get user with relationships
        stmt = select(User).where(User.id == user_id).options(
            selectinload(User.user_cards).selectinload(UserCard.card_catalog),
            selectinload(User.vpn_subscriptions).options( 
                selectinload(VPNSubscription.package)
            ),
            selectinload(User.task_completions).selectinload(TaskCompletion.task),
            selectinload(User.daily_streak)
        )
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Update data
        update_data = user_update.dict(exclude_unset=True) 
        logger.info(f"Processed update data (excluding unset): {update_data}")

        # Handle Telegram ID update
        if 'telegram_id' in update_data:
            telegram_id = update_data['telegram_id']
            logger.info(f"Processing telegram_id update: {telegram_id}")

            if telegram_id is not None:
                # Check if ID is already in use by another user
                existing_stmt = select(User).where(
                    User.telegram_id == telegram_id,
                    User.id != user_id
                )
                existing_result = await db.execute(existing_stmt)
                existing = existing_result.scalar_one_or_none()
                
                if existing:
                    raise HTTPException(
                        status_code=400,
                        detail="Telegram ID is already in use"
                    )

            user.telegram_id = telegram_id
            logger.info(f"Updated telegram_id to {user.telegram_id}")

        # Update all other fields
        for key, value in update_data.items():
            if key != 'telegram_id' and hasattr(user, key):
                setattr(user, key, value)
                logger.info(f"Updated {key} to {value}")

        try:
            db.add(user)
            await db.commit()
            await db.refresh(user)
            logger.info(f"Successfully updated user {user_id}")
            return user

        except Exception as commit_error:
            await db.rollback()
            logger.error(f"Error during commit: {str(commit_error)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to update user: {str(commit_error)}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating user: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Unexpected error: {str(e)}"
        )

@router.put("/{user_id}/discount", response_model=UserOut)
async def update_user_discount(
    user_id: int,
    discount: UserVPNUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update user discount percentage"""
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Only update if marzban_username is provided (using UserVPNUpdate schema)
    if hasattr(discount, 'marzban_username') and discount.marzban_username:
        user.marzban_username = discount.marzban_username
    
    await db.commit()
    await db.refresh(user)
    return user

@router.put("/{user_id}/wallet/charge", response_model=UserOut)
async def charge_user_wallet(
    user_id: int,
    charge_data: AdminUserUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Charge user wallet"""
    try:
        # Get user with relationships for full UserOut response
        stmt = select(User).where(User.id == user_id).options(
            selectinload(User.user_cards).selectinload(UserCard.card_catalog),
            selectinload(User.vpn_subscriptions).options(
                selectinload(VPNSubscription.package)
            ),
            selectinload(User.task_completions).selectinload(TaskCompletion.task),
            selectinload(User.daily_streak)
        )
        result = await db.execute(stmt)
        user = result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        charge_amount = charge_data.wallet_charge
        if charge_amount is None or charge_amount <= 0:
            raise HTTPException(
                status_code=400,
                detail="Charge amount must be positive"
            )

        # Update wallet balance
        old_balance = user.wallet_balance
        user.wallet_balance += charge_amount

        # Create transaction record
        transaction = Transaction(
            user_id=user.id,
            type=TransactionType.admin_adjustment,
            amount=charge_amount,
            description=f"Admin wallet charge by {current_user.username}",
            balance_after=user.wallet_balance,
            reseller_id=current_user.id
        )
        
        db.add(transaction)
        await db.commit()
        await db.refresh(user)
        
        logger.info(f"Admin {current_user.username} charged user {user.username} wallet: ${charge_amount} (${old_balance} -> ${user.wallet_balance})")
        
        return user

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error charging user wallet: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to charge wallet: {str(e)}"
        )

@router.get("/{user_id}/transactions")
async def get_user_transactions(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get user's transaction history"""
    # Verify user exists
    user_stmt = select(User).where(User.id == user_id)
    user_result = await db.execute(user_stmt)
    user = user_result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Get transactions
    stmt = select(Transaction).where(Transaction.user_id == user_id).order_by(Transaction.created_at.desc())
    result = await db.execute(stmt)
    transactions = result.scalars().all()
    
    return [
        {
            "id": t.id,
            "type": t.type.value,
            "amount": t.amount,
            "description": t.description,
            "balance_after": t.balance_after,
            "created_at": t.created_at.isoformat(),
            "package_name": t.package_name
        }
        for t in transactions
    ]

@router.put("/{user_id}/password")
async def update_user_password(
    user_id: int,
    data: dict,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update user password"""
    stmt = select(User).where(User.id == user_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    new_password = data.get("new_password")
    if not new_password:
        raise HTTPException(status_code=400, detail="New password is required")
    
    # Hash the new password
    user.hashed_password = get_password_hash(new_password)
    await db.commit()
    
    return {"message": "Password updated successfully"}

@router.get("/check/{field}/{value}")
async def check_user_field(
    field: str,
    value: str,
    db: AsyncSession = Depends(get_async_db)
) -> Dict[str, bool]:
    """Check if a user field value exists"""
    if field not in ["username", "email"]:
        raise HTTPException(status_code=400, detail="Invalid field")
    
    stmt = select(User).filter(getattr(User, field) == value)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    return {"exists": user is not None}

@router.get("/check/telegram/{telegram_id}")
async def check_telegram_id(
    telegram_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Check if Telegram ID exists"""
    stmt = select(User).filter(User.telegram_id == telegram_id)
    result = await db.execute(stmt)
    user = result.scalar_one_or_none()
    
    if user:
        return {
            "exists": True,
            "user": {
                "id": user.id,
                "username": user.username,
                "first_name": user.first_name,
                "last_name": user.last_name
            }
        }
    
    return {"exists": False}

@router.get("/online")
async def get_online_users(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get currently online users"""
    try:
        # Get online users from the last 5 minutes
        cutoff_time = datetime.utcnow() - timedelta(minutes=5)
        
        stmt = select(OnlineUser).options(
            selectinload(OnlineUser.user)
        ).filter(
            OnlineUser.last_active_at >= cutoff_time
        ).order_by(OnlineUser.last_active_at.desc())
        
        result = await db.execute(stmt)
        online_users = result.scalars().all()
        
        return [
            {
                "user_id": ou.user_id,
                "username": ou.user.username if ou.user else "Unknown",
                "last_active_at": ou.last_active_at.isoformat(),
                "connection_id": ou.connection_id
            }
            for ou in online_users
        ]
        
    except Exception as e:
        logger.error(f"Error getting online users: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{user_id}/activity")
async def get_user_activity(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 30
):
    """Get user activity timeline"""
    try:
        # Verify user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Get date range
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Get transactions
        transaction_stmt = select(Transaction).filter(
            Transaction.user_id == user_id,
            Transaction.created_at >= start_date
        ).order_by(Transaction.created_at.desc())
        
        transaction_result = await db.execute(transaction_stmt)
        transactions = transaction_result.scalars().all()
        
        # Get task completions
        task_stmt = select(TaskCompletion).options(
            selectinload(TaskCompletion.task)
        ).filter(
            TaskCompletion.user_id == user_id,
            TaskCompletion.created_at >= start_date
        ).order_by(TaskCompletion.started_at.desc())
        
        task_result = await db.execute(task_stmt)
        task_completions = task_result.scalars().all()
        
        # Get VPN subscriptions
        vpn_stmt = select(VPNSubscription).options(
            selectinload(VPNSubscription.package)
        ).filter(
            VPNSubscription.user_id == user_id,
            VPNSubscription.created_at >= start_date
        ).order_by(VPNSubscription.created_at.desc())
        
        vpn_result = await db.execute(vpn_stmt)
        vpn_subscriptions = vpn_result.scalars().all()
        
        # Combine activities
        activities = []
        
        # Add transactions
        for t in transactions:
            activities.append({
                "type": "transaction",
                "timestamp": t.created_at.isoformat(),
                "description": f"{t.type.value}: ${t.amount}",
                "details": {
                    "amount": t.amount,
                    "transaction_type": t.type.value,
                    "balance_after": t.balance_after
                }
            })
        
        # Add task completions
        for tc in task_completions:
            activities.append({
                "type": "task_completion",
                "timestamp": tc.started_at.isoformat(),
                "description": f"Task: {tc.task.name} - {tc.status.value}",
                "details": {
                    "task_name": tc.task.name,
                    "status": tc.status.value,
                    "reward_amount": tc.reward_amount
                }
            })
        
        # Add VPN subscriptions
        for vpn in vpn_subscriptions:
            activities.append({
                "type": "vpn_subscription",
                "timestamp": vpn.created_at.isoformat(),
                "description": f"VPN: {vpn.package.name}",
                "details": {
                    "package_name": vpn.package.name,
                    "expires_at": vpn.expires_at.isoformat(),
                    "is_active": vpn.is_active
                }
            })
        
        # Sort by timestamp (newest first)
        activities.sort(key=lambda x: x["timestamp"], reverse=True)
        
        return {
            "user_id": user_id,
            "username": user.username,
            "period_days": days,
            "total_activities": len(activities),
            "activities": activities[:50]  # Limit to 50 most recent
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user activity: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{user_id}/chat-history")
async def get_user_chat_history(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    limit: int = 100,
    is_public: Optional[bool] = None
):
    """Get user's chat message history"""
    try:
        # Verify user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Build query for messages sent by user
        stmt = select(ChatMessage).filter(ChatMessage.sender_id == user_id)
        
        if is_public is not None:
            stmt = stmt.filter(ChatMessage.is_public == is_public)
        
        stmt = stmt.order_by(ChatMessage.created_at.desc()).limit(limit)
        
        result = await db.execute(stmt)
        messages = result.scalars().all()
        
        # Format messages
        chat_history = []
        for msg in messages:
            chat_history.append({
                "id": msg.id,
                "content": msg.content,
                "created_at": msg.created_at.isoformat(),
                "is_public": msg.is_public,
                "receiver_id": msg.receiver_id,
                "conversation_id": msg.conversation_id
            })
        
        # Get message statistics
        total_messages_stmt = select(func.count(ChatMessage.id)).filter(
            ChatMessage.sender_id == user_id
        )
        total_result = await db.execute(total_messages_stmt)
        total_messages = total_result.scalar() or 0
        
        public_messages_stmt = select(func.count(ChatMessage.id)).filter(
            ChatMessage.sender_id == user_id,
            ChatMessage.is_public == True
        )
        public_result = await db.execute(public_messages_stmt)
        public_messages = public_result.scalar() or 0
        
        private_messages = total_messages - public_messages
        
        return {
            "user_id": user_id,
            "username": user.username,
            "total_messages": total_messages,
            "public_messages": public_messages,
            "private_messages": private_messages,
            "recent_messages": chat_history
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user chat history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{user_id}/task-history")
async def get_user_task_history(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    limit: int = 100,
    status: Optional[str] = None
):
    """Get user's task completion history"""
    try:
        # Verify user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Build query
        stmt = select(TaskCompletion).options(
            selectinload(TaskCompletion.task)
        ).filter(TaskCompletion.user_id == user_id)
        
        if status:
            try:
                task_status = TaskStatus(status)
                stmt = stmt.filter(TaskCompletion.status == task_status)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid status")
        
        stmt = stmt.order_by(TaskCompletion.started_at.desc()).limit(limit)
        
        result = await db.execute(stmt)
        task_completions = result.scalars().all()
        
        # Format task history
        task_history = []
        for tc in task_completions:
            task_history.append({
                "id": tc.id,
                "task_id": tc.task_id,
                "task_name": tc.task.name,
                "task_type": tc.task.type.value,
                "status": tc.status.value,
                "started_at": tc.started_at.isoformat(),
                "completed_at": tc.completed_at.isoformat() if tc.completed_at else None,
                "claimed_at": tc.claimed_at.isoformat() if tc.claimed_at else None,
                "reward_amount": tc.reward_amount,
                "is_claimed": tc.is_claimed,
                "current_progress": tc.current_progress,
                "verification_attempts": tc.verification_attempts
            })
        
        # Get task statistics
        stats_queries = {
            "total": select(func.count(TaskCompletion.id)).filter(TaskCompletion.user_id == user_id),
            "completed": select(func.count(TaskCompletion.id)).filter(
                TaskCompletion.user_id == user_id,
                TaskCompletion.status == TaskStatus.COMPLETED
            ),
            "claimed": select(func.count(TaskCompletion.id)).filter(
                TaskCompletion.user_id == user_id,
                TaskCompletion.is_claimed == True
            ),
            "total_rewards": select(func.sum(TaskCompletion.reward_amount)).filter(
                TaskCompletion.user_id == user_id,
                TaskCompletion.is_claimed == True
            )
        }
        
        stats = {}
        for key, query in stats_queries.items():
            result = await db.execute(query)
            stats[key] = result.scalar() or 0
        
        return {
            "user_id": user_id,
            "username": user.username,
            "statistics": stats,
            "task_history": task_history
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user task history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{user_id}/card-history")
async def get_user_card_history(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get user's card ownership and transaction history"""
    try:
        # Verify user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Get user cards
        cards_stmt = select(UserCard).options(
            selectinload(UserCard.card_catalog)
        ).filter(UserCard.user_id == user_id)
        
        cards_result = await db.execute(cards_stmt)
        user_cards = cards_result.scalars().all()
        
        # Get card-related transactions
        card_transactions_stmt = select(Transaction).filter(
            Transaction.user_id == user_id,
            Transaction.type.in_([
                TransactionType.card_purchase,
                TransactionType.card_upgrade,
                TransactionType.card_profit,
                TransactionType.card_profit_all
            ])
        ).order_by(Transaction.created_at.desc())
        
        transactions_result = await db.execute(card_transactions_stmt)
        card_transactions = transactions_result.scalars().all()
        
        # Format card data
        cards_data = []
        total_invested = 0
        total_claimed = 0
        
        for card in user_cards:
            # Calculate investment and earnings for this card
            card_investments = sum(
                t.amount for t in card_transactions 
                if t.type in [TransactionType.card_purchase, TransactionType.card_upgrade]
                and t.description and str(card.card_catalog.name) in t.description
            )
            
            card_earnings = sum(
                t.amount for t in card_transactions 
                if t.type in [TransactionType.card_profit, TransactionType.card_profit_all]
                and t.description and str(card.card_catalog.name) in t.description
            )
            
            total_invested += card_investments
            total_claimed += card_earnings
            
            cards_data.append({
                "id": card.id,
                "card_name": card.card_catalog.name,
                "rarity": card.card_catalog.rarity,
                "level": card.level,
                "current_hourly_profit": card.current_hourly_profit,
                "acquired_at": card.acquired_at.isoformat(),
                "last_claim_at": card.last_claim_at.isoformat(),
                "total_invested": card_investments,
                "total_claimed": card_earnings,
                "roi": ((card_earnings / card_investments) * 100) if card_investments > 0 else 0
            })
        
        # Format transactions
        transaction_history = []
        for t in card_transactions:
            transaction_history.append({
                "id": t.id,
                "type": t.type.value,
                "amount": t.amount,
                "description": t.description,
                "created_at": t.created_at.isoformat(),
                "balance_after": t.balance_after
            })
        
        return {
            "user_id": user_id,
            "username": user.username,
            "summary": {
                "total_cards": len(cards_data),
                "total_invested": total_invested,
                "total_claimed": total_claimed,
                "net_profit": total_claimed - total_invested,
                "overall_roi": ((total_claimed / total_invested) * 100) if total_invested > 0 else 0,
                "current_hourly_income": sum(card.current_hourly_profit for card in user_cards)
            },
            "cards": cards_data,
            "transactions": transaction_history[:50]  # Limit to 50 most recent
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user card history: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/analytics")
async def get_user_analytics(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 30
):
    """Get user analytics and trends"""
    try:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Registration trends
        registration_stmt = select(
            func.date(User.created_at).label('date'),
            func.count(User.id).label('registrations')
        ).filter(
            User.created_at >= start_date
        ).group_by(
            func.date(User.created_at)
        ).order_by('date')
        
        reg_result = await db.execute(registration_stmt)
        registration_data = reg_result.all()
        
        # User activity (transactions as proxy)
        activity_stmt = select(
            func.date(Transaction.created_at).label('date'),
            func.count(func.distinct(Transaction.user_id)).label('active_users')
        ).filter(
            Transaction.created_at >= start_date
        ).group_by(
            func.date(Transaction.created_at)
        ).order_by('date')
        
        activity_result = await db.execute(activity_stmt)
        activity_data = activity_result.all()
        
        # Role distribution
        role_stmt = select(
            User.role,
            func.count(User.id).label('count')
        ).group_by(User.role)
        
        role_result = await db.execute(role_stmt)
        role_data = role_result.all()
        
        # Top users by wallet balance
        top_users_stmt = select(User).order_by(
            User.wallet_balance.desc()
        ).limit(10)
        
        top_users_result = await db.execute(top_users_stmt)
        top_users = top_users_result.scalars().all()
        
        return {
            "period_days": days,
            "registration_trend": [
                {"date": row.date.isoformat(), "registrations": row.registrations}
                for row in registration_data
            ],
            "activity_trend": [
                {"date": row.date.isoformat(), "active_users": row.active_users}
                for row in activity_data
            ],
            "role_distribution": [
                {"role": row.role.value, "count": row.count}
                for row in role_data
            ],
            "top_users_by_balance": [
                {
                    "id": user.id,
                    "username": user.username,
                    "wallet_balance": user.wallet_balance,
                    "created_at": user.created_at.isoformat()
                }
                for user in top_users
            ]
        }
        
    except Exception as e:
        logger.error(f"Error getting user analytics: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/export")
async def export_users(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    format: str = "csv",
    filters: Optional[dict] = None
):
    """Export user data"""
    try:
        # Build query
        stmt = select(User)
        
        # Apply filters if provided
        if filters:
            if filters.get("role"):
                stmt = stmt.filter(User.role == filters["role"])
            if filters.get("is_active") is not None:
                stmt = stmt.filter(User.is_active == filters["is_active"])
            if filters.get("min_balance"):
                stmt = stmt.filter(User.wallet_balance >= filters["min_balance"])
        
        result = await db.execute(stmt)
        users = result.scalars().all()
        
        if format.lower() == "csv":
            # Create CSV
            output = io.StringIO()
            writer = csv.writer(output)
            
            # Headers
            writer.writerow([
                "ID", "Username", "Email", "First Name", "Last Name",
                "Role", "Wallet Balance", "Created At", "Is Active",
                "Telegram ID", "Referral Code"
            ])
            
            # Data
            for user in users:
                writer.writerow([
                    user.id,
                    user.username,
                    user.email or "",
                    user.first_name or "",
                    user.last_name or "",
                    user.role.value,
                    user.wallet_balance,
                    user.created_at.isoformat(),
                    user.is_active,
                    user.telegram_id or "",
                    user.referral_code
                ])
            
            csv_content = output.getvalue()
            output.close()
            
            return {
                "format": "csv",
                "content": csv_content,
                "filename": f"users_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            }
        
        else:
            raise HTTPException(status_code=400, detail="Unsupported export format")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting users: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 