export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

export const formatDate = (date: string | Date): string => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const formatUptime = (seconds: number): string => {
  const days = Math.floor(seconds / (24 * 60 * 60));
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((seconds % (60 * 60)) / 60);

  if (days > 0) {
    return `${days}d ${hours}h`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
};

/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param currency - Currency symbol (default: '$')
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number, currency: string = '$'): string => {
  if (currency === '$') {
    // Use Intl.NumberFormat for USD
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  }
  // Custom format for other currencies
  return `${currency}${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

/**
 * Format a number with thousand separators
 * @param num - Number to format
 * @returns Formatted number string
 */
export const formatNumber = (num: number): string => {
  return num.toLocaleString('en-US');
};

export const formatRemainingDays = (days: number): { text: string; color: string } => {
  if (days <= 0) {
    return { text: 'Expired', color: 'text-red-400' };
  }
  if (days <= 3) {
    return { text: `${days} days left`, color: 'text-yellow-400' };
  }
  return { text: `${days} days left`, color: 'text-green-400' };
};

export const formatTimeLeft = (ms: number): { text: string; color: string } => {
  if (ms <= 0) {
    return { text: 'Expired', color: 'text-red-400' };
  }

  const days = Math.floor(ms / (1000 * 60 * 60 * 24));
  const hours = Math.floor((ms % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));

  let text = '';
  if (days > 0) {
    text += `${days} day${days > 1 ? 's' : ''} `;
  }
  if (hours > 0 || days > 0) {
    text += `${hours} hour${hours > 1 ? 's' : ''} `;
  }
  text += `${minutes} minute${minutes > 1 ? 's' : ''} left`;

  let color = 'text-green-400';
  if (days === 0) {
    if (hours < 12) {
      color = 'text-red-400';
    } else if (hours < 24) {
      color = 'text-yellow-400';
    }
  }

  return { text, color };
};

export const formatReward = (type: string, value: number) => {
  switch (type) {
    case 'wallet_bonus':
      return `$${value.toFixed(2)}`;
    case 'data_bonus':
      return `${value} GB`;
    case 'days_bonus':
      return `${value} Days`;
    case 'vpn_package':
      return 'VPN Package';
    default:
      return `${value}`;
  }
};

/**
 * Capitalize the first letter of a string
 * @param str - String to capitalize
 * @returns Capitalized string
 */
export const capitalize = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

/**
 * Truncate text to a specified length
 * @param text - Text to truncate
 * @param length - Maximum length
 * @returns Truncated text with ellipsis if needed
 */
export const truncateText = (text: string, length: number): string => {
  if (text.length <= length) return text;
  return text.slice(0, length).trim() + '...';
};
