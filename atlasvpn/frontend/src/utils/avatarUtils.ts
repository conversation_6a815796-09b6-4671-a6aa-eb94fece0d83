import * as THREE from 'three';
import { GLTF } from 'three/examples/jsm/loaders/GLTFLoader';

// List of all available avatars with exact filenames
export const DEFAULT_AVATARS = [
  // Guardians and Protectors
  'Antiddos Gaurdian.webp',
  'Datacenter Gauridan.webp',
  'Dataforge Seahorse.webp',
  'Datacracker Beast.webp',
  'Cryptkeeper Cat.webp',
  'Cryptshade Beast.webp',
  'Cipherlord Cat.webp',
  'Cipherfire Fox.webp',
  'Mysticssl Raven.webp',
  'Netshade Angel.webp',
  'Lovely Angel.webp',

  // Specialists and Operators
  'Artificial Rabbit.webp',
  'Bugaddict Panda.webp',
  'Cipherweaver Wolf.webp',
  'Cryptweaver Rabbit.webp',
  'Databrigner Seahorse.webp',
  'Datashade Rabbit.webp',
  'Debugger Penguin.webp',
  'Devleader Penguin.webp',
  'Encrypted Spider.webp',
  'Proxyweaver Rabbit.webp',
  'Shadow Decoder.webp',
  'Uptime Penguin.webp',
];

// Get character name without extension
export const formatCharacterName = (filename: string): string => {
  if (!filename) return 'Unknown Hero';
  return filename.replace('.webp', '');
};

// Get avatar URL from filename
export const getAvatarUrl = (filename: string | null): string => {
  if (!filename) return '/avatars/Elite Guardian Wolf.webp'; // Default avatar
  return `/avatars/${filename}`;
};

// Get character role display name
export const getCharacterRole = (filename: string | null): string => {
  if (!filename) return 'Unknown Hero';
  const name = formatCharacterName(filename);
  // Determine class based on name
  return name.includes('Guardian') ? 'Guardian Class' : 'Specialist Class';
};

// Get avatar display size based on context
export const getAvatarDisplaySize = (
  type: 'profile' | 'selector' | 'header' | 'large'
): { width: number; height: number } => {
  switch (type) {
    case 'profile':
      return { width: 120, height: 120 };
    case 'selector':
      return { width: 280, height: 280 };
    case 'header':
      return { width: 48, height: 48 };
    case 'large':
      return { width: 400, height: 400 };
    default:
      return { width: 96, height: 96 };
  }
};

// Get responsive size
export const getResponsiveSize = (containerWidth: number): { width: number; height: number } => {
  if (containerWidth < 640) return { width: 260, height: 260 };
  if (containerWidth < 768) return { width: 300, height: 300 };
  if (containerWidth < 1024) return { width: 340, height: 340 };
  return { width: 380, height: 380 };
};
