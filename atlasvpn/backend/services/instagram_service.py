import aiohttp
from sqlalchemy.orm import Session
from models import User, TaskCompletion
from typing import Optional, Dict
import logging
import os
from datetime import datetime

logger = logging.getLogger(__name__)

class InstagramService:
    def __init__(self, db: Session):
        self.db = db
        self.api_token = os.getenv("INSTAGRAM_API_TOKEN")
        self.connected_account = os.getenv("INSTAGRAM_ACCOUNT")  # Our Instagram account username
        if not self.api_token:
            raise ValueError("INSTAGRAM_API_TOKEN not found in environment variables")
        if not self.connected_account:
            raise ValueError("INSTAGRAM_ACCOUNT not found in environment variables")
        
    async def verify_follow(self, username: str) -> Dict:
        """
        Verify if a user follows our connected Instagram account
        Uses Instagram Graph API to check our account's followers
        """
        try:
            logger.info(f"Verifying if {username} follows {self.connected_account}")
            
            async with aiohttp.ClientSession() as session:
                # Get our account's followers
                followers_url = f"https://graph.instagram.com/me/followers?access_token={self.api_token}"
                logger.info(f"Getting followers from: {followers_url}")
                
                async with session.get(followers_url) as resp:
                    if resp.status != 200:
                        error_data = await resp.text()
                        logger.error(f"Failed to get followers data: {error_data}")
                        return {
                            "success": False,
                            "error": "Failed to verify followers"
                        }
                        
                    followers_data = await resp.json()
                    logger.info(f"Followers data response: {followers_data}")
                    
                    # Check if user is in followers list
                    followers = followers_data.get("data", [])
                    is_following = any(
                        follower["username"].lower() == username.lower()
                        for follower in followers
                    )
                    
                    if is_following:
                        return {
                            "success": True,
                            "message": f"Successfully verified follow status for {username}"
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"User {username} does not follow {self.connected_account}"
                        }
                
        except Exception as e:
            logger.error(f"Instagram verification error: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to verify Instagram follow: {str(e)}"
            }

    async def verify_task_completion(self, completion: TaskCompletion) -> Dict:
        """Verify Instagram follow task completion"""
        try:
            task = completion.task
            if not task.social_task:
                return {
                    "success": False,
                    "error": "No social task configuration found"
                }
            
            # Get the user's Instagram username
            user = completion.user
            if not user.instagram_username:
                return {
                    "success": False,
                    "error": "User has no Instagram username linked"
                }
            
            # Verify the follow
            result = await self.verify_follow(user.instagram_username)
            
            if result["success"]:
                # Update completion status
                completion.status = TaskStatus.COMPLETED
                completion.completed_at = datetime.utcnow()
                self.db.commit()
            
            return result
            
        except Exception as e:
            logger.error(f"Error verifying Instagram task: {str(e)}")
            return {
                "success": False,
                "error": f"Failed to verify Instagram task: {str(e)}"
            } 