import sqlite3
import os

DB_PATH = "users.db"

# SQL statements to create tables and indexes
# Using TEXT for JSON columns as recommended for SQLite
# Using IF NOT EXISTS to make the script idempotent
SQL_CREATE_CARD_CATALOG = """
CREATE TABLE IF NOT EXISTS card_catalog (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHA<PERSON>(100) NOT NULL UNIQUE,
    rarity VARCHAR(20) NOT NULL DEFAULT 'common',
    level_profits_json TEXT NOT NULL, -- Store JSON string like '[1.0, 1.2, 1.5, ...]'
    level_costs_json TEXT NOT NULL,   -- Store JSON string like '[50, 75, 110, ...]'
    max_level INTEGER NOT NULL DEFAULT 10,
    image_url VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
); 
"""

SQL_CREATE_USER_CARDS = """
CREATE TABLE IF NOT EXISTS user_cards (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    card_catalog_id INTEGER NOT NULL,
    level INTEGER NOT NULL DEFAULT 1,
    last_claim_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    acquired_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
    FOREIGN KEY (card_catalog_id) REFERENCES card_catalog (id),
    UNIQUE (user_id, card_catalog_id)
);
"""

# Indexes (Using IF NOT EXISTS)
SQL_INDEX_USER_CARDS_USER = "CREATE INDEX IF NOT EXISTS idx_user_cards_user_id ON user_cards (user_id);"
SQL_INDEX_USER_CARDS_CARD = "CREATE INDEX IF NOT EXISTS idx_user_cards_card_catalog_id ON user_cards (card_catalog_id);"
SQL_INDEX_USER_CARDS_CLAIM = "CREATE INDEX IF NOT EXISTS idx_user_cards_last_claim_at ON user_cards (last_claim_at);"
SQL_INDEX_CARD_CATALOG_RARITY = "CREATE INDEX IF NOT EXISTS idx_card_catalog_rarity ON card_catalog (rarity);"


def create_schema():
    """Connects to the database and creates tables/indexes if they don't exist."""
    if not os.path.exists(DB_PATH):
        print(f"Error: Database file not found at {DB_PATH}")
        return

    conn = None
    try:
        conn = sqlite3.connect(DB_PATH)
        cursor = conn.cursor()
        print("Database connected.")

        # Enable Foreign Keys (important for relationships)
        cursor.execute("PRAGMA foreign_keys = ON;")
        print("Ensured foreign keys are enabled.")

        # Create tables
        print("Executing: CREATE TABLE IF NOT EXISTS card_catalog...")
        cursor.execute(SQL_CREATE_CARD_CATALOG)
        print("Executing: CREATE TABLE IF NOT EXISTS user_cards...")
        cursor.execute(SQL_CREATE_USER_CARDS)
        print("Tables created or already exist.")

        # Create indexes
        print("Executing: CREATE INDEX IF NOT EXISTS idx_user_cards_user_id...")
        cursor.execute(SQL_INDEX_USER_CARDS_USER)
        print("Executing: CREATE INDEX IF NOT EXISTS idx_user_cards_card_catalog_id...")
        cursor.execute(SQL_INDEX_USER_CARDS_CARD)
        print("Executing: CREATE INDEX IF NOT EXISTS idx_user_cards_last_claim_at...")
        cursor.execute(SQL_INDEX_USER_CARDS_CLAIM)
        print("Executing: CREATE INDEX IF NOT EXISTS idx_card_catalog_rarity...")
        cursor.execute(SQL_INDEX_CARD_CATALOG_RARITY)
        print("Indexes created or already exist.")

        conn.commit()
        print("Schema creation script completed successfully.")

    except sqlite3.Error as e:
        print(f"SQLite error during schema creation: {e}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")
    finally:
        if conn:
            conn.close()
            print("Database connection closed.")

if __name__ == "__main__":
    create_schema() 