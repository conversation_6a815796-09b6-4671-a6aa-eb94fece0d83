#!/usr/bin/env python
"""
Rate Limit and CAPTCHA Test Script
----------------------------------
This script tests the rate limiting and CAPTCHA functionality.
It simulates various scenarios to test the security of the application.
"""
import requests
import json
import logging
import colorama
from colorama import Fore, Style
import sys
import time
import asyncio
import random
import string
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, List, Tuple, Any, Optional, Callable

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("rate_limit_captcha_test.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Initialize colorama
colorama.init()

# Base URL
BASE_URL = "http://localhost:8000"

# Critical endpoints that should be protected by Cloudflare CAPTCHA
CRITICAL_ENDPOINTS = {
    "login": "/auth/token",
    "register": "/auth/register",
    "admin_login": "/auth/admin/login"
}

def print_header(message: str) -> None:
    """Print a formatted header message"""
    print(f"\n{Fore.CYAN}{'=' * 80}")
    print(f"{message.center(80)}")
    print(f"{'=' * 80}{Style.RESET_ALL}\n")

def print_success(message: str) -> None:
    """Print a success message"""
    print(f"{Fore.GREEN}✓ {message}{Style.RESET_ALL}")

def print_warning(message: str) -> None:
    """Print a warning message"""
    print(f"{Fore.YELLOW}⚠ {message}{Style.RESET_ALL}")

def print_error(message: str) -> None:
    """Print an error message"""
    print(f"{Fore.RED}✗ {message}{Style.RESET_ALL}")

def generate_random_string(length: int = 10) -> str:
    """Generate a random string of fixed length"""
    letters = string.ascii_lowercase + string.digits
    return ''.join(random.choice(letters) for _ in range(length))

def test_rate_limit(url: str, method: str = "GET", data: Optional[Dict] = None, 
                   attempts: int = 15) -> Tuple[bool, List[Dict]]:
    """Test rate limiting for a given URL"""
    logger.info(f"Testing rate limiting for: {url} with {attempts} attempts")
    
    responses = []
    
    for i in range(attempts):
        try:
            if method.upper() == "GET":
                response = requests.get(
                    url, 
                    allow_redirects=True,
                    timeout=10,
                    headers={"X-Test-ID": f"rate-limit-test-{i}"}
                )
            else:
                response = requests.post(
                    url,
                    json=data,
                    allow_redirects=True,
                    timeout=10,
                    headers={"X-Test-ID": f"rate-limit-test-{i}"}
                )
            
            # Extract rate limit headers
            rate_limit_headers = {
                k: v for k, v in response.headers.items() 
                if k.lower().startswith("x-ratelimit") or k.lower() == "retry-after"
            }
            
            responses.append({
                "attempt": i + 1,
                "status_code": response.status_code,
                "rate_limit_headers": rate_limit_headers,
                "blocked": response.status_code == 429,
                "body": response.text[:100] + "..." if len(response.text) > 100 else response.text
            })
            
            logger.info(f"Attempt {i+1}: Status {response.status_code}")
            
            # Small delay to avoid overloading
            time.sleep(0.2)
            
        except requests.RequestException as e:
            logger.error(f"Request error on attempt {i+1}: {e}")
            responses.append({
                "attempt": i + 1,
                "error": str(e)
            })
    
    # Check if rate limiting was triggered
    blocked_responses = [r for r in responses if r.get("blocked", False)]
    
    if not blocked_responses:
        print_error(f"Rate limiting was not triggered after {attempts} attempts")
        return False, responses
    
    # Check if rate limiting was triggered after a reasonable number of attempts
    first_block = next((i for i, r in enumerate(responses) if r.get("blocked", False)), None)
    
    if first_block is not None and first_block < 3:
        print_warning(f"Rate limiting triggered too early (after {first_block+1} attempts)")
    elif first_block is not None and first_block > 10:
        print_warning(f"Rate limiting triggered too late (after {first_block+1} attempts)")
    else:
        print_success(f"Rate limiting triggered appropriately (after {first_block+1} attempts)")
    
    return bool(blocked_responses), responses

def test_login_rate_limit(attempts: int = 15) -> Tuple[bool, List[Dict]]:
    """Test rate limiting for login endpoint"""
    login_data = {
        "username": "test_user",
        "password": "wrong_password"
    }
    
    return test_rate_limit(
        url=f"{BASE_URL}/auth/token",
        method="POST",
        data=login_data,
        attempts=attempts
    )

def test_cloudflare_captcha_integration() -> Dict[str, Any]:
    """Test Cloudflare CAPTCHA integration for critical endpoints"""
    results = {}
    
    for name, endpoint in CRITICAL_ENDPOINTS.items():
        logger.info(f"Testing CAPTCHA integration for {name} endpoint: {endpoint}")
        
        try:
            # First, just check if there's any reference to Cloudflare in the page
            response = requests.get(f"{BASE_URL}{endpoint}", timeout=10)
            
            # Check for typical Cloudflare CAPTCHA indicators in response
            contains_captcha = False
            cloudflare_indicators = [
                "cf-turnstile-response",
                "cf_clearance",
                "cf_challenge",
                "cloudflare-js",
                "turnstile.js",
                "hcaptcha",
                "g-recaptcha"
            ]
            
            for indicator in cloudflare_indicators:
                if indicator in response.text.lower():
                    contains_captcha = True
                    break
            
            # Check if we need to handle the CAPTCHA
            if contains_captcha:
                logger.info(f"CAPTCHA detected for {name} endpoint")
                results[name] = {
                    "captcha_detected": True,
                    "status_code": response.status_code
                }
            else:
                # Fallback: Try to detect if the endpoint rejects requests without CAPTCHA
                logger.info(f"No CAPTCHA indicators found for {name} endpoint, checking rejection behavior")
                
                # For login endpoint, attempt a login
                if name == "login":
                    test_data = {"username": "test_user", "password": "wrong_password"}
                    post_response = requests.post(f"{BASE_URL}{endpoint}", json=test_data, timeout=10)
                    
                    # Check if rejection contains CAPTCHA-related info
                    captcha_rejection = any(indicator in post_response.text.lower() for indicator in cloudflare_indicators)
                    
                    results[name] = {
                        "captcha_detected": captcha_rejection,
                        "status_code": post_response.status_code,
                        "requires_captcha_for_login": captcha_rejection
                    }
                else:
                    results[name] = {
                        "captcha_detected": False,
                        "status_code": response.status_code
                    }
        
        except requests.RequestException as e:
            logger.error(f"Error testing CAPTCHA for {name} endpoint: {e}")
            results[name] = {"error": str(e)}
    
    return results

def simulate_distributed_attack(url: str, num_requests: int = 50, concurrency: int = 10) -> Dict[str, Any]:
    """Simulate a distributed attack from multiple IPs using a thread pool"""
    logger.info(f"Simulating distributed attack on {url} with {num_requests} requests, {concurrency} concurrent")
    
    def make_request(i: int) -> Dict[str, Any]:
        try:
            # Simulate different IP by using X-Forwarded-For header
            # Real-world attackers might use proxies or botnets
            fake_ip = f"192.168.{random.randint(1, 254)}.{random.randint(1, 254)}"
            
            # Random user agent
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "python-requests/2.25.1",
                "Wget/1.20.3 (linux-gnu)"
            ]
            
            headers = {
                "X-Forwarded-For": fake_ip,
                "User-Agent": random.choice(user_agents),
                "X-Attack-Simulation": "true"  # So the server knows this is a test
            }
            
            response = requests.get(url, headers=headers, timeout=10)
            
            return {
                "request_id": i,
                "status_code": response.status_code,
                "ip": fake_ip,
                "blocked": response.status_code in [403, 429],
                "body_preview": response.text[:50] + "..." if len(response.text) > 50 else response.text,
                "rate_limit_headers": {
                    k: v for k, v in response.headers.items() 
                    if k.lower().startswith("x-ratelimit") or k.lower() == "retry-after"
                }
            }
        
        except requests.RequestException as e:
            logger.error(f"Request error in distributed attack simulation: {e}")
            return {"request_id": i, "error": str(e), "ip": fake_ip}
    
    with ThreadPoolExecutor(max_workers=concurrency) as executor:
        results = list(executor.map(make_request, range(num_requests)))
    
    # Analyze results
    blocked_count = sum(1 for r in results if r.get("blocked", False) and "error" not in r)
    error_count = sum(1 for r in results if "error" in r)
    success_count = num_requests - blocked_count - error_count
    
    logger.info(f"Distributed attack results: {blocked_count} blocked, {success_count} succeeded, {error_count} errored")
    
    return {
        "total_requests": num_requests,
        "blocked_count": blocked_count,
        "success_count": success_count,
        "error_count": error_count,
        "block_rate": blocked_count / (num_requests - error_count) if num_requests > error_count else 0,
        "details": results
    }

def main() -> None:
    """Main function to run all tests"""
    print_header("RATE LIMITING AND CAPTCHA TEST")
    
    # 1. Test login rate limiting
    print(f"\n{Fore.YELLOW}Testing login rate limiting...{Style.RESET_ALL}")
    login_success, login_results = test_login_rate_limit(attempts=12)
    
    if login_success:
        print_success("Login rate limiting is working")
    else:
        print_error("Login rate limiting is not working properly")
    
    # Print rate limit headers from the responses
    print(f"\n{Fore.YELLOW}Rate limit headers for login attempts:{Style.RESET_ALL}")
    for response in login_results:
        if "error" not in response:
            status = f"{Fore.RED}BLOCKED{Style.RESET_ALL}" if response.get("blocked") else f"{Fore.GREEN}OK{Style.RESET_ALL}"
            headers = response.get("rate_limit_headers", {})
            print(f"  Attempt {response['attempt']}: {status} - Headers: {headers}")
    
    # 2. Test rate limiting for API endpoints
    print(f"\n{Fore.YELLOW}Testing rate limiting for API endpoints...{Style.RESET_ALL}")
    api_endpoints = [
        "/api/tasks/available",
        "/api/tasks/analytics",
        "/auth/register",
    ]
    
    for endpoint in api_endpoints:
        print(f"\nTesting rate limiting for {endpoint}...")
        success, _ = test_rate_limit(f"{BASE_URL}{endpoint}", attempts=10)
        if success:
            print_success(f"Rate limiting is working for {endpoint}")
        else:
            print_error(f"Rate limiting is not working properly for {endpoint}")
    
    # 3. Test Cloudflare CAPTCHA integration
    print(f"\n{Fore.YELLOW}Testing Cloudflare CAPTCHA integration...{Style.RESET_ALL}")
    captcha_results = test_cloudflare_captcha_integration()
    
    for name, result in captcha_results.items():
        if "error" in result:
            print_error(f"Error testing CAPTCHA for {name}: {result['error']}")
        elif result.get("captcha_detected"):
            print_success(f"CAPTCHA detected for {name} endpoint")
        else:
            print_warning(f"No CAPTCHA detected for {name} endpoint")
    
    # 4. Simulate distributed attack
    print(f"\n{Fore.YELLOW}Simulating distributed attack...{Style.RESET_ALL}")
    attack_results = simulate_distributed_attack(f"{BASE_URL}/api/tasks/available", num_requests=30, concurrency=5)
    
    if attack_results["block_rate"] > 0.5:
        print_success(f"Distributed attack was blocked at a rate of {attack_results['block_rate']:.2%}")
    else:
        print_error(f"Distributed attack was not effectively blocked (block rate: {attack_results['block_rate']:.2%})")
    
    # Print some sample attack results
    print(f"\n{Fore.YELLOW}Sample distributed attack results:{Style.RESET_ALL}")
    for i, result in enumerate(attack_results["details"][:5]):  # Show first 5
        if "error" in result:
            status = f"{Fore.RED}ERROR: {result['error']}{Style.RESET_ALL}"
        elif result.get("blocked"):
            status = f"{Fore.GREEN}BLOCKED{Style.RESET_ALL}"
        else:
            status = f"{Fore.YELLOW}ALLOWED{Style.RESET_ALL}"
        
        print(f"  Request from IP {result.get('ip')}: {status}")

if __name__ == "__main__":
    main() 