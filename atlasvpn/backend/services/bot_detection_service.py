from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timed<PERSON>ta
from sqlalchemy.orm import Session
from redis import Redis
import logging
import json
import hashlib
import re
import os
import random
import string
from fastapi import Request, HTTPException, status
from models import SecurityLog
import time

logger = logging.getLogger(__name__)

class BotDetectionService:
    """Service for detecting and handling automated requests"""
    
    def __init__(self, db: Session, redis: Redis):
        self.db = db
        self.redis = redis
        self.enabled = os.getenv("DISABLE_BOT_DETECTION", "false").lower() != "true"
        self.test_mode = os.getenv("TEST_MODE", "false").lower() == "true"
        
        # Redis keys
        self.REDIS_BOT_DETECTION_PREFIX = "bot_detection:"
        self.REDIS_CAPTCHA_PREFIX = "captcha:"
        self.REDIS_FINGERPRINT_PREFIX = "fingerprint:"
        
        # Bot detection thresholds
        self.SUSPICIOUS_REQUEST_THRESHOLD = 5  # Number of suspicious requests before CAPTCHA
        self.CAPTCHA_TTL = 300  # 5 minutes
        self.FINGERPRINT_TTL = 86400  # 24 hours
        
        # Enhanced bot detection patterns
        self.bot_patterns = [
            r"phantomjs|headless|selenium|webdriver|puppeteer|playwright",  # Automation tools
            r"python-requests|httpclient|java|curl|wget|go-http|axios|okhttp",  # API clients
            r"zgrab|masscan|nmap|nikto|burp|zap|acunetix|nessus",  # Security scanners
            r"semrush|ahrefs|majestic|moz|screaming|frog",  # SEO tools
            r"\\x|%[0-9a-f]{2}|eval\(|<script|alert\(|document\.cookie",  # Potential XSS/injection
        ]
        
        # Legitimate bot patterns that should be allowed
        self.allowed_bot_patterns = [
            r"googlebot|bingbot|yandexbot|applebot|duckduckbot|facebookexternalhit|twitterbot|rogerbot|linkedinbot|embedly",
            r"slackbot|discordbot|telegrambot|whatsapp|line|pinterest|baidu|sogou|exabot|ia_archiver"
        ]
        
        # Constants for failed login tracking
        self.REDIS_FAILED_LOGIN_PREFIX = "failed_login:"
        self.FAILED_LOGIN_THRESHOLD = 3  # Number of failed attempts before requiring CAPTCHA
        self.FAILED_LOGIN_TTL = 3600  # 1 hour
        
        # Behavioral patterns for bot detection
        self.BEHAVIORAL_PATTERNS = {
            "request_speed": {
                "threshold": 0.5,  # seconds between requests
                "weight": 0.3
            },
            "path_diversity": {
                "threshold": 3,  # unique paths per minute
                "weight": 0.2
            },
            "session_length": {
                "threshold": 2,  # seconds
                "weight": 0.2
            }
        }
        
    async def initialize(self):
        """Initialize bot detection service"""
        if self.test_mode:
            logger.info("Bot detection service initialized in test mode")
            return True
            
        try:
            # Try to load custom patterns from Redis if available
            custom_patterns = await self.redis.get(f"{self.REDIS_BOT_DETECTION_PREFIX}custom_patterns")
            if custom_patterns:
                try:
                    patterns_data = json.loads(custom_patterns)
                    if "bot_patterns" in patterns_data and isinstance(patterns_data["bot_patterns"], list):
                        self.bot_patterns.extend(patterns_data["bot_patterns"])
                        logger.info(f"Loaded {len(patterns_data['bot_patterns'])} custom bot patterns")
                except Exception as e:
                    logger.error(f"Error loading custom patterns: {str(e)}")
            
            logger.info("Bot detection service initialized with enhanced patterns")
            return True
        except Exception as e:
            logger.error(f"Failed to initialize bot detection service: {str(e)}")
            return False
            
    def _generate_browser_fingerprint(self, request: Request) -> str:
        """Generate a unique browser fingerprint"""
        components = [
            request.client.host,
            request.headers.get("user-agent", ""),
            request.headers.get("accept-language", ""),
            request.headers.get("accept-encoding", ""),
            request.headers.get("accept", ""),
            request.headers.get("sec-ch-ua", ""),  # Browser info
            request.headers.get("sec-ch-ua-platform", ""),  # OS info
        ]
        
        fingerprint = hashlib.sha256(''.join([str(c) for c in components]).encode()).hexdigest()
        return fingerprint
        
    async def is_bot_request(self, request: Request) -> bool:
        """Check if request is from a bot based on user agent"""
        if not self.enabled or self.test_mode:
            return False
            
        user_agent = request.headers.get("user-agent", "").lower()
        
        # First check if it's a legitimate bot that should be allowed
        for pattern in self.allowed_bot_patterns:
            if re.search(pattern, user_agent, re.IGNORECASE):
                logger.info(f"Legitimate bot detected and allowed: {user_agent}")
                return False
                
        # Then check against malicious/unwanted bot patterns
        for pattern in self.bot_patterns:
            if re.search(pattern, user_agent, re.IGNORECASE):
                logger.warning(f"Blocked bot detected: {user_agent}")
                return True
                
        return False
        
    async def is_suspicious_request(self, request: Request) -> bool:
        """Check if request is suspicious based on various factors"""
        if not self.enabled or self.test_mode:
            return False
            
        # Get fingerprint
        fingerprint = self._generate_browser_fingerprint(request)
        
        # Check suspicious factors
        suspicious_factors = []
        
        # 1. Missing or suspicious user agent
        user_agent = request.headers.get("user-agent", "")
        if not user_agent or len(user_agent) < 10:
            suspicious_factors.append("suspicious_user_agent")
            
        # 2. Missing accept header
        if not request.headers.get("accept"):
            suspicious_factors.append("missing_accept")
            
        # 3. Missing or suspicious referer for non-API requests
        if not request.url.path.startswith("/api/"):
            referer = request.headers.get("referer", "")
            if not referer:
                suspicious_factors.append("missing_referer")
                
        # 4. Unusual request rate (check Redis)
        request_count_key = f"{self.REDIS_BOT_DETECTION_PREFIX}request_count:{fingerprint}"
        request_count = await self.redis.get(request_count_key)
        
        if request_count:
            request_count = int(request_count)
            if request_count > 50:  # More than 50 requests in 1 minute
                suspicious_factors.append("high_request_rate")
        
        # Increment request count
        await self.redis.setex(request_count_key, 60, str(int(request_count or 0) + 1))
        
        # 5. Check for suspicious behavioral patterns
        await self._check_behavioral_patterns(request, fingerprint, suspicious_factors)
        
        # 6. Check for suspicious headers
        await self._check_suspicious_headers(request, suspicious_factors)
        
        # Return true if there are any suspicious factors
        return len(suspicious_factors) > 0
        
    async def _check_behavioral_patterns(self, request: Request, fingerprint: str, suspicious_factors: List[str]) -> None:
        """Check for suspicious behavioral patterns"""
        # Track request timestamps
        timestamp_key = f"{self.REDIS_BOT_DETECTION_PREFIX}timestamps:{fingerprint}"
        current_time = time.time()
        
        # Add current timestamp to list
        await self.redis.lpush(timestamp_key, str(current_time))
        await self.redis.ltrim(timestamp_key, 0, 9)  # Keep last 10 timestamps
        await self.redis.expire(timestamp_key, 300)  # 5 minute TTL
        
        # Get timestamps
        timestamps = await self.redis.lrange(timestamp_key, 0, -1)
        if len(timestamps) > 1:
            # Convert to float
            timestamps = [float(ts) for ts in timestamps]
            
            # Check request speed
            time_diffs = [timestamps[i] - timestamps[i+1] for i in range(len(timestamps)-1)]
            avg_time_diff = sum(time_diffs) / len(time_diffs) if time_diffs else 0
            
            if avg_time_diff < self.BEHAVIORAL_PATTERNS["request_speed"]["threshold"]:
                suspicious_factors.append("suspicious_request_speed")
                
        # Track unique paths
        path_key = f"{self.REDIS_BOT_DETECTION_PREFIX}paths:{fingerprint}"
        await self.redis.sadd(path_key, request.url.path)
        await self.redis.expire(path_key, 60)  # 1 minute TTL
        
        # Get unique path count
        path_count = await self.redis.scard(path_key)
        if path_count > self.BEHAVIORAL_PATTERNS["path_diversity"]["threshold"]:
            suspicious_factors.append("suspicious_path_diversity")
            
    async def _check_suspicious_headers(self, request: Request, suspicious_factors: List[str]) -> None:
        """Check for suspicious headers"""
        # Check for inconsistent browser headers
        user_agent = request.headers.get("user-agent", "").lower()
        accept_language = request.headers.get("accept-language", "")
        
        # Check for browser inconsistencies
        if "chrome" in user_agent and "mozilla" not in user_agent:
            suspicious_factors.append("inconsistent_browser_headers")
            
        # Check for missing language header in browser requests
        if ("chrome" in user_agent or "firefox" in user_agent or "safari" in user_agent) and not accept_language:
            suspicious_factors.append("missing_language_header")
            
        # Check for suspicious connection headers
        connection = request.headers.get("connection", "").lower()
        if connection == "close" and ("chrome" in user_agent or "firefox" in user_agent):
            suspicious_factors.append("suspicious_connection_header")
            
    async def track_failed_login(self, username: str, ip_address: str) -> bool:
        """
        Track failed login attempts and determine if CAPTCHA should be required
        
        Args:
            username: The username that failed to login
            ip_address: The IP address of the request
            
        Returns:
            bool: True if CAPTCHA should be required, False otherwise
        """
        # Create keys for tracking
        username_key = f"{self.REDIS_FAILED_LOGIN_PREFIX}user:{username}"
        ip_key = f"{self.REDIS_FAILED_LOGIN_PREFIX}ip:{ip_address}"
        
        # Increment failed login counters
        await self.redis.incr(username_key)
        await self.redis.incr(ip_key)
        
        # Set expiration if not already set
        await self.redis.expire(username_key, self.FAILED_LOGIN_TTL)
        await self.redis.expire(ip_key, self.FAILED_LOGIN_TTL)
        
        # Get current counts
        username_count = int(await self.redis.get(username_key) or 0)
        ip_count = int(await self.redis.get(ip_key) or 0)
        
        # Check if threshold exceeded
        if username_count >= self.FAILED_LOGIN_THRESHOLD or ip_count >= self.FAILED_LOGIN_THRESHOLD:
            # Set CAPTCHA required for this username and IP
            username_captcha_key = f"{self.REDIS_CAPTCHA_PREFIX}required:user:{username}"
            ip_captcha_key = f"{self.REDIS_CAPTCHA_PREFIX}required:ip:{ip_address}"
            
            await self.redis.setex(username_captcha_key, self.CAPTCHA_TTL, "1")
            await self.redis.setex(ip_captcha_key, self.CAPTCHA_TTL, "1")
            
            logger.warning(f"CAPTCHA required for username {username} after {username_count} failed attempts")
            return True
            
        return False
        
    async def reset_failed_login(self, username: str, ip_address: str) -> None:
        """
        Reset failed login attempts after successful login
        
        Args:
            username: The username that successfully logged in
            ip_address: The IP address of the request
        """
        # Delete keys for tracking
        username_key = f"{self.REDIS_FAILED_LOGIN_PREFIX}user:{username}"
        ip_key = f"{self.REDIS_FAILED_LOGIN_PREFIX}ip:{ip_address}"
        username_captcha_key = f"{self.REDIS_CAPTCHA_PREFIX}required:user:{username}"
        ip_captcha_key = f"{self.REDIS_CAPTCHA_PREFIX}required:ip:{ip_address}"
        
        await self.redis.delete(username_key)
        await self.redis.delete(ip_key)
        await self.redis.delete(username_captcha_key)
        await self.redis.delete(ip_captcha_key)
        
    async def needs_captcha(self, request: Request) -> bool:
        """Check if CAPTCHA is required for this request"""
        # Skip in test mode
        if os.getenv("TEST_MODE", "false").lower() == "true":
            return False
            
        # Critical endpoints that always require CAPTCHA
        CRITICAL_ENDPOINTS = ["/auth/token", "/auth/register", "/auth/admin/login"]
        
        # Always require CAPTCHA for critical endpoints
        if request.url.path in CRITICAL_ENDPOINTS:
            return True
            
        # Get fingerprint
        fingerprint = self._generate_browser_fingerprint(request)
        
        # Check if CAPTCHA is already required
        captcha_key = f"{self.REDIS_CAPTCHA_PREFIX}required:{fingerprint}"
        captcha_required = await self.redis.get(captcha_key)
        
        if captcha_required:
            return True
            
        # Check if this is a login request
        if request.url.path == "/auth/token" or request.url.path == "/auth/register" or request.url.path == "/auth/admin/login":
            # Try to get username from request body
            try:
                body = await request.body()
                body_str = body.decode()
                
                # Create new request with original body for later processing
                async def receive():
                    return {"type": "http.request", "body": body}
                
                request._receive = receive
                
                # Parse JSON body
                try:
                    data = json.loads(body_str)
                    username = data.get("username", "")
                    
                    if username:
                        # Check if CAPTCHA is required for this username
                        username_captcha_key = f"{self.REDIS_CAPTCHA_PREFIX}required:user:{username}"
                        username_captcha_required = await self.redis.get(username_captcha_key)
                        
                        if username_captcha_required:
                            return True
                except:
                    # If parsing fails, continue with other checks
                    pass
            except:
                # If body reading fails, continue with other checks
                pass
                
        # Check suspicious request count
        suspicious_count_key = f"{self.REDIS_BOT_DETECTION_PREFIX}suspicious_count:{fingerprint}"
        suspicious_count = await self.redis.get(suspicious_count_key)
        
        if suspicious_count and int(suspicious_count) >= self.SUSPICIOUS_REQUEST_THRESHOLD:
            # Set CAPTCHA required
            await self.redis.setex(captcha_key, self.CAPTCHA_TTL, "1")
            return True
            
        # Check IP address
        ip_address = request.client.host
        ip_captcha_key = f"{self.REDIS_CAPTCHA_PREFIX}required:ip:{ip_address}"
        ip_captcha_required = await self.redis.get(ip_captcha_key)
        
        if ip_captcha_required:
            return True
            
        return False
        
    async def generate_captcha(self, request: Request) -> Dict[str, Any]:
        """Generate a CAPTCHA challenge"""
        # Get fingerprint
        fingerprint = self._generate_browser_fingerprint(request)
        
        # Generate a simple text-based CAPTCHA (in production, use a proper CAPTCHA service)
        captcha_text = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
        captcha_key = f"{self.REDIS_CAPTCHA_PREFIX}challenge:{fingerprint}"
        
        # Store CAPTCHA solution in Redis
        await self.redis.setex(captcha_key, self.CAPTCHA_TTL, captcha_text)
        
        return {
            "captcha_required": True,
            "captcha_id": fingerprint,
            "captcha_text": captcha_text,  # In production, don't return the solution directly
            "expires_in": self.CAPTCHA_TTL
        }
        
    async def verify_captcha(self, fingerprint: str, captcha_solution: str) -> bool:
        """Verify CAPTCHA solution"""
        if not self.enabled or self.test_mode:
            return True
            
        captcha_key = f"{self.REDIS_CAPTCHA_PREFIX}challenge:{fingerprint}"
        stored_solution = await self.redis.get(captcha_key)
        
        if not stored_solution:
            return False
            
        # Convert to string and compare
        stored_solution = stored_solution.decode() if isinstance(stored_solution, bytes) else stored_solution
        
        # Case-insensitive comparison
        is_valid = stored_solution.lower() == captcha_solution.lower()
        
        if is_valid:
            # Remove CAPTCHA requirement
            required_key = f"{self.REDIS_CAPTCHA_PREFIX}required:{fingerprint}"
            await self.redis.delete(required_key)
            
            # Reset suspicious count
            suspicious_count_key = f"{self.REDIS_BOT_DETECTION_PREFIX}suspicious_count:{fingerprint}"
            await self.redis.delete(suspicious_count_key)
            
            # Delete the challenge
            await self.redis.delete(captcha_key)
            
        return is_valid
        
    async def track_suspicious_request(self, request: Request) -> None:
        """Track suspicious request"""
        if not self.enabled or self.test_mode:
            return
            
        # Get fingerprint
        fingerprint = self._generate_browser_fingerprint(request)
        
        # Increment suspicious request count
        suspicious_count_key = f"{self.REDIS_BOT_DETECTION_PREFIX}suspicious_count:{fingerprint}"
        current_count = await self.redis.get(suspicious_count_key)
        
        if current_count:
            await self.redis.setex(
                suspicious_count_key,
                self.CAPTCHA_TTL,
                str(int(current_count) + 1)
            )
        else:
            await self.redis.setex(suspicious_count_key, self.CAPTCHA_TTL, "1")
            
        # Log suspicious request
        try:
            log_entry = SecurityLog(
                action_type="suspicious_request",
                ip_address=request.client.host,
                user_agent=request.headers.get("user-agent"),
                status="detected",
                risk_level="medium",
                details={
                    "fingerprint": fingerprint,
                    "path": request.url.path,
                    "method": request.method,
                    "headers": dict(request.headers)
                },
                created_at=datetime.utcnow()
            )
            self.db.add(log_entry)
            self.db.flush()
        except Exception as e:
            logger.error(f"Error logging suspicious request: {str(e)}")
            
    async def process_request(self, request: Request) -> Optional[Dict[str, Any]]:
        """Process request and return CAPTCHA if needed"""
        if not self.enabled or self.test_mode:
            return None
            
        # Check if it's a bot
        if await self.is_bot_request(request):
            await self.track_suspicious_request(request)
            return {"bot_detected": True}
            
        # Check if request is suspicious
        if await self.is_suspicious_request(request):
            await self.track_suspicious_request(request)
            
            # Check if CAPTCHA is needed
            if await self.needs_captcha(request):
                return await self.generate_captcha(request)
                
        return None
        
    async def is_bot(self, request: Request, check_distributed: bool = False, sensitivity: float = 0.7) -> Tuple[bool, float, Dict[str, Any]]:
        """
        Enhanced bot detection with confidence score
        
        Args:
            request: The request to check
            check_distributed: Whether to check for distributed attacks
            sensitivity: Lower value means more strict detection (0.0-1.0)
            
        Returns:
            Tuple of (is_bot, confidence, details)
        """
        if not self.enabled or self.test_mode:
            return False, 0.0, {"reason": "bot_detection_disabled"}
            
        # Get request details
        ip = request.client.host
        user_agent = request.headers.get("user-agent", "")
        method = request.method
        path = request.url.path
        referer = request.headers.get("referer", "")
        accept = request.headers.get("accept", "")
        
        # Start with zero bot confidence
        confidence = 0.0
        evidence = []
        
        # Check for common bot patterns in user agent
        for pattern in self.bot_patterns:
            if re.search(pattern, user_agent.lower()):
                # Check if it's an allowed bot
                if any(re.search(allowed, user_agent.lower()) for allowed in self.allowed_bot_patterns):
                    continue
                confidence += 0.4
                evidence.append(f"bot_pattern_match: {pattern}")
                
        # Check for missing or suspicious headers
        if not user_agent:
            confidence += 0.3
            evidence.append("missing_user_agent")
            
        if not accept:
            confidence += 0.2
            evidence.append("missing_accept_header")
            
        # Check for unusual behavior patterns
        if method == "POST" and not referer:
            confidence += 0.2
            evidence.append("post_without_referer")
            
        # Browser fingerprinting check
        fingerprint = self._generate_browser_fingerprint(request)
        fp_key = f"{self.REDIS_FINGERPRINT_PREFIX}{fingerprint}"
        
        # Check for distributed attack patterns if requested
        if check_distributed:
            # Get IP request count in last minute
            ip_key = f"{self.REDIS_BOT_DETECTION_PREFIX}ip:{ip}:requests"
            requests_count = await self.redis.get(ip_key)
            
            if requests_count:
                count = int(requests_count)
                if count > 30:  # Very high frequency
                    confidence += 0.5
                    evidence.append(f"high_frequency_ip: {count} requests")
                elif count > 15:  # Moderate frequency
                    confidence += 0.3
                    evidence.append(f"moderate_frequency_ip: {count} requests")
                    
            # Increment IP request count
            await self.redis.incr(ip_key)
            await self.redis.expire(ip_key, 60)  # 1 minute TTL
            
            # Check suspicious IP patterns
            suspicious_key = f"{self.REDIS_BOT_DETECTION_PREFIX}suspicious:{ip}"
            is_suspicious = await self.redis.get(suspicious_key)
            
            if is_suspicious:
                confidence += 0.3
                evidence.append("previously_suspicious")
                
        # Apply sensitivity adjustment
        adjusted_confidence = confidence * (1.0 / sensitivity)
        is_bot_result = adjusted_confidence >= 1.0
        
        details = {
            "evidence": evidence,
            "raw_confidence": confidence,
            "adjusted_confidence": adjusted_confidence,
            "sensitivity": sensitivity,
            "fingerprint": fingerprint,
            "ip": ip
        }
        
        # Log high confidence detections
        if adjusted_confidence > 0.8:
            logger.warning(f"High confidence bot detection: {details}")
            
        return is_bot_result, adjusted_confidence, details
        
    async def mark_suspicious(self, ip: str, duration: int = 3600) -> None:
        """
        Mark an IP as suspicious for a period of time
        
        Args:
            ip: The IP address to mark
            duration: Time in seconds to mark as suspicious
        """
        suspicious_key = f"{self.REDIS_BOT_DETECTION_PREFIX}suspicious:{ip}"
        await self.redis.setex(suspicious_key, duration, "1")
        logger.info(f"Marked IP as suspicious: {ip} for {duration} seconds")
        
    async def add_custom_bot_pattern(self, pattern: str) -> bool:
        """
        Add a custom bot detection pattern
        
        Args:
            pattern: Regex pattern to add
            
        Returns:
            bool: True if pattern was added successfully
        """
        try:
            # Validate pattern by compiling it
            re.compile(pattern)
            
            # Get existing custom patterns
            custom_patterns_key = f"{self.REDIS_BOT_DETECTION_PREFIX}custom_patterns"
            custom_patterns_json = await self.redis.get(custom_patterns_key)
            
            if custom_patterns_json:
                custom_patterns = json.loads(custom_patterns_json)
            else:
                custom_patterns = {"bot_patterns": []}
                
            # Add pattern if it doesn't already exist
            if pattern not in custom_patterns["bot_patterns"]:
                custom_patterns["bot_patterns"].append(pattern)
                
                # Save updated patterns
                await self.redis.set(
                    custom_patterns_key,
                    json.dumps(custom_patterns)
                )
                
                # Add to in-memory patterns
                if pattern not in self.bot_patterns:
                    self.bot_patterns.append(pattern)
                    
                logger.info(f"Added custom bot pattern: {pattern}")
                return True
            else:
                logger.info(f"Pattern already exists: {pattern}")
                return True
                
        except Exception as e:
            logger.error(f"Error adding custom bot pattern: {str(e)}")
            return False
            
    async def get_detection_stats(self) -> Dict[str, Any]:
        """
        Get bot detection statistics
        
        Returns:
            Dict with detection statistics
        """
        try:
            # Get total suspicious IPs
            suspicious_ips = await self.redis.keys(f"{self.REDIS_BOT_DETECTION_PREFIX}suspicious:*")
            suspicious_count = len(suspicious_ips)
            
            # Get total CAPTCHA challenges
            captcha_challenges = await self.redis.keys(f"{self.REDIS_CAPTCHA_PREFIX}challenge:*")
            captcha_count = len(captcha_challenges)
            
            # Get total required CAPTCHAs
            required_captchas = await self.redis.keys(f"{self.REDIS_CAPTCHA_PREFIX}required:*")
            required_count = len(required_captchas)
            
            return {
                "suspicious_ips": suspicious_count,
                "captcha_challenges": captcha_count,
                "required_captchas": required_count,
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting detection stats: {str(e)}")
            return {
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            } 