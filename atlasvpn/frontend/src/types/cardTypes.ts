// frontend/src/types/cardTypes.ts

// Base interface for game card properties
export interface GameCardBase {
  id: number;
  name: string;
  description: string;
  image_url: string | null;
  level: number;
  profit_per_hour: number;
  next_upgrade_cost: number | null;
  rarity: string;
  max_level: number;
}

// Type for the basic card information from the catalog
export interface CardCatalog {
  id: number;
  name: string;
  description: string; // Ensure description is always present
  image_url: string | null; // Allow null for image
  price: number; // Base price (Level 1)
  profit_rate: number; // Base profit rate (Level 1)
  max_level: number;
  rarity: string;
  level_profits?: number[]; // Array of profit per level
  level_costs?: number[]; // Array of cost to upgrade TO each level (index 0 = cost L1->L2)
  level_images?: string[]; // Array of image URLs per level
  is_owned?: boolean; // Track if user owns this card
}

// Type representing a card owned by a user
export interface UserCard {
  id: number; // UserCard unique ID
  user_id: number;
  card_id: number; // Link to CardCatalog ID
  level: number;
  purchase_date: string; // ISO string
  last_profit_claim: string; // ISO string
  total_claimed_profit: number;
  accumulated_profit: number;
  current_hourly_profit: number;
  next_upgrade_cost: number | null;
  card: CardCatalog; // Embed the detailed CardCatalog info
}

// Type for the unified response from the /api/cards/all endpoint
export interface UnifiedCardResponse {
  id: number; // CardCatalog ID
  name: string;
  description: string;
  image_url: string | null; // Usually the Level 1 image or base image
  rarity: string;
  max_level: number;
  profit_rate: number; // Base profit rate (Level 1)
  price: number; // Base purchase price (Level 1)
  level_profits?: number[]; // Full list of profits per level
  next_level_profit_increase?: number | null; // Calculated increase for the *next* level if owned
  is_owned: boolean;
  user_card: {
    // Details ONLY if is_owned is true
    id: number; // UserCard ID
    level: number;
    purchase_date: string;
    last_profit_claim: string;
    total_claimed_profit: number;
    accumulated_profit: number;
    current_hourly_profit: number;
    next_upgrade_cost: number | null;
  } | null;
  // Optional: Include full nested catalog if backend sends it (adjust based on actual API)
  // card_catalog?: CardCatalog;
}

// NEW: Interface for the result of the upgradeCard action
export interface UpgradeResult {
  success: boolean;
  updatedCard?: UserCard; // The full updated card data if successful
  error?: string; // Error message if failed
}

// Add any other related types if needed

export interface BuyCardResult {
  new_card: UserCard;
  new_wallet_balance: number;
}

export interface ClaimAllResponse {
  total_claimed: number;
  new_wallet_balance: number;
  cards_count: number;
  next_claim_available: string; // ISO date string
}
