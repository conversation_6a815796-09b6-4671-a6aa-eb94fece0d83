/**
 * @file apiClient.test.ts
 * @description Tests for API client configuration and backend connectivity
 * Focuses on environment detection, URL construction, and connection testing
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import axios from 'axios';
import { api, createServerApiClient } from '../api';

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(() => ({
      defaults: {},
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() }
      }
    })),
    post: vi.fn(),
    get: vi.fn(),
    put: vi.fn(),
    delete: vi.fn()
  }
}));
const mockAxios = axios as any;

describe('API Client Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset window.location mock
    delete (window as any).location;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('API URL Detection', () => {
    it('should use localhost API for localhost development', () => {
      // Arrange
      Object.defineProperty(window, 'location', {
        value: {
          hostname: 'localhost',
          protocol: 'http:',
        },
        writable: true,
      });

      // Mock console.log to verify URL selection
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // This test verifies the URL selection logic that runs when the module loads
      // Since the API client is created at module load time, we need to test the logic indirectly
      expect(window.location.hostname).toBe('localhost');
      expect(consoleSpy).toHaveBeenCalled();
    });

    it('should use production API for production domain', () => {
      // Arrange
      Object.defineProperty(window, 'location', {
        value: {
          hostname: 'app.atlasvip.cloud',
          protocol: 'https:',
        },
        writable: true,
      });

      expect(window.location.hostname).toBe('app.atlasvip.cloud');
    });

    it('should handle server-side environment detection', () => {
      // Arrange - simulate server-side environment
      delete (global as any).window;

      // Test server-side URL logic
      expect(typeof window).toBe('undefined');
    });
  });

  describe('Request Interceptors', () => {
    it('should add CSRF token for production requests', async () => {
      // Arrange
      const mockCsrfToken = 'test-csrf-token';
      document.head.innerHTML = `<meta name="csrf-token" content="${mockCsrfToken}">`;
      
      Object.defineProperty(window, 'location', {
        value: {
          hostname: 'app.atlasvip.cloud',
          protocol: 'https:',
        },
        writable: true,
      });

      const mockRequest = {
        baseURL: 'https://app.atlasvip.cloud/api',
        url: '/auth/verify-session',
        headers: {},
      };

      // Test that CSRF token would be added for non-localhost requests
      expect(document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')).toBe(mockCsrfToken);
    });

    it('should not add CSRF token for localhost requests', () => {
      // Arrange
      Object.defineProperty(window, 'location', {
        value: {
          hostname: 'localhost',
          protocol: 'http:',
        },
        writable: true,
      });

      const mockRequest = {
        baseURL: 'http://localhost:8000/api',
        url: '/auth/verify-session',
        headers: {},
      };

      // Localhost requests should not require CSRF
      expect(mockRequest.baseURL).toContain('localhost');
    });

    it('should log request details for debugging', () => {
      // Arrange
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      const mockRequest = {
        baseURL: 'http://localhost:8000/api',
        url: '/auth/verify-session',
        headers: {},
      };

      // The request interceptor should log details
      expect(consoleSpy).toHaveBeenCalled();
    });
  });

  describe('Response Interceptors', () => {
    it('should handle 401 errors by redirecting to login', async () => {
      // Arrange
      const mockRedirect = vi.fn();
      Object.defineProperty(window, 'location', {
        value: {
          href: '',
          hostname: 'localhost',
          protocol: 'http:',
        },
        writable: true,
        configurable: true,
      });

      const mock401Error = {
        response: { status: 401 },
      };

      // Test that 401 errors trigger redirect
      expect(mock401Error.response.status).toBe(401);
    });

    it('should pass through successful responses', async () => {
      // Arrange
      const mockResponse = {
        status: 200,
        data: { success: true },
      };

      // Response interceptor should pass through successful responses
      expect(mockResponse.status).toBe(200);
    });
  });

  describe('Server API Client', () => {
    it('should create server-side API client with cookies', () => {
      // Arrange
      const testCookies = 'auth_token=test123; session_id=abc456';
      
      mockAxios.create = vi.fn().mockReturnValue({
        defaults: {
          baseURL: 'https://app.atlasvip.cloud/api',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': testCookies,
          },
        },
      });

      // Act
      const serverClient = createServerApiClient(testCookies);

      // Assert
      expect(mockAxios.create).toHaveBeenCalledWith({
        baseURL: expect.stringContaining('api'),
        timeout: 15000,
        headers: {
          'Content-Type': 'application/json',
          'Cookie': testCookies,
        },
        withCredentials: true,
      });
    });

    it('should handle missing cookies gracefully', () => {
      // Arrange
      mockAxios.create.mockReturnValue({
        defaults: {
          baseURL: 'https://app.atlasvip.cloud/api',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': '',
          },
        },
      } as any);

      // Act
      const serverClient = createServerApiClient();

      // Assert
      expect(mockAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          headers: expect.objectContaining({
            'Cookie': '',
          }),
        })
      );
    });
  });

  describe('Connection Testing', () => {
    it('should test backend connectivity', async () => {
      // Arrange
      mockAxios.post.mockResolvedValueOnce({
        status: 200,
        data: { status: 'ok' },
      });

      // Act - simulate a health check
      const response = await mockAxios.post('/health');

      // Assert
      expect(response.status).toBe(200);
      expect(response.data.status).toBe('ok');
    });

    it('should handle connection timeouts', async () => {
      // Arrange
      mockAxios.post.mockRejectedValueOnce({
        code: 'ECONNABORTED',
        message: 'timeout of 15000ms exceeded',
      });

      // Act & Assert
      try {
        await mockAxios.post('/auth/verify-session');
      } catch (error: any) {
        expect(error.code).toBe('ECONNABORTED');
        expect(error.message).toContain('timeout');
      }
    });

    it('should handle network errors', async () => {
      // Arrange
      mockAxios.post.mockRejectedValueOnce({
        code: 'NETWORK_ERROR',
        message: 'Network Error',
      });

      // Act & Assert
      try {
        await mockAxios.post('/auth/verify-session');
      } catch (error: any) {
        expect(error.code).toBe('NETWORK_ERROR');
      }
    });

    it('should handle CORS errors', async () => {
      // Arrange
      mockAxios.post.mockRejectedValueOnce({
        message: 'Request blocked by CORS policy',
      });

      // Act & Assert
      try {
        await mockAxios.post('/auth/verify-session');
      } catch (error: any) {
        expect(error.message).toContain('CORS');
      }
    });
  });

  describe('Environment Variables', () => {
    it('should handle missing environment variables', () => {
      // Test fallback behavior
      const env = import.meta.env;
      expect(env).toBeDefined();
    });

    it('should use development configuration in dev mode', () => {
      // Test development-specific settings
      if (import.meta.env.DEV) {
        expect(import.meta.env.DEV).toBe(true);
      }
    });

    it('should use production configuration in production', () => {
      // Test production-specific settings
      if (import.meta.env.PROD) {
        expect(import.meta.env.PROD).toBe(true);
      }
    });
  });

  describe('Security Headers', () => {
    it('should set required security headers', () => {
      // Test that required headers are set
      const expectedHeaders = {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      };

      expect(expectedHeaders['Content-Type']).toBe('application/json');
      expect(expectedHeaders['X-Requested-With']).toBe('XMLHttpRequest');
    });

    it('should handle credentials correctly', () => {
      // Test withCredentials configuration
      expect(true).toBe(true); // Placeholder for actual withCredentials test
    });
  });

  describe('Error Message Formatting', () => {
    it('should format API errors properly', () => {
      // Test error message extraction
      const mockError = {
        response: {
          status: 422,
          data: {
            detail: 'Validation error',
            message: 'Invalid input',
          },
        },
      };

      const errorMessage = mockError.response.data.detail || mockError.response.data.message;
      expect(errorMessage).toBe('Validation error');
    });

    it('should handle malformed error responses', () => {
      // Test handling of unexpected error formats
      const mockError = {
        response: {
          status: 500,
          data: null,
        },
      };

      expect(mockError.response.status).toBe(500);
      expect(mockError.response.data).toBeNull();
    });
  });

  describe('Performance Considerations', () => {
    it('should have appropriate timeout configurations', () => {
      // Test timeout settings
      const timeout = 15000; // 15 seconds
      expect(timeout).toBeGreaterThan(5000);
      expect(timeout).toBeLessThanOrEqual(30000);
    });

    it('should handle concurrent requests properly', async () => {
      // Test concurrent request handling
      mockAxios.post
        .mockResolvedValueOnce({ status: 200, data: { result: 'request1' } })
        .mockResolvedValueOnce({ status: 200, data: { result: 'request2' } });

      const requests = [
        mockAxios.post('/endpoint1'),
        mockAxios.post('/endpoint2'),
      ];

      const responses = await Promise.all(requests);
      expect(responses).toHaveLength(2);
      expect(responses[0].data.result).toBe('request1');
      expect(responses[1].data.result).toBe('request2');
    });
  });
});