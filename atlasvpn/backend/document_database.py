import os
import sys

# Add the parent directory to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

from sqlalchemy import text, inspect, select
from database import engine
import logging
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter

logger = logging.getLogger(__name__)

def style_header_row(row, ws):
    """Apply styling to header row"""
    blue_fill = PatternFill(start_color='1E90FF', end_color='1E90FF', fill_type='solid')
    white_font = Font(bold=True, color='FFFFFF')
    alignment = Alignment(horizontal='center', vertical='center')
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for cell in row:
        cell.fill = blue_fill
        cell.font = white_font
        cell.alignment = alignment
        cell.border = border

def style_section_header(row, ws):
    """Apply styling to section header"""
    light_blue_fill = PatternFill(start_color='87CEEB', end_color='87CEEB', fill_type='solid')
    black_font = Font(bold=True)
    alignment = Alignment(horizontal='left', vertical='center')
    
    for cell in row:
        cell.fill = light_blue_fill
        cell.font = black_font
        cell.alignment = alignment

def style_data_row(row):
    """Apply styling to data row"""
    alignment = Alignment(horizontal='left', vertical='center')
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    for cell in row:
        cell.alignment = alignment
        cell.border = border

def adjust_column_width(ws):
    """Adjust column width based on content"""
    for column in ws.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        
        adjusted_width = (max_length + 2)
        ws.column_dimensions[column_letter].width = min(adjusted_width, 50)

def document_database():
    """Document all tables and their columns in the database"""
    try:
        # Setup logging
        logging.basicConfig(level=logging.INFO)
        
        # Create Excel workbook
        wb = Workbook()
        # Remove default sheet
        wb.remove(wb.active)
        
        # Get the absolute path for the output file
        output_file = os.path.join(parent_dir, 'database_schema.xlsx')
        
        connection = engine.connect()
        inspector = inspect(engine)
        
        # Get current timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        logger.info(f"Starting database documentation generation at {timestamp}")
        
        # Create Overview sheet
        overview = wb.create_sheet("Overview")
        overview.append(["Database Schema Documentation"])
        overview.append(["Generated at:", timestamp])
        overview.append([])
        overview.append(["Table Name", "Number of Columns", "Has Foreign Keys", "Has Indexes", "Number of Records"])
        
        # Style overview header
        style_header_row(overview[4-1], overview)  # -1 because rows are 1-based
        
        # Get all tables
        tables = inspector.get_table_names()
        logger.info(f"Found {len(tables)} tables")
        
        for table in tables:
            logger.info(f"Documenting table: {table}")
            
            try:
                # Create worksheet for table
                ws = wb.create_sheet(table)
                
                # Add table schema section
                ws.append(["Table Schema"])
                style_section_header(ws[ws.max_row], ws)
                ws.append([])
                
                # Add table columns section
                ws.append(["Columns"])
                ws.append(["Column Name", "Type", "Nullable", "Default", "Primary Key"])
                style_header_row(ws[ws.max_row], ws)
                
                # Get columns
                columns = inspector.get_columns(table)
                for col in columns:
                    ws.append([
                        col['name'],
                        str(col['type']),
                        'YES' if col.get('nullable', True) else 'NO',
                        str(col.get('default', '')),
                        'YES' if col.get('primary_key', False) else 'NO'
                    ])
                    style_data_row(ws[ws.max_row])
                
                ws.append([])  # Empty row
                
                # Get foreign keys
                fks = inspector.get_foreign_keys(table)
                if fks:
                    ws.append(["Foreign Keys"])
                    ws.append(["Column(s)", "Referenced Table", "Referenced Column(s)", "On Delete"])
                    style_header_row(ws[ws.max_row], ws)
                    
                    for fk in fks:
                        ws.append([
                            ', '.join(fk['constrained_columns']),
                            fk['referred_table'],
                            ', '.join(fk['referred_columns']),
                            fk.get('ondelete', '')
                        ])
                        style_data_row(ws[ws.max_row])
                    
                    ws.append([])  # Empty row
                
                # Get indexes
                indexes = inspector.get_indexes(table)
                if indexes:
                    ws.append(["Indexes"])
                    ws.append(["Index Name", "Columns", "Unique"])
                    style_header_row(ws[ws.max_row], ws)
                    
                    for idx in indexes:
                        ws.append([
                            idx['name'],
                            ', '.join(idx['column_names']),
                            'YES' if idx.get('unique', False) else 'NO'
                        ])
                        style_data_row(ws[ws.max_row])
                    
                    ws.append([])  # Empty row
                
                # Add table data section
                ws.append(["Table Data"])
                style_section_header(ws[ws.max_row], ws)
                ws.append([])
                
                # Get column names for data
                column_names = [col['name'] for col in columns]
                ws.append(column_names)
                style_header_row(ws[ws.max_row], ws)
                
                # Get table data
                result = connection.execute(text(f"SELECT * FROM {table}"))
                rows = result.fetchall()
                
                # Add data rows
                for row in rows:
                    ws.append([str(value) for value in row])
                    style_data_row(ws[ws.max_row])
                
                # Adjust column widths
                adjust_column_width(ws)
                
                # Add to overview
                overview.append([
                    table,
                    len(columns),
                    'YES' if fks else 'NO',
                    'YES' if indexes else 'NO',
                    len(rows)
                ])
                style_data_row(overview[overview.max_row])
                
            except Exception as table_error:
                logger.error(f"Error documenting table {table}: {str(table_error)}")
                continue
        
        # Adjust overview column widths
        adjust_column_width(overview)
        
        # Save workbook
        wb.save(output_file)
        logger.info(f"Documentation saved to: {output_file}")
        
    except Exception as e:
        logger.error(f"Failed to generate database documentation: {str(e)}")
        raise
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == "__main__":
    document_database() 