# Setup Instructions for New Server

## 1. Backend Setup

### Prerequisites
```bash
# Install Python 3.11+, <PERSON>is, PostgreSQL
sudo apt update
sudo apt install python3.11 python3.11-venv redis-server postgresql postgresql-contrib nginx certbot python3-certbot-nginx
```

### Backend Configuration
```bash
cd /opt/atlasvpn/backend

# Create and activate virtual environment
python3.11 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your database, Redis, and Telegram bot configuration

# Run database migrations
alembic upgrade head

# Start backend service
python main.py
```

## 2. Frontend Setup

### Prerequisites
```bash
# Install Node.js 18+ and pnpm
curl -fsSL https://fnm.vercel.app/install | bash
source ~/.bashrc
fnm use --install-if-missing 18
npm install -g pnpm
```

### Frontend Configuration
```bash
cd /opt/atlasvpn/frontend

# Install dependencies
pnpm install

# Create environment files
cat > .env.development << EOF
# Development Environment Configuration
VITE_API_URL=http://localhost:8000
VITE_WS_BASE_URL=ws://localhost:8000/ws
VITE_TELEGRAM_BOT_USERNAME=your_bot_username
NODE_ENV=development
VITE_APP_ENV=development
VITE_DEBUG_API=true
VITE_DEBUG_TELEGRAM=true
EOF

cat > .env.production << EOF
# Production Environment Configuration
VITE_API_URL=https://dev.atlasvip.cloud/api
VITE_WS_BASE_URL=wss://dev.atlasvip.cloud/ws
VITE_TELEGRAM_BOT_USERNAME=your_bot_username
NODE_ENV=production
VITE_APP_ENV=production
EOF

# Build for production
pnpm build

# Start development server (for testing)
pnpm dev
```

## 3. Configure NGINX
```bash
# Copy the nginx configuration
sudo cp /etc/nginx/sites-available/atlas-vpn-site.conf /etc/nginx/sites-available/atlas-vpn-site.conf.backup
sudo nano /etc/nginx/sites-available/atlas-vpn-site.conf

# Test nginx configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

## 4. Set up SSL certificates
```bash
# Obtain SSL certificates
sudo certbot --nginx -d dev.atlasvip.cloud

# Test auto-renewal
sudo certbot renew --dry-run
```

## 5. Set up systemd services

### Backend Service
```bash
sudo cat > /etc/systemd/system/vpn-backend.service << EOF
[Unit]
Description=VPN Backend Service
After=network.target redis.service postgresql.service

[Service]
Type=simple
User=root
WorkingDirectory=/opt/atlasvpn/backend
Environment=PATH=/opt/atlasvpn/backend/venv/bin
ExecStart=/opt/atlasvpn/backend/venv/bin/python main.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable vpn-backend
sudo systemctl start vpn-backend
```

### Frontend Service (if serving via systemd instead of nginx proxy)
```bash
sudo cat > /etc/systemd/system/vpn-frontend.service << EOF
[Unit]
Description=VPN Frontend Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/opt/atlasvpn/frontend
Environment=NODE_ENV=production
ExecStart=/usr/local/bin/pnpm preview --host 0.0.0.0 --port 3000
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable vpn-frontend
sudo systemctl start vpn-frontend
```

## 6. Final steps

### Check service status
```bash
# Backend logs
sudo journalctl -u vpn-backend -f

# Frontend logs (if using systemd)
sudo journalctl -u vpn-frontend -f

# Nginx logs
sudo tail -f /var/log/nginx/error.log
sudo tail -f /var/log/nginx/access.log
```

### Set proper permissions
```bash
sudo chown -R www-data:www-data /opt/atlasvpn/frontend/dist
sudo chown -R root:root /opt/atlasvpn/backend
sudo chmod +x /opt/atlasvpn/backend/venv/bin/python
```

### Configure firewall
```bash
# Allow necessary ports
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8000/tcp  # Backend (internal)
sudo ufw allow 3000/tcp  # Frontend (internal)
sudo ufw --force enable
```

### Telegram Bot Configuration
1. Create a Telegram bot via @BotFather
2. Get the bot token and username
3. Update environment variables:
   - `VITE_TELEGRAM_BOT_USERNAME` in frontend env files
   - `TELEGRAM_BOT_TOKEN` in backend .env
4. Configure webhook URL in Telegram Bot settings

### Health Check URLs
- Frontend: https://dev.atlasvip.cloud
- Backend API: https://dev.atlasvip.cloud/api/health
- API Documentation: https://dev.atlasvip.cloud/docs
- WebSocket: wss://dev.atlasvip.cloud/ws/

### Performance Optimization
```bash
# Enable nginx compression and caching
sudo nano /etc/nginx/nginx.conf
# Add/verify gzip settings are enabled

# Redis optimization
sudo nano /etc/redis/redis.conf
# Set maxmemory and maxmemory-policy

# PostgreSQL optimization
sudo nano /etc/postgresql/*/main/postgresql.conf
# Tune shared_buffers, effective_cache_size, work_mem
```
  
