# Use Node.js 20 (LTS) which supports Astro
FROM node:20-alpine

# Set working directory
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm@latest

# Copy package files
COPY package*.json pnpm-lock.yaml ./

# Install dependencies
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Expose port
EXPOSE 3005

# Start the application in development mode
CMD ["pnpm", "dev", "--host", "0.0.0.0", "--port", "3005"]
