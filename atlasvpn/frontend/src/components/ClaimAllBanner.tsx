import React, { useMemo, useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { formatCurrency } from '../utils/format';
import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>a<PERSON><PERSON><PERSON>, FaBolt } from 'react-icons/fa';
import { isLowEndDevice } from '../utils/performance';

interface ClaimAllBannerProps {
  totalAccumulatedProfit: number;
  totalHourlyProfit: number;
  onClaimAll: () => Promise<void>;
  isClaiming: boolean;
}

const ClaimAllBanner: React.FC<ClaimAllBannerProps> = ({
  totalAccumulatedProfit,
  totalHourlyProfit,
  onClaimAll,
  isClaiming,
}) => {
  const isLowEnd = useMemo(() => isLowEndDevice(), []);
  const [liveProfit, setLiveProfit] = useState(totalAccumulatedProfit);
  const lastUpdateTimeRef = useRef(Date.now());
  const initialProfitRef = useRef(totalAccumulatedProfit);
  const hourlyRateRef = useRef(totalHourlyProfit);

  // Update refs when props change
  useEffect(() => {
    initialProfitRef.current = totalAccumulatedProfit;
    lastUpdateTimeRef.current = Date.now();
    setLiveProfit(totalAccumulatedProfit);
  }, [totalAccumulatedProfit]);

  useEffect(() => {
    hourlyRateRef.current = totalHourlyProfit;
  }, [totalHourlyProfit]);

  // Calculate real-time profit
  useEffect(() => {
    // Don't run interval if hourly profit is zero or claiming
    if (hourlyRateRef.current <= 0 || isClaiming) return;

    const updateLiveProfit = () => {
      const now = Date.now();
      const hoursSinceLastUpdate = (now - lastUpdateTimeRef.current) / 3600000; // Convert ms to hours

      // Calculate accumulated profit in real-time based on hourly rate
      // Apply 3-hour cap to match server behavior
      const maxHoursToAccumulate = 3;
      const hoursSinceInitial = Math.min(hoursSinceLastUpdate, maxHoursToAccumulate);

      // Calculate new profit
      const newProfit = initialProfitRef.current + hourlyRateRef.current * hoursSinceInitial;
      setLiveProfit(newProfit);
    };

    // Update now and then set interval
    updateLiveProfit();
    const timer = setInterval(updateLiveProfit, 5000); // Update every 5 seconds
    return () => clearInterval(timer);
  }, [isClaiming]);

  // Don't render if there's nothing significant to claim
  if (liveProfit <= 0.0001) {
    return null;
  }

  // Animation properties that won't cause linter errors
  const initial = { opacity: 0, y: isLowEnd ? -5 : -10 };
  const animate = { opacity: 1, y: 0 };
  const exit = { opacity: 0, y: isLowEnd ? -5 : -10 };
  const transition = { duration: isLowEnd ? 0.2 : 0.3, ease: 'easeOut' };

  return (
    <motion.div
      initial={initial}
      animate={animate}
      exit={exit}
      transition={transition}
      className="bg-gradient-to-r from-purple-600/20 via-indigo-600/20 to-blue-600/20 border border-purple-500/30 rounded-lg p-3 sm:p-4 mb-4 shadow-lg shadow-purple-500/10"
    >
      <div className="flex flex-col sm:flex-row justify-between items-center gap-3">
        <div className="text-center sm:text-left flex flex-col sm:flex-row gap-4 w-full sm:w-auto">
          <div>
            <h3 className="text-purple-300 font-medium text-xs sm:text-sm mb-0.5">
              Current Profit
            </h3>
            <p className="text-white text-lg sm:text-xl font-bold flex items-center justify-center sm:justify-start gap-1">
              <FaCoins className="text-yellow-400 text-base sm:text-lg" />
              {formatCurrency(liveProfit)}
            </p>
          </div>
          {totalHourlyProfit > 0 && (
            <div>
              <h3 className="text-blue-300 font-medium text-xs sm:text-sm mb-0.5">Hourly Rate</h3>
              <p className="text-white text-lg sm:text-xl font-bold flex items-center justify-center sm:justify-start gap-1">
                <FaBolt className="text-yellow-400 text-base sm:text-lg" />
                {formatCurrency(totalHourlyProfit)}/hr
              </p>
            </div>
          )}
        </div>
        <button
          onClick={onClaimAll}
          disabled={isClaiming}
          className={`px-4 sm:px-5 py-2 sm:py-2.5 rounded-lg font-semibold flex items-center justify-center gap-2 text-sm transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-60 disabled:cursor-not-allowed disabled:shadow-none w-full sm:w-auto
            ${
              isClaiming
                ? 'bg-gray-600 text-white/80'
                : 'bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:from-purple-600 hover:to-blue-600 shadow-purple-500/30'
            }`}
        >
          {isClaiming ? (
            <>
              <FaSpinner className="animate-spin" />
              <span>Claiming...</span>
            </>
          ) : (
            <>
              <FaCoins />
              <span>Claim Now</span>
            </>
          )}
        </button>
      </div>
    </motion.div>
  );
};

export default React.memo(ClaimAllBanner);
