#!/bin/bash

# Clean Frontend Rebuild Script
# Use this script when making persistence or configuration changes
# to ensure Docker containers use the latest files

echo "🧹 Starting clean frontend rebuild..."

# Stop and remove existing container
echo "📦 Stopping and removing existing container..."
docker stop atlasvpn-frontend 2>/dev/null || true
docker rm atlasvpn-frontend 2>/dev/null || true

# Clear Docker build cache
echo "🗑️  Clearing Docker build cache..."
docker system prune -f

# Clear local caches
echo "🧽 Clearing local caches..."
rm -rf .nuxt .output node_modules/.cache

# Rebuild container
echo "🔨 Rebuilding container from scratch..."
cd /opt/atlasvpn
./start-frontend.sh

# Wait for container to start
echo "⏳ Waiting for container to initialize..."
sleep 30

# Verify file sync and dependencies
echo "🔍 Verifying file synchronization and dependencies..."

# Check package.json sync
LOCAL_PKG_TIME=$(stat -c %Y /opt/atlasvpn/frontend-nuxt/package.json)
DOCKER_PKG_TIME=$(docker exec atlasvpn-frontend stat -c %Y /app/package.json 2>/dev/null || echo "0")

if [ "$LOCAL_PKG_TIME" -ne "$DOCKER_PKG_TIME" ]; then
    echo "⚠️  Package.json sync issue detected - updating dependencies..."
    docker cp /opt/atlasvpn/frontend-nuxt/package.json atlasvpn-frontend:/app/package.json
    docker cp /opt/atlasvpn/frontend-nuxt/pnpm-lock.yaml atlasvpn-frontend:/app/pnpm-lock.yaml
    docker exec atlasvpn-frontend pnpm install
    echo "✅ Dependencies synchronized"
fi

# Check store files sync
LOCAL_UI_TIME=$(stat -c %Y /opt/atlasvpn/frontend-nuxt/stores/ui.ts)
DOCKER_UI_TIME=$(docker exec atlasvpn-frontend stat -c %Y /app/stores/ui.ts 2>/dev/null || echo "0")

if [ "$LOCAL_UI_TIME" -eq "$DOCKER_UI_TIME" ]; then
    echo "✅ Store files sync verified - timestamps match"
else
    echo "⚠️  Store files sync issue detected - timestamps don't match"
    echo "   Local: $(date -d @$LOCAL_UI_TIME)"
    echo "   Docker: $(date -d @$DOCKER_UI_TIME)"
    echo "   Manually copying files..."

    # Manual file copy as fallback
    docker cp /opt/atlasvpn/frontend-nuxt/stores/ui.ts atlasvpn-frontend:/app/stores/ui.ts
    docker cp /opt/atlasvpn/frontend-nuxt/stores/user.ts atlasvpn-frontend:/app/stores/user.ts
    docker cp /opt/atlasvpn/frontend-nuxt/stores/vpn.ts atlasvpn-frontend:/app/stores/vpn.ts
    docker cp /opt/atlasvpn/frontend-nuxt/plugins/pinia-persistedstate.client.ts atlasvpn-frontend:/app/plugins/pinia-persistedstate.client.ts

    echo "✅ Store files manually synchronized"
fi

# Verify critical package is available
if docker exec atlasvpn-frontend test -d /app/node_modules/pinia-plugin-persistedstate; then
    echo "✅ pinia-plugin-persistedstate package verified"
else
    echo "❌ pinia-plugin-persistedstate package missing - this will cause import errors"
fi

# Test the application
echo "🧪 Testing application..."
cd /opt/atlasvpn/frontend-nuxt
if [ -f "test-browser.cjs" ]; then
    echo "Running browser test..."
    node test-browser.cjs | grep -E "(ERROR|PASSED|FAILED|Page loaded)"
else
    echo "Browser test not available, checking with curl..."
    curl -s -I https://app.atlasvip.cloud/ | head -1
fi

echo "🎉 Clean rebuild completed!"
echo "📝 Check the logs above for any errors"
echo "🌐 Application should be available at: https://app.atlasvip.cloud/"
