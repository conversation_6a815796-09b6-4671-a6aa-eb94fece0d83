import React from 'react';
import WalletTabPage from './WalletTabPage';
import { useCurrentUserQuery } from '../hooks/authHooks';
import LoadingSpinner from './LoadingSpinner';

const WalletTabPageWrapper: React.FC = () => {
  const { data: user, isLoading: isLoadingUser } = useCurrentUserQuery();

  if (isLoadingUser) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <WalletTabPage
      isLoadingTransactions={false}
      user={user || undefined}
    />
  );
};

export default WalletTabPageWrapper; 