from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, and_
from sqlalchemy.orm import selectinload
from typing import Dict, Optional, List
from datetime import datetime, timedelta
import logging

from database import get_async_db
from models import (
    User, VPNPackage, MarzbanPanel, VPNSubscription, Transaction,
    TransactionType, package_panel_association
)
from schemas import (
    VPNPackageCreate, VPNPackageOut, VPNPackageUpdate,
    MarzbanPanelCreate, MarzbanPanelUpdate, MarzbanPanelOut,
    VPNSubscriptionOut
)
from auth import get_current_admin
from services.marzban_service import get_marzban_service

router = APIRouter(
    prefix="/vpn",
    tags=["Admin - VPN"]
)

logger = logging.getLogger(__name__)

# VPN Package Management
@router.get("/packages", response_model=List[VPNPackageOut])
async def list_packages(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 100
):
    """Get all VPN packages"""
    stmt = select(VPNPackage).options(
        selectinload(VPNPackage.allowed_panels)
    ).offset(skip).limit(limit).order_by(VPNPackage.created_at.desc())
    
    result = await db.execute(stmt)
    packages = result.scalars().all()
    return packages

@router.post("/packages", response_model=VPNPackageOut)
async def create_package(
    package: VPNPackageCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Create a new VPN package"""
    try:
        # Get allowed panels
        panel_ids = package.allowed_panel_ids
        panels = []
        if panel_ids:
            panel_stmt = select(MarzbanPanel).filter(MarzbanPanel.id.in_(panel_ids))
            panel_result = await db.execute(panel_stmt)
            panels = panel_result.scalars().all()

        # Create package
        package_data = package.dict(exclude={'allowed_panel_ids'})
        new_package = VPNPackage(**package_data)
        new_package.allowed_panels = panels
        
        db.add(new_package)
        await db.commit()
        await db.refresh(new_package)
        
        return new_package

    except Exception as e:
        await db.rollback()
        logger.error(f"Error creating package: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/packages/{package_id}", response_model=VPNPackageOut)
async def get_package(
    package_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get a specific VPN package"""
    stmt = select(VPNPackage).options(
        selectinload(VPNPackage.allowed_panels)
    ).filter(VPNPackage.id == package_id)
    
    result = await db.execute(stmt)
    package = result.scalar_one_or_none()
    
    if not package:
        raise HTTPException(status_code=404, detail="Package not found")
    
    return package

@router.put("/packages/{package_id}", response_model=VPNPackageOut)
async def update_package(
    package_id: int,
    package_update: VPNPackageUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update a VPN package"""
    try:
        # Get package
        stmt = select(VPNPackage).options(
            selectinload(VPNPackage.allowed_panels)
        ).filter(VPNPackage.id == package_id)
        
        result = await db.execute(stmt)
        package = result.scalar_one_or_none()
        
        if not package:
            raise HTTPException(status_code=404, detail="Package not found")

        # Update fields
        update_data = package_update.dict(exclude_unset=True, exclude={'allowed_panel_ids'})
        for key, value in update_data.items():
            setattr(package, key, value)

        # Update allowed panels if provided
        if package_update.allowed_panel_ids is not None:
            panel_stmt = select(MarzbanPanel).filter(
                MarzbanPanel.id.in_(package_update.allowed_panel_ids)
            )
            panel_result = await db.execute(panel_stmt)
            panels = panel_result.scalars().all()
            package.allowed_panels = panels

        await db.commit()
        await db.refresh(package)
        
        return package

    except Exception as e:
        await db.rollback()
        logger.error(f"Error updating package: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/packages/{package_id}")
async def delete_package(
    package_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a VPN package"""
    try:
        # Check if package has active subscriptions
        sub_stmt = select(func.count(VPNSubscription.id)).filter(
            VPNSubscription.package_id == package_id,
            VPNSubscription.is_active == True
        )
        sub_result = await db.execute(sub_stmt)
        active_subscriptions = sub_result.scalar()
        
        if active_subscriptions > 0:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot delete package with {active_subscriptions} active subscriptions"
            )

        # Get and delete package
        stmt = select(VPNPackage).filter(VPNPackage.id == package_id)
        result = await db.execute(stmt)
        package = result.scalar_one_or_none()
        
        if not package:
            raise HTTPException(status_code=404, detail="Package not found")

        await db.delete(package)
        await db.commit()
        
        return {"message": "Package deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error deleting package: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Marzban Panel Management
@router.get("/panels", response_model=List[MarzbanPanelOut])
async def list_panels(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get all Marzban panels"""
    stmt = select(MarzbanPanel).order_by(MarzbanPanel.created_at.desc())
    result = await db.execute(stmt)
    panels = result.scalars().all()
    return panels

@router.post("/panels", response_model=MarzbanPanelOut)
async def create_panel(
    panel: MarzbanPanelCreate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Create a new Marzban panel"""
    try:
        # Create panel
        new_panel = MarzbanPanel(**panel.dict())
        db.add(new_panel)
        await db.commit()
        await db.refresh(new_panel)
        
        return new_panel

    except Exception as e:
        await db.rollback()
        logger.error(f"Error creating panel: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/panels/{panel_id}/test")
async def test_panel_connection(
    panel_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Test connection to a Marzban panel"""
    try:
        # Get panel
        stmt = select(MarzbanPanel).filter(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        panel = result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")

        # Test connection using Marzban service
        marzban_service = get_marzban_service()
        success, message = await marzban_service.test_connection(panel)
        
        return {
            "success": success,
            "message": message,
            "panel_id": panel_id,
            "panel_name": panel.name
        }

    except Exception as e:
        logger.error(f"Error testing panel connection: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/panels/{panel_id}", response_model=MarzbanPanelOut)
async def update_panel(
    panel_id: int,
    panel_update: MarzbanPanelUpdate,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Update a Marzban panel"""
    try:
        # Get panel
        stmt = select(MarzbanPanel).filter(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        panel = result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")

        # Update fields
        update_data = panel_update.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(panel, key, value)

        await db.commit()
        await db.refresh(panel)
        
        return panel

    except Exception as e:
        await db.rollback()
        logger.error(f"Error updating panel: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/panels/{panel_id}")
async def delete_panel(
    panel_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a Marzban panel"""
    try:
        # Check if panel has active subscriptions
        sub_stmt = select(func.count(VPNSubscription.id)).join(
            VPNPackage, VPNSubscription.package_id == VPNPackage.id
        ).join(
            package_panel_association,
            VPNPackage.id == package_panel_association.c.package_id
        ).filter(
            package_panel_association.c.panel_id == panel_id,
            VPNSubscription.is_active == True
        )
        sub_result = await db.execute(sub_stmt)
        active_subscriptions = sub_result.scalar()
        
        if active_subscriptions > 0:
            raise HTTPException(
                status_code=400,
                detail=f"Cannot delete panel with {active_subscriptions} active subscriptions"
            )

        # Get and delete panel
        stmt = select(MarzbanPanel).filter(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        panel = result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")

        await db.delete(panel)
        await db.commit()
        
        return {"message": "Panel deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error deleting panel: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/panels/{panel_id}/stats")
async def get_panel_stats(
    panel_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get statistics for a Marzban panel"""
    try:
        # Get panel
        stmt = select(MarzbanPanel).filter(MarzbanPanel.id == panel_id)
        result = await db.execute(stmt)
        panel = result.scalar_one_or_none()
        
        if not panel:
            raise HTTPException(status_code=404, detail="Panel not found")

        # Get subscription statistics for this panel
        sub_stats_stmt = select(
            func.count(VPNSubscription.id).label('total_subscriptions'),
            func.count(
                func.case(
                    (VPNSubscription.is_active == True, 1),
                    else_=None
                )
            ).label('active_subscriptions'),
            func.count(
                func.case(
                    (VPNSubscription.expires_at > datetime.utcnow(), 1),
                    else_=None
                )
            ).label('valid_subscriptions')
        ).select_from(
            VPNSubscription.__table__.join(
                VPNPackage.__table__,
                VPNSubscription.package_id == VPNPackage.id
            ).join(
                package_panel_association,
                VPNPackage.id == package_panel_association.c.package_id
            )
        ).filter(
            package_panel_association.c.panel_id == panel_id
        )
        
        sub_stats_result = await db.execute(sub_stats_stmt)
        sub_stats = sub_stats_result.first()

        return {
            "panel_id": panel_id,
            "panel_name": panel.name,
            "panel_url": panel.api_url,
            "is_active": panel.is_active,
            "total_subscriptions": sub_stats.total_subscriptions if sub_stats else 0,
            "active_subscriptions": sub_stats.active_subscriptions if sub_stats else 0,
            "valid_subscriptions": sub_stats.valid_subscriptions if sub_stats else 0,
            "created_at": panel.created_at.isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting panel stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# VPN Subscription Management
@router.get("/subscriptions", response_model=List[VPNSubscriptionOut])
async def list_subscriptions(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 100,
    user_id: Optional[int] = None,
    is_active: Optional[bool] = None,
    sort_by: str = "created_at",
    sort_dir: str = "desc"
):
    """Get all VPN subscriptions with filtering and sorting"""
    stmt = select(VPNSubscription).options(
        selectinload(VPNSubscription.user),
        selectinload(VPNSubscription.package).selectinload(VPNPackage.allowed_panels)
    )
    
    # Apply filters
    if user_id:
        stmt = stmt.filter(VPNSubscription.user_id == user_id)
    if is_active is not None:
        stmt = stmt.filter(VPNSubscription.is_active == is_active)
    
    # Apply sorting
    if hasattr(VPNSubscription, sort_by):
        order_column = getattr(VPNSubscription, sort_by)
        if sort_dir.lower() == "desc":
            stmt = stmt.order_by(desc(order_column))
        else:
            stmt = stmt.order_by(order_column)
    
    stmt = stmt.offset(skip).limit(limit)
    
    result = await db.execute(stmt)
    subscriptions = result.scalars().all()
    return subscriptions

@router.post("/subscriptions/renew/{subscription_id}")
async def renew_subscription(
    subscription_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    force: bool = False
):
    """Renew a VPN subscription"""
    try:
        # Get subscription with user and package
        stmt = select(VPNSubscription).options(
            selectinload(VPNSubscription.user),
            selectinload(VPNSubscription.package)
        ).filter(VPNSubscription.id == subscription_id)
        
        result = await db.execute(stmt)
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")

        user = subscription.user
        package = subscription.package

        # Check if subscription is already active and not expired
        if not force and subscription.is_active and subscription.expires_at > datetime.utcnow():
            raise HTTPException(
                status_code=400,
                detail="Subscription is already active and not expired"
            )

        # Calculate new expiry
        if subscription.expires_at > datetime.utcnow():
            # Extend from current expiry
            new_expiry = subscription.expires_at + timedelta(days=package.expire_days)
        else:
            # Start from now
            new_expiry = datetime.utcnow() + timedelta(days=package.expire_days)

        # Update subscription
        subscription.expires_at = new_expiry
        subscription.is_active = True
        subscription.data_limit = package.data_limit
        subscription.data_used = 0  # Reset data usage

        # Create transaction record
        transaction = Transaction(
            user_id=user.id,
            type=TransactionType.subscription_renewal,
            amount=-package.price,  # Negative for expense
            description=f"VPN subscription renewal: {package.name}",
            balance_after=user.wallet_balance,
            package_name=package.name,
            package_price=package.price,
            package_data_limit=package.data_limit,
            package_expire_days=package.expire_days,
            subscription_id=subscription.id,
            reseller_id=current_user.id
        )
        
        db.add(transaction)
        await db.commit()
        await db.refresh(subscription)

        return {
            "message": "Subscription renewed successfully",
            "subscription_id": subscription.id,
            "new_expiry": new_expiry.isoformat(),
            "is_active": subscription.is_active
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error renewing subscription: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/subscriptions/create/{user_id}")
async def admin_create_subscription(
    user_id: int,
    package_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days_override: Optional[int] = None,
    charge_wallet: bool = False
):
    """Create a new VPN subscription for a user"""
    try:
        # Get user
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")

        # Get package
        package_stmt = select(VPNPackage).filter(VPNPackage.id == package_id)
        package_result = await db.execute(package_stmt)
        package = package_result.scalar_one_or_none()
        
        if not package:
            raise HTTPException(status_code=404, detail="Package not found")

        # Check if user already has an active subscription
        existing_stmt = select(VPNSubscription).filter(
            VPNSubscription.user_id == user_id,
            VPNSubscription.is_active == True,
            VPNSubscription.expires_at > datetime.utcnow()
        )
        existing_result = await db.execute(existing_stmt)
        existing_subscription = existing_result.scalar_one_or_none()
        
        if existing_subscription:
            raise HTTPException(
                status_code=400,
                detail="User already has an active subscription"
            )

        # Charge wallet if requested
        if charge_wallet:
            if user.wallet_balance < package.price:
                raise HTTPException(
                    status_code=400,
                    detail="Insufficient wallet balance"
                )
            user.wallet_balance -= package.price

        # Create subscription
        expiry_days = days_override if days_override else package.expire_days
        expires_at = datetime.utcnow() + timedelta(days=expiry_days)
        
        # Generate unique username
        import secrets
        import string
        marzban_username = f"user_{user_id}_{secrets.token_hex(4)}"
        
        subscription = VPNSubscription(
            user_id=user.id,
            package_id=package.id,
            marzban_username=marzban_username,
            subscription_url=f"subscription_url_placeholder_{subscription.id}",
            expires_at=expires_at,
            data_limit=package.data_limit,
            data_used=0,
            is_active=True
        )
        
        db.add(subscription)
        await db.flush()  # To get the subscription ID
        
        # Update subscription URL with actual ID
        subscription.subscription_url = f"vless://subscription_{subscription.id}"

        # Create transaction record
        transaction_type = TransactionType.subscription_purchase if charge_wallet else TransactionType.admin_adjustment
        amount = -package.price if charge_wallet else 0
        
        transaction = Transaction(
            user_id=user.id,
            type=transaction_type,
            amount=amount,
            description=f"Admin created VPN subscription: {package.name}",
            balance_after=user.wallet_balance,
            package_name=package.name,
            package_price=package.price,
            package_data_limit=package.data_limit,
            package_expire_days=package.expire_days,
            subscription_id=subscription.id,
            reseller_id=current_user.id
        )
        
        db.add(transaction)
        await db.commit()
        await db.refresh(subscription)

        return {
            "message": "Subscription created successfully",
            "subscription_id": subscription.id,
            "marzban_username": subscription.marzban_username,
            "expires_at": subscription.expires_at.isoformat(),
            "wallet_charged": charge_wallet,
            "new_balance": user.wallet_balance if charge_wallet else None
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error creating subscription: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/subscriptions/{subscription_id}/stats")
async def get_subscription_stats(
    subscription_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Get detailed stats for a specific subscription"""
    try:
        # Get subscription with user and package
        stmt = select(VPNSubscription).options(
            selectinload(VPNSubscription.user),
            selectinload(VPNSubscription.package)
        ).filter(VPNSubscription.id == subscription_id)
        
        result = await db.execute(stmt)
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")

        # Calculate usage statistics
        data_used_gb = subscription.data_used / (1024**3) if subscription.data_used else 0
        data_limit_gb = subscription.data_limit / (1024**3) if subscription.data_limit else 0
        usage_percentage = (data_used_gb / data_limit_gb * 100) if data_limit_gb > 0 else 0
        
        # Time calculations
        now = datetime.utcnow()
        days_remaining = (subscription.expires_at - now).days if subscription.expires_at > now else 0
        days_since_created = (now - subscription.created_at).days
        
        return {
            "subscription_id": subscription.id,
            "user": {
                "id": subscription.user.id,
                "username": subscription.user.username
            },
            "package": {
                "id": subscription.package.id,
                "name": subscription.package.name,
                "price": subscription.package.price
            },
            "status": {
                "is_active": subscription.is_active,
                "is_expired": subscription.expires_at <= now,
                "days_remaining": days_remaining,
                "days_since_created": days_since_created
            },
            "usage": {
                "data_used_gb": round(data_used_gb, 2),
                "data_limit_gb": round(data_limit_gb, 2),
                "usage_percentage": round(usage_percentage, 2),
                "remaining_gb": round(data_limit_gb - data_used_gb, 2)
            },
            "dates": {
                "created_at": subscription.created_at.isoformat(),
                "expires_at": subscription.expires_at.isoformat()
            },
            "marzban_username": subscription.marzban_username
        }

    except Exception as e:
        logger.error(f"Error getting subscription stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/subscriptions/{subscription_id}")
async def delete_subscription(
    subscription_id: int,
    delete_from_panel: bool = Query(True, description="Whether to delete the user from Marzban panel"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a VPN subscription"""
    try:
        # Get subscription
        stmt = select(VPNSubscription).options(
            selectinload(VPNSubscription.user),
            selectinload(VPNSubscription.package)
        ).filter(VPNSubscription.id == subscription_id)
        
        result = await db.execute(stmt)
        subscription = result.scalar_one_or_none()
        
        if not subscription:
            raise HTTPException(status_code=404, detail="Subscription not found")

        # Delete from Marzban panel if requested
        if delete_from_panel:
            try:
                marzban_service = get_marzban_service()
                # This would need implementation in the Marzban service
                # await marzban_service.delete_user(subscription.marzban_username)
                logger.info(f"Would delete {subscription.marzban_username} from Marzban panel")
            except Exception as e:
                logger.warning(f"Failed to delete from panel: {str(e)}")

        # Create cancellation transaction
        transaction = Transaction(
            user_id=subscription.user_id,
            type=TransactionType.admin_adjustment,
            amount=0,
            description=f"Admin deleted VPN subscription: {subscription.package.name}",
            balance_after=subscription.user.wallet_balance,
            package_name=subscription.package.name,
            subscription_id=subscription.id,
            reseller_id=current_user.id
        )
        
        db.add(transaction)
        
        # Delete subscription
        await db.delete(subscription)
        await db.commit()

        return {
            "message": "Subscription deleted successfully",
            "deleted_from_panel": delete_from_panel,
            "marzban_username": subscription.marzban_username
        }

    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error deleting subscription: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/stats")
async def get_vpn_stats(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Get comprehensive VPN system statistics
    
    Features:
    - Package distribution analytics
    - Panel performance metrics
    - Subscription analytics
    - Revenue statistics
    - Usage trends
    """
    try:
        # Total packages
        stmt_total_packages = select(func.count(VPNPackage.id))
        result_total_packages = await db.execute(stmt_total_packages)
        total_packages = result_total_packages.scalar() or 0
        
        # Active packages
        stmt_active_packages = select(func.count(VPNPackage.id)).where(
            VPNPackage.is_active == True
        )
        result_active_packages = await db.execute(stmt_active_packages)
        active_packages = result_active_packages.scalar() or 0
        
        # Total panels
        stmt_total_panels = select(func.count(MarzbanPanel.id))
        result_total_panels = await db.execute(stmt_total_panels)
        total_panels = result_total_panels.scalar() or 0
        
        # Online panels (active panels)
        stmt_online_panels = select(func.count(MarzbanPanel.id)).where(
            MarzbanPanel.is_active == True
        )
        result_online_panels = await db.execute(stmt_online_panels)
        online_panels = result_online_panels.scalar() or 0
        
        # Total subscriptions
        stmt_total_subscriptions = select(func.count(VPNSubscription.id))
        result_total_subscriptions = await db.execute(stmt_total_subscriptions)
        total_subscriptions = result_total_subscriptions.scalar() or 0
        
        # Active subscriptions
        stmt_active_subscriptions = select(func.count(VPNSubscription.id)).where(
            VPNSubscription.is_active == True
        )
        result_active_subscriptions = await db.execute(stmt_active_subscriptions)
        active_subscriptions = result_active_subscriptions.scalar() or 0
        
        # VPN revenue (total)
        stmt_vpn_revenue = select(func.sum(Transaction.amount)).where(
            Transaction.type.in_([
                TransactionType.subscription_purchase,
                TransactionType.subscription_renewal
            ]),
            Transaction.status == "completed"
        )
        result_vpn_revenue = await db.execute(stmt_vpn_revenue)
        total_revenue = result_vpn_revenue.scalar() or 0
        
        # Data usage (approximate - would need real integration)
        data_usage_gb = 0  # Placeholder - would need Marzban panel integration
        
        # Package popularity
        stmt_package_popularity = select(
            VPNPackage.name,
            VPNPackage.price,
            func.count(VPNSubscription.id).label('subscription_count')
        ).join(
            VPNSubscription, VPNPackage.id == VPNSubscription.package_id, isouter=True
        ).group_by(
            VPNPackage.id, VPNPackage.name, VPNPackage.price
        ).order_by(
            func.count(VPNSubscription.id).desc()
        )
        result_package_popularity = await db.execute(stmt_package_popularity)
        package_popularity = [
            {
                "package_name": name,
                "price": float(price),
                "subscription_count": count
            }
            for name, price, count in result_package_popularity.all()
        ]
        
        # Revenue by period (last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        stmt_recent_revenue = select(func.sum(Transaction.amount)).where(
            Transaction.type.in_([
                TransactionType.subscription_purchase,
                TransactionType.subscription_renewal
            ]),
            Transaction.status == "completed",
            Transaction.created_at >= thirty_days_ago
        )
        result_recent_revenue = await db.execute(stmt_recent_revenue)
        revenue_30_days = result_recent_revenue.scalar() or 0
        
        # New subscriptions this month
        stmt_new_subs = select(func.count(VPNSubscription.id)).where(
            VPNSubscription.created_at >= thirty_days_ago
        )
        result_new_subs = await db.execute(stmt_new_subs)
        new_subscriptions_30_days = result_new_subs.scalar() or 0
        
        # Expiring subscriptions (next 7 days)
        seven_days_ahead = datetime.utcnow() + timedelta(days=7)
        stmt_expiring = select(func.count(VPNSubscription.id)).where(
            VPNSubscription.expires_at <= seven_days_ahead,
            VPNSubscription.is_active == True
        )
        result_expiring = await db.execute(stmt_expiring)
        expiring_subscriptions = result_expiring.scalar() or 0
        
        # Panel status summary
        stmt_panel_status = select(
            MarzbanPanel.name,
            MarzbanPanel.is_active,
            func.count(VPNSubscription.id).label('user_count')
        ).join(
            VPNPackage, MarzbanPanel.packages
        ).join(
            VPNSubscription, VPNPackage.id == VPNSubscription.package_id, isouter=True
        ).group_by(
            MarzbanPanel.id, MarzbanPanel.name, MarzbanPanel.is_active
        )
        result_panel_status = await db.execute(stmt_panel_status)
        panel_status = [
            {
                "panel_name": name,
                "is_active": is_active,
                "user_count": count,
                "status": "online" if is_active else "offline"
            }
            for name, is_active, count in result_panel_status.all()
        ]
        
        vpn_stats = {
            "overview": {
                "total_packages": total_packages,
                "active_packages": active_packages,
                "total_panels": total_panels,
                "online_panels": online_panels,
                "total_subscriptions": total_subscriptions,
                "active_subscriptions": active_subscriptions,
                "expiring_subscriptions": expiring_subscriptions
            },
            "financial": {
                "total_revenue": float(total_revenue),
                "revenue_30_days": float(revenue_30_days),
                "new_subscriptions_30_days": new_subscriptions_30_days
            },
            "usage": {
                "data_usage_gb": data_usage_gb,
                "avg_subscriptions_per_package": round(
                    active_subscriptions / active_packages if active_packages > 0 else 0, 2
                )
            },
            "package_popularity": package_popularity,
            "panel_status": panel_status,
            "generated_at": datetime.utcnow().isoformat()
        }
        
        logger.info(f"VPN statistics generated by admin {current_user.username}")
        return vpn_stats
        
    except Exception as e:
        logger.error(f"Error getting VPN stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get VPN statistics"
        )

@router.get("/packages", response_model=List[VPNPackageOut])
async def get_packages(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """Get all VPN packages"""
    try:
        stmt = select(VPNPackage).options(
            selectinload(VPNPackage.allowed_panels)
        ).order_by(VPNPackage.created_at.desc())
        
        result = await db.execute(stmt)
        packages = result.scalars().all()
        
        return packages
    except Exception as e:
        logger.error(f"Error getting VPN packages: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 