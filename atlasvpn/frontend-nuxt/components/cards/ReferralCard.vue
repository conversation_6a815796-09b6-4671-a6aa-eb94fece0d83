<template>
  <div class="referral-card relative overflow-hidden rounded-xl border border-purple-500/20 bg-gradient-to-br from-purple-900/20 to-blue-900/20 backdrop-blur-sm shadow-lg shadow-purple-500/15">
    <!-- Glow Effect -->
    <div class="absolute top-0 right-0 w-32 h-32 bg-purple-500/10 rounded-full blur-3xl -translate-y-16 translate-x-16"></div>
    
    <!-- Corner Accents -->
    <div class="absolute top-0 left-0 w-8 h-8 opacity-70">
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
        <path d="M 0 12 V 0 H 12" stroke="#8b5cf6" stroke-width="1" />
        <path d="M 0 8 V 0 H 8" stroke="#8b5cf6" stroke-width="2" />
      </svg>
    </div>

    <div class="relative p-6">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center animate-float">
            <Icon name="i-heroicons-user-group" class="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 class="text-xl font-bold text-white">Invite Friends</h3>
            <p class="text-purple-300 text-sm">Earn rewards together</p>
          </div>
        </div>
        
        <UButton
          @click="showQRCode = !showQRCode"
          variant="outline"
          size="sm"
          :icon="showQRCode ? 'i-heroicons-x-mark' : 'i-heroicons-qr-code'"
        >
          {{ showQRCode ? 'Hide' : 'QR Code' }}
        </UButton>
      </div>

      <!-- QR Code Section -->
      <div v-if="showQRCode" class="mb-6 text-center">
        <div class="bg-white p-4 rounded-lg inline-block">
          <QRCodeCanvas
            :value="referralLink"
            :size="150"
            :margin="2"
            level="M"
          />
        </div>
        <p class="text-gray-400 text-xs mt-2">Scan to join with your referral link</p>
      </div>

      <!-- Referral Link -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-purple-300 mb-2">
          Your Referral Link
        </label>
        <div class="flex items-center space-x-2">
          <div class="flex-1 bg-gray-800/50 rounded-lg p-3 border border-gray-700">
            <div class="text-white text-sm font-mono break-all">
              {{ referralLink }}
            </div>
          </div>
          <UButton
            @click="copyReferralLink"
            variant="outline"
            size="sm"
            :icon="linkCopied ? 'i-heroicons-check' : 'i-heroicons-clipboard'"
            :color="linkCopied ? 'green' : 'primary'"
          >
            {{ linkCopied ? 'Copied!' : 'Copy' }}
          </UButton>
        </div>
      </div>

      <!-- Referral Code -->
      <div class="mb-6">
        <label class="block text-sm font-medium text-purple-300 mb-2">
          Referral Code
        </label>
        <div class="flex items-center space-x-2">
          <div class="flex-1 bg-gray-800/50 rounded-lg p-3 border border-gray-700 text-center">
            <div class="text-2xl font-bold text-purple-400 font-mono">
              {{ referralCode }}
            </div>
          </div>
          <UButton
            @click="copyReferralCode"
            variant="outline"
            size="sm"
            :icon="codeCopied ? 'i-heroicons-check' : 'i-heroicons-clipboard'"
            :color="codeCopied ? 'green' : 'primary'"
          >
            {{ codeCopied ? 'Copied!' : 'Copy' }}
          </UButton>
        </div>
      </div>

      <!-- Share Buttons -->
      <div class="grid grid-cols-2 gap-3 mb-6">
        <UButton
          @click="shareOnTelegram"
          color="primary"
          class="w-full"
        >
          <template #leading>
            <Icon name="i-simple-icons-telegram" class="w-4 h-4" />
          </template>
          Share on Telegram
        </UButton>
        
        <UButton
          @click="shareGeneric"
          variant="outline"
          class="w-full"
        >
          <template #leading>
            <Icon name="i-heroicons-share" class="w-4 h-4" />
          </template>
          Share Link
        </UButton>
      </div>

      <!-- Referral Stats -->
      <div class="grid grid-cols-3 gap-4">
        <div class="text-center">
          <div class="text-2xl font-bold text-white mb-1">{{ totalReferrals }}</div>
          <div class="text-xs text-purple-300">Friends</div>
        </div>
        
        <div class="text-center">
          <div class="text-2xl font-bold text-green-400 mb-1">
            ${{ totalEarned.toFixed(2) }}
          </div>
          <div class="text-xs text-purple-300">Earned</div>
        </div>
        
        <div class="text-center">
          <div class="text-2xl font-bold text-yellow-400 mb-1">
            ${{ nextReward.toFixed(2) }}
          </div>
          <div class="text-xs text-purple-300">Next Reward</div>
        </div>
      </div>

      <!-- Reward Info -->
      <div class="mt-6 bg-purple-500/10 rounded-lg p-4 border border-purple-500/20">
        <div class="flex items-center space-x-2 mb-2">
          <Icon name="i-heroicons-gift" class="w-5 h-5 text-purple-400" />
          <span class="text-purple-300 font-medium">Referral Rewards</span>
        </div>
        <div class="text-sm text-gray-300">
          • Earn $5 when a friend joins and makes their first purchase
        </div>
        <div class="text-sm text-gray-300">
          • Your friend gets a 10% discount on their first order
        </div>
        <div class="text-sm text-gray-300">
          • Unlimited referrals, unlimited earnings!
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  referralCode: string
  referralLink: string
  totalReferrals: number
  totalEarned: number
  nextReward?: number
}

const props = withDefaults(defineProps<Props>(), {
  nextReward: 5.0
})

const emit = defineEmits<{
  share: [platform: string]
}>()

const showQRCode = ref(false)
const linkCopied = ref(false)
const codeCopied = ref(false)

const copyReferralLink = async () => {
  try {
    await navigator.clipboard.writeText(props.referralLink)
    linkCopied.value = true
    setTimeout(() => {
      linkCopied.value = false
    }, 2000)
    
    const uiStore = useUiStore()
    uiStore.showSuccessToast('Referral link copied!')
  } catch (error) {
    console.error('Failed to copy:', error)
  }
}

const copyReferralCode = async () => {
  try {
    await navigator.clipboard.writeText(props.referralCode)
    codeCopied.value = true
    setTimeout(() => {
      codeCopied.value = false
    }, 2000)
    
    const uiStore = useUiStore()
    uiStore.showSuccessToast('Referral code copied!')
  } catch (error) {
    console.error('Failed to copy:', error)
  }
}

const shareOnTelegram = () => {
  const text = `🚀 Join me on AtlasVPN and get secure VPN access!\n\n💰 Use my referral code: ${props.referralCode}\n🔗 Or click: ${props.referralLink}\n\n🎁 You'll get 10% off your first purchase!`
  const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(props.referralLink)}&text=${encodeURIComponent(text)}`
  
  if (typeof window !== 'undefined') {
    window.open(telegramUrl, '_blank')
  }
  
  emit('share', 'telegram')
}

const shareGeneric = async () => {
  const shareData = {
    title: 'Join AtlasVPN',
    text: `Join me on AtlasVPN and get secure VPN access! Use code: ${props.referralCode}`,
    url: props.referralLink
  }
  
  try {
    if (navigator.share) {
      await navigator.share(shareData)
    } else {
      // Fallback to copying
      await copyReferralLink()
    }
    emit('share', 'generic')
  } catch (error) {
    console.error('Failed to share:', error)
  }
}
</script>

<style scoped>
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.referral-card {
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%);
}
</style>
