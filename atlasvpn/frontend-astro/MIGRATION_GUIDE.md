# API Hooks Migration Guide

This guide helps you migrate from the old hook files to the new centralized API structure.

## Overview

All hooks have been consolidated into a single, well-organized API client structure located in `/src/api/`. This improves performance, maintainability, and provides better TypeScript support.

## Migration Steps

### 1. Update Import Statements

**Before:**
```typescript
// Old imports from separate files
import { useCurrentUserQuery, useTelegramLoginMutation } from '../hooks/authHooks';
import { useTasksQuery, useStartTaskMutation } from '../hooks/taskHooks';
import { useUserTransactionsQuery } from '../hooks/transactionHooks';
import { useVpnPackagesQuery } from '../hooks/vpnHooks';
import { useCardsQuery, useClaimProfitsMutation } from '../hooks/useApiQueries';
```

**After:**
```typescript
// New centralized imports
import {
  useCurrentUserQuery,
  useTelegramLoginMutation,
  useTasksQuery,
  useStartTaskMutation,
  useUserTransactionsQuery,
  useVpnPackagesQuery,
  useAllCardsQuery,
  useClaimAllProfitsMutation
} from '../api/hooks';

// Or import everything
import * as apiHooks from '../api/hooks';
```

### 2. Hook Name Changes

Some hooks have been renamed for consistency:

| Old Name | New Name |
|----------|----------|
| `useCardsQuery` | `useAllCardsQuery` |
| `useClaimProfitsMutation` | `useClaimAllProfitsMutation` |
| `useCompleteTaskMutation` | `useClaimTaskMutation` |
| `useVpnSubscriptionsQuery` | `useVpnSubscriptionsQuery` (no change) |

### 3. Utility Functions

All utility functions are now available from the hooks file:

**Before:**
```typescript
// From taskHooks.ts
import { getTaskStatusInfo, formatTaskCompletionTime } from '../hooks/taskHooks';

// From transactionHooks.ts
import { getTransactionDetails, formatTransactionAmount } from '../hooks/transactionHooks';
```

**After:**
```typescript
// All utilities from one place
import {
  getTaskStatusInfo,
  formatTaskCompletionTime,
  getTransactionDetails,
  formatTransactionAmount,
  getTransactionStats,
  calculateTaskProgress,
  canStartTask,
  canVerifyTask,
  canClaimTask,
  getTaskTypeDisplayName,
  useApiErrorHandler
} from '../api/hooks';
```

### 4. New Features Available

The new API structure provides additional features:

#### Enhanced VPN Functionality
```typescript
import { useVpnDashboardQuery } from '../api/hooks';

// Unified VPN data query
const { subscriptions, packages, isLoading, error, refetch } = useVpnDashboardQuery();
```

#### Better Error Handling
```typescript
import { useApiErrorHandler } from '../api/hooks';

const handleError = useApiErrorHandler();
const errorMessage = handleError(error);
```

#### Transaction Statistics
```typescript
import { getTransactionStats } from '../api/hooks';

const stats = getTransactionStats(transactions);
console.log(stats.totalIncome, stats.totalExpenses, stats.typeBreakdown);
```

### 5. Type Imports

Types are now centralized as well:

**Before:**
```typescript
// Types were scattered across different files
import { User } from '../services/api';
import { Task } from '../hooks/taskHooks';
```

**After:**
```typescript
// All types from one place
import {
  User,
  Task,
  Transaction,
  VpnPackage,
  VpnSubscription,
  Card,
  UserStats
} from '../api/types';
```

### 6. Service Layer Access

If you need direct service access:

```typescript
// Access individual services
import { authService, userService, taskService } from '../api/services';

// Or access the complete API client
import { client } from '../api/client';
```

## Benefits of Migration

1. **Performance**: Reduced bundle size and better tree-shaking
2. **Maintainability**: Single source of truth for API logic
3. **Type Safety**: Better TypeScript support and IntelliSense
4. **Consistency**: Unified patterns and error handling
5. **Documentation**: Comprehensive documentation in one place

## Common Issues and Solutions

### Issue: Import errors after migration
**Solution**: Make sure to update all import paths from `../hooks/*` to `../api/hooks`

### Issue: Hook not found
**Solution**: Check the hook name mapping table above, some hooks were renamed

### Issue: Type errors
**Solution**: Import types from `../api/types` instead of individual hook files

### Issue: Missing utility functions
**Solution**: All utilities are now in `../api/hooks`, check the complete export list

## Need Help?

Refer to the comprehensive documentation in `/src/api/README.md` for detailed usage examples and API reference.