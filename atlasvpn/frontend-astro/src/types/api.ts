// API Response Types
export interface ApiResponse<T = any> {
  status: 'success' | 'error' | 'authenticated' | 'unauthenticated';
  message?: string;
  data?: T;
  error?: string;
}

// User Types
export interface User {
  id: number;
  telegram_id: number;
  username?: string;
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  balance: number;
  total_earned: number;
  referral_code: string;
  created_at: string;
  updated_at: string;
}

// Dashboard Stats
export interface DashboardStats {
  balance: number;
  total_earned: number;
  cards_count: number;
  hourly_profit: number;
  total_referrals: number;
  active_tasks: number;
  vpn_subscriptions: number;
}

// Card Types
export interface UserCard {
  id: number;
  card_id: number;
  name: string;
  emoji: string;
  level: number;
  hourly_profit: number;
  upgrade_cost: number;
  max_level: number;
  category: string;
  description?: string;
}

export interface CardCatalog {
  id: number;
  name: string;
  emoji: string;
  description: string;
  base_cost: number;
  base_profit: number;
  category: string;
  max_level: number;
  unlock_requirement?: string;
}

// Task Types
export type TaskStatus = 'available' | 'in_progress' | 'completed' | 'claimed';
export type TaskType = 'daily' | 'social' | 'referral' | 'special';

export interface Task {
  id: number;
  title: string;
  description: string;
  type: TaskType;
  status: TaskStatus;
  reward: number;
  icon: string;
  url?: string;
  requirement?: string;
  expires_at?: string;
  created_at: string;
}

// Transaction Types
export interface Transaction {
  id: number;
  type: 'earn' | 'spend' | 'referral' | 'task' | 'card_upgrade' | 'vpn_purchase';
  amount: number;
  description: string;
  created_at: string;
  metadata?: Record<string, any>;
}

// Referral Types
export interface ReferralInfo {
  referral_code: string;
  total_referrals: number;
  total_earned: number;
  referral_link: string;
}

export interface ReferredUser {
  id: number;
  username?: string;
  first_name?: string;
  earned_amount: number;
  joined_at: string;
  is_active: boolean;
}

// VPN Types
export interface VPNSubscription {
  id: number;
  package_id: number;
  package_name: string;
  status: 'active' | 'expired' | 'cancelled';
  expires_at: string;
  created_at: string;
}

export interface VPNPackage {
  id: number;
  name: string;
  description: string;
  price: number;
  duration_days: number;
  features: string[];
  is_popular?: boolean;
}

// Chat Types
export interface ChatMessage {
  id: number;
  user_id: number;
  username?: string;
  message: string;
  timestamp: string;
  type: 'text' | 'system' | 'emoji';
}

// Verse (3D World) Types
export interface LandPlot {
  id: number;
  x: number;
  y: number;
  z: number;
  owner_id?: number;
  owner_username?: string;
  price?: number;
  is_for_sale: boolean;
  land_type: 'basic' | 'premium' | 'special';
  metadata?: Record<string, any>;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: 'profit_update' | 'task_completed' | 'new_referral' | 'chat_message' | 'system_notification';
  data: any;
  timestamp: string;
}

// Form Types
export interface LoginForm {
  init_data: string;
  auth_method: 'telegram';
}

export interface UserSetupForm {
  username?: string;
  avatar_preference?: string;
  notification_preferences?: {
    tasks: boolean;
    referrals: boolean;
    system: boolean;
  };
}

// Error Types
export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}

// Pagination Types
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

// Filter Types
export interface TaskFilter {
  type?: TaskType;
  status?: TaskStatus;
  category?: string;
}

export interface TransactionFilter {
  type?: Transaction['type'];
  date_from?: string;
  date_to?: string;
}

export interface CardFilter {
  category?: string;
  min_level?: number;
  max_level?: number;
}
