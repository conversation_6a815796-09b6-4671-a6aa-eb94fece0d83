#!/usr/bin/env python3
"""
Migration to add is_public and is_read columns to the chat_messages table if they don't exist.
This ensures the schema matches the model definition.
"""

import sqlite3
import os
import sys
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def main():
    """
    Main function to add missing columns to chat_messages table
    """
    # Get the path to the database file
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'users.db')
    
    if not os.path.exists(db_path):
        logger.error(f"Database file not found at: {db_path}")
        sys.exit(1)
    
    logger.info(f"Using database at: {db_path}")
    
    try:
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get existing columns
        cursor.execute("PRAGMA table_info(chat_messages)")
        existing_columns = [col[1] for col in cursor.fetchall()]
        logger.info(f"Existing columns: {existing_columns}")
        
        # Check and add is_public column if it doesn't exist
        if 'is_public' not in existing_columns:
            logger.info("Adding missing column: is_public")
            cursor.execute("""
                ALTER TABLE chat_messages 
                ADD COLUMN is_public BOOLEAN NOT NULL DEFAULT 0
            """)
            logger.info("Added is_public column")
        else:
            logger.info("Column is_public already exists")
        
        # Check and add is_read column if it doesn't exist
        if 'is_read' not in existing_columns:
            logger.info("Adding missing column: is_read")
            cursor.execute("""
                ALTER TABLE chat_messages 
                ADD COLUMN is_read BOOLEAN NOT NULL DEFAULT 0
            """)
            logger.info("Added is_read column")
        else:
            logger.info("Column is_read already exists")
        
        # Commit the changes
        conn.commit()
        logger.info("Database migration completed successfully")
        
    except Exception as e:
        logger.error(f"Error during migration: {e}")
        sys.exit(1)
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    main() 