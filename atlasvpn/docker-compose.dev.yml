# Development Docker Compose Override
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up frontend

services:
  frontend:
    build:
      context: ./frontend-nuxt
      dockerfile: Dockerfile.dev
    container_name: atlasvpn-frontend-dev
    environment:
      - NODE_ENV=development
      - PORT=3005
      - NITRO_PORT=3005
      - NUXT_PUBLIC_API_URL=https://app.atlasvip.cloud/api
      - NUXT_PUBLIC_WS_BASE_URL=wss://app.atlasvip.cloud/ws
      - NUXT_PUBLIC_TELEGRAM_WEBAPP_URL=https://app.atlasvip.cloud
      - NUXT_PUBLIC_TELEGRAM_BOT_USERNAME=${TELEGRAM_BOT_USERNAME}
      # Development-specific environment variables
      - NUXT_DEVTOOLS=true
      - NUXT_DEV_SSR_LOGS=true
    volumes:
      # Mount source code for hot reload
      - ./frontend-nuxt:/app
      # Prevent node_modules from being overwritten
      - /app/node_modules
      # Cache pnpm store for faster installs
      - pnpm-store:/root/.local/share/pnpm/store
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"
      
      # Main Frontend Route - Exclude API and WS paths
      - "traefik.http.routers.frontend-dev.rule=Host(`app.atlasvip.cloud`) && !PathPrefix(`/api/`) && !PathPrefix(`/ws/`) && !PathPrefix(`/docs`) && !PathPrefix(`/redoc`) && !PathPrefix(`/openapi.json`)"
      - "traefik.http.routers.frontend-dev.entrypoints=websecure"
      - "traefik.http.routers.frontend-dev.tls=true"
      - "traefik.http.routers.frontend-dev.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend-dev.loadbalancer.server.port=3005"
      - "traefik.http.routers.frontend-dev.priority=1"
      
      # Development Files - NUXT AUTOMATIC PROXYING!
      - "traefik.http.routers.dev-files.rule=Host(`app.atlasvip.cloud`) && (PathPrefix(`/_nuxt/`) || PathPrefix(`/node_modules/`) || PathPrefix(`/__nuxt_devtools__/`))"
      - "traefik.http.routers.dev-files.entrypoints=websecure"
      - "traefik.http.routers.dev-files.tls=true"
      - "traefik.http.routers.dev-files.tls.certresolver=letsencrypt"
      - "traefik.http.routers.dev-files.priority=10"

      # HMR WebSocket - NUXT AUTOMATIC!
      - "traefik.http.routers.hmr.rule=Host(`app.atlasvip.cloud`) && PathPrefix(`/_nuxt/hmr`)"
      - "traefik.http.routers.hmr.entrypoints=websecure"
      - "traefik.http.routers.hmr.tls=true"
      - "traefik.http.routers.hmr.tls.certresolver=letsencrypt"
      - "traefik.http.routers.hmr.priority=15"
      
      # Telegram Mini App Headers - CRITICAL!
      - "traefik.http.middlewares.telegram-headers.headers.customrequestheaders.Access-Control-Allow-Origin=*"
      - "traefik.http.middlewares.telegram-headers.headers.frameDeny=false"
      - "traefik.http.middlewares.telegram-headers.headers.customresponseheaders.X-Frame-Options="
      - "traefik.http.middlewares.telegram-headers.headers.contentSecurityPolicy=default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://telegram.org https://*.telegram.org; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' data: https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' wss: https:; frame-src https://telegram.org https://*.telegram.org; frame-ancestors https://web.telegram.org https://*.telegram.org https://telegram.org;"
      - "traefik.http.routers.frontend-dev.middlewares=telegram-headers"
      - "traefik.http.routers.dev-files.middlewares=telegram-headers"
      
    networks:
      - atlasvpn-traefik
    restart: unless-stopped
    # Development-friendly health check
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://127.0.0.1:3005/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  pnpm-store:
    driver: local

networks:
  atlasvpn-traefik:
    external: true
