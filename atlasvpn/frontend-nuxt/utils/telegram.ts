/**
 * Telegram WebApp utilities for integration with Telegram Bot API
 */

import { TELEGRAM_CONFIG, DEV_CONFIG } from './constants';

// Telegram WebApp interface definitions
interface TelegramWebApp {
  initData: string;
  initDataUnsafe: {
    user?: {
      id: number;
      first_name: string;
      last_name?: string;
      username?: string;
      language_code?: string;
      is_premium?: boolean;
      photo_url?: string;
    };
    chat?: {
      id: number;
      type: string;
      title?: string;
      username?: string;
      photo_url?: string;
    };
    auth_date: number;
    hash: string;
    start_param?: string;
  };
  version: string;
  platform: string;
  colorScheme: 'light' | 'dark';
  themeParams: {
    bg_color?: string;
    text_color?: string;
    hint_color?: string;
    link_color?: string;
    button_color?: string;
    button_text_color?: string;
    secondary_bg_color?: string;
  };
  isExpanded: boolean;
  viewportHeight: number;
  viewportStableHeight: number;
  headerColor: string;
  backgroundColor: string;
  isClosingConfirmationEnabled: boolean;
  
  // Methods
  ready(): void;
  expand(): void;
  close(): void;
  enableClosingConfirmation(): void;
  disableClosingConfirmation(): void;
  showPopup(params: {
    title?: string;
    message: string;
    buttons?: Array<{
      id?: string;
      type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive';
      text: string;
    }>;
  }, callback?: (buttonId: string) => void): void;
  showAlert(message: string, callback?: () => void): void;
  showConfirm(message: string, callback?: (confirmed: boolean) => void): void;
  showScanQrPopup(params: {
    text?: string;
  }, callback?: (text: string) => void): void;
  closeScanQrPopup(): void;
  readTextFromClipboard(callback?: (text: string) => void): void;
  requestWriteAccess(callback?: (granted: boolean) => void): void;
  requestContact(callback?: (granted: boolean) => void): void;
  
  // Haptic Feedback
  HapticFeedback: {
    impactOccurred(style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft'): void;
    notificationOccurred(type: 'error' | 'success' | 'warning'): void;
    selectionChanged(): void;
  };
  
  // Main Button
  MainButton: {
    text: string;
    color: string;
    textColor: string;
    isVisible: boolean;
    isActive: boolean;
    isProgressVisible: boolean;
    setText(text: string): void;
    onClick(callback: () => void): void;
    offClick(callback: () => void): void;
    show(): void;
    hide(): void;
    enable(): void;
    disable(): void;
    showProgress(leaveActive?: boolean): void;
    hideProgress(): void;
    setParams(params: {
      text?: string;
      color?: string;
      text_color?: string;
      is_active?: boolean;
      is_visible?: boolean;
    }): void;
  };
  
  // Back Button
  BackButton: {
    isVisible: boolean;
    onClick(callback: () => void): void;
    offClick(callback: () => void): void;
    show(): void;
    hide(): void;
  };
  
  // Settings Button
  SettingsButton: {
    isVisible: boolean;
    onClick(callback: () => void): void;
    offClick(callback: () => void): void;
    show(): void;
    hide(): void;
  };
}

declare global {
  interface Window {
    Telegram?: {
      WebApp: TelegramWebApp;
    };
  }
}

/**
 * Get Telegram WebApp instance
 * @returns Telegram WebApp instance or mock data in development
 */
export function getTelegramWebApp(): TelegramWebApp | null {
  if (typeof window === 'undefined') return null;
  
  // Return mock data in development if Telegram WebApp is not available
  if (!window.Telegram?.WebApp && process.env.NODE_ENV === 'development') {
    return DEV_CONFIG.MOCK_TELEGRAM_DATA as any;
  }
  
  return window.Telegram?.WebApp || null;
}

/**
 * Check if running inside Telegram WebApp
 * @returns True if running in Telegram WebApp
 */
export function isTelegramWebApp(): boolean {
  const webApp = getTelegramWebApp();
  return webApp !== null;
}

/**
 * Get Telegram user data
 * @returns User data from Telegram WebApp
 */
export function getTelegramUser() {
  const webApp = getTelegramWebApp();
  return webApp?.initDataUnsafe?.user || null;
}

/**
 * Get Telegram chat data
 * @returns Chat data from Telegram WebApp
 */
export function getTelegramChat() {
  const webApp = getTelegramWebApp();
  return webApp?.initDataUnsafe?.chat || null;
}

/**
 * Get Telegram start parameter
 * @returns Start parameter from deep link
 */
export function getTelegramStartParam(): string | null {
  const webApp = getTelegramWebApp();
  return webApp?.initDataUnsafe?.start_param || null;
}

/**
 * Initialize Telegram WebApp
 * @param options - Initialization options
 */
export function initTelegramWebApp(options: {
  enableClosingConfirmation?: boolean;
  headerColor?: string;
  backgroundColor?: string;
} = {}) {
  const webApp = getTelegramWebApp();
  if (!webApp) return;
  
  // Set up the WebApp
  webApp.ready();
  webApp.expand();
  
  // Configure closing confirmation
  if (options.enableClosingConfirmation) {
    webApp.enableClosingConfirmation();
  } else {
    webApp.disableClosingConfirmation();
  }
  
  // Set colors if provided
  if (options.headerColor) {
    webApp.headerColor = options.headerColor;
  }
  
  if (options.backgroundColor) {
    webApp.backgroundColor = options.backgroundColor;
  }
}

/**
 * Show Telegram popup
 * @param title - Popup title
 * @param message - Popup message
 * @param buttons - Popup buttons
 * @returns Promise that resolves with button ID
 */
export function showTelegramPopup(
  title: string,
  message: string,
  buttons: Array<{
    id?: string;
    type?: 'default' | 'ok' | 'close' | 'cancel' | 'destructive';
    text: string;
  }> = [{ type: 'ok', text: 'OK' }]
): Promise<string> {
  return new Promise((resolve) => {
    const webApp = getTelegramWebApp();
    if (!webApp) {
      resolve('ok');
      return;
    }
    
    webApp.showPopup({ title, message, buttons }, (buttonId) => {
      resolve(buttonId);
    });
  });
}

/**
 * Show Telegram alert
 * @param message - Alert message
 * @returns Promise that resolves when alert is closed
 */
export function showTelegramAlert(message: string): Promise<void> {
  return new Promise((resolve) => {
    const webApp = getTelegramWebApp();
    if (!webApp) {
      alert(message);
      resolve();
      return;
    }
    
    webApp.showAlert(message, () => {
      resolve();
    });
  });
}

/**
 * Show Telegram confirmation
 * @param message - Confirmation message
 * @returns Promise that resolves with confirmation result
 */
export function showTelegramConfirm(message: string): Promise<boolean> {
  return new Promise((resolve) => {
    const webApp = getTelegramWebApp();
    if (!webApp) {
      resolve(confirm(message));
      return;
    }
    
    webApp.showConfirm(message, (confirmed) => {
      resolve(confirmed);
    });
  });
}

/**
 * Trigger haptic feedback
 * @param type - Type of haptic feedback
 */
export function triggerHapticFeedback(
  type: 'impact' | 'notification' | 'selection',
  style?: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft' | 'error' | 'success' | 'warning'
) {
  const webApp = getTelegramWebApp();
  if (!webApp?.HapticFeedback) return;
  
  switch (type) {
    case 'impact':
      webApp.HapticFeedback.impactOccurred(style as any || 'medium');
      break;
    case 'notification':
      webApp.HapticFeedback.notificationOccurred(style as any || 'success');
      break;
    case 'selection':
      webApp.HapticFeedback.selectionChanged();
      break;
  }
}

/**
 * Configure Telegram Main Button
 * @param config - Main button configuration
 */
export function configureTelegramMainButton(config: {
  text?: string;
  color?: string;
  textColor?: string;
  isVisible?: boolean;
  isActive?: boolean;
  onClick?: () => void;
}) {
  const webApp = getTelegramWebApp();
  if (!webApp?.MainButton) return;
  
  const { onClick, ...params } = config;
  
  // Set parameters
  webApp.MainButton.setParams({
    text: params.text,
    color: params.color,
    text_color: params.textColor,
    is_active: params.isActive,
    is_visible: params.isVisible,
  });
  
  // Set click handler
  if (onClick) {
    webApp.MainButton.onClick(onClick);
  }
}

/**
 * Configure Telegram Back Button
 * @param config - Back button configuration
 */
export function configureTelegramBackButton(config: {
  isVisible?: boolean;
  onClick?: () => void;
}) {
  const webApp = getTelegramWebApp();
  if (!webApp?.BackButton) return;
  
  if (config.isVisible) {
    webApp.BackButton.show();
  } else {
    webApp.BackButton.hide();
  }
  
  if (config.onClick) {
    webApp.BackButton.onClick(config.onClick);
  }
}

/**
 * Share content via Telegram
 * @param url - URL to share
 * @param text - Text to share
 */
export function shareTelegram(url: string, text?: string) {
  const shareUrl = TELEGRAM_CONFIG.SHARE_URL_TEMPLATE
    .replace('{url}', encodeURIComponent(url))
    .replace('{text}', encodeURIComponent(text || ''));
  
  if (typeof window !== 'undefined') {
    window.open(shareUrl, '_blank');
  }
}

/**
 * Get Telegram theme colors
 * @returns Theme colors from Telegram WebApp
 */
export function getTelegramThemeColors() {
  const webApp = getTelegramWebApp();
  return webApp?.themeParams || TELEGRAM_CONFIG.THEME_COLORS;
}

/**
 * Validate Telegram WebApp init data
 * @param initData - Init data string
 * @param botToken - Bot token for validation
 * @returns True if valid
 */
export function validateTelegramInitData(initData: string, botToken: string): boolean {
  // This should be implemented on the server side for security
  // Client-side validation is not secure
  console.warn('Telegram init data validation should be done on the server side');
  return true;
}
