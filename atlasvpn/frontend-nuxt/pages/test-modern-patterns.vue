<template>
  <div class="min-h-screen bg-gray-900 text-white p-6">
    <div class="max-w-6xl mx-auto">
      <h1 class="text-4xl font-bold mb-8 text-center">Modern Data Fetching Patterns Test</h1>
      
      <!-- Connection Status -->
      <div class="mb-8 p-4 rounded-lg" :class="connectionStatusClass">
        <h2 class="text-xl font-semibold mb-2">Connection Status</h2>
        <div class="flex items-center space-x-4">
          <span class="flex items-center">
            <div class="w-3 h-3 rounded-full mr-2" :class="connectionDotClass"></div>
            {{ connectionStatus.toUpperCase() }}
          </span>
          <span v-if="hasPendingActions" class="text-yellow-400">
            {{ pendingActionsCount }} pending actions
          </span>
          <span v-if="isConnecting" class="text-blue-400">
            Syncing...
          </span>
        </div>
      </div>

      <!-- Performance Metrics -->
      <div class="mb-8 p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">Performance Metrics</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold" :class="performanceGradeColor">{{ performanceGrade }}</div>
            <div class="text-sm text-gray-400">Performance Grade</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold">{{ performanceScore }}</div>
            <div class="text-sm text-gray-400">Score</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold">{{ coreWebVitals.LCP.toFixed(0) }}ms</div>
            <div class="text-sm text-gray-400">LCP</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold">{{ coreWebVitals.FID.toFixed(0) }}ms</div>
            <div class="text-sm text-gray-400">FID</div>
          </div>
        </div>
      </div>

      <!-- Dashboard Data Test -->
      <div class="mb-8 p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">Dashboard Data (Modern useFetch)</h2>
        <div v-if="isLoadingStats" class="text-blue-400">Loading dashboard stats...</div>
        <div v-else-if="statsError" class="text-red-400">Error: {{ statsError }}</div>
        <div v-else class="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-green-400">{{ dashboardStats.active_subscriptions }}</div>
            <div class="text-sm text-gray-400">Active Subscriptions</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-blue-400">${{ dashboardStats.wallet_balance }}</div>
            <div class="text-sm text-gray-400">Wallet Balance</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-purple-400">{{ dashboardStats.total_accumulated_card_profit }}</div>
            <div class="text-sm text-gray-400">Card Profits</div>
          </div>
        </div>
        <button 
          @click="refreshStats()" 
          class="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
        >
          Refresh Stats
        </button>
      </div>

      <!-- Tasks Test -->
      <div class="mb-8 p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">Tasks (Modern useFetch with Optimistic Updates)</h2>
        <div v-if="isLoadingTasks" class="text-blue-400">Loading tasks...</div>
        <div v-else-if="tasksError" class="text-red-400">Error: {{ tasksError }}</div>
        <div v-else>
          <div class="mb-4">
            <span class="text-lg">Total Tasks: {{ tasks.length }}</span>
            <span class="ml-4 text-green-400">Available: {{ availableTasks.length }}</span>
            <span class="ml-4 text-blue-400">Completed: {{ completedTasks.length }}</span>
          </div>
          <div class="grid gap-2 max-h-40 overflow-y-auto">
            <div 
              v-for="task in tasks.slice(0, 5)" 
              :key="task.id"
              class="flex items-center justify-between p-2 bg-gray-700 rounded"
            >
              <span>{{ task.title || `Task ${task.id}` }}</span>
              <span class="text-sm" :class="getTaskStatusColor(task.status)">
                {{ task.status }}
              </span>
            </div>
          </div>
          <button 
            @click="refreshTasks()" 
            class="mt-4 px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
          >
            Refresh Tasks
          </button>
        </div>
      </div>

      <!-- Cards Test -->
      <div class="mb-8 p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">Cards (Modern Patterns with Caching)</h2>
        <div v-if="isLoadingOwned" class="text-blue-400">Loading cards...</div>
        <div v-else-if="ownedError" class="text-red-400">Error: {{ ownedError }}</div>
        <div v-else>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div class="text-center">
              <div class="text-2xl font-bold text-yellow-400">{{ totalCardsOwned }}</div>
              <div class="text-sm text-gray-400">Cards Owned</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-400">${{ totalProfitAvailable.toFixed(2) }}</div>
              <div class="text-sm text-gray-400">Profit Available</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-400">${{ totalHourlyIncome.toFixed(2) }}</div>
              <div class="text-sm text-gray-400">Hourly Income</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-400">{{ cardsWithProfits.length }}</div>
              <div class="text-sm text-gray-400">Cards with Profits</div>
            </div>
          </div>
          <button 
            @click="refreshOwned()" 
            class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded transition-colors"
          >
            Refresh Cards
          </button>
        </div>
      </div>

      <!-- Offline Features Test -->
      <div class="mb-8 p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">Offline Features</h2>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
          <div class="text-center">
            <div class="text-2xl font-bold">{{ cacheSize }}</div>
            <div class="text-sm text-gray-400">Cached Items</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold">{{ pendingActionsCount }}</div>
            <div class="text-sm text-gray-400">Pending Actions</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold" :class="isOnline ? 'text-green-400' : 'text-red-400'">
              {{ isOnline ? 'Online' : 'Offline' }}
            </div>
            <div class="text-sm text-gray-400">Connection</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold" :class="syncInProgress ? 'text-blue-400' : 'text-gray-400'">
              {{ syncInProgress ? 'Syncing' : 'Idle' }}
            </div>
            <div class="text-sm text-gray-400">Sync Status</div>
          </div>
        </div>
        <div class="flex space-x-2">
          <button 
            @click="clearCache()" 
            class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded transition-colors"
          >
            Clear Cache
          </button>
          <button 
            @click="syncPendingActions()" 
            :disabled="!hasPendingActions"
            class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors disabled:opacity-50"
          >
            Sync Now
          </button>
        </div>
      </div>

      <!-- Telegram Integration Test -->
      <div class="p-4 bg-gray-800 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">Telegram Integration</h2>
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div>
            <div class="text-sm text-gray-400">WebApp Available</div>
            <div class="text-lg">{{ telegramWebApp ? 'Yes' : 'No' }}</div>
          </div>
          <div>
            <div class="text-sm text-gray-400">User ID</div>
            <div class="text-lg">{{ telegramUser?.id || 'N/A' }}</div>
          </div>
        </div>
        <div class="flex space-x-2">
          <button 
            @click="triggerHaptic('impact')" 
            class="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 rounded transition-colors"
          >
            Haptic Feedback
          </button>
          <button 
            @click="showMainButton('Test Button', () => alert('Button clicked!'))" 
            class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
          >
            Show Main Button
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Import all the modern composables
const { 
  dashboardStats, 
  isLoadingStats, 
  statsError, 
  refreshStats 
} = useDashboard()

const { 
  tasks, 
  availableTasks, 
  completedTasks, 
  isLoadingTasks, 
  tasksError, 
  refreshTasks 
} = useTasks()

const { 
  ownedCards, 
  totalCardsOwned, 
  totalProfitAvailable, 
  totalHourlyIncome, 
  cardsWithProfits, 
  isLoadingOwned, 
  ownedError, 
  refreshOwned 
} = useCards()

const { 
  isOnline, 
  isConnecting, 
  syncInProgress, 
  connectionStatus, 
  hasPendingActions, 
  pendingActionsCount, 
  cacheSize, 
  clearCache, 
  syncPendingActions 
} = useOffline()

const { 
  performanceScore, 
  getPerformanceGrade, 
  coreWebVitals 
} = usePerformance()

const { 
  telegramWebApp, 
  telegramUser 
} = useTelegram()

// Initialize offline handling
onMounted(() => {
  const offline = useOffline()
  offline.init()
})

// Computed properties
const performanceGrade = getPerformanceGrade
const connectionStatusClass = computed(() => ({
  'bg-green-800': connectionStatus.value === 'online',
  'bg-red-800': connectionStatus.value === 'offline',
  'bg-yellow-800': connectionStatus.value === 'connecting'
}))

const connectionDotClass = computed(() => ({
  'bg-green-400': connectionStatus.value === 'online',
  'bg-red-400': connectionStatus.value === 'offline',
  'bg-yellow-400': connectionStatus.value === 'connecting'
}))

const performanceGradeColor = computed(() => ({
  'text-green-400': ['A', 'B'].includes(performanceGrade.value),
  'text-yellow-400': ['C', 'D'].includes(performanceGrade.value),
  'text-red-400': performanceGrade.value === 'F'
}))

// Utility functions
const getTaskStatusColor = (status: string) => ({
  'text-green-400': status === 'completed',
  'text-blue-400': status === 'available',
  'text-yellow-400': status === 'pending',
  'text-red-400': status === 'failed'
})

// Telegram optimization utilities
const { $telegramOptimization } = useNuxtApp()

const triggerHaptic = (type: 'impact' | 'notification' | 'selection') => {
  $telegramOptimization?.triggerHaptic(type)
}

const showMainButton = (text: string, callback: () => void) => {
  $telegramOptimization?.showMainButton(text, callback)
}

// Page meta
definePageMeta({
  title: 'Modern Patterns Test',
  description: 'Test page for modern data fetching patterns and Telegram Mini App optimizations'
})
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
