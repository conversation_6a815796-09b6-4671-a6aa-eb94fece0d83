import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { CreditCard, Plus, Gift, Search, Users, DollarSign, Edit, Trash2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface CardCatalog {
  id: number
  name: string
  description: string
  rarity: string
  max_level: number
  level_profits: number[]
  level_costs: number[]
  image_url?: string
  created_at: string
}

interface UserCard {
  id: number
  user_id: number
  username: string
  card_name: string
  level: number
  current_hourly_profit: number
  next_upgrade_cost?: number
  last_claim_at: string
  acquired_at: string
}

interface CardStats {
  total_catalog_cards: number
  total_user_cards: number
  users_with_cards: number
  avg_cards_per_user: number
  rarity_distribution: Array<{ rarity: string; count: number }>
  total_passive_income_rate: number
}

interface GrantCardData {
  user_id: number
  card_catalog_id: number
  level?: number
}

interface CreateCardData {
  name: string
  description: string
  rarity: string
  max_level: number
  level_profits: number[]
  level_costs: number[]
  image_url?: string
}

export function Cards() {
  const [searchTerm, setSearchTerm] = useState('')
  const [rarityFilter, setRarityFilter] = useState<string>('all')
  const [grantDialogOpen, setGrantDialogOpen] = useState(false)
  const [createCardDialogOpen, setCreateCardDialogOpen] = useState(false)
  const [editCardDialogOpen, setEditCardDialogOpen] = useState(false)
  const [selectedCard, setSelectedCard] = useState<CardCatalog | null>(null)
  const [selectedCardForEdit, setSelectedCardForEdit] = useState<CardCatalog | null>(null)
  
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const { data: cardCatalog, isLoading: isCatalogLoading } = useQuery<CardCatalog[]>({
    queryKey: ['card-catalog'],
    queryFn: () => api.get('/admin/cards/catalog').then(res => res.data)
  })

  const { data: userCards, isLoading: isUserCardsLoading } = useQuery<UserCard[]>({
    queryKey: ['user-cards'],
    queryFn: () => api.get('/admin/cards/users').then(res => res.data)
  })

  const { data: cardStats, isLoading: isStatsLoading } = useQuery<CardStats>({
    queryKey: ['card-stats'],
    queryFn: () => api.get('/admin/cards/stats').then(res => res.data)
  })

  const { data: users } = useQuery({
    queryKey: ['users-list'],
    queryFn: () => api.get('/admin/users/').then(res => res.data)
  })

  const grantCardMutation = useMutation({
    mutationFn: (data: GrantCardData) => 
      api.post(`/admin/cards/grant?user_id=${data.user_id}`, {
        card_catalog_id: data.card_catalog_id,
        level: data.level
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-cards'] })
      queryClient.invalidateQueries({ queryKey: ['card-stats'] })
      setGrantDialogOpen(false)
      setSelectedCard(null)
      toast({
        title: "Success",
        description: "Card granted successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to grant card",
        variant: "destructive",
      })
    }
  })

  const createCardMutation = useMutation({
    mutationFn: (cardData: CreateCardData) => api.post('/admin/cards/catalog', cardData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['card-catalog'] })
      queryClient.invalidateQueries({ queryKey: ['card-stats'] })
      setCreateCardDialogOpen(false)
      toast({
        title: "Success",
        description: "Card created successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to create card",
        variant: "destructive",
      })
    }
  })

  const updateCardMutation = useMutation({
    mutationFn: ({ id, data }: { id: number, data: CreateCardData }) => 
      api.put(`/admin/cards/catalog/${id}`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['card-catalog'] })
      queryClient.invalidateQueries({ queryKey: ['card-stats'] })
      setEditCardDialogOpen(false)
      setSelectedCardForEdit(null)
      toast({
        title: "Success",
        description: "Card updated successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to update card",
        variant: "destructive",
      })
    }
  })

  const deleteCardMutation = useMutation({
    mutationFn: (cardId: number) => api.delete(`/admin/cards/catalog/${cardId}`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['card-catalog'] })
      queryClient.invalidateQueries({ queryKey: ['card-stats'] })
      toast({
        title: "Success",
        description: "Card deleted successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to delete card",
        variant: "destructive",
      })
    }
  })

  // Filter catalog cards
  const filteredCatalog = cardCatalog?.filter(card => {
    const matchesSearch = card.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         card.description.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRarity = rarityFilter === 'all' || card.rarity === rarityFilter
    
    return matchesSearch && matchesRarity
  })

  // Filter user cards
  const filteredUserCards = userCards?.filter(card => {
    return card.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
           card.card_name.toLowerCase().includes(searchTerm.toLowerCase())
  })

  const CardFormDialog = ({ 
    isEdit = false, 
    open, 
    onOpenChange 
  }: { 
    isEdit?: boolean
    open: boolean
    onOpenChange: (open: boolean) => void 
  }) => {
    const [formData, setFormData] = useState<CreateCardData>({
      name: isEdit ? selectedCardForEdit?.name || '' : '',
      description: isEdit ? selectedCardForEdit?.description || '' : '',
      rarity: isEdit ? selectedCardForEdit?.rarity || 'common' : 'common',
      max_level: isEdit ? selectedCardForEdit?.max_level || 10 : 10,
      level_profits: isEdit ? selectedCardForEdit?.level_profits || [1] : [1],
      level_costs: isEdit ? selectedCardForEdit?.level_costs || [100] : [100],
      image_url: isEdit ? selectedCardForEdit?.image_url || '' : ''
    })

    const [profitsInput, setProfitsInput] = useState(
      isEdit ? selectedCardForEdit?.level_profits.join(', ') || '1' : '1'
    )
    const [costsInput, setCostsInput] = useState(
      isEdit ? selectedCardForEdit?.level_costs.join(', ') || '100' : '100'
    )

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      
      // Parse profits and costs
      const profits = profitsInput.split(',').map(p => parseFloat(p.trim())).filter(p => !isNaN(p))
      const costs = costsInput.split(',').map(c => parseFloat(c.trim())).filter(c => !isNaN(c))
      
      if (profits.length === 0 || costs.length === 0) {
        toast({
          title: "Error",
          description: "Please provide valid profits and costs",
          variant: "destructive",
        })
        return
      }

      const cardData = {
        ...formData,
        level_profits: profits,
        level_costs: costs
      }

      if (isEdit && selectedCardForEdit) {
        updateCardMutation.mutate({ id: selectedCardForEdit.id, data: cardData })
      } else {
        createCardMutation.mutate(cardData)
      }
    }

    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{isEdit ? 'Edit Card' : 'Create New Card'}</DialogTitle>
            <DialogDescription>
              {isEdit ? `Update card: ${selectedCardForEdit?.name}` : 'Add a new card to the catalog'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Card Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>
              <div>
                <Label htmlFor="rarity">Rarity</Label>
                <Select value={formData.rarity} onValueChange={(value) => setFormData({...formData, rarity: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="common">Common</SelectItem>
                    <SelectItem value="rare">Rare</SelectItem>
                    <SelectItem value="epic">Epic</SelectItem>
                    <SelectItem value="legendary">Legendary</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
                required
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="max_level">Max Level</Label>
                <Input
                  id="max_level"
                  type="number"
                  min="1"
                  max="100"
                  value={formData.max_level}
                  onChange={(e) => setFormData({...formData, max_level: parseInt(e.target.value) || 10})}
                  required
                />
              </div>
              <div>
                <Label htmlFor="image_url">Image URL (Optional)</Label>
                <Input
                  id="image_url"
                  type="url"
                  value={formData.image_url}
                  onChange={(e) => setFormData({...formData, image_url: e.target.value})}
                  placeholder="https://..."
                />
              </div>
            </div>

            <div>
              <Label htmlFor="level_profits">Level Profits (comma-separated)</Label>
              <Input
                id="level_profits"
                value={profitsInput}
                onChange={(e) => setProfitsInput(e.target.value)}
                placeholder="1, 2, 3, 5, 8, 13, 21, 34, 55, 89"
                required
              />
              <p className="text-sm text-gray-600 mt-1">
                Enter hourly profit for each level, separated by commas
              </p>
            </div>

            <div>
              <Label htmlFor="level_costs">Level Costs (comma-separated)</Label>
              <Input
                id="level_costs"
                value={costsInput}
                onChange={(e) => setCostsInput(e.target.value)}
                placeholder="100, 200, 400, 800, 1600, 3200, 6400, 12800, 25600, 51200"
                required
              />
              <p className="text-sm text-gray-600 mt-1">
                Enter upgrade cost for each level, separated by commas
              </p>
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={createCardMutation.isPending || updateCardMutation.isPending}>
                {(createCardMutation.isPending || updateCardMutation.isPending) ? 'Saving...' : (isEdit ? 'Update Card' : 'Create Card')}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    )
  }

  const GrantCardDialog = () => {
    const [formData, setFormData] = useState<GrantCardData>({
      user_id: 0,
      card_catalog_id: selectedCard?.id || 0,
      level: 1
    })

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      if (formData.user_id && formData.card_catalog_id) {
        grantCardMutation.mutate(formData)
      }
    }

    return (
      <Dialog open={grantDialogOpen} onOpenChange={setGrantDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Grant Card to User</DialogTitle>
            <DialogDescription>
              Grant "{selectedCard?.name}" to a user
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="user_id">Select User</Label>
              <Select 
                value={formData.user_id.toString()} 
                onValueChange={(value) => setFormData({...formData, user_id: parseInt(value)})}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Choose a user" />
                </SelectTrigger>
                <SelectContent>
                  {users?.map((user: any) => (
                    <SelectItem key={user.id} value={user.id.toString()}>
                      {user.username} ({user.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="level">Initial Level</Label>
              <Input
                id="level"
                type="number"
                min="1"
                max={selectedCard?.max_level || 10}
                value={formData.level}
                onChange={(e) => setFormData({...formData, level: parseInt(e.target.value) || 1})}
              />
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setGrantDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={grantCardMutation.isPending || !formData.user_id}>
                {grantCardMutation.isPending ? 'Granting...' : 'Grant Card'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    )
  }

  const StatCard = ({ 
    title, 
    value, 
    description, 
    icon: Icon, 
    isLoading 
  }: { 
    title: string
    value: string | number
    description: string
    icon: any
    isLoading: boolean
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-4 w-32" />
          </div>
        ) : (
          <>
            <div className="text-2xl font-bold">{value}</div>
            <p className="text-xs text-muted-foreground">{description}</p>
          </>
        )}
      </CardContent>
    </Card>
  )

  const getRarityColor = (rarity: string) => {
    switch (rarity.toLowerCase()) {
      case 'common': return 'bg-gray-100 text-gray-800'
      case 'rare': return 'bg-blue-100 text-blue-800'
      case 'epic': return 'bg-purple-100 text-purple-800'
      case 'legendary': return 'bg-yellow-100 text-yellow-800'
      case 'mythic': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (isCatalogLoading || isUserCardsLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Cards</h1>
          <p className="text-gray-600">Manage card system</p>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Cards</h1>
          <p className="text-gray-600">Manage card catalog and user cards</p>
        </div>
        <Button onClick={() => setCreateCardDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Create Card
        </Button>
      </div>

      {/* Card Statistics */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Card System Statistics</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Catalog Cards"
            value={cardStats?.total_catalog_cards || 0}
            description="Cards available in catalog"
            icon={CreditCard}
            isLoading={isStatsLoading}
          />
          <StatCard
            title="User Cards"
            value={cardStats?.total_user_cards || 0}
            description="Cards owned by users"
            icon={CreditCard}
            isLoading={isStatsLoading}
          />
          <StatCard
            title="Users with Cards"
            value={cardStats?.users_with_cards || 0}
            description="Users who own cards"
            icon={Users}
            isLoading={isStatsLoading}
          />
          <StatCard
            title="Passive Income Rate"
            value={`$${cardStats?.total_passive_income_rate?.toFixed(2) || '0.00'}/hr`}
            description="Total hourly income from cards"
            icon={DollarSign}
            isLoading={isStatsLoading}
          />
        </div>
      </div>

      {/* Tabs for different views */}
      <Tabs defaultValue="catalog" className="space-y-4">
        <TabsList>
          <TabsTrigger value="catalog">Card Catalog</TabsTrigger>
          <TabsTrigger value="user-cards">User Cards</TabsTrigger>
        </TabsList>

        <TabsContent value="catalog" className="space-y-4">
          {/* Search and Filters */}
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search cards..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Select value={rarityFilter} onValueChange={setRarityFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Rarity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Rarities</SelectItem>
                      <SelectItem value="common">Common</SelectItem>
                      <SelectItem value="rare">Rare</SelectItem>
                      <SelectItem value="epic">Epic</SelectItem>
                      <SelectItem value="legendary">Legendary</SelectItem>
                      <SelectItem value="mythic">Mythic</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Card Catalog Table */}
          <Card>
            <CardHeader>
              <CardTitle>Card Catalog ({filteredCatalog?.length || 0})</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Card</TableHead>
                    <TableHead>Rarity</TableHead>
                    <TableHead>Max Level</TableHead>
                    <TableHead>Base Profit</TableHead>
                    <TableHead>Max Profit</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredCatalog?.map((card) => (
                    <TableRow key={card.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          {card.image_url && (
                            <img 
                              src={card.image_url} 
                              alt={card.name}
                              className="w-10 h-10 rounded-lg object-cover"
                            />
                          )}
                          <div>
                            <div className="font-medium">{card.name}</div>
                            <div className="text-sm text-gray-600 max-w-xs truncate">
                              {card.description}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getRarityColor(card.rarity)}>
                          {card.rarity}
                        </Badge>
                      </TableCell>
                      <TableCell>{card.max_level}</TableCell>
                      <TableCell>
                        ${card.level_profits?.[0]?.toFixed(2) || '0.00'}/hr
                      </TableCell>
                      <TableCell>
                        ${card.level_profits?.[card.max_level - 1]?.toFixed(2) || '0.00'}/hr
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedCard(card)
                              setGrantDialogOpen(true)
                            }}
                          >
                            <Gift className="h-4 w-4 mr-2" />
                            Grant
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedCardForEdit(card)
                              setEditCardDialogOpen(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="outline" size="sm">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Delete Card</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Are you sure you want to delete "{card.name}"? This action cannot be undone and will affect all users who own this card.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancel</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => deleteCardMutation.mutate(card.id)}
                                >
                                  Delete
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredCatalog && filteredCatalog.length === 0 && (
                <div className="text-center py-12">
                  <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No cards found</h3>
                  <p className="text-gray-600">
                    {searchTerm || rarityFilter !== 'all' 
                      ? 'Try adjusting your search or filters.' 
                      : 'No cards available in the catalog.'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="user-cards" className="space-y-4">
          {/* Search */}
          <Card>
            <CardContent className="p-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by username or card name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardContent>
          </Card>

          {/* User Cards Table */}
          <Card>
            <CardHeader>
              <CardTitle>User Cards ({filteredUserCards?.length || 0})</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Card</TableHead>
                    <TableHead>Level</TableHead>
                    <TableHead>Hourly Profit</TableHead>
                    <TableHead>Upgrade Cost</TableHead>
                    <TableHead>Acquired</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUserCards?.map((userCard) => (
                    <TableRow key={userCard.id}>
                      <TableCell>
                        <div className="font-medium">{userCard.username}</div>
                        <div className="text-sm text-gray-600">ID: {userCard.user_id}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{userCard.card_name}</div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">Level {userCard.level}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          ${userCard.current_hourly_profit.toFixed(2)}/hr
                        </div>
                      </TableCell>
                      <TableCell>
                        {userCard.next_upgrade_cost ? (
                          <div className="font-medium">
                            ${userCard.next_upgrade_cost.toFixed(2)}
                          </div>
                        ) : (
                          <span className="text-gray-500">Max Level</span>
                        )}
                      </TableCell>
                      <TableCell>
                        {new Date(userCard.acquired_at).toLocaleDateString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredUserCards && filteredUserCards.length === 0 && (
                <div className="text-center py-12">
                  <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No user cards found</h3>
                  <p className="text-gray-600">
                    {searchTerm 
                      ? 'Try adjusting your search.' 
                      : 'No users have cards yet.'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <GrantCardDialog />
      <CardFormDialog 
        isEdit={false}
        open={createCardDialogOpen}
        onOpenChange={setCreateCardDialogOpen}
      />
      <CardFormDialog 
        isEdit={true}
        open={editCardDialogOpen}
        onOpenChange={setEditCardDialogOpen}
      />
    </div>
  )
} 