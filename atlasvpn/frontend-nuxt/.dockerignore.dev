# Development .dockerignore - More permissive for development workflow

# Git
.git
.gitignore

# Documentation
README.md
*.md

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs (but allow some for debugging)
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories (will be installed in container)
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output directories (will be generated)
.output/
.nuxt/
dist/

# Environment files (handled by docker-compose)
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary folders
tmp/
temp/

# Docker files
Dockerfile
Dockerfile.*
docker-compose*.yml
.dockerignore*
