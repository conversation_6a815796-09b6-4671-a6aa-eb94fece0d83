<template>
  <div class="verse-page min-h-screen bg-[#030303] text-white">
    <!-- Header -->
    <div class="p-4 border-b border-gray-800">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <UButton
            @click="navigateTo('/dashboard')"
            variant="ghost"
            size="sm"
            icon="i-heroicons-arrow-left"
          />
          <div>
            <h1 class="text-xl font-bold">AtlasVerse</h1>
            <p class="text-gray-400 text-sm">3D Virtual World</p>
          </div>
        </div>
        
        <div class="flex items-center space-x-2">
          <UButton
            @click="toggleFullscreen"
            variant="ghost"
            size="sm"
            :icon="isFullscreen ? 'i-heroicons-arrows-pointing-in' : 'i-heroicons-arrows-pointing-out'"
          />
          <UButton
            @click="showSettings = true"
            variant="ghost"
            size="sm"
            icon="i-heroicons-cog-6-tooth"
          />
        </div>
      </div>
    </div>

    <!-- 3D Scene Container -->
    <div class="relative flex-1">
      <!-- 3D Canvas will be rendered here -->
      <div 
        ref="canvasContainer"
        class="w-full h-[calc(100vh-80px)] bg-gradient-to-b from-purple-900/20 to-blue-900/20"
      >
        <!-- Loading State -->
        <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center">
          <div class="text-center">
            <div class="loading-spinner w-12 h-12 mx-auto mb-4"></div>
            <p class="text-white">Loading AtlasVerse...</p>
            <p class="text-gray-400 text-sm">{{ loadingProgress }}%</p>
          </div>
        </div>

        <!-- Placeholder 3D Scene -->
        <div v-else class="absolute inset-0 flex items-center justify-center">
          <div class="text-center max-w-md">
            <div class="w-32 h-32 mx-auto mb-6 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
              <Icon name="i-heroicons-cube" class="w-16 h-16 text-white" />
            </div>
            <h2 class="text-2xl font-bold mb-4">Welcome to AtlasVerse</h2>
            <p class="text-gray-400 mb-6">
              Explore the 3D virtual world, customize your avatar, and interact with other users.
            </p>
            <div class="space-y-3">
              <UButton
                @click="initializeVerse"
                color="primary"
                size="lg"
                class="w-full"
              >
                Enter AtlasVerse
              </UButton>
              <UButton
                @click="showAvatarCustomizer = true"
                variant="outline"
                size="lg"
                class="w-full"
              >
                Customize Avatar
              </UButton>
            </div>
          </div>
        </div>
      </div>

      <!-- UI Overlay -->
      <div class="absolute top-4 left-4 right-4 pointer-events-none">
        <div class="flex justify-between items-start">
          <!-- User Info -->
          <div class="bg-black/50 backdrop-blur-sm rounded-lg p-3 pointer-events-auto">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 rounded-full bg-purple-500 flex items-center justify-center">
                <span class="text-white font-bold">{{ userInitials }}</span>
              </div>
              <div>
                <div class="text-white font-medium">{{ userDisplayName }}</div>
                <div class="text-gray-400 text-sm">Level {{ userLevel }}</div>
              </div>
            </div>
          </div>

          <!-- Mini Map -->
          <div class="bg-black/50 backdrop-blur-sm rounded-lg p-3 pointer-events-auto">
            <div class="w-24 h-24 bg-gray-800 rounded border-2 border-gray-600 relative">
              <div class="absolute top-1/2 left-1/2 w-2 h-2 bg-blue-400 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
              <div class="absolute top-2 right-2 w-1 h-1 bg-green-400 rounded-full"></div>
              <div class="absolute bottom-3 left-3 w-1 h-1 bg-red-400 rounded-full"></div>
            </div>
            <div class="text-center text-xs text-gray-400 mt-1">Mini Map</div>
          </div>
        </div>
      </div>

      <!-- Bottom Controls -->
      <div class="absolute bottom-4 left-4 right-4 pointer-events-none">
        <div class="flex justify-center">
          <div class="bg-black/50 backdrop-blur-sm rounded-lg p-3 pointer-events-auto">
            <div class="flex items-center space-x-4">
              <UButton
                @click="toggleMovement"
                :variant="movementEnabled ? 'solid' : 'outline'"
                size="sm"
                icon="i-heroicons-arrows-up-down-left-right"
              />
              <UButton
                @click="toggleChat"
                variant="outline"
                size="sm"
                icon="i-heroicons-chat-bubble-left"
              />
              <UButton
                @click="toggleVoice"
                :variant="voiceEnabled ? 'solid' : 'outline'"
                size="sm"
                icon="i-heroicons-microphone"
              />
              <UButton
                @click="showInventory = true"
                variant="outline"
                size="sm"
                icon="i-heroicons-cube"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Avatar Customizer Modal -->
    <UModal v-model="showAvatarCustomizer">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-white mb-4">Customize Avatar</h3>
        <div class="grid grid-cols-2 gap-4 mb-6">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Hair Style</label>
            <USelect
              v-model="avatarSettings.hairStyle"
              :options="hairStyles"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Hair Color</label>
            <USelect
              v-model="avatarSettings.hairColor"
              :options="hairColors"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Outfit</label>
            <USelect
              v-model="avatarSettings.outfit"
              :options="outfits"
              class="w-full"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Accessories</label>
            <USelect
              v-model="avatarSettings.accessories"
              :options="accessories"
              class="w-full"
            />
          </div>
        </div>
        
        <div class="flex space-x-3">
          <UButton
            @click="saveAvatarSettings"
            color="primary"
            class="flex-1"
          >
            Save Changes
          </UButton>
          <UButton
            @click="showAvatarCustomizer = false"
            variant="outline"
          >
            Cancel
          </UButton>
        </div>
      </div>
    </UModal>

    <!-- Settings Modal -->
    <UModal v-model="showSettings">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-white mb-4">Verse Settings</h3>
        <div class="space-y-4 mb-6">
          <div class="flex items-center justify-between">
            <span class="text-gray-300">Graphics Quality</span>
            <USelect
              v-model="settings.graphicsQuality"
              :options="['Low', 'Medium', 'High', 'Ultra']"
              class="w-32"
            />
          </div>
          <div class="flex items-center justify-between">
            <span class="text-gray-300">Sound Effects</span>
            <UToggle v-model="settings.soundEffects" />
          </div>
          <div class="flex items-center justify-between">
            <span class="text-gray-300">Background Music</span>
            <UToggle v-model="settings.backgroundMusic" />
          </div>
          <div class="flex items-center justify-between">
            <span class="text-gray-300">Show Other Players</span>
            <UToggle v-model="settings.showOtherPlayers" />
          </div>
        </div>
        
        <div class="flex space-x-3">
          <UButton
            @click="saveSettings"
            color="primary"
            class="flex-1"
          >
            Save Settings
          </UButton>
          <UButton
            @click="showSettings = false"
            variant="outline"
          >
            Cancel
          </UButton>
        </div>
      </div>
    </UModal>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth',
  layout: false
})

// SEO
useHead({
  title: 'AtlasVerse - 3D Virtual World'
})

const userStore = useUserStore()

// Computed properties
const userDisplayName = computed(() => userStore.userDisplayName)
const userInitials = computed(() => userStore.userInitials)
const userLevel = computed(() => 1) // Placeholder

// Local state
const canvasContainer = ref<HTMLElement>()
const isLoading = ref(false)
const loadingProgress = ref(0)
const isFullscreen = ref(false)
const movementEnabled = ref(false)
const voiceEnabled = ref(false)

// Modal states
const showAvatarCustomizer = ref(false)
const showSettings = ref(false)
const showInventory = ref(false)

// Avatar settings
const avatarSettings = reactive({
  hairStyle: 'Short',
  hairColor: 'Brown',
  outfit: 'Casual',
  accessories: 'None'
})

const hairStyles = ['Short', 'Long', 'Curly', 'Bald']
const hairColors = ['Black', 'Brown', 'Blonde', 'Red', 'Blue', 'Green']
const outfits = ['Casual', 'Formal', 'Sports', 'Futuristic']
const accessories = ['None', 'Glasses', 'Hat', 'Earrings', 'Watch']

// Settings
const settings = reactive({
  graphicsQuality: 'Medium',
  soundEffects: true,
  backgroundMusic: true,
  showOtherPlayers: true
})

// Actions
const initializeVerse = async () => {
  isLoading.value = true
  loadingProgress.value = 0
  
  // Simulate loading progress
  const interval = setInterval(() => {
    loadingProgress.value += 10
    if (loadingProgress.value >= 100) {
      clearInterval(interval)
      isLoading.value = false
      // Here you would initialize the 3D scene
      console.log('AtlasVerse initialized')
    }
  }, 200)
}

const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

const toggleMovement = () => {
  movementEnabled.value = !movementEnabled.value
}

const toggleChat = () => {
  console.log('Toggle chat')
}

const toggleVoice = () => {
  voiceEnabled.value = !voiceEnabled.value
}

const saveAvatarSettings = () => {
  console.log('Save avatar settings:', avatarSettings)
  showAvatarCustomizer.value = false
  
  const uiStore = useUiStore()
  uiStore.showSuccessToast('Avatar updated!')
}

const saveSettings = () => {
  console.log('Save settings:', settings)
  showSettings.value = false
  
  const uiStore = useUiStore()
  uiStore.showSuccessToast('Settings saved!')
}

// Handle fullscreen changes
onMounted(() => {
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement
  })
})
</script>
