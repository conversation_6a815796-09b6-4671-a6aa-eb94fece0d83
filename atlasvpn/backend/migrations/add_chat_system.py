"""
Migration script to add chat_messages table for the chat system.
"""

import sys
import os
# Explicitly add the parent directory (backend) to the Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
backend_dir = os.path.dirname(current_dir) # Get the directory containing migrations
sys.path.insert(0, backend_dir)

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, func, Index, text
from sqlalchemy.ext.declarative import declarative_base
import asyncio
from database import async_engine, Base
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def create_chat_tables():
    """Create the chat_messages table if it doesn't exist."""
    logger.info("Creating chat_messages table...")
    
    async with async_engine.begin() as conn:
        # Use run_sync for synchronous-style execution within async context
        def check_table_exists_sync(sync_conn):
            # Use SQLite-specific query to check for table existence
            result = sync_conn.execute(
                text("SELECT name FROM sqlite_master WHERE type='table' AND name=:table_name"),
                {"table_name": "chat_messages"}
            )
            return result.scalar() is not None # Check if any row was returned
            
        table_exists = await conn.run_sync(check_table_exists_sync)
        
        if table_exists:
            logger.info("Table 'chat_messages' already exists. Dropping it first...")
            # Drop the existing table using run_sync
            def drop_table_sync(sync_conn):
                sync_conn.execute(text("DROP TABLE chat_messages;"))
            await conn.run_sync(drop_table_sync)
            logger.info("Existing chat_messages table dropped.")
            # No return here, proceed to create
        
        # Create the chat_messages table using run_sync
        logger.info("Creating new chat_messages table...")
        def create_table_sync(sync_conn):
            # Execute each statement individually
            sync_conn.execute(text(""" 
                CREATE TABLE chat_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sender_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                    receiver_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
                    content TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL
                );
            """))
            
            # Add indexes
            sync_conn.execute(text("CREATE INDEX ix_private_message_pair ON chat_messages (sender_id, receiver_id, created_at);"))
            sync_conn.execute(text("CREATE INDEX ix_public_messages ON chat_messages (created_at) WHERE receiver_id IS NULL;"))
            sync_conn.execute(text("CREATE INDEX ix_chat_messages_sender_id ON chat_messages (sender_id);"))
            sync_conn.execute(text("CREATE INDEX ix_chat_messages_receiver_id ON chat_messages (receiver_id);"))
            sync_conn.execute(text("CREATE INDEX ix_chat_messages_created_at ON chat_messages (created_at DESC);"))
            
        await conn.run_sync(create_table_sync)
        
        logger.info("Successfully created chat_messages table.")

async def main():
    """Run the migration."""
    try:
        await create_chat_tables()
        logger.info("Chat system migration completed successfully.")
    except Exception as e:
        logger.error(f"Error during migration: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main()) 