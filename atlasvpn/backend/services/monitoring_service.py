from typing import Dict, Any, Optional, List
from datetime import datetime, timed<PERSON><PERSON>
from sqlalchemy.orm import Session
from redis import Redis
import psutil
import logging
import json
import aiohttp
import os
from fastapi import HTTPException
import asyncio
from models import User, Task, TaskCompletion, Transaction, SecurityLog, TaskVerification
from sqlalchemy import func, and_, or_, text
from prometheus_client import Counter, Gauge, Histogram, REGISTRY, CollectorRegistry
from prometheus_client.metrics import MetricW<PERSON>perB<PERSON>
from .cache_service import CacheService

logger = logging.getLogger(__name__)

# Environment variables for monitoring
MONITOR_BOT_TOKEN = os.getenv("MONITOR_BOT_TOKEN")
MONITOR_BOT_ADMIN = os.getenv("MONITOR_BOT_ADMIN")
ALERT_THRESHOLDS = {
    "cpu_percent": 80.0,  # Alert if CPU usage > 80%
    "memory_percent": 85.0,  # Alert if memory usage > 85%
    "disk_percent": 90.0,  # Alert if disk usage > 90%
    "failed_logins": 10,  # Alert if > 10 failed logins in 5 minutes
    "high_risk_events": 5,  # Alert if > 5 high risk events in 5 minutes
}

# Prometheus metrics
class PrometheusMetrics:
    def __init__(self):
        # System metrics
        self.cpu_usage = Gauge('system_cpu_usage_percent', 'CPU usage percentage')
        self.memory_usage = Gauge('system_memory_usage_bytes', 'Memory usage in bytes')
        self.disk_usage = Gauge('system_disk_usage_bytes', 'Disk usage in bytes')
        
        # Redis metrics
        self.redis_connected_clients = Gauge('redis_connected_clients', 'Number of connected Redis clients')
        self.redis_memory_used = Gauge('redis_memory_used_bytes', 'Redis memory usage in bytes')
        self.redis_ops_per_sec = Gauge('redis_operations_per_second', 'Redis operations per second')
        self.redis_hits = Counter('redis_cache_hits_total', 'Total number of Redis cache hits')
        self.redis_misses = Counter('redis_cache_misses_total', 'Total number of Redis cache misses')
        
        # Application metrics
        self.active_users = Gauge('app_active_users', 'Number of active users')
        self.total_tasks = Gauge('app_total_tasks', 'Total number of tasks')
        self.task_completions = Counter('app_task_completions_total', 'Total number of task completions')
        self.api_requests = Counter('app_api_requests_total', 'Total API requests', ['method', 'path', 'status'])
        self.request_duration = Histogram('app_request_duration_seconds', 'Request duration in seconds')
        
        # Security metrics
        self.security_events = Counter('security_events_total', 'Total security events', ['type', 'risk_level'])
        self.rate_limits_hit = Counter('rate_limits_hit_total', 'Total rate limit hits', ['action_type'])
        self.blocked_ips = Gauge('security_blocked_ips', 'Number of currently blocked IPs')

class MonitoringService:
    """Comprehensive system monitoring service with Prometheus integration"""
    
    def __init__(self, db: Session, cache_service: CacheService, redis_client: Optional[Any] = None):
        self.db = db
        self.cache = cache_service
        self.redis = redis_client
        self.REDIS_MONITOR_PREFIX = "system_monitor:"
        self.metrics = PrometheusMetrics()
        self.logger = logging.getLogger(__name__)
        
        # Cache keys
        self.TASK_STATS_KEY = "monitoring:task_stats"
        self.USER_STATS_KEY = "monitoring:user_stats"
        self.PERFORMANCE_STATS_KEY = "monitoring:performance_stats"
        
    async def initialize(self):
        """Initialize monitoring service"""
        try:
            # Test Redis connection if available
            if self.redis:
                client = await self.redis.get_client()
                await client.ping()
                
            # Initialize metrics
            if hasattr(self.metrics, 'initialize'):
                await self.metrics.initialize()
                
            self.logger.info("Monitoring service initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize monitoring service: {str(e)}")
            return False
            
    async def get_system_metrics(self) -> Dict[str, Any]:
        """Get current system metrics and update Prometheus"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Update Prometheus metrics
            self.metrics.cpu_usage.set(cpu_percent)
            self.metrics.memory_usage.set(memory.used)
            self.metrics.disk_usage.set(disk.used)
            
            metrics = {
                "timestamp": datetime.utcnow().isoformat(),
                "cpu": {
                    "percent": cpu_percent,
                    "cores": psutil.cpu_count(),
                    "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                },
                "memory": {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percent": memory.percent
                },
                "disk": {
                    "total": disk.total,
                    "used": disk.used,
                    "free": disk.free,
                    "percent": disk.percent
                },
                "network": self._get_network_stats(),
                "processes": len(psutil.pids())
            }
            
            # Store metrics in Redis with TTL
            await self._store_metrics(metrics)
            
            # Check thresholds and send alerts if needed
            await self._check_thresholds(metrics)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting system metrics: {str(e)}")
            return {"error": str(e)}
            
    def _get_network_stats(self) -> Dict[str, Any]:
        """Get network statistics"""
        net_io = psutil.net_io_counters()
        return {
            "bytes_sent": net_io.bytes_sent,
            "bytes_recv": net_io.bytes_recv,
            "packets_sent": net_io.packets_sent,
            "packets_recv": net_io.packets_recv,
            "error_in": net_io.errin,
            "error_out": net_io.errout,
            "drop_in": net_io.dropin,
            "drop_out": net_io.dropout
        }
        
    async def _store_metrics(self, metrics: Dict[str, Any]) -> None:
        """Store metrics in Redis with TTL"""
        try:
            date_key = datetime.utcnow().strftime('%Y-%m-%d-%H')
            key = f"{self.REDIS_MONITOR_PREFIX}metrics:{date_key}"
            
            # Store as JSON string
            await self.redis.lpush(key, json.dumps(metrics))
            
            # Keep only last 720 entries (1 hour of data at 5-second intervals)
            await self.redis.ltrim(key, 0, 719)
            
            # Set expiry for 48 hours
            await self.redis.expire(key, 172800)
            
        except Exception as e:
            logger.error(f"Error storing metrics: {str(e)}")
            
    async def _check_thresholds(self, metrics: Dict[str, Any]) -> None:
        """Check metrics against thresholds and send alerts"""
        alerts = []
        
        # Check CPU
        if metrics["cpu"]["percent"] > ALERT_THRESHOLDS["cpu_percent"]:
            alerts.append(f"🚨 High CPU Usage: {metrics['cpu']['percent']}%")
            
        # Check Memory
        if metrics["memory"]["percent"] > ALERT_THRESHOLDS["memory_percent"]:
            alerts.append(f"🚨 High Memory Usage: {metrics['memory']['percent']}%")
            
        # Check Disk
        if metrics["disk"]["percent"] > ALERT_THRESHOLDS["disk_percent"]:
            alerts.append(f"🚨 High Disk Usage: {metrics['disk']['percent']}%")
            
        if alerts:
            await self._send_telegram_alert("\n".join(alerts))
            
    async def get_application_stats(self) -> Dict[str, Any]:
        """Get application statistics and update Prometheus"""
        try:
            now = datetime.utcnow()
            hour_ago = now - timedelta(hours=1)
            day_ago = now - timedelta(days=1)
            
            # Get various stats
            stats = {
                "users": {
                    "total": await self.db.query(User).count(),
                    "active_today": await self.db.query(User).filter(
                        User.last_login >= day_ago
                    ).count(),
                    "new_today": await self.db.query(User).filter(
                        User.created_at >= day_ago
                    ).count()
                },
                "tasks": {
                    "total_active": await self.db.query(Task).filter(
                        Task.is_active == True
                    ).count(),
                    "completions_today": await self.db.query(TaskCompletion).filter(
                        TaskCompletion.completed_at >= day_ago
                    ).count()
                },
                "security": {
                    "failed_logins_hour": await self.db.query(SecurityLog).filter(
                        and_(
                            SecurityLog.action_type == "login",
                            SecurityLog.status == "failure",
                            SecurityLog.created_at >= hour_ago
                        )
                    ).count(),
                    "high_risk_events_hour": await self.db.query(SecurityLog).filter(
                        and_(
                            SecurityLog.risk_level.in_(["high", "critical"]),
                            SecurityLog.created_at >= hour_ago
                        )
                    ).count()
                },
                "transactions": {
                    "count_today": await self.db.query(Transaction).filter(
                        Transaction.created_at >= day_ago
                    ).count(),
                    "volume_today": float(await self.db.query(
                        func.sum(Transaction.amount)
                    ).filter(
                        Transaction.created_at >= day_ago
                    ).scalar() or 0)
                }
            }
            
            # Update Prometheus metrics
            self.metrics.active_users.set(stats["users"]["active_today"])
            self.metrics.total_tasks.set(stats["tasks"]["total_active"])
            self.metrics.task_completions._value.set(stats["tasks"]["completions_today"])
            
            # Check security thresholds
            if stats["security"]["failed_logins_hour"] > ALERT_THRESHOLDS["failed_logins"]:
                await self._send_telegram_alert(
                    f"🚨 High number of failed logins: {stats['security']['failed_logins_hour']} in the last hour"
                )
                
            if stats["security"]["high_risk_events_hour"] > ALERT_THRESHOLDS["high_risk_events"]:
                await self._send_telegram_alert(
                    f"🚨 High number of security events: {stats['security']['high_risk_events_hour']} in the last hour"
                )
                
            return stats
            
        except Exception as e:
            logger.error(f"Error getting application stats: {str(e)}")
            return {"error": str(e)}
            
    async def get_redis_stats(self) -> Dict[str, Any]:
        """Get Redis server statistics and update Prometheus"""
        try:
            info = await self.redis.info()
            
            # Update Prometheus metrics
            self.metrics.redis_connected_clients.set(info.get("connected_clients", 0))
            self.metrics.redis_memory_used.set(info.get("used_memory", 0))
            self.metrics.redis_ops_per_sec.set(info.get("instantaneous_ops_per_sec", 0))
            
            # Update hit/miss counters
            hits = info.get("keyspace_hits", 0)
            misses = info.get("keyspace_misses", 0)
            self.metrics.redis_hits._value.set(hits)
            self.metrics.redis_misses._value.set(misses)
            
            return {
                "connected_clients": info.get("connected_clients", 0),
                "used_memory": info.get("used_memory", 0),
                "used_memory_peak": info.get("used_memory_peak", 0),
                "total_connections_received": info.get("total_connections_received", 0),
                "total_commands_processed": info.get("total_commands_processed", 0),
                "instantaneous_ops_per_sec": info.get("instantaneous_ops_per_sec", 0),
                "keyspace_hits": hits,
                "keyspace_misses": misses,
                "hit_rate": hits / (hits + misses) if (hits + misses) > 0 else 0
            }
        except Exception as e:
            logger.error(f"Error getting Redis stats: {str(e)}")
            return {"error": str(e)}
            
    async def get_historical_metrics(
        self,
        hours: int = 24
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Get historical system metrics"""
        try:
            metrics = []
            now = datetime.utcnow()
            
            for hour in range(hours):
                date_key = (now - timedelta(hours=hour)).strftime('%Y-%m-%d-%H')
                key = f"{self.REDIS_MONITOR_PREFIX}metrics:{date_key}"
                
                # Get all metrics for this hour
                hour_metrics = await self.redis.lrange(key, 0, -1)
                metrics.extend([json.loads(m) for m in hour_metrics])
                
            return {
                "start_time": (now - timedelta(hours=hours)).isoformat(),
                "end_time": now.isoformat(),
                "metrics": metrics
            }
            
        except Exception as e:
            logger.error(f"Error getting historical metrics: {str(e)}")
            return {"error": str(e)}
            
    async def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        try:
            metrics = await self.get_system_metrics()
            app_stats = await self.get_application_stats()
            redis_stats = await self.get_redis_stats()
            
            # Calculate health scores
            cpu_health = 100 - metrics["cpu"]["percent"]
            memory_health = 100 - metrics["memory"]["percent"]
            disk_health = 100 - metrics["disk"]["percent"]
            
            # Calculate overall health score (0-100)
            overall_health = (cpu_health + memory_health + disk_health) / 3
            
            return {
                "status": "healthy" if overall_health > 70 else "degraded" if overall_health > 50 else "critical",
                "health_score": overall_health,
                "components": {
                    "cpu": {
                        "status": "healthy" if cpu_health > 70 else "degraded" if cpu_health > 50 else "critical",
                        "score": cpu_health
                    },
                    "memory": {
                        "status": "healthy" if memory_health > 70 else "degraded" if memory_health > 50 else "critical",
                        "score": memory_health
                    },
                    "disk": {
                        "status": "healthy" if disk_health > 70 else "degraded" if disk_health > 50 else "critical",
                        "score": disk_health
                    },
                    "redis": {
                        "status": "healthy" if "error" not in redis_stats else "critical",
                        "metrics": redis_stats
                    },
                    "application": {
                        "status": "healthy" if "error" not in app_stats else "critical",
                        "metrics": app_stats
                    }
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting system health: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
            
    async def get_security_metrics(self) -> Dict[str, Any]:
        """Get security metrics and update Prometheus"""
        try:
            now = datetime.utcnow()
            hour_ago = now - timedelta(hours=1)
            
            # Get security events by type and risk level
            security_events = await self.db.query(
                SecurityLog.action_type,
                SecurityLog.risk_level,
                func.count(SecurityLog.id)
            ).filter(
                SecurityLog.created_at >= hour_ago
            ).group_by(
                SecurityLog.action_type,
                SecurityLog.risk_level
            ).all()
            
            # Update Prometheus metrics
            for action_type, risk_level, count in security_events:
                self.metrics.security_events.labels(
                    type=action_type,
                    risk_level=risk_level
                )._value.set(count)
            
            # Get blocked IPs count
            blocked_ips = await self.db.query(func.count(SecurityLog.ip_address.distinct())).filter(
                SecurityLog.is_blocked == True,
                SecurityLog.blocked_until > now
            ).scalar()
            
            self.metrics.blocked_ips.set(blocked_ips)
            
            return {
                "events_by_type": {
                    f"{action_type}_{risk_level}": count
                    for action_type, risk_level, count in security_events
                },
                "blocked_ips": blocked_ips
            }
            
        except Exception as e:
            logger.error(f"Error getting security metrics: {str(e)}")
            return {"error": str(e)}
            
    async def get_prometheus_metrics(self) -> str:
        """Get all metrics in Prometheus format"""
        try:
            # Update all metrics first
            await asyncio.gather(
                self.get_system_metrics(),
                self.get_redis_stats(),
                self.get_application_stats(),
                self.get_security_metrics()
            )
            
            # Generate metrics output
            from prometheus_client import generate_latest
            return generate_latest(REGISTRY).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error generating Prometheus metrics: {str(e)}")
            return f"# Error generating metrics: {str(e)}"
            
    async def _send_telegram_alert(self, message: str, level: str = "info") -> None:
        """Send alert to Telegram with proper formatting"""
        if not MONITOR_BOT_TOKEN or not MONITOR_BOT_ADMIN:
            return
            
        try:
            # Add emoji based on level
            emoji = {
                "info": "ℹ️",
                "warning": "⚠️",
                "error": "🚨",
                "critical": "💀"
            }.get(level, "ℹ️")
            
            formatted_message = f"{emoji} <b>Monitoring Alert</b>\n{message}"
            
            url = f"https://api.telegram.org/bot{MONITOR_BOT_TOKEN}/sendMessage"
            async with aiohttp.ClientSession() as session:
                await session.post(url, json={
                    "chat_id": MONITOR_BOT_ADMIN,
                    "text": formatted_message,
                    "parse_mode": "HTML"
                })
        except Exception as e:
            logger.error(f"Error sending Telegram alert: {str(e)}")
            
    async def record_request_metric(
        self,
        method: str,
        path: str,
        status_code: int,
        duration: float
    ) -> None:
        """Record API request metrics"""
        try:
            self.metrics.api_requests.labels(
                method=method,
                path=path,
                status=status_code
            ).inc()
            
            self.metrics.request_duration.observe(duration)
        except Exception as e:
            logger.error(f"Error recording request metric: {str(e)}")

    async def get_task_statistics(self, force_refresh: bool = False) -> Dict:
        """Get comprehensive task statistics"""
        cache_key = self.TASK_STATS_KEY
        
        if not force_refresh:
            cached_stats = await self.cache.get_cached_data(cache_key)
            if cached_stats:
                return cached_stats
        
        try:
            # Get basic task stats
            total_tasks = self.db.query(Task).count()
            active_tasks = self.db.query(Task).filter(Task.is_active == True).count()
            
            # Get completion stats
            completion_stats = self.db.query(
                TaskCompletion.status,
                func.count(TaskCompletion.id).label('count')
            ).group_by(TaskCompletion.status).all()
            
            completion_counts = {
                status.value: count for status, count in completion_stats
            }
            
            # Calculate success rate
            total_completions = sum(completion_counts.values())
            success_rate = (
                completion_counts.get(TaskStatus.COMPLETED.value, 0) / total_completions
                if total_completions > 0 else 0
            )
            
            # Get verification stats
            verification_stats = self.db.query(
                func.count(TaskVerification.id).label('total'),
                func.avg(
                    (TaskVerification.verified_at - TaskCompletion.started_at)
                    .cast(text('FLOAT'))
                ).label('avg_time')
            ).join(
                TaskCompletion,
                TaskVerification.task_completion_id == TaskCompletion.id
            ).first()
            
            stats = {
                "total_tasks": total_tasks,
                "active_tasks": active_tasks,
                "completion_stats": completion_counts,
                "success_rate": success_rate,
                "verification_stats": {
                    "total_verifications": verification_stats[0] or 0,
                    "avg_verification_time": float(verification_stats[1] or 0)
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Cache the results
            await self.cache.set_cached_data(
                cache_key,
                stats,
                expire_time=3600  # 1 hour
            )
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting task statistics: {str(e)}")
            return {}

    async def get_user_statistics(self, force_refresh: bool = False) -> Dict:
        """Get user activity and engagement statistics"""
        cache_key = self.USER_STATS_KEY
        
        if not force_refresh:
            cached_stats = await self.cache.get_cached_data(cache_key)
            if cached_stats:
                return cached_stats
        
        try:
            # Get basic user stats
            total_users = self.db.query(User).count()
            active_users = self.db.query(User).filter(User.is_active == True).count()
            
            # Get user engagement stats
            engagement_stats = self.db.query(
                func.count(TaskCompletion.user_id.distinct()).label('active_users'),
                func.avg(TaskCompletion.current_progress).label('avg_progress'),
                func.count(TaskCompletion.id).label('total_completions')
            ).filter(
                TaskCompletion.started_at >= datetime.utcnow() - timedelta(days=7)
            ).first()
            
            # Get top users
            top_users = self.db.query(
                User.id,
                User.username,
                func.count(TaskCompletion.id).label('completed_tasks')
            ).join(
                TaskCompletion,
                TaskCompletion.user_id == User.id
            ).filter(
                TaskCompletion.status == TaskStatus.COMPLETED
            ).group_by(
                User.id
            ).order_by(
                text('completed_tasks DESC')
            ).limit(10).all()
            
            stats = {
                "total_users": total_users,
                "active_users": active_users,
                "engagement_stats": {
                    "weekly_active_users": engagement_stats[0] or 0,
                    "avg_task_progress": float(engagement_stats[1] or 0),
                    "total_completions": engagement_stats[2] or 0
                },
                "top_users": [
                    {
                        "id": user.id,
                        "username": user.username,
                        "completed_tasks": completed
                    }
                    for user, completed in top_users
                ],
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Cache the results
            await self.cache.set_cached_data(
                cache_key,
                stats,
                expire_time=1800  # 30 minutes
            )
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Error getting user statistics: {str(e)}")
            return {}

    async def get_performance_metrics(self) -> Dict:
        """Get system performance metrics"""
        try:
            # Get database stats
            db_stats = self.db.execute(text("""
                SELECT 
                    (SELECT COUNT(*) FROM users) as total_users,
                    (SELECT COUNT(*) FROM task_completions) as total_completions,
                    (SELECT COUNT(*) FROM transactions) as total_transactions,
                    (SELECT COUNT(*) FROM security_logs) as total_logs
            """)).first()
            
            # Get cache stats
            cache_stats = await self.cache.get_cache_stats()
            
            # Get recent errors
            recent_errors = self.db.query(SecurityLog).filter(
                SecurityLog.status == 'failure',
                SecurityLog.created_at >= datetime.utcnow() - timedelta(hours=24)
            ).count()
            
            # Calculate performance metrics
            metrics = {
                "database": {
                    "total_records": sum(db_stats),
                    "tables": {
                        "users": db_stats[0],
                        "task_completions": db_stats[1],
                        "transactions": db_stats[2],
                        "security_logs": db_stats[3]
                    }
                },
                "cache": cache_stats,
                "errors": {
                    "recent_errors": recent_errors,
                    "error_rate": recent_errors / max(db_stats[1], 1)  # Error rate based on completions
                },
                "timestamp": datetime.utcnow().isoformat()
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {str(e)}")
            return {} 