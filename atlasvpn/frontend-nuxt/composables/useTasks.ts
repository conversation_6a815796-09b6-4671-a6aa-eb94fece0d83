import type { Task, TaskCompletion, DailyTaskStreak, DailyCheckInResponse } from '~/types'

/**
 * Modern tasks composable using Nuxt 4 data fetching patterns
 * Optimized for Telegram Mini App with proper caching and error handling
 */
export const useTasks = () => {
  // Default fetch options optimized for Telegram Mini App
  const defaultOptions = {
    server: false, // Client-side only for Telegram Mini App
    retry: 3,
    retryDelay: 1000,
    timeout: 15000,
    onRequestError({ error }: { error: any }) {
      console.error('[Tasks] Request error:', error)
    },
    onResponseError({ response }: { response: any }) {
      console.error('[Tasks] Response error:', response.status, response.statusText)
    }
  }

  // Task completion state (local state for UI feedback)
  const completingTasks = ref(new Set<number>())
  const claimingTasks = ref(new Set<number>())

  // Available tasks with modern useFetch
  const {
    data: rawTasks,
    pending: isLoadingTasks,
    error: tasksError,
    refresh: refreshTasks
  } = useFetch<Task[]>('/api/tasks/available', {
    ...defaultOptions,
    key: 'available-tasks',
    default: () => [],
    transform: (data: any) => {
      const tasks = data?.data || data || []

      // Process and filter tasks
      const processedTasks = tasks
        .filter((task: any) => task && typeof task === 'object' && task.type)
        .map((task: any) => ({
          ...task,
          avg_completion_time:
            task.avg_completion_time !== undefined &&
            task.avg_completion_time !== null &&
            task.avg_completion_time < 0
              ? null
              : task.avg_completion_time,
        }))

      // Sort tasks by status priority
      processedTasks.sort((a: any, b: any) => {
        const statusPriority: Record<string, number> = {
          pending: 0,
          completed: a.is_claimed ? 3 : 1,
          available: 2,
          failed: 4,
        }
        return statusPriority[a.status] - statusPriority[b.status]
      })

      return processedTasks
    }
  })

  // Daily streak with modern useFetch
  const {
    data: dailyStreak,
    pending: isLoadingStreak,
    error: streakError,
    refresh: refreshDailyStreak
  } = useFetch<DailyTaskStreak>('/api/tasks/daily-streak', {
    ...defaultOptions,
    key: 'daily-task-streak',
    default: () => null,
    transform: (data: any) => data?.data || data
  })

  // Computed tasks (using the processed data from useFetch)
  const tasks = computed(() => rawTasks.value || [])

  // Complete a task with optimistic updates
  const completeTask = async (taskId: number): Promise<TaskCompletion | null> => {
    if (completingTasks.value.has(taskId)) return null

    completingTasks.value.add(taskId)

    // Optimistic update
    const taskIndex = rawTasks.value?.findIndex(t => t.id === taskId) ?? -1
    if (taskIndex !== -1 && rawTasks.value) {
      rawTasks.value[taskIndex] = {
        ...rawTasks.value[taskIndex],
        status: 'completed',
        completed_at: new Date().toISOString()
      }
    }

    try {
      const response = await $fetch<{ data: TaskCompletion }>(`/api/tasks/${taskId}/complete`, {
        method: 'POST'
      })

      // Refresh tasks to get the actual state
      await refreshTasks()

      return response.data
    } catch (error: any) {
      console.error('[Tasks] Failed to complete task:', error)
      // Revert optimistic update on error
      await refreshTasks()
      throw error
    } finally {
      completingTasks.value.delete(taskId)
    }
  }

  // Claim task reward with optimistic updates
  const claimTaskReward = async (taskId: number): Promise<any> => {
    if (claimingTasks.value.has(taskId)) return null

    claimingTasks.value.add(taskId)

    // Optimistic update
    const taskIndex = rawTasks.value?.findIndex(t => t.id === taskId) ?? -1
    if (taskIndex !== -1 && rawTasks.value) {
      rawTasks.value[taskIndex] = {
        ...rawTasks.value[taskIndex],
        is_claimed: true,
        claimed_at: new Date().toISOString()
      }
    }

    try {
      const response = await $fetch(`/api/tasks/${taskId}/claim`, {
        method: 'POST'
      })

      // Refresh tasks to get the actual state
      await refreshTasks()

      return response
    } catch (error: any) {
      console.error('[Tasks] Failed to claim task reward:', error)
      // Revert optimistic update on error
      await refreshTasks()
      throw error
    } finally {
      claimingTasks.value.delete(taskId)
    }
  }

  // Daily check-in
  const dailyCheckIn = async (): Promise<DailyCheckInResponse | null> => {
    try {
      const response = await $fetch<{ data: DailyCheckInResponse }>('/api/tasks/daily-checkin', {
        method: 'POST'
      })

      // Refresh daily streak after check-in
      await refreshDailyStreak()

      return response.data
    } catch (error: any) {
      console.error('[Tasks] Failed to perform daily check-in:', error)
      throw error
    }
  }

  // Verify task completion
  const verifyTask = async (taskId: number): Promise<any> => {
    try {
      const response = await $fetch(`/api/tasks/${taskId}/verify`, {
        method: 'POST'
      })

      // Refresh tasks after verification
      await refreshTasks()

      return response
    } catch (error: any) {
      console.error('[Tasks] Failed to verify task:', error)
      throw error
    }
  }

  // Refresh all task data
  const refreshAllTasks = async () => {
    await Promise.all([
      refreshTasks(),
      refreshDailyStreak()
    ])
  }

  // Computed properties
  const availableTasks = computed(() => 
    tasks.value.filter(task => task.status === 'available')
  )

  const completedTasks = computed(() => 
    tasks.value.filter(task => task.status === 'completed' && !task.is_claimed)
  )

  const claimedTasks = computed(() => 
    tasks.value.filter(task => task.is_claimed)
  )

  const totalTasksCompleted = computed(() => 
    tasks.value.filter(task => task.status === 'completed' || task.is_claimed).length
  )

  const totalRewardsEarned = computed(() => 
    tasks.value
      .filter(task => task.is_claimed)
      .reduce((total, task) => total + (task.reward_amount || 0), 0)
  )

  const canCheckIn = computed(() => {
    if (!dailyStreak.value) return false
    const lastCheckIn = dailyStreak.value.last_checkin_date
    if (!lastCheckIn) return true
    
    const today = new Date().toDateString()
    const lastCheckInDate = new Date(lastCheckIn).toDateString()
    return today !== lastCheckInDate
  })

  return {
    // State (reactive from useFetch)
    tasks: readonly(tasks),
    dailyStreak: readonly(dailyStreak),
    isLoadingTasks: readonly(isLoadingTasks),
    isLoadingStreak: readonly(isLoadingStreak),
    tasksError: readonly(tasksError),
    streakError: readonly(streakError),
    completingTasks: readonly(completingTasks),
    claimingTasks: readonly(claimingTasks),

    // Actions
    refreshTasks,
    refreshDailyStreak,
    refreshAllTasks,
    completeTask,
    claimTaskReward,
    dailyCheckIn,
    verifyTask,

    // Computed
    availableTasks,
    completedTasks,
    claimedTasks,
    totalTasksCompleted,
    totalRewardsEarned,
    canCheckIn
  }
}
