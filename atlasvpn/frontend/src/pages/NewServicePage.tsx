import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import NewServiceForm from '../components/NewServiceForm';
import { motion } from 'framer-motion';
import { api } from '../utils/apiClient';
import { VPNPackage, MarzbanPanel } from '../types';
import LoadingSpinner from '../components/LoadingSpinner';
import { toast } from 'react-hot-toast';

const NewServicePage: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [packages, setPackages] = useState<VPNPackage[]>([]);
  const [panels, setPanels] = useState<MarzbanPanel[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [packagesRes, panelsRes] = await Promise.all([
          api.get<VPNPackage[]>('/packages'),
          api.get<MarzbanPanel[]>('/panels')
        ]);

        if (!packagesRes.data?.length) {
          throw new Error('No packages available');
        }

        setPackages(packagesRes.data);
        setPanels(panelsRes.data);
      } catch (error) {
        console.error('Failed to load data:', error);
        toast.error('Failed to load data');
        setError('Failed to load required data');
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [navigate]);

  if (loading)
    return (
      <div
        className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-12 
                    flex justify-center items-center"
      >
        <LoadingSpinner size="lg" />
      </div>
    );

  return (
    <div className="min-h-screen bg-gradient-to-b from-gray-900 to-black py-12">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-2xl mx-auto"
        >
          <div className="flex items-center justify-between mb-8">
            <h1 className="text-2xl font-bold text-white">Create New Service</h1>
            <button
              onClick={() => navigate('/dashboard')}
              className="px-4 py-2 text-sm text-white/80 bg-white/5 rounded-lg 
                       hover:bg-white/10 transition-colors"
            >
              Back to Dashboard
            </button>
          </div>

          <NewServiceForm
            vpnPackage={null}
            availablePackages={packages}
            availablePanels={panels}
            onSuccess={async () => {
              navigate('/dashboard');
            }}
            onClose={() => navigate('/dashboard')}
          />
        </motion.div>
      </div>
    </div>
  );
};

export default NewServicePage;
