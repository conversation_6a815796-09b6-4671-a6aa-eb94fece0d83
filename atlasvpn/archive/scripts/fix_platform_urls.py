#!/usr/bin/env python
"""
Fix Platform URLs in Tasks

This script inspects tasks in the database and adds or corrects platform_url fields
for tasks that should have them (YouTube, Instagram, Telegram, etc.).
"""
import sys
import os
from pathlib import Path
import logging
import json
from typing import Dict, List, Optional, Any

# Add backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

# Import database and models
from database import init_db, get_db_context
from models import Task, TaskType
from sqlalchemy import inspect, select

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TaskInspector:
    """Inspect and fix tasks in the database"""
    
    def __init__(self):
        """Initialize the task inspector"""
        self.stats = {
            "total_tasks": 0,
            "updated_tasks": 0,
            "types": {}
        }
    
    def inspect_task_structure(self):
        """Inspect the structure of Task objects in the database"""
        try:
            with get_db_context() as db:
                # Get one task to inspect
                task = db.query(Task).first()
                if not task:
                    logger.info("No tasks found in the database.")
                    return False
                
                # Get all column names and their values
                inspector = inspect(task)
                attrs = {}
                for attr in inspector.attrs:
                    attrs[attr.key] = getattr(task, attr.key)
                
                # Print task structure
                logger.info("Task Structure:")
                for key, value in attrs.items():
                    logger.info(f"  {key}: {value} ({type(value).__name__})")
                
                return True
        except Exception as e:
            logger.error(f"Error inspecting task structure: {str(e)}")
            return False
    
    def list_tasks_by_type(self, fix=False):
        """List all tasks grouped by type and check which ones are missing platform URLs"""
        try:
            with get_db_context() as db:
                # Get all tasks
                tasks = db.query(Task).all()
                self.stats["total_tasks"] = len(tasks)
                
                # Group by type
                by_type = {}
                for task in tasks:
                    task_type = task.type.value if hasattr(task.type, 'value') else str(task.type)
                    if task_type not in by_type:
                        by_type[task_type] = []
                    
                    by_type[task_type].append(task)
                
                # Print summary of tasks by type
                logger.info("Tasks by Type:")
                for task_type, tasks_list in by_type.items():
                    self.stats["types"][task_type] = len(tasks_list)
                    logger.info(f"  {task_type}: {len(tasks_list)} tasks")
                
                # Check which tasks need platform URLs
                if fix:
                    self.fix_platform_urls(db, by_type)
                
                db.commit()
                
                return True
        except Exception as e:
            logger.error(f"Error listing tasks by type: {str(e)}")
            return False
    
    def fix_platform_urls(self, db, tasks_by_type):
        """Fix platform URLs for tasks that should have them"""
        logger.info("Checking for tasks that need platform URLs...")
        
        # Define sample platform URLs for each task type
        sample_urls = {
            TaskType.YOUTUBE_VIEW: "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            TaskType.INSTAGRAM_VIEW: "https://www.instagram.com/p/CxYzAbcDEf/",
            TaskType.INSTAGRAM_FOLLOW: "https://www.instagram.com/ourcompany/",
            TaskType.TELEGRAM_CHANNEL: "https://t.me/our_channel",
            TaskType.TWITTER_FOLLOW: "https://twitter.com/ourcompany",
            TaskType.WEBSITE_VISIT: "https://ourwebsite.com/features"
        }
        
        # Counter for updated tasks
        updated_count = 0
        
        # List of task types that should have platform URLs
        url_required_types = [
            TaskType.YOUTUBE_VIEW,
            TaskType.INSTAGRAM_VIEW,
            TaskType.INSTAGRAM_FOLLOW,
            TaskType.TELEGRAM_CHANNEL, 
            TaskType.TWITTER_FOLLOW,
            TaskType.WEBSITE_VISIT
        ]
        
        # Go through each task type and fix missing platform URLs
        for task_type in url_required_types:
            type_key = task_type.value if hasattr(task_type, 'value') else str(task_type)
            if type_key not in tasks_by_type:
                continue
                
            # Get default URL for this type
            default_url = sample_urls.get(task_type, "")
            if not default_url:
                continue
                
            # Check each task of this type
            logger.info(f"Checking {len(tasks_by_type[type_key])} {type_key} tasks...")
            for task in tasks_by_type[type_key]:
                # Check if platform URL is missing
                if not task.platform_url:
                    task.platform_url = default_url
                    updated_count += 1
                    logger.info(f"Updated task {task.id} ({task.name}) with URL: {default_url}")
        
        self.stats["updated_tasks"] = updated_count
        logger.info(f"Updated {updated_count} tasks with proper platform URLs")
    
    def print_report(self):
        """Print final report"""
        logger.info("=" * 50)
        logger.info("Task Inspection Report")
        logger.info("=" * 50)
        logger.info(f"Total tasks: {self.stats['total_tasks']}")
        logger.info(f"Tasks by type:")
        for task_type, count in self.stats["types"].items():
            logger.info(f"  {task_type}: {count} tasks")
        logger.info(f"Updated tasks: {self.stats['updated_tasks']}")
        logger.info("=" * 50)
        
    def print_missing_platform_urls(self):
        """List tasks that should have platform URLs but don't"""
        try:
            with get_db_context() as db:
                # List of task types that should have platform URLs
                url_required_types = [
                    TaskType.YOUTUBE_VIEW,
                    TaskType.INSTAGRAM_VIEW,
                    TaskType.INSTAGRAM_FOLLOW,
                    TaskType.TELEGRAM_CHANNEL, 
                    TaskType.TWITTER_FOLLOW,
                    TaskType.WEBSITE_VISIT
                ]
                
                # Check for tasks without platform URLs
                missing_url_tasks = db.query(Task).filter(
                    Task.type.in_(url_required_types),
                    (Task.platform_url == None) | (Task.platform_url == '')
                ).all()
                
                if not missing_url_tasks:
                    logger.info("No tasks missing platform URLs!")
                    return True
                
                logger.info(f"Found {len(missing_url_tasks)} tasks missing platform URLs:")
                for task in missing_url_tasks:
                    task_type = task.type.value if hasattr(task.type, 'value') else str(task.type)
                    logger.info(f"  ID: {task.id}, Name: {task.name}, Type: {task_type}")
                
                return True
        except Exception as e:
            logger.error(f"Error checking for missing platform URLs: {str(e)}")
            return False

def main():
    """Main function to run the task inspector"""
    try:
        logger.info("Starting task inspector...")
        
        # Check for fix flag
        fix = "--fix" in sys.argv
        
        # Initialize database
        init_db()
        logger.info("Database initialized")
        
        # Create task inspector
        inspector = TaskInspector()
        
        # Inspect task structure
        inspector.inspect_task_structure()
        
        # List tasks by type and fix platform URLs if requested
        inspector.list_tasks_by_type(fix=fix)
        
        # Print tasks missing platform URLs
        inspector.print_missing_platform_urls()
        
        # Print report
        inspector.print_report()
        
        if not fix:
            logger.info("Run with --fix to update platform URLs for tasks without them")
        
        logger.info("Task inspection completed successfully")
    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main() 