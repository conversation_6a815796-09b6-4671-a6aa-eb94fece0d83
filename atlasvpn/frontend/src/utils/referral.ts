/**
 * Utilities for handling referrals and building referral links
 */

/**
 * Build a referral link using the provided referral code
 *
 * @param referralCode - The referral code to use
 * @returns A referral link that can be shared
 */
export function buildReferralLink(referralCode: string): string {
  if (!referralCode) {
    return '';
  }

  // Try to get the bot username from environment configuration
  const botUsername = (window as any)?.ENV_CONFIG?.BOT_USERNAME;

  if (botUsername) {
    // If bot username is available, build a Telegram deep link
    return `https://t.me/${botUsername}/app?startapp=ref_${referralCode}`;
  }

  // Fallback to using the current origin (web app based)
  return `${window.location.origin}/start?ref=${referralCode}`;
}

/**
 * Format a referral link for sharing via Telegram
 *
 * @param referralLink - The referral link to format
 * @returns A formatted link escaped for MarkdownV2 format
 */
export function formatReferralLinkForTelegram(referralLink: string): string {
  if (!referralLink) {
    return '';
  }

  // Escape MarkdownV2 special characters
  return referralLink.replace(/[_*[\]()~`>#+\-=|{}.!]/g, '\\$&');
}

/**
 * Create a clean referral message that works with HTML formatting
 *
 * @param referralLink - The referral link to include
 * @returns Formatted message with HTML
 */
export function createReferralMessageHTML(referralLink: string): string {
  if (!referralLink) {
    return '';
  }

  // Updated message for a verse game
  return `
🚀 <b>Explore the VIPVerse with me!</b>

Join this exciting verse game and start your adventure. Use my invite link:
<a href="${referralLink}">${referralLink}</a>
  `.trim();
}

/**
 * Create a plain text referral message
 *
 * @param referralLink - The referral link to include
 * @returns Plain text message without formatting
 */
export function createReferralMessagePlain(referralLink: string): string {
  if (!referralLink) {
    return '';
  }

  // Updated message for a verse game
  return `
🚀 Explore the VIPVerse with me!

Join this exciting verse game and start your adventure. Use my invite link:
${referralLink}
  `.trim();
}

/**
 * Get referral info from either dedicated referral info or user data
 *
 * @param referralInfo - The referral info object from API
 * @param user - The user object from auth
 * @returns An object with referral code and link
 */
export function getReferralInfo(
  referralInfo: any,
  user: any
): {
  referralCode: string;
  referralLink: string;
} {
  // Get referral code from referralInfo or user
  const referralCode = referralInfo?.referral_code || user?.referral_code || '';

  // Get referral link from referralInfo or build it
  const referralLink = referralInfo?.referral_link || buildReferralLink(referralCode);

  return {
    referralCode,
    referralLink,
  };
}
