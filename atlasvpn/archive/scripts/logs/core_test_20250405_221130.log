
Starting Core Task API Tests...
2025-04-05 22:11:54,492 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:11:54,496 - INFO - Retrieved CSRF token for admin login
2025-04-05 22:11:56,685 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 22:11:56,726 - INFO - Admin login successful
2025-04-05 22:11:56,770 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 22:11:56,773 - INFO - Retrieved CSRF token for user login
2025-04-05 22:11:59,526 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 22:11:59,612 - INFO - User login successful
2025-04-05 22:11:59,802 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:11:59,804 - INFO - Created YOUTUBE_VIEW task with ID: 387
2025-04-05 22:11:59,893 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 22:11:59,937 - INFO - Created TELEGRAM_CHANNEL task with ID: 388
2025-04-05 22:12:00,515 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/387/start "HTTP/1.1 200 OK"
2025-04-05 22:12:00,557 - INFO - Started task 387
2025-04-05 22:12:00,676 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/387/verify "HTTP/1.1 200 OK"
2025-04-05 22:12:00,717 - INFO - Verified task 387: True
2025-04-05 22:12:01,814 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/387/verify "HTTP/1.1 200 OK"
2025-04-05 22:12:01,815 - INFO - Verified task 387: True
2025-04-05 22:12:01,917 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/387/claim "HTTP/1.1 200 OK"
2025-04-05 22:12:01,961 - INFO - Claimed reward for task 387
2025-04-05 22:12:01,962 - INFO - YouTube task flow completed successfully
2025-04-05 22:12:02,718 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/388/start "HTTP/1.1 200 OK"
2025-04-05 22:12:02,761 - INFO - Started task 388
2025-04-05 22:12:02,907 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/388/verify "HTTP/1.1 200 OK"
2025-04-05 22:12:02,949 - INFO - Verified task 388: True
2025-04-05 22:12:03,560 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/388/claim "HTTP/1.1 200 OK"
2025-04-05 22:12:03,601 - INFO - Claimed reward for task 388
2025-04-05 22:12:03,601 - INFO - Telegram task flow completed successfully
2025-04-05 22:12:03,714 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/387/toggle "HTTP/1.1 200 OK"
2025-04-05 22:12:03,753 - INFO - Toggled task 387 active status to False
2025-04-05 22:12:03,851 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/387/toggle "HTTP/1.1 200 OK"
2025-04-05 22:12:03,893 - INFO - Toggled task 387 active status to True
2025-04-05 22:12:03,957 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/387/start "HTTP/1.1 400 Bad Request"
2025-04-05 22:12:04,464 - ERROR - Failed to start task 387: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
2025-04-05 22:12:04,464 - INFO - Admin functions tested successfully
  Setup completed 
  Running tests...

Task API Test Report
       Test Summary       
┏━━━━━━━━━━━━━━┳━━━━━━━━━┓
┃ Metric       ┃ Value   ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━┩
│ Total Tests  │ 3       │
│ Passed       │ 3       │
│ Failed       │ 0       │
│ Success Rate │ 100.00% │
└──────────────┴─────────┘

Report saved to /root/project/backend/scripts/logs/task_api_test_report_20250405_221204.json
