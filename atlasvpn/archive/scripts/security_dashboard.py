#!/usr/bin/env python3
"""
Security Dashboard Generator

This script generates a security dashboard HTML file with visualizations of security metrics.
It processes log files and Redis data to create charts and graphs for monitoring security.
"""

import os
import sys
import json
import logging
import argparse
from datetime import datetime, timedelta
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import seaborn as sns
import numpy as np
import re
from typing import Dict, List, Any, Optional, Tuple
import redis
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join('logs', 'security_dashboard.log'))
    ]
)
logger = logging.getLogger('security_dashboard')

class SecurityDashboardGenerator:
    """Class to generate security monitoring dashboard"""
    
    def __init__(
        self,
        log_dir: str = 'logs',
        output_dir: str = 'reports',
        redis_host: str = 'localhost',
        redis_port: int = 6379,
        redis_password: Optional[str] = None,
        days_to_analyze: int = 7
    ):
        """
        Initialize the dashboard generator
        
        Args:
            log_dir: Directory containing log files
            output_dir: Directory to save the dashboard
            redis_host: Redis host
            redis_port: Redis port
            redis_password: Redis password
            days_to_analyze: Number of days of logs to analyze
        """
        self.log_dir = log_dir
        self.output_dir = output_dir
        self.days_to_analyze = days_to_analyze
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize Redis client
        try:
            self.redis = redis.Redis(
                host=redis_host,
                port=redis_port,
                password=redis_password,
                decode_responses=True
            )
            logger.info(f"Connected to Redis at {redis_host}:{redis_port}")
        except Exception as e:
            logger.error(f"Failed to connect to Redis: {str(e)}")
            self.redis = None
            
        # Initialize data structures
        self.security_logs = []
        self.rate_limit_data = []
        self.bot_detection_data = []
        self.captcha_data = []
        
        # Set up plot style
        sns.set_style("whitegrid")
        plt.rcParams['figure.figsize'] = (12, 6)
        
    def load_logs(self) -> bool:
        """
        Load security logs from log files
        
        Returns:
            bool: True if logs were loaded successfully
        """
        try:
            # Get list of log files
            log_files = []
            for root, _, files in os.walk(self.log_dir):
                for file in files:
                    if file.endswith('.log') and ('security' in file or 'bot' in file or 'rate' in file):
                        log_files.append(os.path.join(root, file))
            
            if not log_files:
                logger.warning(f"No security log files found in {self.log_dir}")
                return False
                
            logger.info(f"Found {len(log_files)} security log files")
            
            # Process each log file
            for log_file in log_files:
                self._process_log_file(log_file)
                
            return True
            
        except Exception as e:
            logger.error(f"Error loading logs: {str(e)}")
            return False
            
    def _process_log_file(self, log_file: str) -> None:
        """
        Process a single log file
        
        Args:
            log_file: Path to log file
        """
        logger.info(f"Processing log file: {log_file}")
        
        try:
            # Read log file
            with open(log_file, 'r') as f:
                lines = f.readlines()
                
            # Extract log entries
            for line in lines:
                try:
                    # Parse timestamp
                    timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                    if not timestamp_match:
                        continue
                        
                    timestamp = datetime.strptime(timestamp_match.group(1), '%Y-%m-%d %H:%M:%S')
                    
                    # Skip old logs
                    if timestamp < datetime.now() - timedelta(days=self.days_to_analyze):
                        continue
                        
                    # Extract log level
                    level_match = re.search(r'(INFO|WARNING|ERROR|CRITICAL)', line)
                    level = level_match.group(1) if level_match else 'UNKNOWN'
                    
                    # Extract IP address
                    ip_match = re.search(r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})', line)
                    ip = ip_match.group(1) if ip_match else None
                    
                    # Categorize log entry
                    category = 'general'
                    if 'bot' in line.lower() or 'suspicious' in line.lower():
                        category = 'bot_detection'
                    elif 'rate limit' in line.lower() or 'blocked' in line.lower():
                        category = 'rate_limit'
                    elif 'captcha' in line.lower():
                        category = 'captcha'
                        
                    # Create log entry
                    log_entry = {
                        'timestamp': timestamp,
                        'level': level,
                        'ip': ip,
                        'category': category,
                        'message': line.strip(),
                    }
                    
                    # Add to appropriate list
                    self.security_logs.append(log_entry)
                    if category == 'bot_detection':
                        self.bot_detection_data.append(log_entry)
                    elif category == 'rate_limit':
                        self.rate_limit_data.append(log_entry)
                    elif category == 'captcha':
                        self.captcha_data.append(log_entry)
                        
                except Exception as e:
                    logger.debug(f"Error parsing log line: {str(e)}")
                    continue
                    
            logger.info(f"Processed {len(lines)} lines from {log_file}")
            
        except Exception as e:
            logger.error(f"Error processing log file {log_file}: {str(e)}")
            
    def _get_redis_metrics(self) -> Dict[str, Any]:
        """
        Get security metrics from Redis
        
        Returns:
            Dict with Redis metrics
        """
        metrics = {
            'rate_limit': {
                'active_limits': 0,
                'blocked_ips': 0,
            },
            'bot_detection': {
                'suspicious_ips': 0,
                'captcha_challenges': 0,
            }
        }
        
        if not self.redis:
            return metrics
            
        try:
            # Get rate limit metrics
            rate_limit_keys = self.redis.keys('rate_limit:*')
            metrics['rate_limit']['active_limits'] = len(rate_limit_keys)
            
            blocked_keys = self.redis.keys('rate_limit:blocked:*')
            metrics['rate_limit']['blocked_ips'] = len(blocked_keys)
            
            # Get bot detection metrics
            suspicious_keys = self.redis.keys('bot_detection:suspicious:*')
            metrics['bot_detection']['suspicious_ips'] = len(suspicious_keys)
            
            captcha_keys = self.redis.keys('captcha:challenge:*')
            metrics['bot_detection']['captcha_challenges'] = len(captcha_keys)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting Redis metrics: {str(e)}")
            return metrics
            
    def generate_time_series_chart(self) -> str:
        """
        Generate time series chart of security events
        
        Returns:
            Path to saved chart
        """
        try:
            # Create DataFrame from logs
            if not self.security_logs:
                logger.warning("No security logs to generate time series chart")
                return ""
                
            df = pd.DataFrame(self.security_logs)
            
            # Convert timestamp to datetime if it's not already
            if not pd.api.types.is_datetime64_any_dtype(df['timestamp']):
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                
            # Group by hour and category
            df['hour'] = df['timestamp'].dt.floor('H')
            hourly_counts = df.groupby(['hour', 'category']).size().unstack().fillna(0)
            
            # Plot
            plt.figure(figsize=(14, 7))
            hourly_counts.plot(kind='line', ax=plt.gca())
            
            plt.title('Security Events Over Time')
            plt.xlabel('Time')
            plt.ylabel('Number of Events')
            plt.legend(title='Event Type')
            plt.tight_layout()
            
            # Save chart
            chart_path = os.path.join(self.output_dir, 'security_time_series.png')
            plt.savefig(chart_path)
            plt.close()
            
            logger.info(f"Generated time series chart: {chart_path}")
            return chart_path
            
        except Exception as e:
            logger.error(f"Error generating time series chart: {str(e)}")
            return ""
            
    def generate_ip_heatmap(self) -> str:
        """
        Generate heatmap of IP addresses with security events
        
        Returns:
            Path to saved chart
        """
        try:
            # Filter logs with IP addresses
            ip_logs = [log for log in self.security_logs if log.get('ip')]
            
            if not ip_logs:
                logger.warning("No IP logs to generate heatmap")
                return ""
                
            # Count events by IP and category
            ip_counts = {}
            for log in ip_logs:
                ip = log['ip']
                category = log['category']
                
                if ip not in ip_counts:
                    ip_counts[ip] = {'bot_detection': 0, 'rate_limit': 0, 'captcha': 0, 'general': 0}
                    
                ip_counts[ip][category] += 1
                
            # Convert to DataFrame
            ip_df = pd.DataFrame.from_dict(ip_counts, orient='index')
            
            # Sort by total events
            ip_df['total'] = ip_df.sum(axis=1)
            ip_df = ip_df.sort_values('total', ascending=False).head(20)  # Top 20 IPs
            ip_df = ip_df.drop('total', axis=1)
            
            # Plot heatmap
            plt.figure(figsize=(12, 10))
            sns.heatmap(ip_df, annot=True, fmt='d', cmap='YlOrRd')
            
            plt.title('Security Events by IP Address')
            plt.ylabel('IP Address')
            plt.xlabel('Event Category')
            plt.tight_layout()
            
            # Save chart
            chart_path = os.path.join(self.output_dir, 'ip_heatmap.png')
            plt.savefig(chart_path)
            plt.close()
            
            logger.info(f"Generated IP heatmap: {chart_path}")
            return chart_path
            
        except Exception as e:
            logger.error(f"Error generating IP heatmap: {str(e)}")
            return ""
            
    def generate_endpoint_chart(self) -> str:
        """
        Generate chart of security events by endpoint
        
        Returns:
            Path to saved chart
        """
        try:
            # Extract endpoints from log messages
            endpoints = []
            for log in self.security_logs:
                message = log['message']
                # Look for URL patterns
                endpoint_match = re.search(r'(GET|POST|PUT|DELETE) (/[^\s"]+)', message)
                if endpoint_match:
                    endpoints.append({
                        'method': endpoint_match.group(1),
                        'path': endpoint_match.group(2),
                        'category': log['category']
                    })
                    
            if not endpoints:
                logger.warning("No endpoints found in logs")
                return ""
                
            # Convert to DataFrame
            endpoint_df = pd.DataFrame(endpoints)
            
            # Count events by endpoint
            endpoint_counts = endpoint_df.groupby(['path', 'category']).size().unstack().fillna(0)
            
            # Sort by total events
            endpoint_counts['total'] = endpoint_counts.sum(axis=1)
            endpoint_counts = endpoint_counts.sort_values('total', ascending=False).head(15)  # Top 15 endpoints
            endpoint_counts = endpoint_counts.drop('total', axis=1)
            
            # Plot
            plt.figure(figsize=(14, 8))
            endpoint_counts.plot(kind='barh', stacked=True, ax=plt.gca())
            
            plt.title('Security Events by Endpoint')
            plt.xlabel('Number of Events')
            plt.ylabel('Endpoint')
            plt.legend(title='Event Type')
            plt.tight_layout()
            
            # Save chart
            chart_path = os.path.join(self.output_dir, 'endpoint_chart.png')
            plt.savefig(chart_path)
            plt.close()
            
            logger.info(f"Generated endpoint chart: {chart_path}")
            return chart_path
            
        except Exception as e:
            logger.error(f"Error generating endpoint chart: {str(e)}")
            return ""
            
    def generate_summary_chart(self) -> str:
        """
        Generate summary chart of security events
        
        Returns:
            Path to saved chart
        """
        try:
            # Count events by category and level
            summary_data = {
                'bot_detection': {'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0},
                'rate_limit': {'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0},
                'captcha': {'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0},
                'general': {'INFO': 0, 'WARNING': 0, 'ERROR': 0, 'CRITICAL': 0}
            }
            
            for log in self.security_logs:
                category = log['category']
                level = log['level']
                
                if level in summary_data[category]:
                    summary_data[category][level] += 1
                    
            # Convert to DataFrame
            summary_df = pd.DataFrame.from_dict(summary_data, orient='index')
            
            # Plot
            plt.figure(figsize=(12, 8))
            summary_df.plot(kind='bar', stacked=True, ax=plt.gca())
            
            plt.title('Security Events Summary')
            plt.xlabel('Event Category')
            plt.ylabel('Number of Events')
            plt.legend(title='Severity Level')
            plt.tight_layout()
            
            # Save chart
            chart_path = os.path.join(self.output_dir, 'summary_chart.png')
            plt.savefig(chart_path)
            plt.close()
            
            logger.info(f"Generated summary chart: {chart_path}")
            return chart_path
            
        except Exception as e:
            logger.error(f"Error generating summary chart: {str(e)}")
            return ""
            
    def generate_dashboard(self) -> str:
        """
        Generate HTML dashboard with all charts
        
        Returns:
            Path to saved dashboard
        """
        try:
            # Generate charts
            time_series_chart = self.generate_time_series_chart()
            ip_heatmap = self.generate_ip_heatmap()
            endpoint_chart = self.generate_endpoint_chart()
            summary_chart = self.generate_summary_chart()
            
            # Get Redis metrics
            redis_metrics = self._get_redis_metrics()
            
            # Calculate summary statistics
            total_events = len(self.security_logs)
            bot_events = len(self.bot_detection_data)
            rate_limit_events = len(self.rate_limit_data)
            captcha_events = len(self.captcha_data)
            
            warning_events = len([log for log in self.security_logs if log['level'] == 'WARNING'])
            error_events = len([log for log in self.security_logs if log['level'] in ('ERROR', 'CRITICAL')])
            
            # Generate HTML
            html = f"""
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Security Dashboard</title>
                <style>
                    body {{
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 0;
                        background-color: #f5f5f5;
                    }}
                    .container {{
                        max-width: 1200px;
                        margin: 0 auto;
                        padding: 20px;
                    }}
                    .header {{
                        background-color: #2c3e50;
                        color: white;
                        padding: 20px;
                        text-align: center;
                        margin-bottom: 20px;
                    }}
                    .summary-cards {{
                        display: flex;
                        flex-wrap: wrap;
                        gap: 20px;
                        margin-bottom: 20px;
                    }}
                    .card {{
                        background-color: white;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                        padding: 20px;
                        flex: 1;
                        min-width: 200px;
                    }}
                    .card h3 {{
                        margin-top: 0;
                        color: #2c3e50;
                    }}
                    .card.warning {{
                        border-left: 4px solid #f39c12;
                    }}
                    .card.danger {{
                        border-left: 4px solid #e74c3c;
                    }}
                    .card.info {{
                        border-left: 4px solid #3498db;
                    }}
                    .card.success {{
                        border-left: 4px solid #2ecc71;
                    }}
                    .chart-container {{
                        background-color: white;
                        border-radius: 5px;
                        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                        padding: 20px;
                        margin-bottom: 20px;
                    }}
                    .chart-container h2 {{
                        margin-top: 0;
                        color: #2c3e50;
                    }}
                    .chart {{
                        width: 100%;
                        max-width: 100%;
                        height: auto;
                    }}
                    .footer {{
                        text-align: center;
                        margin-top: 20px;
                        padding: 20px;
                        color: #7f8c8d;
                    }}
                    .metrics-table {{
                        width: 100%;
                        border-collapse: collapse;
                    }}
                    .metrics-table th, .metrics-table td {{
                        padding: 10px;
                        text-align: left;
                        border-bottom: 1px solid #ddd;
                    }}
                    .metrics-table th {{
                        background-color: #f2f2f2;
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>Security Dashboard</h1>
                    <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <div class="container">
                    <div class="summary-cards">
                        <div class="card info">
                            <h3>Total Events</h3>
                            <p style="font-size: 24px;">{total_events}</p>
                            <p>Last {self.days_to_analyze} days</p>
                        </div>
                        
                        <div class="card warning">
                            <h3>Bot Detection</h3>
                            <p style="font-size: 24px;">{bot_events}</p>
                            <p>Suspicious activity detected</p>
                        </div>
                        
                        <div class="card danger">
                            <h3>Rate Limiting</h3>
                            <p style="font-size: 24px;">{rate_limit_events}</p>
                            <p>Rate limit events</p>
                        </div>
                        
                        <div class="card success">
                            <h3>CAPTCHA</h3>
                            <p style="font-size: 24px;">{captcha_events}</p>
                            <p>CAPTCHA challenges</p>
                        </div>
                    </div>
                    
                    <div class="chart-container">
                        <h2>Current Security Metrics</h2>
                        <table class="metrics-table">
                            <tr>
                                <th>Metric</th>
                                <th>Value</th>
                            </tr>
                            <tr>
                                <td>Active Rate Limits</td>
                                <td>{redis_metrics['rate_limit']['active_limits']}</td>
                            </tr>
                            <tr>
                                <td>Blocked IPs</td>
                                <td>{redis_metrics['rate_limit']['blocked_ips']}</td>
                            </tr>
                            <tr>
                                <td>Suspicious IPs</td>
                                <td>{redis_metrics['bot_detection']['suspicious_ips']}</td>
                            </tr>
                            <tr>
                                <td>Active CAPTCHA Challenges</td>
                                <td>{redis_metrics['bot_detection']['captcha_challenges']}</td>
                            </tr>
                            <tr>
                                <td>Warning Events</td>
                                <td>{warning_events}</td>
                            </tr>
                            <tr>
                                <td>Error Events</td>
                                <td>{error_events}</td>
                            </tr>
                        </table>
                    </div>
            """
            
            # Add charts if they were generated
            if time_series_chart:
                html += f"""
                    <div class="chart-container">
                        <h2>Security Events Over Time</h2>
                        <img class="chart" src="{os.path.basename(time_series_chart)}" alt="Time Series Chart">
                    </div>
                """
                
            if ip_heatmap:
                html += f"""
                    <div class="chart-container">
                        <h2>Security Events by IP Address</h2>
                        <img class="chart" src="{os.path.basename(ip_heatmap)}" alt="IP Heatmap">
                    </div>
                """
                
            if endpoint_chart:
                html += f"""
                    <div class="chart-container">
                        <h2>Security Events by Endpoint</h2>
                        <img class="chart" src="{os.path.basename(endpoint_chart)}" alt="Endpoint Chart">
                    </div>
                """
                
            if summary_chart:
                html += f"""
                    <div class="chart-container">
                        <h2>Security Events Summary</h2>
                        <img class="chart" src="{os.path.basename(summary_chart)}" alt="Summary Chart">
                    </div>
                """
                
            # Close HTML
            html += f"""
                    <div class="footer">
                        <p>Security Dashboard | Generated by Security Dashboard Generator</p>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Save dashboard
            dashboard_path = os.path.join(self.output_dir, 'security_dashboard.html')
            with open(dashboard_path, 'w') as f:
                f.write(html)
                
            logger.info(f"Generated security dashboard: {dashboard_path}")
            return dashboard_path
            
        except Exception as e:
            logger.error(f"Error generating dashboard: {str(e)}")
            return ""

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Generate security dashboard')
    parser.add_argument('--log-dir', default='logs', help='Directory containing log files')
    parser.add_argument('--output-dir', default='reports', help='Directory to save dashboard')
    parser.add_argument('--redis-host', default='localhost', help='Redis host')
    parser.add_argument('--redis-port', type=int, default=6379, help='Redis port')
    parser.add_argument('--redis-password', help='Redis password')
    parser.add_argument('--days', type=int, default=7, help='Number of days to analyze')
    
    args = parser.parse_args()
    
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Generate dashboard
    generator = SecurityDashboardGenerator(
        log_dir=args.log_dir,
        output_dir=args.output_dir,
        redis_host=args.redis_host,
        redis_port=args.redis_port,
        redis_password=args.redis_password,
        days_to_analyze=args.days
    )
    
    # Load logs
    if not generator.load_logs():
        logger.error("Failed to load logs")
        return 1
        
    # Generate dashboard
    dashboard_path = generator.generate_dashboard()
    
    if dashboard_path:
        print(f"Dashboard generated successfully: {dashboard_path}")
        print(f"Access the dashboard at: http://localhost:8000/api/security/dashboard")
        return 0
    else:
        logger.error("Failed to generate dashboard")
        return 1

if __name__ == '__main__':
    sys.exit(main()) 