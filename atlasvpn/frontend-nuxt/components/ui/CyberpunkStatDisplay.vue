<template>
  <div 
    class="group relative overflow-hidden rounded-xl border transition-all duration-300 hover:scale-[1.02]"
    :class="[
      colorScheme.borderColor,
      colorScheme.bgGlow,
      'bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm'
    ]"
  >
    <!-- Corner Accents -->
    <div class="absolute top-0 left-0 w-8 h-8 opacity-70">
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
        <path d="M 0 12 V 0 H 12" stroke="#a855f7" stroke-width="1" />
        <path d="M 0 8 V 0 H 8" stroke="#a855f7" stroke-width="2" />
      </svg>
    </div>
    
    <div class="absolute top-0 right-0 w-8 h-8 opacity-70 rotate-90">
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
        <path d="M 0 12 V 0 H 12" stroke="#a855f7" stroke-width="1" />
        <path d="M 0 8 V 0 H 8" stroke="#a855f7" stroke-width="2" />
      </svg>
    </div>

    <!-- Content -->
    <div class="relative p-4">
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center space-x-2">
          <div 
            class="w-8 h-8 rounded-lg flex items-center justify-center"
            :class="colorScheme.iconBg"
          >
            <Icon 
              :name="icon" 
              class="w-4 h-4"
              :class="colorScheme.iconColor"
            />
          </div>
          <div 
            class="text-xs uppercase tracking-wider font-bold"
            :class="colorScheme.labelColor"
          >
            {{ label }}
          </div>
        </div>
        
        <!-- Animated Circle -->
        <div v-if="showCircle" class="relative w-8 h-8">
          <svg class="w-8 h-8 transform -rotate-90" viewBox="0 0 32 32">
            <circle
              cx="16"
              cy="16"
              r="12"
              stroke="currentColor"
              stroke-width="2"
              fill="none"
              class="text-gray-700"
            />
            <circle
              ref="animatedCircle"
              cx="16"
              cy="16"
              r="12"
              stroke="currentColor"
              stroke-width="2"
              fill="none"
              stroke-linecap="round"
              :class="colorScheme.iconColor"
              :stroke-dasharray="circleCircumference"
              :stroke-dashoffset="circleOffset"
              class="transition-all duration-1000 ease-out"
            />
          </svg>
        </div>
      </div>
      
      <div class="mt-1">
        <div 
          class="text-base sm:text-xl font-bold tracking-wide text-white group-hover:scale-[1.03] transition-transform truncate"
          :style="glitchStyle"
        >
          {{ value }}
        </div>
      </div>
      
      <!-- Gradient Line -->
      <div 
        class="mt-2 h-px bg-gradient-to-r opacity-50"
        :class="`${colorScheme.gradientFrom} via-transparent to-transparent`"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  label: string
  value: string
  icon: string
  color: 'gold' | 'purple' | 'blue' | 'green'
  animate?: boolean
  showCircle?: boolean
  circleProgress?: number
}

const props = withDefaults(defineProps<Props>(), {
  animate: false,
  showCircle: false,
  circleProgress: 0
})

const animatedCircle = ref<SVGCircleElement>()
const glitchOffset = ref({ x: 0, y: 0 })

// Color schemes
const colorSchemes = {
  gold: {
    gradientFrom: 'from-yellow-400',
    gradientTo: 'to-amber-600',
    iconColor: 'text-yellow-300',
    iconBg: 'bg-yellow-500/20',
    labelColor: 'text-yellow-300',
    borderColor: 'border-yellow-500/30',
    bgGlow: 'bg-yellow-500/5',
  },
  purple: {
    gradientFrom: 'from-violet-400',
    gradientTo: 'to-purple-600',
    iconColor: 'text-purple-300',
    iconBg: 'bg-purple-500/20',
    labelColor: 'text-purple-300',
    borderColor: 'border-purple-500/30',
    bgGlow: 'bg-purple-500/5',
  },
  blue: {
    gradientFrom: 'from-blue-400',
    gradientTo: 'to-indigo-600',
    iconColor: 'text-blue-300',
    iconBg: 'bg-blue-500/20',
    labelColor: 'text-blue-300',
    borderColor: 'border-blue-500/30',
    bgGlow: 'bg-blue-500/5',
  },
  green: {
    gradientFrom: 'from-green-400',
    gradientTo: 'to-emerald-600',
    iconColor: 'text-green-300',
    iconBg: 'bg-green-500/20',
    labelColor: 'text-green-300',
    borderColor: 'border-green-500/30',
    bgGlow: 'bg-green-500/5',
  }
}

const colorScheme = computed(() => colorSchemes[props.color])

// Circle animation
const circleCircumference = 2 * Math.PI * 12 // radius = 12
const circleOffset = computed(() => {
  return circleCircumference - (props.circleProgress / 100) * circleCircumference
})

// Glitch effect
const glitchStyle = computed(() => {
  if (!props.animate) return {}
  return {
    transform: `translate(${glitchOffset.value.x}px, ${glitchOffset.value.y}px)`
  }
})

// Glitch animation
let glitchInterval: NodeJS.Timeout | null = null

onMounted(() => {
  if (props.animate) {
    glitchInterval = setInterval(() => {
      if (Math.random() > 0.9) {
        glitchOffset.value = {
          x: Math.random() * 4 - 2,
          y: Math.random() * 4 - 2,
        }
        setTimeout(() => {
          glitchOffset.value = { x: 0, y: 0 }
        }, 100)
      }
    }, 500)
  }
})

onUnmounted(() => {
  if (glitchInterval) {
    clearInterval(glitchInterval)
  }
})
</script>
