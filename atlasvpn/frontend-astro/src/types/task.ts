// Task Types
export interface Task {
  id: number;
  title: string;
  description: string;
  type: TaskType;
  status: TaskStatus;
  reward_amount: number;
  required_progress: number;
  current_progress: number;
  is_active: boolean;
  is_claimed: boolean;
  created_at: string;
  updated_at: string;
  expires_at?: string;
  started_at?: string;
  completed_at?: string;
  claimed_at?: string;
  verification_attempts: number;
  max_verification_attempts: number;
  last_verified_at?: string;
  completion_id?: number;
  avg_completion_time?: number | null;
  verification_data?: any;
  metadata?: Record<string, any>;
  platform_link?: string;
  duration?: number;
}

export interface TaskCompletion {
  id: number;
  task_id: number;
  user_id: number;
  status: TaskStatus;
  started_at: string;
  completed_at?: string;
  claimed_at?: string;
  current_progress: number;
  verification_attempts: number;
  last_verified_at?: string;
  is_verified: boolean;
  is_claimed: boolean;
  reward_amount: number;
  verification_data?: any;
  verification_method?: string;
  expires_at?: string;
  streak_day?: number;
  cycle_day?: number;
  cycle_id?: number;
}

export interface TaskVerificationResult {
  success: boolean;
  message: string;
  verified: boolean;
  progress?: number;
  completed?: boolean;
  reward_amount?: number;
}

export interface DailyTaskStreak {
  id: number;
  user_id: number;
  task_id: number;
  current_streak: number;
  max_streak: number;
  last_check_in: string;
  next_check_in: string;
  current_cycle_day: number;
  total_cycles_completed: number;
  daily_rewards: number[];
  cycle_bonus_reward: number;
  is_cycle_complete: boolean;
  created_at: string;
  updated_at: string;
}

export interface DailyCheckInResponse {
  success: boolean;
  message: string;
  streak: DailyTaskStreak;
  reward_amount: number;
  cycle_completed: boolean;
  cycle_bonus?: number;
  next_check_in: string;
}

export interface TaskAnalytics {
  task_id: number;
  task_title: string;
  task_type: TaskType;
  total_starts: number;
  total_completions: number;
  total_claims: number;
  completion_rate: number;
  claim_rate: number;
  avg_completion_time: number;
  total_rewards_distributed: number;
  active_users: number;
  created_at: string;
}

// Task Creation and Update Types
export interface CreateTaskData {
  title: string;
  description: string;
  type: TaskType;
  reward_amount: number;
  required_progress?: number;
  max_verification_attempts?: number;
  duration?: number;
  platform_link?: string;
  metadata?: Record<string, any>;
  is_active?: boolean;
}

export interface UpdateTaskData {
  title?: string;
  description?: string;
  reward_amount?: number;
  required_progress?: number;
  max_verification_attempts?: number;
  duration?: number;
  platform_link?: string;
  metadata?: Record<string, any>;
  is_active?: boolean;
}

// Enums
export enum TaskType {
  TELEGRAM_JOIN = 'TELEGRAM_JOIN',
  WEBSITE_VISIT = 'WEBSITE_VISIT',
  SOCIAL_FOLLOW = 'SOCIAL_FOLLOW',
  DAILY_CHECKIN = 'DAILY_CHECKIN',
  REFERRAL = 'REFERRAL',
  CUSTOM = 'CUSTOM',
}

export enum TaskStatus {
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  EXPIRED = 'EXPIRED',
  REVOKED = 'REVOKED',
}

// Task Filter and Sort Types
export interface TaskFilters {
  type?: TaskType;
  status?: TaskStatus;
  is_active?: boolean;
  is_claimed?: boolean;
}

export interface TaskSortOptions {
  field: 'created_at' | 'reward_amount' | 'title' | 'status';
  direction: 'asc' | 'desc';
}

// Task Statistics
export interface TaskStats {
  total_tasks: number;
  active_tasks: number;
  completed_tasks: number;
  total_rewards_distributed: number;
  avg_completion_rate: number;
  most_popular_task_type: TaskType;
  daily_completions: number;
  weekly_completions: number;
  monthly_completions: number;
}