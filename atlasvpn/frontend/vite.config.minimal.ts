import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react-swc';
import * as path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '~': path.resolve(__dirname, './src'),
      // React 19 compatibility: redirect use-sync-external-store to our custom shims
      'use-sync-external-store/shim/with-selector.js': path.resolve(__dirname, './src/shims/with-selector.js'),
      'use-sync-external-store/shim/with-selector': path.resolve(__dirname, './src/shims/with-selector.js'),
      'use-sync-external-store/shim': path.resolve(__dirname, './src/shims/use-sync-external-store.js'),
      'use-sync-external-store': path.resolve(__dirname, './src/shims/use-sync-external-store.js'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 3000,
    strictPort: true,
    open: false,
    cors: true,
    hmr: {
      port: 3000,
      host: 'localhost',
      protocol: 'ws',
      clientPort: 3000,
    },
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify('development'),
  },
  esbuild: {
    target: 'es2020',
    keepNames: true,
  },
});
