import type { SystemStats, SystemNotification, SystemSettings, AdminUser } from '~/types'

/**
 * Modern system composable using Nuxt 4 data fetching patterns
 * Handles admin features, system stats, and global settings
 */
export const useSystem = () => {
  // Default fetch options optimized for Telegram Mini App
  const defaultOptions = {
    server: false, // Client-side only for Telegram Mini App
    retry: 3,
    retryDelay: 1000,
    timeout: 15000,
    onRequestError({ error }: { error: any }) {
      console.error('[System] Request error:', error)
    },
    onResponseError({ response }: { response: any }) {
      console.error('[System] Response error:', response.status, response.statusText)
    }
  }

  // System statistics with modern useFetch
  const { 
    data: systemStats, 
    pending: isLoadingStats, 
    error: statsError, 
    refresh: refreshStats 
  } = useFetch<SystemStats>('/api/admin/system/stats', {
    ...defaultOptions,
    key: 'system-stats',
    default: () => ({
      total_users: 0,
      active_users: 0,
      total_transactions: 0,
      total_revenue: 0,
      total_tasks_completed: 0,
      total_cards_purchased: 0,
      total_vpn_subscriptions: 0,
      server_uptime: 0,
      database_size: 0,
      cache_hit_rate: 0
    }),
    transform: (data: any) => data?.data || data
  })

  // System notifications with modern useFetch
  const { 
    data: notifications, 
    pending: isLoadingNotifications, 
    error: notificationsError, 
    refresh: refreshNotifications 
  } = useFetch<SystemNotification[]>('/api/admin/system/notifications', {
    ...defaultOptions,
    key: 'system-notifications',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // System settings with modern useFetch
  const { 
    data: systemSettings, 
    pending: isLoadingSettings, 
    error: settingsError, 
    refresh: refreshSettings 
  } = useFetch<SystemSettings>('/api/admin/system/settings', {
    ...defaultOptions,
    key: 'system-settings',
    default: () => ({
      maintenance_mode: false,
      registration_enabled: true,
      max_users: 10000,
      default_currency: 'USD',
      telegram_bot_token: '',
      api_rate_limit: 100,
      session_timeout: 3600,
      backup_enabled: true,
      debug_mode: false
    }),
    transform: (data: any) => data?.data || data
  })

  // Admin users with modern useFetch
  const { 
    data: adminUsers, 
    pending: isLoadingAdmins, 
    error: adminsError, 
    refresh: refreshAdmins 
  } = useFetch<AdminUser[]>('/api/admin/users', {
    ...defaultOptions,
    key: 'admin-users',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // Update system settings
  const updateSystemSettings = async (settings: Partial<SystemSettings>): Promise<SystemSettings> => {
    try {
      const response = await $fetch<{ data: SystemSettings }>('/api/admin/system/settings', {
        method: 'PUT',
        body: settings
      })
      
      // Refresh settings to get updated data
      await refreshSettings()
      
      return response.data
    } catch (error: any) {
      console.error('[System] Failed to update settings:', error)
      throw error
    }
  }

  // Create system notification
  const createNotification = async (notification: {
    title: string
    message: string
    type: 'info' | 'warning' | 'error' | 'success'
    target_users?: 'all' | 'admins' | 'active'
  }): Promise<SystemNotification> => {
    try {
      const response = await $fetch<{ data: SystemNotification }>('/api/admin/system/notifications', {
        method: 'POST',
        body: notification
      })
      
      // Refresh notifications to get updated list
      await refreshNotifications()
      
      return response.data
    } catch (error: any) {
      console.error('[System] Failed to create notification:', error)
      throw error
    }
  }

  // Delete system notification
  const deleteNotification = async (notificationId: number): Promise<void> => {
    try {
      await $fetch(`/api/admin/system/notifications/${notificationId}`, {
        method: 'DELETE'
      })
      
      // Remove from local state
      if (notifications.value) {
        const index = notifications.value.findIndex(n => n.id === notificationId)
        if (index !== -1) {
          notifications.value.splice(index, 1)
        }
      }
    } catch (error: any) {
      console.error('[System] Failed to delete notification:', error)
      throw error
    }
  }

  // Toggle maintenance mode
  const toggleMaintenanceMode = async (enabled: boolean): Promise<void> => {
    try {
      await $fetch('/api/admin/system/maintenance', {
        method: 'POST',
        body: { enabled }
      })
      
      // Update local state
      if (systemSettings.value) {
        systemSettings.value.maintenance_mode = enabled
      }
    } catch (error: any) {
      console.error('[System] Failed to toggle maintenance mode:', error)
      throw error
    }
  }

  // Create backup
  const createBackup = async (): Promise<{ backup_id: string; download_url: string }> => {
    try {
      const response = await $fetch<{ data: { backup_id: string; download_url: string } }>('/api/admin/system/backup', {
        method: 'POST'
      })
      
      return response.data
    } catch (error: any) {
      console.error('[System] Failed to create backup:', error)
      throw error
    }
  }

  // Get system health
  const getSystemHealth = async (): Promise<{
    status: 'healthy' | 'warning' | 'critical'
    checks: Array<{ name: string; status: 'pass' | 'fail'; message?: string }>
  }> => {
    try {
      const response = await $fetch<{ data: any }>('/api/admin/system/health')
      return response.data
    } catch (error: any) {
      console.error('[System] Failed to get system health:', error)
      throw error
    }
  }

  // Clear cache
  const clearCache = async (cacheType?: 'all' | 'user' | 'api' | 'static'): Promise<void> => {
    try {
      await $fetch('/api/admin/system/cache/clear', {
        method: 'POST',
        body: { type: cacheType || 'all' }
      })
    } catch (error: any) {
      console.error('[System] Failed to clear cache:', error)
      throw error
    }
  }

  // Refresh all system data
  const refreshAllSystem = async () => {
    await Promise.all([
      refreshStats(),
      refreshNotifications(),
      refreshSettings(),
      refreshAdmins()
    ])
  }

  // Computed properties
  const isMaintenanceMode = computed(() => systemSettings.value?.maintenance_mode || false)

  const activeUsersPercentage = computed(() => {
    if (!systemStats.value || systemStats.value.total_users === 0) return 0
    return (systemStats.value.active_users / systemStats.value.total_users) * 100
  })

  const unreadNotifications = computed(() => 
    notifications.value?.filter(n => !n.is_read) || []
  )

  const criticalNotifications = computed(() => 
    notifications.value?.filter(n => n.type === 'error' && !n.is_read) || []
  )

  const systemHealth = computed(() => {
    if (!systemStats.value) return 'unknown'
    
    const { cache_hit_rate, server_uptime } = systemStats.value
    
    if (cache_hit_rate < 50 || server_uptime < 3600) return 'critical'
    if (cache_hit_rate < 80 || server_uptime < 86400) return 'warning'
    return 'healthy'
  })

  const formattedUptime = computed(() => {
    if (!systemStats.value?.server_uptime) return '0s'
    
    const uptime = systemStats.value.server_uptime
    const days = Math.floor(uptime / 86400)
    const hours = Math.floor((uptime % 86400) / 3600)
    const minutes = Math.floor((uptime % 3600) / 60)
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`
    if (hours > 0) return `${hours}h ${minutes}m`
    return `${minutes}m`
  })

  // Utility functions
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: systemSettings.value?.default_currency || 'USD'
    }).format(amount)
  }

  const getHealthColor = (health: string): string => {
    switch (health) {
      case 'healthy': return 'green'
      case 'warning': return 'yellow'
      case 'critical': return 'red'
      default: return 'gray'
    }
  }

  return {
    // State (reactive from useFetch)
    systemStats: readonly(systemStats),
    notifications: readonly(notifications),
    systemSettings: readonly(systemSettings),
    adminUsers: readonly(adminUsers),
    isLoadingStats: readonly(isLoadingStats),
    isLoadingNotifications: readonly(isLoadingNotifications),
    isLoadingSettings: readonly(isLoadingSettings),
    isLoadingAdmins: readonly(isLoadingAdmins),
    statsError: readonly(statsError),
    notificationsError: readonly(notificationsError),
    settingsError: readonly(settingsError),
    adminsError: readonly(adminsError),

    // Actions
    refreshStats,
    refreshNotifications,
    refreshSettings,
    refreshAdmins,
    refreshAllSystem,
    updateSystemSettings,
    createNotification,
    deleteNotification,
    toggleMaintenanceMode,
    createBackup,
    getSystemHealth,
    clearCache,

    // Computed
    isMaintenanceMode,
    activeUsersPercentage,
    unreadNotifications,
    criticalNotifications,
    systemHealth,
    formattedUptime,

    // Utilities
    formatBytes,
    formatCurrency,
    getHealthColor
  }
}
