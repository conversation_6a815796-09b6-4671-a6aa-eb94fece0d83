{"timestamp": "2025-03-08T15:53:52.655776", "total_tests": 2, "passed": 2, "failed": 0, "success_rate": 100.0, "results": [{"script": "test_security_headers.py", "start_time": "2025-03-08T15:52:39.713755", "success": true, "output": "\n================================================================================\n                    SECURITY HEADERS AND BOT DETECTION TEST                     \n================================================================================\n\n\nTesting security headers on main page...\n2025-03-08 15:52:43,742 - INFO - Testing security headers for: http://localhost:8000\n✓ All required security headers are present\n\nTesting Content-Security-Policy header...\n2025-03-08 15:52:43,753 - WARNING - CSP allows unsafe inline scripts\n⚠ CSP allows unsafe inline scripts - this can reduce XSS protection\n✓ Content-Security-Policy is properly configured\n\nTesting security headers on API endpoints...\n2025-03-08 15:52:43,757 - INFO - Testing security headers for: http://localhost:8000/api/tasks/available\n✓ Endpoint /api/tasks/available has all required security headers\n2025-03-08 15:52:43,778 - INFO - Testing security headers for: http://localhost:8000/api/security/verify-captcha\n✓ Endpoint /api/security/verify-captcha has all required security headers\n2025-03-08 15:52:43,790 - INFO - Testing security headers for: http://localhost:8000/auth/token\n✓ Endpoint /auth/token has all required security headers\n\nTesting bot detection...\n2025-03-08 15:52:43,834 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)': 404\n2025-03-08 15:52:43,933 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)': 404\n2025-03-08 15:52:43,978 - INFO - Bot test with User-Agent 'python-requests/2.25.1': 404\n2025-03-08 15:52:44,045 - INFO - Bot test with User-Agent 'Wget/1.20.3 (linux-gnu)': 404\n2025-03-08 15:52:44,618 - INFO - Bot test with User-Agent 'curl/7.68.0': 404\n⚠ Legitimate search engine bots blocked: Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)\n⚠ Automation tools allowed without CAPTCHA: python-requests/2.25.1, Wget/1.20.3 (linux-gnu), curl/7.68.0\n✗ Bot detection has issues\n\nDetailed Bot Detection Results:\n  Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html): ✓ OK\n  Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm): ✓ OK\n  python-requests/2.25.1: ✓ OK\n  Wget/1.20.3 (linux-gnu): ✓ OK\n  curl/7.68.0: ✓ OK\n", "error": "", "duration": 6.168094158172607}, {"script": "test_rate_limit_captcha.py", "start_time": "2025-03-08T15:52:45.885851", "success": true, "output": "\n================================================================================\n                         RATE LIMITING AND CAPTCHA TEST                         \n================================================================================\n\n\nTesting login rate limiting...\n2025-03-08 15:52:57,818 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts\n2025-03-08 15:53:07,901 - INFO - Attempt 1: Status 401\n2025-03-08 15:53:09,955 - INFO - Attempt 2: Status 401\n2025-03-08 15:53:11,850 - INFO - Attempt 3: Status 401\n2025-03-08 15:53:13,602 - INFO - Attempt 4: Status 401\n2025-03-08 15:53:15,788 - INFO - Attempt 5: Status 401\n2025-03-08 15:53:17,744 - INFO - Attempt 6: Status 401\n2025-03-08 15:53:20,775 - INFO - Attempt 7: Status 401\n2025-03-08 15:53:22,939 - INFO - Attempt 8: Status 401\n2025-03-08 15:53:24,676 - INFO - Attempt 9: Status 401\n2025-03-08 15:53:26,684 - INFO - Attempt 10: Status 401\n2025-03-08 15:53:29,699 - INFO - Attempt 11: Status 401\n2025-03-08 15:53:31,868 - INFO - Attempt 12: Status 401\n✗ Rate limiting was not triggered after 12 attempts\n✗ Login rate limiting is not working properly\n\nRate limit headers for login attempts:\n  Attempt 1: OK - Headers: {}\n  Attempt 2: OK - Headers: {}\n  Attempt 3: OK - Headers: {}\n  Attempt 4: OK - Headers: {}\n  Attempt 5: OK - Headers: {}\n  Attempt 6: OK - Headers: {}\n  Attempt 7: OK - Headers: {}\n  Attempt 8: OK - Headers: {}\n  Attempt 9: OK - Headers: {}\n  Attempt 10: OK - Headers: {}\n  Attempt 11: OK - Headers: {}\n  Attempt 12: OK - Headers: {}\n\nTesting rate limiting for API endpoints...\n\nTesting rate limiting for /api/tasks/available...\n2025-03-08 15:53:32,458 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts\n2025-03-08 15:53:32,580 - INFO - Attempt 1: Status 401\n2025-03-08 15:53:32,824 - INFO - Attempt 2: Status 401\n2025-03-08 15:53:33,573 - INFO - Attempt 3: Status 401\n2025-03-08 15:53:33,817 - INFO - Attempt 4: Status 401\n2025-03-08 15:53:34,617 - INFO - Attempt 5: Status 401\n2025-03-08 15:53:34,850 - INFO - Attempt 6: Status 401\n2025-03-08 15:53:35,624 - INFO - Attempt 7: Status 401\n2025-03-08 15:53:35,876 - INFO - Attempt 8: Status 401\n2025-03-08 15:53:36,501 - INFO - Attempt 9: Status 401\n2025-03-08 15:53:36,760 - INFO - Attempt 10: Status 401\n✗ Rate limiting was not triggered after 10 attempts\n✗ Rate limiting is not working properly for /api/tasks/available\n\nTesting rate limiting for /api/tasks/analytics...\n2025-03-08 15:53:37,456 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts\n2025-03-08 15:53:37,522 - INFO - Attempt 1: Status 401\n2025-03-08 15:53:37,778 - INFO - Attempt 2: Status 401\n2025-03-08 15:53:38,502 - INFO - Attempt 3: Status 401\n2025-03-08 15:53:38,743 - INFO - Attempt 4: Status 401\n2025-03-08 15:53:39,612 - INFO - Attempt 5: Status 401\n2025-03-08 15:53:39,834 - INFO - Attempt 6: Status 401\n2025-03-08 15:53:40,691 - INFO - Attempt 7: Status 401\n2025-03-08 15:53:40,957 - INFO - Attempt 8: Status 401\n2025-03-08 15:53:41,492 - INFO - Attempt 9: Status 401\n2025-03-08 15:53:41,732 - INFO - Attempt 10: Status 401\n✗ Rate limiting was not triggered after 10 attempts\n✗ Rate limiting is not working properly for /api/tasks/analytics\n\nTesting rate limiting for /auth/register...\n2025-03-08 15:53:41,934 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts\n2025-03-08 15:53:43,557 - INFO - Attempt 1: Status 405\n2025-03-08 15:53:43,784 - INFO - Attempt 2: Status 405\n2025-03-08 15:53:43,995 - INFO - Attempt 3: Status 405\n2025-03-08 15:53:44,575 - INFO - Attempt 4: Status 405\n2025-03-08 15:53:44,799 - INFO - Attempt 5: Status 405\n2025-03-08 15:53:45,024 - INFO - Attempt 6: Status 405\n2025-03-08 15:53:45,598 - INFO - Attempt 7: Status 405\n2025-03-08 15:53:45,820 - INFO - Attempt 8: Status 405\n2025-03-08 15:53:46,045 - INFO - Attempt 9: Status 405\n2025-03-08 15:53:46,593 - INFO - Attempt 10: Status 405\n✗ Rate limiting was not triggered after 10 attempts\n✗ Rate limiting is not working properly for /auth/register\n\nTesting Cloudflare CAPTCHA integration...\n2025-03-08 15:53:46,795 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token\n2025-03-08 15:53:46,806 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior\n2025-03-08 15:53:47,835 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register\n2025-03-08 15:53:47,852 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior\n2025-03-08 15:53:47,852 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login\n2025-03-08 15:53:47,874 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior\n⚠ No CAPTCHA detected for login endpoint\n⚠ No CAPTCHA detected for register endpoint\n⚠ No CAPTCHA detected for admin_login endpoint\n\nSimulating distributed attack...\n2025-03-08 15:53:47,876 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent\n2025-03-08 15:53:50,861 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored\n✗ Distributed attack was not effectively blocked (block rate: 0.00%)\n\nSample distributed attack results:\n  Request from IP **************: ALLOWED\n  Request from IP **************: ALLOWED\n  Request from IP ***************: ALLOWED\n  Request from IP ***************: ALLOWED\n  Request from IP **************: ALLOWED\n", "error": "", "duration": 66.76816606521606}]}