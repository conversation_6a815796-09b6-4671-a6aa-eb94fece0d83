#!/bin/bash

# VIPVerse Frontend - Bun Setup & Development Script
# This script sets up bun and runs development commands

set -e  # Exit on any error

echo "🚀 VIPVerse Frontend - Bun Development Setup"
echo "============================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if bun is installed
check_bun() {
    if command -v bun &> /dev/null; then
        print_success "Bun is already installed: $(bun --version)"
        return 0
    else
        print_warning "Bun not found. Installing..."
        return 1
    fi
}

# Install bun
install_bun() {
    print_status "Installing Bun package manager..."
    
    # Try multiple installation methods
    if curl -fsSL https://bun.sh/install | bash; then
        # Add bun to PATH for this session
        export PATH="$HOME/.bun/bin:$PATH"
        print_success "Bun installed successfully"
    elif command -v npm &> /dev/null; then
        print_status "Fallback: Installing bun via npm..."
        npm install -g bun
        print_success "Bun installed via npm"
    else
        print_error "Could not install bun. Please install manually."
        exit 1
    fi
}

# Install dependencies
install_dependencies() {
    print_status "Installing frontend dependencies with bun..."
    
    if bun install; then
        print_success "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
}

# Run type checking
run_type_check() {
    print_status "Running TypeScript type checking..."
    
    if bun run type-check; then
        print_success "✅ No TypeScript errors found!"
    else
        print_warning "❌ TypeScript errors detected. Check output above."
        return 1
    fi
}

# Run prettier formatting
run_prettier() {
    print_status "Running Prettier code formatting..."
    
    # Check formatting first
    if bun run format:check; then
        print_success "✅ Code is properly formatted!"
    else
        print_warning "Code formatting issues found. Auto-fixing..."
        if bun run format; then
            print_success "✅ Code formatted successfully!"
        else
            print_error "Failed to format code"
            return 1
        fi
    fi
}

# Run linting
run_linting() {
    print_status "Running ESLint checks..."
    
    if bun run lint; then
        print_success "✅ No linting errors found!"
    else
        print_warning "Linting issues found. Attempting auto-fix..."
        if bun run lint:fix; then
            print_success "✅ Linting issues fixed!"
        else
            print_error "Could not auto-fix all linting issues"
            return 1
        fi
    fi
}

# Start development server
start_dev_server() {
    print_status "Starting development server..."
    print_success "🔥 Development server will start with hot reload enabled"
    print_status "Server will be available at: http://localhost:5173"
    print_status "Press Ctrl+C to stop the server"
    
    exec bun run dev
}

# Main execution
main() {
    echo
    print_status "Starting setup process..."
    
    # Navigate to frontend directory
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Make sure you're in the frontend directory."
        exit 1
    fi
    
    # Check and install bun if needed
    if ! check_bun; then
        install_bun
    fi
    
    # Install dependencies
    install_dependencies
    
    echo
    print_status "Running code quality checks..."
    
    # Run type checking
    run_type_check
    TYPE_CHECK_SUCCESS=$?
    
    # Run prettier
    run_prettier
    PRETTIER_SUCCESS=$?
    
    # Run linting
    run_linting
    LINT_SUCCESS=$?
    
    echo
    print_status "============================================"
    
    # Summary
    if [ $TYPE_CHECK_SUCCESS -eq 0 ] && [ $PRETTIER_SUCCESS -eq 0 ] && [ $LINT_SUCCESS -eq 0 ]; then
        print_success "🎉 All checks passed! Code is ready for development."
    else
        print_warning "⚠️  Some checks failed. Please review the output above."
    fi
    
    echo
    print_status "Authentication System Status:"
    echo "  ✅ Unified authManager with UI state management"
    echo "  ✅ Enhanced Telegram integration"
    echo "  ✅ Comprehensive loading states"
    echo "  ✅ Type-safe error handling"
    echo "  ✅ Rate limiting with visual feedback"
    echo
    
    # Ask if user wants to start dev server
    echo
    read -p "🚀 Start development server? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        start_dev_server
    else
        print_success "Setup complete! Run 'bun run dev' when ready to start development."
        echo
        print_status "Available commands:"
        echo "  • bun run dev          - Start development server"
        echo "  • bun run type-check   - Check TypeScript types"
        echo "  • bun run format       - Format code with Prettier"
        echo "  • bun run lint         - Run ESLint checks"
        echo "  • bun run build        - Build for production"
    fi
}

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi 