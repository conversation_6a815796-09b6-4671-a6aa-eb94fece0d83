/**
 * @file VerseLayout.tsx
 * @description Heavy 3D layout component that can be lazy loaded
 */

import React, { Suspense, useState, useMemo, useCallback, useEffect, useRef } from 'react';
import { Canvas, useThree } from '@react-three/fiber';
import { Preload, Stats } from '@react-three/drei';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { FaBolt, FaArrowLeft, FaVolumeUp, FaVolumeMute, FaCog, FaQuestion } from 'react-icons/fa';

// Import Stores & Hooks
import { useDashboardStatsQuery } from '../../hooks/dashboardHooks';
import { useCurrentUserQuery } from '../../hooks/authHooks';
import { useUIChatStore } from '../../stores/uiChatStore';
import { useChatMessagesQuery } from '../../hooks/chatHooks';

// Import Components
import Avatar3DViewer from '../Avatar3DViewer';
import SceneSetup from './SceneSetup';
import LandPlots from './LandPlots';
import NetworkViz from './NetworkViz';
import { LandDetailCard } from './LandDetailCard';
import { PaintToolPanel } from './PaintToolPanel';
import ErrorBoundary from '../ErrorBoundary';
import FloatingChatMessages from './FloatingChatMessages';
import GuideDialog from './GuideDialog';

// Import Utils & Types
import { generateMockVerseData, LandPlot, latLonToCartesian } from '../../utils/mockVerseData';
import {
  disableVerticalSwipes,
  enableVerticalSwipes,
  useTelegramSafeArea,
  tg,
} from '../../utils/telegram';
import { formatCurrency } from '../../utils/format';
import { useAudio } from '../../hooks/use_audio';
import { ChatViewMode } from '../../types/chat';
import encryptedStorage from '../../utils/encryptedStorage';

// Constants
const PLANET_RADIUS = 16;
const NUM_LANDS = 120;
const STORAGE_PREFIX = 'vpnverse_land_paint_';

// Helper component to set pixel ratio based on performance mode
function SetPixelRatio({ isPerformanceMode }: { isPerformanceMode: boolean }) {
  const { gl } = useThree();

  useEffect(() => {
    const pixelRatio = isPerformanceMode
      ? Math.min(window.devicePixelRatio, 1.0)
      : Math.min(window.devicePixelRatio, 1.5);
    gl.setPixelRatio(pixelRatio);
  }, [gl, isPerformanceMode]);

  return null;
}

// Interface for props passed to VerseSceneContent
interface VerseSceneContentProps {
  hoveredLandId: string | null;
  selectedLandId: string | null;
  onLandHover: (landId: string | null) => void;
  onLandClick: (land: LandPlot) => void;
  setLands: React.Dispatch<React.SetStateAction<LandPlot[]>>;
  lands: LandPlot[];
  isPerformanceMode: boolean;
  isAppVisible: boolean;
  currentUser: any;
}

// Component containing the actual scene elements
function VerseSceneContent({
  hoveredLandId,
  selectedLandId,
  onLandHover,
  onLandClick,
  setLands,
  lands,
  isPerformanceMode,
  isAppVisible,
  currentUser,
}: VerseSceneContentProps): React.ReactElement {
  const landPositions = useMemo(() => {
    const visibleLands = isPerformanceMode ? lands.slice(0, lands.length * 0.8) : lands;
    return visibleLands.map(land => ({
      ...land,
      position: latLonToCartesian(land.lat, land.lon, PLANET_RADIUS),
      size: [land.angularSize, land.angularSize] as [number, number],
    }));
  }, [lands, isPerformanceMode]);

  return (
    <>
      <SceneSetup isPerformanceMode={isPerformanceMode} />
      <LandPlots
        lands={landPositions as Array<LandPlot & { position: [number, number, number]; size: [number, number] }>}
        onHover={onLandHover}
        onClick={onLandClick}
        hoveredLandId={hoveredLandId}
        selectedLandId={selectedLandId}
        isPerformanceMode={isPerformanceMode}
        isAppVisible={isAppVisible}
        currentUser={currentUser}
      />
      <NetworkViz
        lands={landPositions as Array<LandPlot & { position: [number, number, number]; size: [number, number] }>}
        hoveredLandId={hoveredLandId}
        isPerformanceMode={isPerformanceMode}
        isAppVisible={isAppVisible}
      />
    </>
  );
}

const VerseLayout: React.FC = () => {
  const navigate = useNavigate();
  const safeAreaInsets = useTelegramSafeArea();
  const audioRef = useRef<HTMLAudioElement | null>(null);
  
  // State management
  const [isAppVisible, setIsAppVisible] = useState(true);
  const [isPerformanceMode, setIsPerformanceMode] = useState(false);
  const [lands, setLands] = useState<LandPlot[]>([]);
  const [hoveredLandId, setHoveredLandId] = useState<string | null>(null);
  const [selectedLand, setSelectedLand] = useState<LandPlot | null>(null);
  const [isPainting, setIsPainting] = useState<LandPlot | null>(null);
  const [showGuide, setShowGuide] = useState(false);

  // Audio hook
  const { isMuted, mute, unmute } = useAudio({
    url: '/music/verse-ambient.mp3',
    loop: true,
    autoPlay: false,
    audioRef,
  });

  // API queries
  const { data: currentUser } = useCurrentUserQuery();
  const { data: dashboardStats } = useDashboardStatsQuery();
  const { data: chatMessages } = useChatMessagesQuery('public');

  // Chat store
  const { viewMode: chatViewMode, setViewMode } = useUIChatStore();

  // Initialize lands data
  useEffect(() => {
    const mockLands = generateMockVerseData(NUM_LANDS);
    setLands(mockLands);
  }, []);

  // Load performance mode from storage
  useEffect(() => {
    const loadPerformanceMode = async () => {
      try {
        const saved = await encryptedStorage.localStorage.getItem('verse_performance_mode');
        if (saved === 'true') {
          setIsPerformanceMode(true);
        }
      } catch (error) {
        console.warn('Failed to load performance mode setting:', error);
      }
    };
    loadPerformanceMode();
  }, []);

  // Event handlers
  const handleLandHover = useCallback((landId: string | null) => {
    setHoveredLandId(landId);
  }, []);

  const handleLandClick = useCallback((land: LandPlot) => {
    setSelectedLand(land);
  }, []);

  const handleCloseCard = useCallback(() => {
    setSelectedLand(null);
  }, []);

  const handleOpenPaintTool = useCallback((land: LandPlot) => {
    setIsPainting(land);
  }, []);

  const handleClosePaintTool = useCallback(() => {
    setIsPainting(null);
  }, []);

  const handleSavePaint = useCallback((landId: string, paintData: string | null) => {
    setLands(prevLands =>
      prevLands.map(l => (l.id === landId ? { ...l, paintData } : l))
    );
    setIsPainting(null);
  }, []);

  const handleTogglePerformanceMode = useCallback(async () => {
    const newMode = !isPerformanceMode;
    setIsPerformanceMode(newMode);
    try {
      await encryptedStorage.localStorage.setItem('verse_performance_mode', newMode.toString());
    } catch (error) {
      console.warn('Failed to save performance mode setting:', error);
    }
  }, [isPerformanceMode]);

  const handleToggleMute = useCallback(() => {
    if (isMuted) {
      unmute();
        } else {
      mute();
        }
  }, [isMuted, mute, unmute]);

  const handleToggleGuide = useCallback(() => {
    setShowGuide(!showGuide);
  }, [showGuide]);

  const handleToggleChatView = useCallback(() => {
    setViewMode(chatViewMode === ChatViewMode.FULL ? ChatViewMode.HIDDEN : ChatViewMode.FULL);
  }, [chatViewMode, setViewMode]);

  // Get last five public messages
  const lastFivePublicMessages = useMemo(() => {
    if (!chatMessages || !Array.isArray(chatMessages)) return [];
    return chatMessages
      .filter(msg => msg.is_public)
      .slice(-5);
  }, [chatMessages]);

  return (
    <div className="flex-1 w-full h-full relative">
      {/* Audio element */}
      <audio ref={audioRef} loop>
        <source src="/music/verse-ambient.mp3" type="audio/mpeg" />
      </audio>

      {/* 3D Canvas */}
      {chatViewMode !== ChatViewMode.FULL && (
        <ErrorBoundary
          fallback={
            <p style={{ color: 'white', padding: '1rem' }}>
              Canvas failed to load. Please refresh.
            </p>
          }
        >
          <Canvas
            gl={{
              alpha: true,
              antialias: !isPerformanceMode,
              powerPreference: 'high-performance',
              depth: true,
              stencil: false,
              logarithmicDepthBuffer: false,
      }}
            camera={{
              fov: 50,
              near: 0.1,
              far: 1000,
              position: [0, 0, 50],
            }}
            className="w-full h-full pointer-events-auto"
            style={{ touchAction: 'none' }}
            frameloop={isAppVisible ? 'always' : 'demand'}
            performance={{ min: 0.5 }}
          >
            <Suspense fallback={null}>
              <VerseSceneContent
                hoveredLandId={hoveredLandId}
                selectedLandId={selectedLand?.id ?? null}
                onLandHover={handleLandHover}
                onLandClick={handleLandClick}
                lands={lands}
                setLands={setLands}
                isPerformanceMode={isPerformanceMode}
                isAppVisible={isAppVisible}
                currentUser={currentUser}
              />
              <SetPixelRatio isPerformanceMode={isPerformanceMode} />
              <Preload all />
              {process.env.NODE_ENV === 'development' && <Stats />}
            </Suspense>
          </Canvas>
        </ErrorBoundary>
      )}

      {/* UI Overlay */}
      {chatViewMode !== ChatViewMode.FULL && (
        <div
          className="absolute inset-0 z-10 text-white pointer-events-none"
          style={{
            paddingTop: safeAreaInsets.top > 0 ? `${safeAreaInsets.top}px` : '1rem',
            paddingLeft: safeAreaInsets.left > 0 ? `${safeAreaInsets.left}px` : '1rem',
            paddingRight: safeAreaInsets.right > 0 ? `${safeAreaInsets.right}px` : '1rem',
            paddingBottom: safeAreaInsets.bottom > 0 ? `${safeAreaInsets.bottom}px` : '1rem',
          }}
        >
          {/* Top Bar UI */}
          <div className="flex justify-between items-start">
            <div className="flex items-center gap-2 pointer-events-auto">
              <button
                onClick={() => navigate('/dashboard/home')}
                className="bg-black/30 backdrop-blur-sm p-2 rounded-full border border-white/10 hover:bg-white/10 transition-colors"
                aria-label="Back to Dashboard"
              >
                <FaArrowLeft className="h-4 w-4" />
              </button>
              <button
                onClick={handleToggleMute}
                className="bg-black/30 backdrop-blur-sm p-2 rounded-full border border-white/10 hover:bg-white/10 transition-colors"
                aria-label={isMuted ? 'Unmute Music' : 'Mute Music'}
              >
                {isMuted ? <FaVolumeMute className="h-4 w-4" /> : <FaVolumeUp className="h-4 w-4" />}
              </button>
              <button
                onClick={handleTogglePerformanceMode}
                className={`bg-black/30 backdrop-blur-sm p-2 rounded-full border transition-colors ${
                  isPerformanceMode 
                    ? 'border-red-500/50 text-red-400 hover:bg-red-500/20' 
                    : 'border-white/10 text-white hover:bg-white/10'
                }`}
                aria-label={isPerformanceMode ? 'Disable Performance Mode' : 'Enable Performance Mode'}
              >
                <FaCog className={`h-4 w-4 ${isPerformanceMode ? 'animate-spin-slow' : ''}`} />
              </button>
              <button
                onClick={handleToggleGuide}
                className="bg-black/30 backdrop-blur-sm p-2 rounded-full border border-white/10 hover:bg-white/10 transition-colors"
                aria-label="Open Guide"
              >
                <FaQuestion className="h-4 w-4" />
              </button>
            </div>

            {/* User Info */}
            {currentUser && (
              <div
                className="relative flex items-center gap-2 bg-black/30 backdrop-blur-sm p-2 rounded-lg border border-white/10 pointer-events-auto cursor-pointer"
                onClick={handleToggleChatView}
              >
                <div className="flex flex-col items-end text-right">
                  <div className="text-xs text-white/60 leading-tight">Balance</div>
                  <div className="text-sm font-medium text-accent-300 leading-tight">
                    ${formatCurrency(dashboardStats?.wallet_balance || 0)}
                  </div>
                  {dashboardStats?.total_passive_hourly_income && dashboardStats.total_passive_hourly_income > 0 && (
                    <div className="flex items-center justify-end gap-1 text-[10px] bg-accent-500/10 px-1 py-0 rounded-sm mt-0.5">
                      <FaBolt size={8} className="text-accent-300" />
                      <span className="text-accent-300 leading-none">
                        {formatCurrency(dashboardStats.total_passive_hourly_income)}/h
                      </span>
                    </div>
                  )}
                </div>
                <div className="relative overflow-hidden rounded-full border-2 border-accent-500/30 shadow-md">
                  <Avatar3DViewer
                    filename={`${currentUser?.telegram_photo_url || 'Lovely Angel'}.webp`}
                    size={{ width: 40, height: 40 }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Floating Chat Messages */}
          <FloatingChatMessages messages={lastFivePublicMessages} />
        </div>
      )}

      {/* Land Detail Card */}
      {chatViewMode !== ChatViewMode.FULL && (
        <AnimatePresence>
          {selectedLand && (
            <div
              className="absolute bottom-16 left-4 right-4 md:left-auto md:right-4 md:bottom-4 md:w-80 z-20 pointer-events-auto"
              style={{
                marginBottom: safeAreaInsets.bottom > 0 ? `${safeAreaInsets.bottom}px` : '0',
              }}
            >
              <LandDetailCard
                land={selectedLand}
                onClose={handleCloseCard}
                onPaint={handleOpenPaintTool}
              />
            </div>
          )}
        </AnimatePresence>
      )}

      {/* Paint Tool Panel */}
      {chatViewMode !== ChatViewMode.FULL && (
        <AnimatePresence>
          {isPainting && (
            <PaintToolPanel
              land={isPainting}
              initialPaintData={isPainting.paintData}
              onSave={handleSavePaint}
              onClose={handleClosePaintTool}
            />
          )}
        </AnimatePresence>
      )}

      {/* Guide Dialog */}
      <AnimatePresence>
        {showGuide && <GuideDialog onClose={handleToggleGuide} />}
      </AnimatePresence>
    </div>
  );
};

export default VerseLayout;
