#!/usr/bin/env python3
"""
Continuous monitoring script for file descriptor leaks

This script:
1. Monitors file descriptor usage of FastAPI processes
2. Alerts when thresholds are approached
3. Takes corrective action when critical thresholds are reached
4. Keeps logs of system resource usage
"""

import os
import sys
import time
import psutil
import logging
import argparse
import subprocess
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import smtplib
from email.message import EmailMessage
import socket
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/var/log/fd_monitor.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("monitor")

# Default configuration
DEFAULT_CONFIG = {
    "process_filters": ["uvicorn", "gunicorn"],  # Process names to monitor
    "check_interval": 60,                        # Seconds between checks
    "warning_threshold": 0.7,                    # 70% of max FDs
    "critical_threshold": 0.85,                  # 85% of max FDs
    "max_log_entries": 1000,                     # Max entries in log file
    "history_file": "/var/log/fd_history.json",  # File to store history
    "alert_email": None,                         # Email to send alerts
    "smtp_server": "localhost",                  # SMTP server for alerts
    "auto_restart": False,                       # Auto restart when critical
    "service_name": "atlasvpn",                  # Service name to restart
    "log_level": "INFO"                          # Logging level
}

def find_api_processes(process_filters: List[str]) -> List[psutil.Process]:
    """Find running API processes matching any of the filter strings"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = " ".join(proc.cmdline()) if proc.cmdline() else ""
            for filter_str in process_filters:
                if filter_str in cmdline:
                    processes.append(proc)
                    logger.debug(f"Found process: PID {proc.pid}, Command: {cmdline}")
                    break
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return processes

def get_file_descriptor_limits() -> Dict[str, int]:
    """Get system file descriptor limits"""
    soft_limit, hard_limit = None, None
    
    # Try using resource module
    try:
        import resource
        soft_limit, hard_limit = resource.getrlimit(resource.RLIMIT_NOFILE)
        return {"soft": soft_limit, "hard": hard_limit}
    except (ImportError, AttributeError):
        pass
    
    # Fallback to /proc/sys/fs/file-max if available
    try:
        with open('/proc/sys/fs/file-max', 'r') as f:
            system_limit = int(f.read().strip())
            return {"soft": 1024, "hard": system_limit}  # Assuming default soft limit
    except:
        pass
    
    # Default values if nothing else works
    return {"soft": 1024, "hard": 65536}

def analyze_process_fds(process: psutil.Process) -> Dict[str, Any]:
    """Analyze file descriptors for a process"""
    try:
        # Get file descriptor limits for the process
        cmd = f"cat /proc/{process.pid}/limits | grep 'Max open files'"
        try:
            result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
            limits_line = result.stdout.strip()
            parts = limits_line.split()
            if len(parts) >= 6:
                soft_limit = int(parts[3]) if parts[3] != "unlimited" else float('inf')
                hard_limit = int(parts[4]) if parts[4] != "unlimited" else float('inf')
            else:
                # Fallback to system limits
                limits = get_file_descriptor_limits()
                soft_limit, hard_limit = limits["soft"], limits["hard"]
        except:
            # Fallback to system limits
            limits = get_file_descriptor_limits()
            soft_limit, hard_limit = limits["soft"], limits["hard"]
        
        # Count open file descriptors
        try:
            fd_dir = f"/proc/{process.pid}/fd"
            if os.path.exists(fd_dir):
                open_fds = len(os.listdir(fd_dir))
            else:
                open_fds = len(process.open_files())
        except:
            open_fds = len(process.open_files())
        
        # Calculate usage percentages
        soft_usage = open_fds / soft_limit if soft_limit > 0 else 0
        hard_usage = open_fds / hard_limit if hard_limit > 0 else 0
        
        # Count sockets, pipes, etc.
        socket_count = 0
        tcp_count = 0
        udp_count = 0
        unix_count = 0
        
        try:
            for conn in process.net_connections(kind='all'):
                if conn.family == socket.AF_INET or conn.family == socket.AF_INET6:
                    socket_count += 1
                    if conn.type == socket.SOCK_STREAM:
                        tcp_count += 1
                    elif conn.type == socket.SOCK_DGRAM:
                        udp_count += 1
                elif conn.family == socket.AF_UNIX:
                    unix_count += 1
        except:
            pass
        
        # Memory info
        try:
            memory_info = process.memory_info()
            memory_usage = {
                "rss": memory_info.rss,  # Resident Set Size
                "vms": memory_info.vms,  # Virtual Memory Size
            }
        except:
            memory_usage = {"rss": 0, "vms": 0}
        
        return {
            "pid": process.pid,
            "name": process.name(),
            "cmd": " ".join(process.cmdline()),
            "open_fds": open_fds,
            "soft_limit": soft_limit,
            "hard_limit": hard_limit,
            "soft_usage": soft_usage,
            "hard_usage": hard_usage,
            "socket_count": socket_count,
            "tcp_count": tcp_count,
            "udp_count": udp_count,
            "unix_count": unix_count,
            "memory": memory_usage,
            "timestamp": datetime.now().isoformat()
        }
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        return {
            "pid": process.pid,
            "error": "Process no longer exists or access denied",
            "timestamp": datetime.now().isoformat()
        }

def check_system_fds() -> Dict[str, Any]:
    """Check overall system file descriptor usage"""
    try:
        # Try to get allocated file descriptors
        with open('/proc/sys/fs/file-nr', 'r') as f:
            values = f.read().strip().split()
            allocated = int(values[0])
            unused = int(values[1])
            max_fds = int(values[2])
            return {
                "allocated": allocated,
                "unused": unused,
                "max": max_fds,
                "usage": allocated / max_fds
            }
    except:
        # Fallback method using lsof
        try:
            result = subprocess.run("lsof | wc -l", shell=True, check=True, 
                                  capture_output=True, text=True)
            count = int(result.stdout.strip())
            limits = get_file_descriptor_limits()
            return {
                "allocated": count,
                "max": limits["hard"],
                "usage": count / limits["hard"]
            }
        except:
            return {
                "error": "Could not determine system FD usage"
            }

def save_history(data: Dict[str, Any], history_file: str, max_entries: int) -> None:
    """Save monitoring data to history file"""
    try:
        history = []
        
        # Create directory if it doesn't exist
        history_path = Path(history_file)
        history_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Load existing history if available
        if os.path.exists(history_file):
            with open(history_file, 'r') as f:
                history = json.load(f)
        
        # Add new entry and trim if needed
        history.append(data)
        if len(history) > max_entries:
            history = history[-max_entries:]
        
        # Save updated history
        with open(history_file, 'w') as f:
            json.dump(history, f, indent=2)
            
    except Exception as e:
        logger.error(f"Failed to save history: {str(e)}")

def send_alert(subject: str, message: str, config: Dict[str, Any]) -> bool:
    """Send email alert"""
    if not config.get("alert_email"):
        logger.warning("No alert email configured, skipping alert")
        return False
    
    try:
        msg = EmailMessage()
        msg.set_content(message)
        msg['Subject'] = subject
        msg['From'] = f"FD Monitor <fd-monitor@{socket.gethostname()}>"
        msg['To'] = config["alert_email"]
        
        s = smtplib.SMTP(config["smtp_server"])
        s.send_message(msg)
        s.quit()
        
        logger.info(f"Alert email sent to {config['alert_email']}")
        return True
    except Exception as e:
        logger.error(f"Failed to send alert email: {str(e)}")
        return False

def restart_service(service_name: str) -> bool:
    """Restart a systemd service"""
    try:
        logger.warning(f"Attempting to restart service: {service_name}")
        result = subprocess.run(['systemctl', 'restart', service_name], 
                               check=True, capture_output=True, text=True)
        logger.warning(f"Service {service_name} restarted successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to restart service {service_name}: {e.stderr}")
        return False

def check_fd_usage(config: Dict[str, Any]) -> Tuple[bool, List[Dict[str, Any]]]:
    """
    Check file descriptor usage against thresholds
    Returns: (trigger_alert, process_data)
    """
    processes = find_api_processes(config["process_filters"])
    if not processes:
        logger.warning(f"No processes found matching filters: {config['process_filters']}")
        return False, []
    
    system_fds = check_system_fds()
    process_data = []
    critical_processes = []
    warning_processes = []
    
    for process in processes:
        try:
            proc_data = analyze_process_fds(process)
            process_data.append(proc_data)
            
            # Check thresholds if data is available
            if "error" not in proc_data:
                if proc_data["soft_usage"] >= config["critical_threshold"]:
                    critical_processes.append(proc_data)
                    logger.critical(f"Process {proc_data['pid']} ({proc_data['name']}) "
                                  f"has critical FD usage: {proc_data['open_fds']}/{proc_data['soft_limit']} "
                                  f"({proc_data['soft_usage']*100:.1f}%)")
                elif proc_data["soft_usage"] >= config["warning_threshold"]:
                    warning_processes.append(proc_data)
                    logger.warning(f"Process {proc_data['pid']} ({proc_data['name']}) "
                                 f"has high FD usage: {proc_data['open_fds']}/{proc_data['soft_limit']} "
                                 f"({proc_data['soft_usage']*100:.1f}%)")
        except Exception as e:
            logger.error(f"Error analyzing process {process.pid}: {str(e)}")
    
    # Check if we need to trigger an alert
    trigger_alert = len(critical_processes) > 0
    
    # Generate monitoring summary
    timestamp = datetime.now().isoformat()
    monitoring_data = {
        "timestamp": timestamp,
        "system": system_fds,
        "processes": process_data,
        "critical_count": len(critical_processes),
        "warning_count": len(warning_processes)
    }
    
    # Save history
    save_history(monitoring_data, config["history_file"], config["max_log_entries"])
    
    # Handle critical processes
    if critical_processes and config["auto_restart"]:
        logger.critical(f"Detected {len(critical_processes)} processes with critical FD usage, "
                      f"triggering service restart")
        restart_service(config["service_name"])
    
    return trigger_alert, process_data

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='File descriptor monitor')
    parser.add_argument('--config', type=str, help='Path to config file')
    parser.add_argument('--interval', type=int, help='Check interval in seconds')
    parser.add_argument('--daemon', action='store_true', help='Run as daemon')
    parser.add_argument('--process', type=str, help='Process filter (comma-separated)')
    parser.add_argument('--warning', type=float, help='Warning threshold (0.0-1.0)')
    parser.add_argument('--critical', type=float, help='Critical threshold (0.0-1.0)')
    parser.add_argument('--auto-restart', action='store_true', help='Auto restart on critical')
    parser.add_argument('--service', type=str, help='Service to restart')
    parser.add_argument('--log-level', type=str, choices=['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'],
                      help='Logging level')
    parser.add_argument('--email', type=str, help='Email for alerts')
    return parser.parse_args()

def load_config(args) -> Dict[str, Any]:
    """Load configuration from file and command line args"""
    config = DEFAULT_CONFIG.copy()
    
    # Load from config file if specified
    if args.config and os.path.exists(args.config):
        try:
            with open(args.config, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
        except Exception as e:
            logger.error(f"Error loading config file: {str(e)}")
    
    # Override with command line args
    if args.interval:
        config["check_interval"] = args.interval
    if args.process:
        config["process_filters"] = args.process.split(',')
    if args.warning:
        config["warning_threshold"] = args.warning
    if args.critical:
        config["critical_threshold"] = args.critical
    if args.auto_restart:
        config["auto_restart"] = True
    if args.service:
        config["service_name"] = args.service
    if args.log_level:
        config["log_level"] = args.log_level
    if args.email:
        config["alert_email"] = args.email
    
    # Set log level
    log_level = getattr(logging, config["log_level"])
    logger.setLevel(log_level)
    
    return config

def main():
    """Main entry point"""
    args = parse_arguments()
    config = load_config(args)
    
    logger.info(f"Starting file descriptor monitor with config: {json.dumps(config, indent=2)}")
    
    # Run once if not daemon mode
    if not args.daemon:
        logger.info("Running single check")
        trigger_alert, processes = check_fd_usage(config)
        
        # Print summary
        print(f"Found {len(processes)} matching processes")
        for proc in processes:
            if "error" not in proc:
                print(f"PID {proc['pid']}: {proc['open_fds']}/{proc['soft_limit']} "
                     f"FDs ({proc['soft_usage']*100:.1f}%)")
        
        if trigger_alert:
            message = "Critical file descriptor usage detected:\n\n"
            for proc in processes:
                if "error" not in proc and proc["soft_usage"] >= config["critical_threshold"]:
                    message += (f"PID {proc['pid']} ({proc['name']}): "
                              f"{proc['open_fds']}/{proc['soft_limit']} "
                              f"FDs ({proc['soft_usage']*100:.1f}%)\n")
            
            print("\nCRITICAL: " + message)
            send_alert("CRITICAL: High file descriptor usage", message, config)
        
        return
    
    # Daemon mode - continuous monitoring
    logger.info(f"Starting daemon mode, checking every {config['check_interval']} seconds")
    
    last_alert_time = datetime.min
    alert_cooldown = timedelta(hours=1)  # Don't send alerts more than once per hour
    
    try:
        while True:
            try:
                trigger_alert, _ = check_fd_usage(config)
                
                # Send alert if needed and not in cooldown
                now = datetime.now()
                if trigger_alert and (now - last_alert_time) > alert_cooldown:
                    message = generate_alert_message(config)
                    send_alert("CRITICAL: High file descriptor usage", message, config)
                    last_alert_time = now
                
            except Exception as e:
                logger.error(f"Error during monitoring: {str(e)}")
            
            # Sleep until next check
            time.sleep(config["check_interval"])
    
    except KeyboardInterrupt:
        logger.info("Monitoring stopped by user")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")

def generate_alert_message(config: Dict[str, Any]) -> str:
    """Generate detailed alert message"""
    try:
        # System information
        hostname = socket.gethostname()
        system_fds = check_system_fds()
        
        # Process information
        processes = find_api_processes(config["process_filters"])
        process_data = [analyze_process_fds(p) for p in processes]
        
        # Format message
        message = f"Host: {hostname}\n"
        message += f"Time: {datetime.now().isoformat()}\n\n"
        
        message += "System FD Usage:\n"
        if "error" not in system_fds:
            message += f"  Allocated: {system_fds.get('allocated', 'N/A')}\n"
            message += f"  Maximum: {system_fds.get('max', 'N/A')}\n"
            message += f"  Usage: {system_fds.get('usage', 0)*100:.1f}%\n\n"
        else:
            message += f"  Error: {system_fds['error']}\n\n"
        
        message += f"Process FD Usage ({len(process_data)} processes):\n"
        for proc in process_data:
            if "error" not in proc:
                message += f"  PID {proc['pid']} ({proc['name']}):\n"
                message += f"    Open FDs: {proc['open_fds']}\n"
                message += f"    Limit: {proc['soft_limit']}\n"
                message += f"    Usage: {proc['soft_usage']*100:.1f}%\n"
                message += f"    Sockets: {proc['socket_count']} (TCP: {proc['tcp_count']}, UDP: {proc['udp_count']}, Unix: {proc['unix_count']})\n"
                message += f"    Memory: {proc['memory']['rss'] / (1024*1024):.1f} MB RSS\n\n"
        
        return message
    except Exception as e:
        logger.error(f"Error generating alert message: {str(e)}")
        return f"Critical FD usage detected. Error generating details: {str(e)}"

if __name__ == "__main__":
    main() 