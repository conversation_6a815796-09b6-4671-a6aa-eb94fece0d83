<template>
  <div class="landing-page">
    <!-- Hero Section -->
    <section class="hero-section">
      <div class="container-responsive">
        <div class="hero-content">
          <!-- Logo/Brand -->
          <div class="hero-brand">
            <div class="brand-icon">
              <Icon name="i-heroicons-shield-check" class="icon" />
            </div>
            <h1 class="hero-title">AtlasVPN</h1>
            <p class="hero-subtitle">
              Secure your digital journey with premium VPN protection
            </p>
            <p class="hero-tagline">
              🚀 Earn rewards while staying protected
            </p>
          </div>

          <!-- Call to Action -->
          <div v-if="!isAuthenticated" class="hero-cta">
            <UButton
              @click="handleTelegramLogin"
              size="xl"
              :loading="isLoading"
              :disabled="!telegramAvailable || isLoading"
              class="cta-button"
            >
              <template #leading>
                <Icon name="i-simple-icons-telegram" class="w-6 h-6" />
              </template>
              {{ isLoading ? 'Connecting...' : '🚀 Start Earning with Telegram' }}
            </UButton>

            <!-- Telegram Not Available Message -->
            <div v-if="!telegramAvailable" class="telegram-notice">
              <div class="notice-content">
                <Icon name="i-heroicons-information-circle" class="notice-icon" />
                <div class="notice-text">
                  <p class="notice-title">Telegram Required</p>
                  <p class="notice-description">
                    This app is optimized for Telegram Mini Apps. Please access it through our Telegram bot for the best experience.
                  </p>
                </div>
              </div>
            </div>

            <!-- Authentication Status -->
            <div v-if="authError" class="auth-error">
              <Icon name="i-heroicons-exclamation-triangle" class="error-icon" />
              <p class="error-message">{{ authError }}</p>
            </div>
          </div>

          <!-- Already Authenticated -->
          <div v-else class="hero-authenticated">
            <div class="auth-icon">
              <Icon name="i-heroicons-check-circle" class="icon" />
            </div>
            <h2 class="auth-title">Welcome back!</h2>
            <p class="auth-subtitle">Ready to continue your journey?</p>
            <UButton
              @click="navigateTo('/dashboard')"
              size="xl"
              class="auth-button"
            >
              <Icon name="i-heroicons-arrow-right" class="w-5 h-5 mr-2" />
              Enter Dashboard
            </UButton>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
      <div class="container-responsive">
        <div class="features-header">
          <h2 class="section-title">Why Choose AtlasVPN?</h2>
          <p class="section-subtitle">
            Experience the perfect blend of security, speed, and rewards
          </p>
        </div>

        <div class="features-grid card-grid">
          <div class="feature-card">
            <div class="feature-icon earn-icon">
              <Icon name="i-heroicons-currency-dollar" class="icon" />
            </div>
            <h3 class="feature-title">Earn Rewards</h3>
            <p class="feature-description">
              Get paid for using our VPN service. Complete tasks and earn tokens while staying secure.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon security-icon">
              <Icon name="i-heroicons-shield-check" class="icon" />
            </div>
            <h3 class="feature-title">Military-Grade Security</h3>
            <p class="feature-description">
              Advanced encryption protocols protect your data with bank-level security standards.
            </p>
          </div>

          <div class="feature-card">
            <div class="feature-icon speed-icon">
              <Icon name="i-heroicons-bolt" class="icon" />
            </div>
            <h3 class="feature-title">Lightning Fast</h3>
            <p class="feature-description">
              High-speed servers worldwide ensure seamless browsing without compromising security.
            </p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'guest',
  layout: 'default'
})

// SEO
useHead({
  title: 'AtlasVPN - Secure VPN Service',
  meta: [
    { name: 'description', content: 'Secure VPN service for your privacy and freedom. Connect through Telegram for instant access.' },
    { name: 'keywords', content: 'VPN, security, privacy, Telegram, rewards, earn money' },
    { property: 'og:title', content: 'AtlasVPN - Secure VPN Service' },
    { property: 'og:description', content: 'Earn rewards while staying protected with premium VPN service' },
    { property: 'og:type', content: 'website' }
  ]
})

const { isAuthenticated, login, isLoading, verifySession, attemptTelegramAuth } = useAuth()
const { init: initTelegram, isAvailable: telegramAvailable, user: telegramUser, initData, hasValidAuthData, initError } = useTelegram()
const uiStore = useUiStore()

// Authentication state
const authError = ref('')
const isInitializing = ref(true)
const autoAuthAttempted = ref(false)

const handleTelegramLogin = async () => {
  authError.value = ''

  try {
    if (!telegramAvailable.value) {
      throw new Error('Telegram WebApp is not available. Please open this app through Telegram.')
    }

    if (!initData.value) {
      throw new Error('Telegram authentication data is not available.')
    }

    await login()

    // Show success feedback
    uiStore.showToast({
      type: 'success',
      title: 'Welcome!',
      message: 'Successfully authenticated with Telegram.'
    })
  } catch (err: any) {
    console.error('Login failed:', err)
    authError.value = err.message || 'Authentication failed. Please try again.'

    uiStore.showToast({
      type: 'error',
      title: 'Login Failed',
      message: err.message || 'Please try again later.'
    })
  }
}

// Initialize Telegram and check authentication on mount
onMounted(async () => {
  try {
    console.log('[Landing] Starting initialization...')

    // Initialize Telegram WebApp
    const webApp = initTelegram()

    if (webApp) {
      console.log('[Landing] Telegram WebApp initialized successfully')

      // First, try to verify existing session
      const isSessionValid = await verifySession()
      if (isSessionValid) {
        console.log('[Landing] Valid session found, redirecting to dashboard')
        await navigateTo('/dashboard')
        return
      }

      // If no valid session, attempt automatic Telegram authentication
      if (hasValidAuthData.value && !autoAuthAttempted.value) {
        autoAuthAttempted.value = true
        console.log('[Landing] Attempting automatic Telegram authentication...')

        try {
          const authResult = await attemptTelegramAuth()

          if (authResult.success) {
            console.log('[Landing] Automatic authentication successful')

            if (authResult.isNewUser) {
              console.log('[Landing] New user detected, redirecting to setup')
              await navigateTo('/auth/new-user-setup')
            } else {
              console.log('[Landing] Existing user, redirecting to dashboard')
              await navigateTo('/dashboard')
            }
            return
          } else {
            console.warn('[Landing] Automatic authentication failed:', authResult.error)
            authError.value = authResult.error || 'Automatic authentication failed'
          }
        } catch (error: any) {
          console.error('[Landing] Automatic authentication error:', error)
          authError.value = error.message || 'Authentication failed'
        }
      } else if (!hasValidAuthData.value) {
        console.warn('[Landing] No valid Telegram authentication data available')
        authError.value = 'No Telegram authentication data available. Please ensure you opened this app through Telegram.'
      }
    } else {
      console.warn('[Landing] Telegram WebApp not available - running in browser mode')
      if (initError.value) {
        authError.value = `Telegram initialization failed: ${initError.value}`
      }
    }
  } catch (error: any) {
    console.error('[Landing] Initialization error:', error)
    authError.value = error.message || 'Initialization failed'
  } finally {
    isInitializing.value = false
  }
})
</script>

<style scoped>
.landing-page {
  min-height: 100vh;
  background-color: var(--color-atlas-black);
  color: var(--ui-text);
  display: flex;
  flex-direction: column;
}

/* Hero Section - Flexbox Layout */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: radial-gradient(ellipse at center, rgba(124, 58, 237, 0.1) 0%, transparent 70%);
  padding: var(--spacing-8) 0;
  position: relative;
  flex: 1;
  width: 100%;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  max-width: 800px;
  width: 100%;
  margin: 0 auto;
  gap: var(--spacing-8);
  padding: var(--spacing-4);
}

.hero-brand {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-6);
}

.brand-icon {
  width: 80px;
  height: 80px;
  border-radius: 1.5rem;
  background: linear-gradient(135deg, var(--color-atlas-purple-500), var(--color-atlas-purple-600));
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 30px rgba(124, 58, 237, 0.4);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.brand-icon:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 40px rgba(124, 58, 237, 0.5);
}

.brand-icon .icon {
  width: 40px;
  height: 40px;
  color: white;
}

.hero-title {
  font-size: var(--font-size-6xl);
  font-weight: 800;
  margin-bottom: var(--spacing-6);
  background: linear-gradient(135deg, var(--color-atlas-purple-400), var(--color-atlas-cyan));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: var(--line-height-tight);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--ui-text-toned);
  margin-bottom: var(--spacing-4);
  line-height: var(--line-height-relaxed);
}

.hero-tagline {
  font-size: var(--font-size-lg);
  color: var(--color-atlas-purple-300);
  margin-bottom: var(--spacing-10);
  font-weight: 500;
}

/* Call to Action - Flexbox Layout */
.hero-cta {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-6);
  width: 100%;
  max-width: 400px;
}

.cta-button {
  background: linear-gradient(135deg, var(--color-atlas-purple-500), var(--color-atlas-purple-600));
  border: none;
  color: white;
  font-weight: 600;
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: 1rem;
  font-size: var(--font-size-lg);
  min-height: 64px;
  min-width: 280px;
  width: 100%;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-3);
}

.cta-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(124, 58, 237, 0.4);
  background: linear-gradient(135deg, var(--color-atlas-purple-400), var(--color-atlas-purple-500));
}

/* Telegram Notice */
.telegram-notice {
  max-width: 500px;
  margin: 0 auto;
}

.notice-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(124, 58, 237, 0.2);
  border-radius: 1rem;
  text-align: left;
}

.notice-icon {
  width: 20px;
  height: 20px;
  color: var(--color-atlas-purple-400);
  flex-shrink: 0;
  margin-top: 2px;
}

.notice-title {
  font-size: var(--font-size-sm);
  font-weight: 600;
  color: var(--color-atlas-purple-300);
  margin-bottom: var(--spacing-1);
}

.notice-description {
  font-size: var(--font-size-xs);
  color: var(--ui-text-muted);
  line-height: var(--line-height-relaxed);
}

/* Authentication Error */
.auth-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4);
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 0.75rem;
  width: 100%;
  max-width: 400px;
}

.error-icon {
  width: 20px;
  height: 20px;
  color: #ef4444;
  flex-shrink: 0;
}

.error-message {
  font-size: var(--font-size-sm);
  color: #ef4444;
  font-weight: 500;
  margin: 0;
}

/* Authenticated State */
.hero-authenticated {
  text-align: center;
}

.auth-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-6);
  border-radius: 50%;
  background: rgba(16, 185, 129, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

.auth-icon .icon {
  width: 32px;
  height: 32px;
  color: var(--color-atlas-emerald);
}

.auth-title {
  font-size: var(--font-size-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-4);
  background: linear-gradient(135deg, var(--color-atlas-gold), var(--color-atlas-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.auth-subtitle {
  font-size: var(--font-size-lg);
  color: var(--color-atlas-purple-300);
  margin-bottom: var(--spacing-8);
}

.auth-button {
  background: linear-gradient(135deg, var(--color-atlas-gold), var(--color-atlas-gold-dark));
  border: none;
  color: var(--color-atlas-black);
  font-weight: 600;
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: 1rem;
  font-size: var(--font-size-lg);
  min-height: 64px;
  min-width: 240px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.3);
}

.auth-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(255, 215, 0, 0.4);
}

/* Features Section */
.features-section {
  padding: var(--spacing-20) 0;
  background: linear-gradient(180deg, transparent 0%, rgba(26, 26, 26, 0.3) 100%);
}

.features-header {
  text-align: center;
  margin-bottom: var(--spacing-16);
}

.section-title {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--spacing-4);
  background: linear-gradient(135deg, var(--color-atlas-purple-300), var(--color-atlas-cyan));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: var(--font-size-lg);
  color: var(--ui-text-toned);
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--line-height-relaxed);
}

.features-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-8);
}

.feature-card {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 58, 237, 0.2);
  border-radius: 1.5rem;
  padding: var(--spacing-8);
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(124, 58, 237, 0.5), transparent);
}

.feature-card:hover {
  transform: translateY(-8px);
  border-color: rgba(124, 58, 237, 0.4);
  box-shadow: 0 20px 40px rgba(124, 58, 237, 0.15);
}

.feature-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto var(--spacing-6);
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.earn-icon {
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.security-icon {
  background: rgba(16, 185, 129, 0.2);
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.speed-icon {
  background: rgba(6, 182, 212, 0.2);
  border: 1px solid rgba(6, 182, 212, 0.3);
}

.feature-icon .icon {
  width: 28px;
  height: 28px;
}

.earn-icon .icon {
  color: var(--color-atlas-gold);
}

.security-icon .icon {
  color: var(--color-atlas-emerald);
}

.speed-icon .icon {
  color: var(--color-atlas-cyan);
}

.feature-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-3);
  color: var(--ui-text);
}

.feature-description {
  font-size: var(--font-size-base);
  color: var(--ui-text-toned);
  line-height: var(--line-height-relaxed);
}

/* Responsive Design */
@media (max-width: 639px) {
  .hero-section {
    padding: var(--spacing-6) 0;
    min-height: 100vh;
  }

  .hero-content {
    padding: var(--spacing-6);
    gap: var(--spacing-6);
    max-width: 100%;
  }

  .hero-brand {
    gap: var(--spacing-4);
    width: 100%;
  }

  .hero-cta {
    gap: var(--spacing-4);
    width: 100%;
  }

  .hero-title {
    font-size: var(--font-size-4xl);
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-3);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
    line-height: var(--line-height-normal);
    margin-bottom: var(--spacing-2);
  }

  .hero-tagline {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-4);
  }

  .brand-icon {
    width: 64px;
    height: 64px;
  }

  .brand-icon .icon {
    width: 32px;
    height: 32px;
  }

  .cta-button {
    min-width: 240px;
    font-size: var(--font-size-base);
    width: 100%;
    max-width: 320px;
  }

  .section-title {
    font-size: var(--font-size-3xl);
  }

  .section-subtitle {
    font-size: var(--font-size-base);
  }

  .feature-card {
    padding: var(--spacing-6);
  }

  .feature-icon {
    width: 48px;
    height: 48px;
  }

  .feature-icon .icon {
    width: 24px;
    height: 24px;
  }

  .features-section {
    padding: var(--spacing-16) 0;
  }

  /* Ensure proper text alignment and spacing on mobile */
  .telegram-notice,
  .auth-error {
    margin-top: var(--spacing-4);
    width: 100%;
  }

  .hero-authenticated {
    gap: var(--spacing-4);
    width: 100%;
  }

  /* Fix container responsive on mobile */
  .container-responsive {
    padding-left: var(--spacing-4);
    padding-right: var(--spacing-4);
  }
}

@media (min-width: 640px) and (max-width: 767px) {
  .hero-title {
    font-size: var(--font-size-5xl);
  }

  .features-grid {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--spacing-6);
  }

  .feature-card {
    flex: 1 1 calc(50% - var(--spacing-3));
    min-width: 280px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .hero-title {
    font-size: var(--font-size-6xl);
  }

  .features-grid {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: var(--spacing-8);
  }

  .feature-card {
    flex: 1 1 calc(50% - var(--spacing-4));
    min-width: 320px;
  }
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: var(--font-size-7xl);
  }

  .hero-content {
    flex-direction: row;
    align-items: center;
    text-align: left;
    gap: var(--spacing-12);
  }

  .hero-brand {
    flex: 1;
    align-items: flex-start;
  }

  .hero-cta {
    flex: 0 0 auto;
    max-width: 320px;
  }

  .features-grid {
    display: flex;
    flex-direction: row;
    gap: var(--spacing-8);
  }

  .feature-card {
    flex: 1;
    min-width: 0;
  }
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .brand-icon:hover,
  .cta-button:hover,
  .auth-button:hover,
  .feature-card:hover {
    transform: none;
  }

  .auth-icon {
    animation: none;
  }
}
</style>

