import React, { useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from './ToastProvider';
import { getTelegramData, triggerHapticFeedback } from '../../utils/telegram';
import { api } from '../../api';
import LoadingSpinner from './LoadingSpinner';

interface FormData {
  username: string;
  email: string;
  telegram_photo_url: string;
  first_name?: string;
  last_name?: string;
  telegram_id?: string;
}

interface TelegramData {
  telegram_id: string;
  username?: string;
  first_name?: string;
  last_name?: string;
  photo_url?: string;
}

interface VerifiedData {
  telegramData: TelegramData;
  timestamp: number;
}

const STEPS = {
  WELCOME: 'welcome',
  GUARDIAN: 'guardian',
  SETUP: 'setup',
  COMPLETING: 'completing',
  SUCCESS: 'success',
};

interface NewUserSetupInteractiveProps {
  onNavigate: (path: string) => void;
}

const NewUserSetupInteractive: React.FC<NewUserSetupInteractiveProps> = ({ onNavigate }) => {
  const { showToast } = useToast();
  const [currentStep, setCurrentStep] = useState(STEPS.WELCOME);
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    telegram_photo_url: 'Lovely Angel',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [verifiedData, setVerifiedData] = useState<VerifiedData | null>(null);
  const [usernameStatus, setUsernameStatus] = useState({ isValid: false, message: '' });

  // Initialize with Telegram data
  useEffect(() => {
    const telegramData = getTelegramData();
    if (telegramData?.user) {
      setVerifiedData({
        telegramData: {
          telegram_id: String(telegramData.user.id),
          username: telegramData.user.username,
          first_name: telegramData.user.first_name,
          last_name: telegramData.user.last_name,
          photo_url: undefined,
        },
        timestamp: Date.now(),
      });

      setFormData(prev => ({
        ...prev,
        username: telegramData.user.username?.toLowerCase() || '',
        first_name: telegramData.user.first_name || '',
        last_name: telegramData.user.last_name || '',
      }));
    }
  }, []);

  // Username validation
  useEffect(() => {
    const validateUsername = async () => {
      if (!formData.username || formData.username.length < 3) {
        setUsernameStatus({ isValid: false, message: 'Username must be at least 3 characters' });
        return;
      }

      if (!/^[a-zA-Z0-9_]+$/.test(formData.username)) {
        setUsernameStatus({ isValid: false, message: 'Username can only contain letters, numbers, and underscores' });
        return;
      }

      try {
        const response = await api.get(`/auth/check-username/${formData.username}`);
        if (response.data?.available) {
          setUsernameStatus({ isValid: true, message: 'Username is available' });
        } else {
          setUsernameStatus({ isValid: false, message: 'Username is already taken' });
        }
      } catch (error) {
        setUsernameStatus({ isValid: false, message: 'Error checking username availability' });
      }
    };

    const timeoutId = setTimeout(validateUsername, 500);
    return () => clearTimeout(timeoutId);
  }, [formData.username]);

  const handleNext = () => {
    triggerHapticFeedback('light');
    if (currentStep === STEPS.WELCOME) {
      setCurrentStep(STEPS.GUARDIAN);
    } else if (currentStep === STEPS.GUARDIAN) {
      setCurrentStep(STEPS.SETUP);
    }
  };

  const handleBack = () => {
    triggerHapticFeedback('light');
    if (currentStep === STEPS.SETUP) {
      setCurrentStep(STEPS.GUARDIAN);
    } else if (currentStep === STEPS.GUARDIAN) {
      setCurrentStep(STEPS.WELCOME);
    }
  };

  const handleSubmit = async () => {
    if (!verifiedData?.telegramData) {
      showToast('No verified data available for submission', 'error');
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setCurrentStep(STEPS.COMPLETING);
      triggerHapticFeedback('light');

      const telegramData = getTelegramData();
      if (!telegramData?.initData) {
        throw new Error('No Telegram WebApp data available');
      }

      const submissionData = {
        init_data: telegramData.initData,
        username: formData.username,
        selected_avatar: formData.telegram_photo_url,
      };

      const response = await api.post('/auth/telegram/register', {
        init_data: submissionData.init_data,
        username: submissionData.username,
        selected_avatar: submissionData.selected_avatar,
        auth_method: 'telegram'
      });

      if (response.data && !response.data.error) {
        triggerHapticFeedback('success');
        showToast('Registration successful! Welcome to VIPVerse!', 'success');
        
        setTimeout(() => {
          onNavigate('/dashboard');
        }, 1000);
      } else {
        throw new Error(response.data.message || response.data.error || 'Registration failed');
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      triggerHapticFeedback('error');
      setError(error.message || 'Registration failed. Please try again.');
      setCurrentStep(STEPS.SETUP);
      showToast(error.message || 'Registration failed. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const validateEmail = (email: string): boolean => {
    return !email || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  if (!verifiedData?.telegramData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-purple-400">Loading setup...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex items-center justify-center p-4 bg-transparent">
      <div className="relative z-10">
        <AnimatePresence mode="wait">
          {currentStep === STEPS.WELCOME && (
            <WelcomeStep telegramData={verifiedData.telegramData} onNext={handleNext} />
          )}

          {currentStep === STEPS.GUARDIAN && (
            <GuardianStep
              formData={formData}
              setFormData={setFormData}
              onNext={handleNext}
              onBack={handleBack}
            />
          )}

          {currentStep === STEPS.SETUP && (
            <SetupStep
              formData={formData}
              setFormData={setFormData}
              usernameStatus={usernameStatus}
              onNext={handleSubmit}
              onBack={handleBack}
              error={error}
              isValid={usernameStatus.isValid && validateEmail(formData.email)}
            />
          )}

          {currentStep === STEPS.COMPLETING && <LoadingStep />}
        </AnimatePresence>
      </div>
    </div>
  );
};

// Welcome Step Component
const WelcomeStep: React.FC<{
  telegramData: TelegramData;
  onNext: () => void;
}> = ({ telegramData, onNext }) => {
  const [messages, setMessages] = useState<string[]>([]);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    const welcomeMessages = [
      'SECURITY ALERT...',
      `Unauthorized access detected: ${telegramData.first_name}`,
      'Initiating secure protocol...',
      'Analyzing connection...',
      'Establishing encrypted channel...',
      `Welcome to the resistance, ${telegramData.first_name}`,
    ];

    let currentIndex = 0;
    const messageInterval = setInterval(() => {
      if (currentIndex < welcomeMessages.length) {
        setMessages(prev => [...prev, welcomeMessages[currentIndex]]);
        currentIndex++;
      } else {
        clearInterval(messageInterval);
        setIsComplete(true);
      }
    }, 800);

    return () => clearInterval(messageInterval);
  }, [telegramData.first_name]);

  return (
    <motion.div
      key="welcome"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="max-w-md w-full bg-black/90 rounded-2xl p-8 border border-purple-500/20 backdrop-blur-sm"
    >
      <div className="space-y-4 mb-8">
        {messages.map((message, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
            className="text-green-400 font-mono text-sm"
          >
            {'>'} {message}
          </motion.div>
        ))}
      </div>

      {isComplete && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center"
        >
          <motion.button
            onClick={onNext}
            className="w-full py-3 px-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl shadow-lg transition-all duration-300 hover:from-purple-700 hover:to-blue-700"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            Initialize Guardian Protocol
          </motion.button>
        </motion.div>
      )}
    </motion.div>
  );
};

// Guardian Step Component
const GuardianStep: React.FC<{
  formData: FormData;
  setFormData: (data: FormData) => void;
  onNext: () => void;
  onBack: () => void;
}> = ({ formData, setFormData, onNext, onBack }) => {
  const avatars = [
    'Lovely Angel', 'Cyber Warrior', 'Digital Ghost', 'Code Breaker',
    'Shadow Runner', 'Data Miner', 'Quantum Hacker', 'Neural Knight'
  ];

  return (
    <motion.div
      key="guardian"
      initial={{ opacity: 0, x: 100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -100 }}
      className="max-w-md w-full bg-black/90 rounded-2xl p-8 border border-purple-500/20 backdrop-blur-sm"
    >
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-purple-400 mb-2">Choose Your Guardian</h2>
        <p className="text-gray-300 text-sm">Select your digital identity</p>
      </div>

      <div className="grid grid-cols-2 gap-3 mb-8">
        {avatars.map((avatar) => (
          <motion.button
            key={avatar}
            onClick={() => setFormData({ ...formData, telegram_photo_url: avatar })}
            className={`p-4 rounded-lg border-2 transition-all ${
              formData.telegram_photo_url === avatar
                ? 'border-purple-500 bg-purple-500/20'
                : 'border-gray-600 bg-gray-800/50 hover:border-purple-400'
            }`}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="text-2xl mb-2">🛡️</div>
            <div className="text-xs text-gray-300">{avatar}</div>
          </motion.button>
        ))}
      </div>

      <div className="flex space-x-3">
        <motion.button
          onClick={onBack}
          className="flex-1 py-3 px-6 bg-gray-700 text-white font-semibold rounded-xl"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          Back
        </motion.button>
        <motion.button
          onClick={onNext}
          className="flex-1 py-3 px-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          Continue
        </motion.button>
      </div>
    </motion.div>
  );
};

// Setup Step Component
const SetupStep: React.FC<{
  formData: FormData;
  setFormData: (data: FormData) => void;
  usernameStatus: { isValid: boolean; message: string };
  onNext: () => void;
  onBack: () => void;
  error: string | null;
  isValid: boolean;
}> = ({ formData, setFormData, usernameStatus, onNext, onBack, error, isValid }) => {
  return (
    <motion.div
      key="setup"
      initial={{ opacity: 0, x: 100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -100 }}
      className="max-w-md w-full bg-black/90 rounded-2xl p-8 border border-purple-500/20 backdrop-blur-sm"
    >
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-purple-400 mb-2">System Configuration</h2>
        <p className="text-gray-300 text-sm">Complete your profile setup</p>
      </div>

      <div className="space-y-6 mb-8">
        <div>
          <label className="block text-xs font-mono text-purple-200/90 mb-1">
            {'>'} System username required:
          </label>
          <input
            type="text"
            value={formData.username}
            onChange={e => setFormData({ ...formData, username: e.target.value.toLowerCase() })}
            className="w-full px-3 py-2 rounded-lg bg-purple-900/20 border border-purple-500/20 
                     text-purple-100 placeholder-purple-300/30 focus:border-purple-400 text-sm font-mono"
            placeholder="Enter username"
          />
          {usernameStatus.message && (
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className={`mt-1 text-xs font-mono ${
                usernameStatus.isValid ? 'text-green-400' : 'text-red-400'
              }`}
            >
              {'>'} {usernameStatus.message}
            </motion.p>
          )}
        </div>

        <div>
          <label className="block text-xs font-mono text-purple-200/90 mb-1">
            {'>'} Secure email required:
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={e => setFormData({ ...formData, email: e.target.value })}
            className="w-full px-3 py-2 rounded-lg bg-purple-900/20 border border-purple-500/20 
                     text-purple-100 placeholder-purple-300/30 focus:border-purple-400 text-sm font-mono"
            placeholder="Enter email"
            required
          />
          <p className="mt-1 text-xs font-mono text-purple-400">
            {'>'} Required for account recovery and security alerts
          </p>
        </div>

        {error && (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-red-400 text-xs font-mono"
          >
            {'>'} Error: {error}
          </motion.p>
        )}
      </div>

      <div className="flex space-x-3">
        <motion.button
          onClick={onBack}
          className="flex-1 py-3 px-6 bg-gray-700 text-white font-semibold rounded-xl"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          Back
        </motion.button>
        <motion.button
          onClick={onNext}
          disabled={!isValid}
          className="flex-1 py-3 px-6 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl disabled:opacity-50 disabled:cursor-not-allowed"
          whileHover={isValid ? { scale: 1.02 } : {}}
          whileTap={isValid ? { scale: 0.98 } : {}}
        >
          Complete Setup
        </motion.button>
      </div>
    </motion.div>
  );
};

// Loading Step Component
const LoadingStep: React.FC = () => {
  return (
    <motion.div
      key="loading"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="max-w-md w-full bg-black/90 rounded-2xl p-8 border border-purple-500/20 backdrop-blur-sm text-center"
    >
      <LoadingSpinner size="lg" className="mb-6" />
      <h2 className="text-xl font-bold text-purple-400 mb-2">Initializing Account</h2>
      <p className="text-gray-300 text-sm">Please wait while we set up your VIPVerse profile...</p>
    </motion.div>
  );
};

export default NewUserSetupInteractive;
