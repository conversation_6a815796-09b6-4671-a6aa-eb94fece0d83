<template>
  <span
    class="animated-counter font-mono"
    :class="textClass"
  >
    {{ formattedDisplayValue }}
  </span>
</template>

<script setup lang="ts">
interface Props {
  value: number
  duration?: number
  decimals?: number
  prefix?: string
  suffix?: string
  textClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  duration: 1000,
  decimals: 0,
  prefix: '',
  suffix: '',
  textClass: ''
})

const displayValue = ref(0)
const animationId = ref<number | null>(null)

const formatValue = (val: number) => {
  const formatted = val.toFixed(props.decimals)
  return `${props.prefix}${formatted}${props.suffix}`
}

const animateToValue = (targetValue: number) => {
  const startValue = displayValue.value
  const startTime = performance.now()
  
  const animate = (currentTime: number) => {
    const elapsed = currentTime - startTime
    const progress = Math.min(elapsed / props.duration, 1)
    
    // Easing function (ease-out)
    const easeOut = 1 - Math.pow(1 - progress, 3)
    
    const currentValue = startValue + (targetValue - startValue) * easeOut
    displayValue.value = currentValue
    
    if (progress < 1) {
      animationId.value = requestAnimationFrame(animate)
    } else {
      displayValue.value = targetValue
      animationId.value = null
    }
  }
  
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
  }
  
  animationId.value = requestAnimationFrame(animate)
}

// Watch for value changes
watch(() => props.value, (newValue) => {
  animateToValue(newValue)
}, { immediate: true })

// Format display value
const formattedDisplayValue = computed(() => formatValue(displayValue.value))

// Clean up animation on unmount
onUnmounted(() => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value)
  }
})
</script>

<style scoped>
.animated-counter {
  display: inline-block;
  min-width: 1ch;
  text-align: right;
}
</style>
