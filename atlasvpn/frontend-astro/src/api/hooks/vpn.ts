/**
 * @file api/hooks/vpn.ts
 * @description VPN-related React Query hooks
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type {
  VpnPackage,
  VpnSubscription,
  VpnPanel,
  QueryOptions,
  MutationOptions
} from '../types';
import { vpnService } from '../services';

// Query Keys
export const VPN_QUERY_KEYS = {
  VPN_PACKAGES: ['vpn', 'packages'],
  VPN_SUBSCRIPTIONS: ['vpn', 'subscriptions'],
  VPN_PANELS: ['vpn', 'panels']
} as const;

// Default query options
const DEFAULT_QUERY_OPTIONS: QueryOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  retry: false
};

// VPN Hooks
export function useVpnPackagesQuery(options?: {
  staleTime?: number;
  enabled?: boolean;
  refetchInterval?: number | false;
}) {
  return useQuery({
    queryKey: VPN_QUERY_KEYS.VPN_PACKAGES,
    queryFn: async (): Promise<VpnPackage[]> => {
      try {
        return await vpnService.getAvailablePackages();
      } catch (error: any) {
        console.error('Failed to fetch VPN packages:', error);
        if (error?.response?.status === 404) {
          return [];
        }
        throw error;
      }
    },
    ...DEFAULT_QUERY_OPTIONS,
    staleTime: options?.staleTime ?? (10 * 60 * 1000), // 10 minutes for packages
    enabled: options?.enabled,
    refetchInterval: options?.refetchInterval,
  });
}

export function useVpnDashboardQuery() {
  const subscriptionsQuery = useVpnSubscriptionsQuery();
  const packagesQuery = useVpnPackagesQuery();

  return {
    subscriptions: subscriptionsQuery.data || [],
    packages: packagesQuery.data || [],
    isLoading: subscriptionsQuery.isLoading || packagesQuery.isLoading,
    isError: subscriptionsQuery.isError || packagesQuery.isError,
    error: subscriptionsQuery.error || packagesQuery.error,
    refetch: () => {
      subscriptionsQuery.refetch();
      packagesQuery.refetch();
    },
  };
}

export function useVpnSubscriptionsQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: VPN_QUERY_KEYS.VPN_SUBSCRIPTIONS,
    queryFn: () => vpnService.getUserSubscriptions(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function useVpnPanelsQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: VPN_QUERY_KEYS.VPN_PANELS,
    queryFn: () => vpnService.getVpnPanels(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function usePurchaseVpnPackageMutation(options?: MutationOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (packageId: number) => vpnService.purchasePackage(packageId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: VPN_QUERY_KEYS.VPN_SUBSCRIPTIONS });
      queryClient.invalidateQueries({ queryKey: ['user', 'stats'] });
    },
    ...options
  });
}