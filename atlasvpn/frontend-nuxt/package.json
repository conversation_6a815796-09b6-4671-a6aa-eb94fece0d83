{"name": "atlasvpn-nuxt-frontend", "version": "1.0.0", "private": true, "type": "module", "packageManager": "pnpm@9.0.0", "scripts": {"build": "nuxt build", "dev": "nuxt dev --host 0.0.0.0 --port 3005", "dev:fast": "nuxt dev --host 0.0.0.0 --port 3005 --no-clear", "generate": "nuxt generate", "preview": "nuxt preview --host 0.0.0.0 --port 3005", "postinstall": "nuxt prepare", "type-check": "nuxt typecheck", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write \"**/*.{ts,vue,js,json,css,md}\"", "format:check": "prettier --check \"**/*.{ts,vue,js,json,css,md}\"", "test": "vitest", "test:run": "vitest run", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "clean": "rm -rf .nuxt .output dist node_modules/.cache", "analyze": "nuxt analyze"}, "dependencies": {"@heroicons/vue": "^2.2.0", "@nuxt/ui": "^3.3.0", "@pinia/nuxt": "^0.11.2", "@tanstack/vue-query": "^5.56.0", "@telegram-apps/sdk": "^3.11.4", "@tresjs/cientos": "^4.1.0", "@tresjs/core": "^4.3.6", "@vueuse/nuxt": "^13.6.0", "axios": "^1.11.0", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "js-cookie": "^3.0.5", "lodash.debounce": "^4.0.8", "lucide-vue-next": "^0.513.0", "nuxt": "^4.0.2", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "qrcode": "^1.5.4", "three": "^0.177.0", "vue": "^3.5.18", "vue-router": "^4.4.0", "zod": "^3.25.51"}, "devDependencies": {"@nuxt/devtools": "^2.6.2", "@nuxt/eslint": "^0.7.4", "@nuxt/test-utils": "^3.15.2", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/lodash.debounce": "^4.0.9", "@types/node": "^22.17.0", "@types/qrcode": "^1.5.5", "@types/three": "^0.177.0", "@vitest/coverage-v8": "^3.2.1", "@vitest/ui": "^3.2.1", "@vue/test-utils": "^2.4.6", "eslint": "^9.28.0", "jsdom": "^26.1.0", "playwright": "^1.54.1", "prettier": "^3.5.3", "typescript": "^5.8.3", "vitest": "^3.2.1"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}}