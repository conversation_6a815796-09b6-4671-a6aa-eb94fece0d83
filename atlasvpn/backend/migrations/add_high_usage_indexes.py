"""Add high-usage indexes to existing tables."""
from sqlalchemy import text
import logging

logger = logging.getLogger(__name__)

def upgrade(connection):
    """Add high-usage indexes to tables."""
    try:
        logger.info("Adding high-usage indexes...")
        
        # Task indexes for frequent queries
        connection.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_task_type_active ON tasks(type, is_active);
            CREATE INDEX IF NOT EXISTS idx_task_reward ON tasks(reward_type, reward_value);
            CREATE INDEX IF NOT EXISTS idx_task_verification ON tasks(type, verification_type, is_active);
        """))
        
        # Task completion indexes for status checks
        connection.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_user_task_status ON task_completions(user_id, task_id, status);
            CREATE INDEX IF NOT EXISTS idx_user_completion ON task_completions(user_id, is_claimed, status);
            CREATE INDEX IF NOT EXISTS idx_task_verification ON task_completions(task_id, status, last_verified_at);
        """))
        
        # Daily streak indexes for validation
        connection.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_user_streak ON daily_task_streaks(user_id, last_check_in);
            CREATE INDEX IF NOT EXISTS idx_user_cycle ON daily_task_streaks(user_id, current_cycle_day, last_check_in);
        """))
        
        # Task pack indexes for sequential access
        connection.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_pack_task_order ON task_pack_tasks(pack_id, "order");
        """))
        
        logger.info("Successfully added all high-usage indexes")
        
    except Exception as e:
        logger.error(f"Error adding indexes: {str(e)}")
        raise

def downgrade(connection):
    """Remove added high-usage indexes."""
    try:
        logger.info("Removing high-usage indexes...")
        
        indexes_to_drop = [
            "idx_task_type_active",
            "idx_task_reward",
            "idx_task_verification",
            "idx_user_task_status",
            "idx_user_completion",
            "idx_task_verification",
            "idx_user_streak",
            "idx_user_cycle",
            "idx_pack_task_order"
        ]
        
        for index_name in indexes_to_drop:
            connection.execute(text(f"DROP INDEX IF EXISTS {index_name}"))
            
        logger.info("Successfully removed all high-usage indexes")
        
    except Exception as e:
        logger.error(f"Error removing indexes: {str(e)}")
        raise 