import { defineStore } from 'pinia'
import type { User, DashboardStats, Transaction } from '~/types'

export interface UserState {
  // User data
  currentUser: User | null
  dashboardStats: DashboardStats | null
  transactions: Transaction[]
  
  // Loading states
  isLoading: boolean
  isLoadingStats: boolean
  isLoadingTransactions: boolean
  
  // Error states
  error: string | null
  statsError: string | null
  transactionsError: string | null
  
  // Cache timestamps
  lastStatsUpdate: number
  lastTransactionsUpdate: number
  
  // Preferences
  preferences: {
    notifications: boolean
    autoRenew: boolean
    language: string
    currency: string
  }
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    // User data
    currentUser: null,
    dashboardStats: null,
    transactions: [],
    
    // Loading states
    isLoading: false,
    isLoadingStats: false,
    isLoadingTransactions: false,
    
    // Error states
    error: null,
    statsError: null,
    transactionsError: null,
    
    // Cache timestamps
    lastStatsUpdate: 0,
    lastTransactionsUpdate: 0,
    
    // Preferences
    preferences: {
      notifications: true,
      autoRenew: false,
      language: 'en',
      currency: 'USD'
    }
  }),

  persist: {
    key: 'atlas-user-store',
    storage: typeof window !== 'undefined' ? localStorage : undefined,
    pick: [
      'currentUser',
      'preferences'
    ]
  },

  getters: {
    // User info
    isAuthenticated: (state) => !!state.currentUser,
    
    userDisplayName: (state) => {
      if (!state.currentUser) return 'Guest'
      return state.currentUser.username || 
             state.currentUser.first_name || 
             `User ${state.currentUser.id}`
    },
    
    userInitials: (state) => {
      if (!state.currentUser) return 'G'
      const name = state.currentUser.username || state.currentUser.first_name || 'User'
      return name.charAt(0).toUpperCase()
    },
    
    isNewUser: (state) => state.currentUser?.isNewUser === 1,
    
    // Wallet & Balance
    walletBalance: (state) => state.currentUser?.wallet_balance || 0,
    
    formattedBalance: (state) => {
      const balance = state.currentUser?.wallet_balance || 0
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
      }).format(balance)
    },
    
    // Dashboard stats
    activeSubscriptions: (state) => state.dashboardStats?.active_subscriptions || 0,
    
    dataUsagePercentage: (state) => {
      if (!state.dashboardStats || state.dashboardStats.total_data_limit === 0) return 0
      return Math.min(
        (state.dashboardStats.total_data_used / state.dashboardStats.total_data_limit) * 100,
        100
      )
    },
    
    formattedDataUsage: (state) => {
      if (!state.dashboardStats) return { used: '0 GB', total: '0 GB' }
      
      const formatBytes = (bytes: number) => {
        if (bytes === 0) return '0 GB'
        const gb = bytes / (1024 * 1024 * 1024)
        return `${gb.toFixed(1)} GB`
      }

      return {
        used: formatBytes(state.dashboardStats.total_data_used),
        total: formatBytes(state.dashboardStats.total_data_limit)
      }
    },
    
    totalCardEarnings: (state) => state.dashboardStats?.total_accumulated_card_profit || 0,
    
    hourlyPassiveIncome: (state) => state.dashboardStats?.total_passive_hourly_income || 0,
    
    // Transactions
    recentTransactions: (state) => state.transactions.slice(0, 5),
    
    totalEarnings: (state) => 
      state.transactions
        .filter(t => t.amount > 0)
        .reduce((total, t) => total + t.amount, 0),
    
    totalSpending: (state) => 
      state.transactions
        .filter(t => t.amount < 0)
        .reduce((total, t) => total + Math.abs(t.amount), 0),
    
    // Referral info
    referralCode: (state) => state.currentUser?.referral_code || '',
    
    hasReferrer: (state) => !!state.currentUser?.referred_by,
    
    referrerUsername: (state) => state.currentUser?.referred_by_username || '',
    
    discountPercent: (state) => state.currentUser?.discount_percent || 0,
    
    // Account status
    isActive: (state) => state.currentUser?.is_active || false,
    
    isVerified: (state) => state.currentUser?.is_verified || false,
    
    accountAge: (state) => {
      if (!state.currentUser?.created_at) return 0
      const created = new Date(state.currentUser.created_at)
      const now = new Date()
      return Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24))
    },
    
    // Cache status
    isStatsStale: (state) => {
      const CACHE_TTL = 2 * 60 * 1000 // 2 minutes
      return Date.now() - state.lastStatsUpdate > CACHE_TTL
    },
    
    isTransactionsStale: (state) => {
      const CACHE_TTL = 5 * 60 * 1000 // 5 minutes
      return Date.now() - state.lastTransactionsUpdate > CACHE_TTL
    }
  },

  actions: {
    // User management
    setUser(user: User | null) {
      this.currentUser = user
    },

    updateUser(updates: Partial<User>) {
      if (this.currentUser) {
        this.currentUser = { ...this.currentUser, ...updates }
      }
    },

    updateWalletBalance(balance: number) {
      if (this.currentUser) {
        this.currentUser.wallet_balance = balance
      }
    },

    // Dashboard stats
    setDashboardStats(stats: DashboardStats) {
      this.dashboardStats = stats
      this.lastStatsUpdate = Date.now()
    },

    updateDashboardStats(updates: Partial<DashboardStats>) {
      if (this.dashboardStats) {
        this.dashboardStats = { ...this.dashboardStats, ...updates }
      }
    },

    // Transactions
    setTransactions(transactions: Transaction[]) {
      this.transactions = transactions
      this.lastTransactionsUpdate = Date.now()
    },

    addTransaction(transaction: Transaction) {
      this.transactions.unshift(transaction)
      // Update wallet balance if it's a wallet transaction
      if (this.currentUser && transaction.balance_after !== undefined) {
        this.currentUser.wallet_balance = transaction.balance_after
      }
    },

    // Loading states
    setLoading(loading: boolean) {
      this.isLoading = loading
    },

    setStatsLoading(loading: boolean) {
      this.isLoadingStats = loading
    },

    setTransactionsLoading(loading: boolean) {
      this.isLoadingTransactions = loading
    },

    // Error states
    setError(error: string | null) {
      this.error = error
    },

    setStatsError(error: string | null) {
      this.statsError = error
    },

    setTransactionsError(error: string | null) {
      this.transactionsError = error
    },

    // Preferences
    updatePreferences(updates: Partial<UserState['preferences']>) {
      this.preferences = { ...this.preferences, ...updates }
    },

    // Utility actions
    clearUserData() {
      this.currentUser = null
      this.dashboardStats = null
      this.transactions = []
      this.error = null
      this.statsError = null
      this.transactionsError = null
      this.lastStatsUpdate = 0
      this.lastTransactionsUpdate = 0
    },

    // Format currency
    formatCurrency(amount: number): string {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: this.preferences.currency
      }).format(amount)
    },

    // Check if user can perform action
    canAfford(amount: number): boolean {
      return this.walletBalance >= amount
    },

    // Get user role display
    getRoleDisplay(): string {
      if (!this.currentUser) return 'Guest'
      
      const roleMap = {
        admin: 'Administrator',
        reseller: 'Reseller',
        user: 'User'
      }
      
      return roleMap[this.currentUser.role] || 'User'
    }
  }
})
