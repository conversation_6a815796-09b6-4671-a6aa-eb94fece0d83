import * as CryptoJS from 'crypto-js';

const ENCRYPTION_KEY =
  process.env.VITE_ENCRYPTION_KEY || crypto.getRandomValues(new Uint8Array(32)).join('');

/**
 * Encrypt data using AES encryption
 */
export const encryptData = async (data: string): Promise<string> => {
  try {
    return CryptoJS.AES.encrypt(data, ENCRYPTION_KEY).toString();
  } catch (error) {
    throw new Error(`Encryption failed: ${error}`);
  }
};

/**
 * Decrypt data using AES decryption
 */
export const decryptData = async (encryptedData: string): Promise<string> => {
  try {
    const decrypted = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY).toString(
      CryptoJS.enc.Utf8
    );
    if (!decrypted) {
      throw new Error('Decryption resulted in empty string');
    }
    return decrypted;
  } catch (error) {
    throw new Error(`Decryption failed: ${error}`);
  }
};

export const secureStorage = {
  set: (key: string, value: any) => {
    try {
      const stringValue = JSON.stringify(value);
      const encrypted = CryptoJS.AES.encrypt(stringValue, ENCRYPTION_KEY).toString();
      sessionStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Storage encryption failed:', error);
    }
  },

  get: (key: string) => {
    try {
      const encryptedData = sessionStorage.getItem(key);
      if (!encryptedData) {
        return null;
      }
      const decrypted = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY).toString(
        CryptoJS.enc.Utf8
      );
      return JSON.parse(decrypted);
    } catch (error) {
      console.error(`[secureStorage] Error getting/decrypting ${key}:`, error);
      return null;
    }
  },

  clear: (key: string) => {
    try {
      sessionStorage.removeItem(key);
    } catch (error) {
      console.error('Storage clear failed:', error);
    }
  },
};
