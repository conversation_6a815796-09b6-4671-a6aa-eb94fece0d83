<template>
  <div class="p-6 max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-6">Backend Connectivity Test</h1>
    
    <!-- Health Check Test -->
    <div class="mb-8 p-4 border rounded-lg">
      <h2 class="text-xl font-semibold mb-4">1. Health Check Test</h2>
      <div v-if="healthPending" class="text-blue-600">Loading health status...</div>
      <div v-else-if="healthData" class="text-green-600">
        ✅ Backend is healthy: {{ healthData.status }} (v{{ healthData.version }})
      </div>
      <div v-else-if="healthError" class="text-red-600">
        ❌ Health check failed: {{ healthError }}
      </div>
      <button 
        @click="refreshHealth()" 
        class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Refresh Health Check
      </button>
    </div>

    <!-- User Authentication Test -->
    <div class="mb-8 p-4 border rounded-lg">
      <h2 class="text-xl font-semibold mb-4">2. User Authentication Test</h2>
      <div v-if="userPending" class="text-blue-600">Loading user data...</div>
      <div v-else-if="userData" class="text-green-600">
        ✅ User authenticated: {{ userData.username }}
      </div>
      <div v-else-if="userError" class="text-orange-600">
        ⚠️ Not authenticated (expected): {{ userError.data?.detail || userError.message }}
      </div>
      <button 
        @click="refreshUser()" 
        class="mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      >
        Test User Endpoint
      </button>
    </div>

    <!-- Modern API Composable Test -->
    <div class="mb-8 p-4 border rounded-lg">
      <h2 class="text-xl font-semibold mb-4">3. Modern API Composable Test</h2>
      <div v-if="modernHealthPending" class="text-blue-600">Loading with modern composable...</div>
      <div v-else-if="modernHealthData" class="text-green-600">
        ✅ Modern composable works: {{ modernHealthData.status }}
      </div>
      <div v-else-if="modernHealthError" class="text-red-600">
        ❌ Modern composable failed: {{ modernHealthError }}
      </div>
      <button 
        @click="refreshModernHealth()" 
        class="mt-2 px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
      >
        Test Modern Composable
      </button>
    </div>

    <!-- Network Information -->
    <div class="mb-8 p-4 border rounded-lg bg-gray-50">
      <h2 class="text-xl font-semibold mb-4">4. Network Configuration</h2>
      <div class="space-y-2 text-sm">
        <p><strong>API URL:</strong> {{ config.public.apiUrl }}</p>
        <p><strong>WebSocket URL:</strong> {{ config.public.wsBaseUrl }}</p>
        <p><strong>Environment:</strong> {{ config.public.environment }}</p>
      </div>
    </div>

    <!-- Test Results Summary -->
    <div class="p-4 border rounded-lg bg-blue-50">
      <h2 class="text-xl font-semibold mb-4">5. Test Summary</h2>
      <div class="space-y-2">
        <div class="flex items-center">
          <span class="w-4 h-4 rounded-full mr-2" :class="healthData ? 'bg-green-500' : 'bg-red-500'"></span>
          <span>Backend Health Check</span>
        </div>
        <div class="flex items-center">
          <span class="w-4 h-4 rounded-full mr-2" :class="userError?.status === 401 ? 'bg-green-500' : 'bg-red-500'"></span>
          <span>Authentication Endpoint (401 expected)</span>
        </div>
        <div class="flex items-center">
          <span class="w-4 h-4 rounded-full mr-2" :class="modernHealthData ? 'bg-green-500' : 'bg-red-500'"></span>
          <span>Modern API Composables</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Test basic useFetch with health endpoint
const { data: healthData, pending: healthPending, error: healthError, refresh: refreshHealth } = 
  await useFetch('/api/health', {
    server: false,
    baseURL: 'https://app.atlasvip.cloud'
  })

// Test user endpoint (should return 401)
const { data: userData, pending: userPending, error: userError, refresh: refreshUser } = 
  await useFetch('/api/user/me', {
    server: false,
    baseURL: 'https://app.atlasvip.cloud'
  })

// Test modern API composable
const { useModernFetch } = await import('~/composables/useModernApi')
const { data: modernHealthData, pending: modernHealthPending, error: modernHealthError, refresh: refreshModernHealth } = 
  useModernFetch('/health', {
    key: 'modern-health-test',
    baseURL: 'https://app.atlasvip.cloud/api'
  })

// Get runtime config
const config = useRuntimeConfig()

// Set page meta
definePageMeta({
  title: 'Backend Connectivity Test',
  description: 'Test page to verify backend connectivity and modern API patterns'
})
</script>

<style scoped>
/* Test page specific styles */
</style>
