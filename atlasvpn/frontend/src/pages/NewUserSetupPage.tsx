import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { api } from '../utils/apiClient';
import LoadingSpinner from '../components/LoadingSpinner';
import { TypeAnimation } from 'react-type-animation';
import { haptics } from '../utils/haptics';
import Avatar3DViewer from '../components/Avatar3DViewer';
import AvatarSelector from '../components/AvatarSelector';
import { initTelegramWebApp } from '../utils/telegram';
import { secureStorage } from '../utils/secureStorage';
import { sessionData } from '../utils/storage';
import { secureTelegramStorage } from '../utils/secureTelegramStorage';
import { useLoginMutation } from '../hooks/authHooks';
import { TelegramData } from '../types';
import { toast } from 'react-hot-toast';

interface FormData {
  username: string;
  email: string;
  telegram_photo_url: string;
  first_name?: string;
  last_name?: string;
  telegram_id?: string;
}

interface VerifiedData {
  telegramData: TelegramData;
  timestamp: number;
}

const STEPS = {
  WELCOME: 'welcome',
  GUARDIAN: 'guardian',
  SETUP: 'setup',
  COMPLETING: 'completing',
  SUCCESS: 'success',
};

const NewUserSetupPage = () => {
  const [currentStep, setCurrentStep] = useState(STEPS.WELCOME);
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    telegram_photo_url: 'Lovely Angel',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [verifiedData, setVerifiedData] = useState<VerifiedData | null>(null);
  const [usernameStatus, setUsernameStatus] = useState({ isValid: false, message: '' });
  const [userCredentials, setUserCredentials] = useState<{
    username: string;
    temp_password: string;
    message?: string;
  } | null>(null);
  const navigate = useNavigate();
  const loginMutation = useLoginMutation();

  // Simple initialization - only get basic Telegram data when needed
  useEffect(() => {
    if (window.Telegram?.WebApp?.initData) {
      // Create basic verified data from Telegram WebApp
      const webAppData = window.Telegram.WebApp.initDataUnsafe;
      if (webAppData?.user) {
        setVerifiedData({
          telegramData: {
            telegram_id: String(webAppData.user.id),
            username: webAppData.user.username,
            first_name: webAppData.user.first_name,
            last_name: webAppData.user.last_name,
            photo_url: undefined, // Will be set from avatar selector
          },
          timestamp: Date.now(),
        });

        // Set basic form data
        setFormData(prev => ({
          ...prev,
          username: webAppData.user.username?.toLowerCase() || '',
          first_name: webAppData.user.first_name || '',
          last_name: webAppData.user.last_name || '',
        }));
      }
    }
  }, []);

  // Check username availability
  useEffect(() => {
    const checkUsername = async () => {
      if (!formData.username || formData.username.length < 3) {
        setUsernameStatus({ isValid: false, message: 'Username must be at least 3 characters' });
        return;
      }

      try {
        console.log('[Setup Debug] Checking username availability:', formData.username);
        const response = await api.get<{ exists: boolean }>(`/auth/check-username/${formData.username}`);
        console.log('[Setup Debug] Username check response:', response.data);
        setUsernameStatus({
          isValid: !response.data.exists,
          message: response.data.exists ? 'Username is taken' : 'Username is available',
        });
      } catch (error) {
        console.error('[Setup Debug] Error checking username:', error);
        setUsernameStatus({ isValid: false, message: 'Error checking username' });
      }
    };

    const debounceTimer = setTimeout(checkUsername, 500);
    return () => clearTimeout(debounceTimer);
  }, [formData.username]);

  const handleNext = () => {
    if (currentStep === STEPS.WELCOME) {
      setCurrentStep(STEPS.GUARDIAN);
    } else if (currentStep === STEPS.GUARDIAN) {
      setCurrentStep(STEPS.SETUP);
    }
  };

  const handleBack = () => {
    if (currentStep === STEPS.SETUP) {
      setCurrentStep(STEPS.GUARDIAN);
    } else if (currentStep === STEPS.GUARDIAN) {
      setCurrentStep(STEPS.WELCOME);
    }
  };

  const handleSubmit = async () => {
    if (!verifiedData?.telegramData) {
      console.error('[Setup Debug] No verified data available for submission');
      return;
    }

    try {
      console.log('[Setup Debug] Starting user setup submission');
      setIsLoading(true);
      setError(null);
      setCurrentStep(STEPS.COMPLETING);

      const WebApp = window.Telegram?.WebApp;
      if (!WebApp?.initData) {
        console.error('[Setup Debug] No Telegram WebApp data available');
        throw new Error('No Telegram WebApp data available');
      }

      // Get referral code from secure storage if exists
      const referralCode = await secureTelegramStorage.getReferralCode();
      console.log('[Setup Debug] Referral code:', referralCode);

      // Create submission data with all necessary information
      const submissionData = {
        init_data: WebApp.initData,
        username: formData.username,
        selected_avatar: formData.telegram_photo_url,
        referral_code: referralCode || undefined,
      };

      console.log('[Setup Debug] Submitting registration data:', submissionData);

      // Use telegramLogin instead of register for new user registration
      const response = await api.auth.telegramLogin({
        init_data: submissionData.init_data,
        auth_method: 'telegram'
      });

      console.log('[NewUserSetup] Registration successful:', response);

      if (response.data && !response.error) {
        toast.success('Registration successful! Welcome to VIPVerse!');
        
        // Navigate to dashboard after successful registration
        setTimeout(() => {
          navigate('/dashboard', { replace: true });
        }, 1000);
      } else {
        throw new Error(response.message || response.error || 'Registration failed');
      }
    } catch (err: any) {
      console.error('[Setup Debug] Setup error:', err);
      const errorMessage =
        err.response?.data?.detail?.message ||
        err.response?.data?.detail ||
        err.message ||
        'Setup failed';
      setError(errorMessage);
      setCurrentStep(STEPS.SETUP);
      haptics.notification('error');
      const tg = window.Telegram?.WebApp;
      if (tg?.showAlert) {
        tg.showAlert('Failed to create account: ' + errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleContinueToDashboard = async () => {
    try {
      console.log('[Setup Debug] Attempting to continue to dashboard');
      if (!userCredentials) {
        console.error('[Setup Debug] No user credentials available');
        throw new Error('No user credentials available');
      }

      console.log('[Setup Debug] Logging in with credentials');
      await loginMutation.mutateAsync({
        username: userCredentials.username,
        password: userCredentials.temp_password,
      });

      console.log('[Setup Debug] Login successful, navigating to dashboard');
      haptics.notification('success');
      navigate('/dashboard', { replace: true });
    } catch (error) {
      console.error('[Setup Debug] Navigation error:', error);
      const tg = window.Telegram?.WebApp;
      if (tg?.showAlert) {
        tg.showAlert('Error accessing dashboard. Please try logging in again.');
      }
    }
  };

  const validateEmail = (email: string): boolean => {
    return !email || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };

  if (!verifiedData?.telegramData) {
    return null;
  }

  return (
    <div className="flex-1 flex items-center justify-center p-4 bg-transparent">
      <div className="relative z-10">
        <AnimatePresence mode="wait">
          {currentStep === STEPS.WELCOME && (
            <WelcomeStep telegramData={verifiedData.telegramData} onNext={handleNext} />
          )}

          {currentStep === STEPS.GUARDIAN && (
            <GuardianStep
              formData={formData}
              setFormData={setFormData}
              onNext={handleNext}
              onBack={handleBack}
            />
          )}

          {currentStep === STEPS.SETUP && (
            <SetupStep
              formData={formData}
              setFormData={setFormData}
              usernameStatus={usernameStatus}
              onNext={handleSubmit}
              onBack={handleBack}
              error={error}
              isValid={usernameStatus.isValid && validateEmail(formData.email)}
            />
          )}

          {currentStep === STEPS.COMPLETING && <LoadingStep />}

          {currentStep === STEPS.SUCCESS && userCredentials && (
            <SuccessStep credentials={userCredentials} onContinue={handleContinueToDashboard} />
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

const WelcomeStep = ({
  telegramData,
  onNext,
}: {
  telegramData: TelegramData;
  onNext: () => void;
}) => {
  const [messages, setMessages] = useState<string[]>([]);
  const [isComplete, setIsComplete] = useState(false);

  useEffect(() => {
    const welcomeMessages = [
      'SECURITY ALERT...',
      `Unauthorized access detected: ${telegramData.first_name}`,
      'Initiating secure protocol...',
      'Analyzing connection...',
      'Establishing encrypted channel...',
      `Welcome to the resistance, ${telegramData.first_name}`,
    ];

    let currentIndex = 0;
    const messageInterval = setInterval(() => {
      if (currentIndex < welcomeMessages.length) {
        setMessages(prev => [...prev, welcomeMessages[currentIndex]]);
        currentIndex++;
      } else {
        clearInterval(messageInterval);
        setIsComplete(true);
      }
    }, 800); // Show a new message every 800ms

    return () => clearInterval(messageInterval);
  }, [telegramData.first_name]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="max-w-md w-full space-y-8 bg-purple-900/30 p-8 rounded-2xl backdrop-blur-lg border border-purple-500/20"
    >
      <div className="text-left space-y-2">
        {messages.map((message, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="font-mono text-sm text-purple-200"
          >
            <span className="text-purple-400">{'>>'}</span> {message}
          </motion.div>
        ))}

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: isComplete ? 1 : 0 }}
          transition={{ duration: 0.5 }}
          className="pt-6"
        >
          <motion.button
            onClick={onNext}
            className="w-full py-3 px-4 rounded-xl bg-gradient-to-r from-purple-600 to-purple-400
                     text-white font-medium relative overflow-hidden group disabled:opacity-50
                     disabled:cursor-not-allowed"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            disabled={!isComplete}
          >
            <span className="relative z-10">{'>'} Begin Secure Setup</span>
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-purple-500 to-purple-300 opacity-0 group-hover:opacity-100 transition-opacity"
              initial={false}
              exit={{ opacity: 0 }}
            />
          </motion.button>
        </motion.div>
      </div>
    </motion.div>
  );
};

const GuardianStep = ({
  formData,
  setFormData,
  onNext,
  onBack,
}: {
  formData: FormData;
  setFormData: (data: FormData) => void;
  onNext: () => void;
  onBack: () => void;
}) => {
  const [showAvatarSelector, setShowAvatarSelector] = useState(false);
  const [messages] = useState([
    'INITIALIZING PRIVACY PROTOCOL...',
    'CONFIGURING SECURE ENVIRONMENT...',
    'SYSTEM: Privacy is our core mission',
    '> Zero logs policy',
    '> End-to-end encryption',
    '> Transparent security',
    'SELECT YOUR GUARDIAN...',
  ]);

  return (
    <motion.div
      initial={{ opacity: 0, x: 100 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -100 }}
      className="max-w-md w-full bg-purple-900/10 backdrop-blur-xl p-6 rounded-2xl border border-purple-500/20 shadow-2xl"
    >
      <div className="space-y-3">
        {messages.map((message, index) => (
          <motion.p
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.15, duration: 0.2 }}
            className={`text-sm ${
              message.startsWith('SYSTEM:')
                ? 'text-green-400'
                : message.startsWith('>')
                  ? 'text-purple-300 pl-4 font-mono text-xs'
                  : 'text-purple-200 font-mono'
            }`}
          >
            {message.startsWith('SYSTEM:') ? message.replace('SYSTEM:', '>>') : message}
          </motion.p>
        ))}

        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 1.2 }}
          className="relative group cursor-pointer mt-4"
          onClick={() => setShowAvatarSelector(true)}
        >
          <div
            className="rounded-full overflow-hidden bg-purple-800/20 ring-2 ring-purple-500/20 hover:ring-purple-500/40 transition-all mx-auto"
            style={{ width: '120px', height: '120px' }}
          >
            <Avatar3DViewer
              filename={`${formData.telegram_photo_url}.webp`}
              size={{ width: 120, height: 120 }}
              className="transform hover:scale-105 transition-all"
            />
          </div>
          <div className="mt-2 text-center">
            <p className="text-sm font-medium text-purple-200">Your Guardian</p>
            <p className="text-xs text-purple-400 font-mono">{formData.telegram_photo_url}</p>
          </div>
        </motion.div>

        <div className="flex space-x-3 mt-4">
          <motion.button
            onClick={onBack}
            className="flex-1 py-2 px-4 rounded-lg bg-purple-900/30 text-purple-200 text-sm font-medium border border-purple-500/20"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            {'<'} Back
          </motion.button>
          <motion.button
            onClick={onNext}
            className="flex-1 py-2 px-4 rounded-lg bg-gradient-to-r from-purple-600/80 to-purple-400/80 backdrop-blur-xl
                     text-white text-sm font-medium border border-purple-400/20"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            Continue {'>'}
          </motion.button>
        </div>
      </div>

      {/* Avatar Selector Modal */}
      <AnimatePresence>
        {showAvatarSelector && (
          <motion.div
            initial={{ opacity: 0, y: '100%' }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed inset-0 z-[9999]"
          >
            <div className="absolute inset-0 bg-black/90 backdrop-blur-sm" />
            <motion.div
              initial={{ y: '100%' }}
              animate={{ y: 0 }}
              exit={{ y: '100%' }}
              className="relative h-full"
            >
              <AvatarSelector
                currentAvatar={`${formData.telegram_photo_url}.webp`}
                onSelect={avatarName => {
                  const cleanName = avatarName.replace('.webp', '');
                  setFormData({ ...formData, telegram_photo_url: cleanName });
                  setShowAvatarSelector(false);
                }}
                onClose={() => setShowAvatarSelector(false)}
                isLoading={false}
              />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

const SetupStep = ({
  formData,
  setFormData,
  usernameStatus,
  onNext,
  onBack,
  error,
  isValid,
}: {
  formData: FormData;
  setFormData: (data: FormData) => void;
  usernameStatus: { isValid: boolean; message: string };
  onNext: () => void;
  onBack: () => void;
  error: string | null;
  isValid: boolean;
}) => (
  <motion.div
    initial={{ opacity: 0, x: 100 }}
    animate={{ opacity: 1, x: 0 }}
    exit={{ opacity: 0, x: -100 }}
    className="max-w-md w-full bg-purple-900/10 backdrop-blur-xl p-6 rounded-2xl border border-purple-500/20 shadow-2xl"
  >
    <div className="space-y-4">
      <div className="text-center mb-4">
        <div
          className="rounded-full overflow-hidden bg-purple-800/20 ring-2 ring-purple-500/20 mx-auto"
          style={{ width: '80px', height: '80px' }}
        >
          <Avatar3DViewer
            filename={`${formData.telegram_photo_url}.webp`}
            size={{ width: 80, height: 80 }}
            className="transform hover:scale-105 transition-all"
          />
        </div>
        <p className="mt-2 text-purple-200 text-sm font-mono">
          {'>>'} Guardian activated: {formData.telegram_photo_url}
        </p>
      </div>

      <div>
        <label className="block text-xs font-mono text-purple-200/90 mb-1">
          {'>>'} System username required:
        </label>
        <input
          type="text"
          value={formData.username}
          onChange={e => setFormData({ ...formData, username: e.target.value.toLowerCase() })}
          className="w-full px-3 py-2 rounded-lg bg-purple-900/20 border border-purple-500/20 
                   text-purple-100 placeholder-purple-300/30 focus:border-purple-400 text-sm font-mono"
          placeholder="Enter username"
        />
        {usernameStatus.message && (
          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className={`mt-1 text-xs font-mono ${
              usernameStatus.isValid ? 'text-green-400' : 'text-red-400'
            }`}
          >
            {'>>'} {usernameStatus.message}
          </motion.p>
        )}
      </div>

      <div>
        <label className="block text-xs font-mono text-purple-200/90 mb-1">
          {'>>'} Secure email required:
        </label>
        <input
          type="email"
          value={formData.email}
          onChange={e => setFormData({ ...formData, email: e.target.value })}
          className="w-full px-3 py-2 rounded-lg bg-purple-900/20 border border-purple-500/20 
                   text-purple-100 placeholder-purple-300/30 focus:border-purple-400 text-sm font-mono"
          placeholder="Enter email"
          required
        />
        <p className="mt-1 text-xs font-mono text-purple-400">
          {'>>'} Required for account recovery and security alerts
        </p>
      </div>

      {error && (
        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="text-red-400 text-xs font-mono"
        >
          {'>>'} Error: {error}
        </motion.p>
      )}

      <div className="flex space-x-3 pt-2">
        <motion.button
          onClick={onBack}
          className="flex-1 py-2 px-4 rounded-lg bg-purple-900/30 text-purple-200 text-sm font-medium border border-purple-500/20"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          {'<'} Back
        </motion.button>
        <motion.button
          onClick={onNext}
          disabled={!isValid}
          className={`flex-1 py-2 px-4 rounded-lg ${
            isValid
              ? 'bg-gradient-to-r from-purple-600/80 to-purple-400/80 backdrop-blur-xl border border-purple-400/20'
              : 'bg-purple-900/30 border border-purple-500/20 cursor-not-allowed'
          } text-white text-sm font-medium`}
          whileHover={isValid ? { scale: 1.02 } : {}}
          whileTap={isValid ? { scale: 0.98 } : {}}
        >
          Complete Setup {'>'}
        </motion.button>
      </div>
    </div>
  </motion.div>
);

const LoadingStep = () => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    className="max-w-md w-full space-y-8 bg-purple-900/30 p-8 rounded-2xl backdrop-blur-lg"
  >
    <div className="text-center space-y-4">
      <LoadingSpinner size="lg" />
      <motion.p
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-purple-100 text-lg font-medium"
      >
        {'>'} Creating your secure account...
      </motion.p>
    </div>
  </motion.div>
);

const SuccessStep = ({
  credentials,
  onContinue,
}: {
  credentials: {
    username: string;
    temp_password: string;
    message?: string;
  };
  onContinue: () => void;
}) => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    exit={{ opacity: 0 }}
    className="max-w-md w-full space-y-8 bg-purple-900/30 p-8 rounded-2xl backdrop-blur-lg border border-purple-500/20"
  >
    <div className="text-center space-y-6">
      <motion.div
        initial={{ scale: 0.5 }}
        animate={{ scale: 1 }}
        className="w-20 h-20 bg-green-500/20 rounded-full mx-auto flex items-center justify-center"
      >
        <span className="text-4xl">✓</span>
      </motion.div>

      <h2 className="text-2xl font-bold text-purple-200">Account Created Successfully!</h2>

      <div className="space-y-4 text-left bg-purple-950/50 p-4 rounded-xl">
        <div>
          <p className="text-sm text-purple-300">Username:</p>
          <p className="font-mono text-purple-100">{credentials.username}</p>
        </div>
        <div>
          <p className="text-sm text-purple-300">Temporary Password:</p>
          <p className="font-mono text-purple-100 break-all">{credentials.temp_password}</p>
        </div>
      </div>

      <p className="text-sm text-purple-300">
        Please save these credentials securely. You'll need them to access your account outside of
        Telegram.
      </p>

      <motion.button
        onClick={onContinue}
        className="w-full py-3 px-4 rounded-xl bg-gradient-to-r from-purple-600 to-purple-400
                 text-white font-medium relative overflow-hidden group"
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <span className="relative z-10">Continue to Dashboard {'>'}</span>
      </motion.button>
    </div>
  </motion.div>
);

export default NewUserSetupPage;
