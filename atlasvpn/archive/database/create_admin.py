from database import SessionLocal
from models import User, Role
from auth import pwd_context

def create_admin_user():
    username = "admin123"
    password = "@admin123"
    db = SessionLocal()
    try:
        existing_user = db.query(User).filter(User.username == username).first()
        if existing_user:
            print(f"User {username} already exists")
            return
        hashed_password = pwd_context.hash(password)
        admin_user = User(
            username=username, 
            hashed_password=hashed_password, 
            role=Role.admin, 
            is_active=True, 
            referral_code="ADMIN123"
        )
        db.add(admin_user)
        db.commit()
        print(f"Admin user {username} created successfully")
    except Exception as e:
        db.rollback()
        print(f"Error creating admin user: {str(e)}")
    finally:
        db.close()

if __name__ == "__main__":
    create_admin_user()
