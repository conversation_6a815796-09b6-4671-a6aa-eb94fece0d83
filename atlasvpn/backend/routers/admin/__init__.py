"""
Admin routers package - Organized admin functionality

This package contains all admin-related routers organized by domain:
- users: User management and analytics
- vpn: VPN packages, panels, and subscriptions
- tasks: Task management, completion verification, and task packs (keeping original /admin/tasks/ routes)
- cards: Card catalog and user card management
- chat: Chat monitoring and messaging
- system: System configuration, security, and dashboard
- daily_tasks: Daily task streaks and cycles
- referrals: Referral system management and analytics

Each router is focused on a specific domain and includes:
- CRUD operations for the domain entities
- Analytics and reporting endpoints
- Admin-specific management features
- Comprehensive error handling and logging
"""

from fastapi import APIRouter
from . import users, vpn, tasks, cards, chat, system, daily_tasks, referrals

# Create main admin router
admin_router = APIRouter(prefix="/admin", tags=["Admin"])

# Include all sub-routers
admin_router.include_router(users.router)
admin_router.include_router(vpn.router)
admin_router.include_router(tasks.router)
admin_router.include_router(cards.router)
admin_router.include_router(chat.router)
admin_router.include_router(system.router)
admin_router.include_router(daily_tasks.router)
admin_router.include_router(referrals.router)

# Export individual routers for flexibility
__all__ = [
    "admin_router",
    "users",
    "vpn", 
    "tasks",
    "cards",
    "chat",
    "system",
    "daily_tasks",
    "referrals"
] 