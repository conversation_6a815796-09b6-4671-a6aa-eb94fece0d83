"""
Admin Referrals Router

Handles all referral system management functionality including:
- Referral analytics and reporting
- Referral tree visualization  
- Commission management
- Referral system statistics
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging

from auth import get_current_admin
from database import get_async_db
from models import User, Transaction, TransactionType
from schemas import UserOut

# Configure logging
logger = logging.getLogger(__name__)

# Create router with admin prefix
router = APIRouter(
    prefix="/referrals",
    tags=["Admin - Referrals"],
    dependencies=[Depends(get_current_admin)]
)


@router.get("/", response_model=List[Dict[str, Any]])
async def get_admin_referrals(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db),
    skip: int = 0,
    limit: int = 100,
    has_referrals_only: bool = False
):
    """
    Get comprehensive referral data for admin dashboard
    
    Features:
    - Paginated referral data
    - Direct referral counts
    - Commission earnings per user
    - Filter by users with referrals only
    """
    try:
        # Build base query for users
        query = select(User).options(
            selectinload(User.referrals)
        ).order_by(User.created_at.desc())
        
        # Apply filters
        if has_referrals_only:
            # Only show users who have referred others
            subquery = select(User.id).where(User.referred_by.isnot(None))
            query = query.where(User.id.in_(subquery))
        
        # Apply pagination
        query = query.offset(skip).limit(limit)
        
        # Execute query
        result = await db.execute(query)
        users = result.scalars().all()
        
        referral_data = []
        
        for user in users:
            # Get referrer information if user was referred
            referrer = None
            if user.referred_by:
                stmt_referrer = select(User).where(User.id == user.referred_by)
                result_referrer = await db.execute(stmt_referrer)
                referrer = result_referrer.scalar_one_or_none()
            
            # Count direct referrals
            stmt_direct = select(func.count(User.id)).where(User.referred_by == user.id)
            result_direct = await db.execute(stmt_direct)
            direct_referrals = result_direct.scalar_one()
            
            # Calculate total commissions earned by this user
            stmt_commissions = select(func.sum(Transaction.amount)).where(
                Transaction.user_id == user.id,
                Transaction.type == TransactionType.referral_commission
            )
            result_commissions = await db.execute(stmt_commissions)
            total_commissions = result_commissions.scalar_one() or 0
            
            user_data = {
                "id": user.id,
                "username": user.username,
                "email": user.email,
                "referral_code": user.referral_code,
                "referred_by_id": user.referred_by,
                "referred_by_username": referrer.username if referrer else None,
                "direct_referrals": direct_referrals,
                "total_commissions_earned": float(total_commissions),
                "created_at": user.created_at.isoformat(),
                "last_login": user.last_login.isoformat() if user.last_login else None,
                "is_active": user.is_active,
                "wallet_balance": float(user.wallet_balance)
            }
            
            referral_data.append(user_data)
        
        logger.info(f"Retrieved {len(referral_data)} referral records (admin: {current_user.username})")
        return referral_data
        
    except Exception as e:
        logger.error(f"Error getting admin referrals: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get referral data"
        )


@router.get("/{user_id}/tree", response_model=Dict[str, Any])
async def get_referral_tree(
    user_id: int,
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db),
    include_second_level: bool = True
):
    """
    Get referral tree for a specific user showing their referral network
    
    Features:
    - Hierarchical referral visualization
    - Multi-level referral tracking
    - User activity status
    - Commission tracking per level
    """
    try:
        # Get the root user
        stmt_root = select(User).where(User.id == user_id)
        result_root = await db.execute(stmt_root)
        root_user = result_root.scalar_one_or_none()
        
        if not root_user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # Get all direct referrals
        stmt_referrals = select(User).where(
            User.referred_by == user_id
        ).order_by(User.created_at.desc())
        result_referrals = await db.execute(stmt_referrals)
        direct_referrals = result_referrals.scalars().all()
        
        # Calculate total commissions for root user
        stmt_root_commissions = select(func.sum(Transaction.amount)).where(
            Transaction.user_id == user_id,
            Transaction.type == TransactionType.referral_commission
        )
        result_root_commissions = await db.execute(stmt_root_commissions)
        root_commissions = result_root_commissions.scalar_one() or 0
        
        referral_tree = {
            "user_id": root_user.id,
            "username": root_user.username,
            "email": root_user.email,
            "referral_code": root_user.referral_code,
            "total_commissions_earned": float(root_commissions),
            "wallet_balance": float(root_user.wallet_balance),
            "is_active": root_user.is_active,
            "created_at": root_user.created_at.isoformat(),
            "direct_referrals_count": len(direct_referrals),
            "direct_referrals": []
        }
        
        total_second_level = 0
        
        for referral in direct_referrals:
            # Get commissions for this referral
            stmt_ref_commissions = select(func.sum(Transaction.amount)).where(
                Transaction.user_id == referral.id,
                Transaction.type == TransactionType.referral_commission
            )
            result_ref_commissions = await db.execute(stmt_ref_commissions)
            ref_commissions = result_ref_commissions.scalar_one() or 0
            
            referral_data = {
                "user_id": referral.id,
                "username": referral.username,
                "email": referral.email,
                "referral_code": referral.referral_code,
                "created_at": referral.created_at.isoformat(),
                "is_active": referral.is_active,
                "wallet_balance": float(referral.wallet_balance),
                "commissions_earned": float(ref_commissions),
                "second_level_referrals": []
            }
            
            if include_second_level:
                # Get referral's referrals (second level)
                stmt_second_level = select(User).where(
                    User.referred_by == referral.id
                ).order_by(User.created_at.desc())
                result_second_level = await db.execute(stmt_second_level)
                second_level_referrals = result_second_level.scalars().all()
                
                total_second_level += len(second_level_referrals)
                
                for second_ref in second_level_referrals:
                    # Get commissions for second level referral
                    stmt_second_commissions = select(func.sum(Transaction.amount)).where(
                        Transaction.user_id == second_ref.id,
                        Transaction.type == TransactionType.referral_commission
                    )
                    result_second_commissions = await db.execute(stmt_second_commissions)
                    second_commissions = result_second_commissions.scalar_one() or 0
                    
                    second_ref_data = {
                        "user_id": second_ref.id,
                        "username": second_ref.username,
                        "email": second_ref.email,
                        "created_at": second_ref.created_at.isoformat(),
                        "is_active": second_ref.is_active,
                        "wallet_balance": float(second_ref.wallet_balance),
                        "commissions_earned": float(second_commissions)
                    }
                    referral_data["second_level_referrals"].append(second_ref_data)
                
                referral_data["second_level_count"] = len(second_level_referrals)
            
            referral_tree["direct_referrals"].append(referral_data)
        
        referral_tree["total_second_level_count"] = total_second_level
        
        logger.info(f"Retrieved referral tree for user {user_id} (admin: {current_user.username})")
        return referral_tree
        
    except Exception as e:
        logger.error(f"Error getting referral tree: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get referral tree"
        )


@router.get("/stats", response_model=Dict[str, Any])
async def get_referral_stats(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db),
    days: int = 30
):
    """
    Get comprehensive referral system statistics
    
    Features:
    - Overall referral penetration metrics
    - Top performing referrers
    - Commission distribution analytics
    - Trend analysis over time period
    """
    try:
        # Total users
        stmt_total_users = select(func.count(User.id))
        result_total_users = await db.execute(stmt_total_users)
        total_users = result_total_users.scalar_one()
        
        # Users with referrals (users who were referred by someone)
        stmt_users_with_referrals = select(func.count(User.id)).where(
            User.referred_by.isnot(None)
        )
        result_users_with_referrals = await db.execute(stmt_users_with_referrals)
        users_with_referrals = result_users_with_referrals.scalar_one()
        
        # Users who have referred others
        stmt_active_referrers = select(func.count(func.distinct(User.referred_by))).where(
            User.referred_by.isnot(None)
        )
        result_active_referrers = await db.execute(stmt_active_referrers)
        active_referrers = result_active_referrers.scalar_one()
        
        # Total referral commissions paid
        stmt_total_commissions = select(func.sum(Transaction.amount)).where(
            Transaction.type == TransactionType.referral_commission
        )
        result_total_commissions = await db.execute(stmt_total_commissions)
        total_commissions = result_total_commissions.scalar_one() or 0
        
        # Top referrers (users with most direct referrals)
        stmt_top_referrers = select(
            User.id,
            User.username,
            User.email,
            func.count(User.referrals).label('referral_count')
        ).join(
            User.referrals, isouter=True
        ).group_by(
            User.id, User.username, User.email
        ).having(
            func.count(User.referrals) > 0
        ).order_by(
            func.count(User.referrals).desc()
        ).limit(10)
        
        result_top_referrers = await db.execute(stmt_top_referrers)
        top_referrers_raw = result_top_referrers.all()
        
        # Get commission data for top referrers
        top_referrers = []
        for user_id, username, email, referral_count in top_referrers_raw:
            stmt_user_commissions = select(func.sum(Transaction.amount)).where(
                Transaction.user_id == user_id,
                Transaction.type == TransactionType.referral_commission
            )
            result_user_commissions = await db.execute(stmt_user_commissions)
            user_commissions = result_user_commissions.scalar_one() or 0
            
            top_referrers.append({
                "user_id": user_id,
                "username": username,
                "email": email,
                "referral_count": referral_count,
                "total_commissions": float(user_commissions)
            })
        
        # Recent referral activity (last 30 days)
        from datetime import datetime, timedelta
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        stmt_recent_referrals = select(func.count(User.id)).where(
            User.referred_by.isnot(None),
            User.created_at >= cutoff_date
        )
        result_recent_referrals = await db.execute(stmt_recent_referrals)
        recent_referrals = result_recent_referrals.scalar_one()
        
        # Recent commissions paid
        stmt_recent_commissions = select(func.sum(Transaction.amount)).where(
            Transaction.type == TransactionType.referral_commission,
            Transaction.created_at >= cutoff_date
        )
        result_recent_commissions = await db.execute(stmt_recent_commissions)
        recent_commissions = result_recent_commissions.scalar_one() or 0
        
        # Calculate rates and averages
        referral_penetration_rate = (users_with_referrals / total_users * 100) if total_users > 0 else 0
        active_referrer_rate = (active_referrers / total_users * 100) if total_users > 0 else 0
        avg_commission_per_referral = (total_commissions / users_with_referrals) if users_with_referrals > 0 else 0
        avg_referrals_per_referrer = (users_with_referrals / active_referrers) if active_referrers > 0 else 0
        
        stats = {
            "overview": {
                "total_users": total_users,
                "users_with_referrals": users_with_referrals,
                "active_referrers": active_referrers,
                "referral_penetration_rate": round(referral_penetration_rate, 2),
                "active_referrer_rate": round(active_referrer_rate, 2)
            },
            "financial": {
                "total_commissions_paid": float(total_commissions),
                "avg_commission_per_referral": round(float(avg_commission_per_referral), 2),
                "recent_commissions_paid": float(recent_commissions)
            },
            "performance": {
                "avg_referrals_per_referrer": round(avg_referrals_per_referrer, 2),
                "recent_referrals": recent_referrals,
                "top_referrers": top_referrers
            },
            "period_info": {
                "analysis_period_days": days,
                "analysis_start_date": cutoff_date.isoformat(),
                "generated_at": datetime.utcnow().isoformat()
            }
        }
        
        logger.info(f"Generated referral statistics (admin: {current_user.username})")
        return stats
        
    except Exception as e:
        logger.error(f"Error getting referral stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get referral statistics"
        )


@router.post("/{user_id}/commission", response_model=Dict[str, Any])
async def grant_referral_commission(
    user_id: int,
    commission_data: Dict[str, Any],
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Manually grant referral commission to a user
    
    Features:
    - Manual commission adjustments
    - Detailed audit trail
    - Balance updates with transaction records
    - Admin action logging
    """
    try:
        # Validate commission data
        if "amount" not in commission_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Commission amount is required"
            )
        
        commission_amount = float(commission_data["amount"])
        if commission_amount <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Commission amount must be positive"
            )
        
        # Get user
        stmt_user = select(User).where(User.id == user_id)
        result_user = await db.execute(stmt_user)
        user = result_user.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        description = commission_data.get(
            "description", 
            f"Manual referral commission granted by admin {current_user.username}"
        )
        
        # Update user balance
        old_balance = user.wallet_balance
        user.wallet_balance += commission_amount
        
        # Create transaction record
        transaction = Transaction(
            user_id=user_id,
            type=TransactionType.referral_commission,
            amount=commission_amount,
            description=description,
            balance_after=user.wallet_balance,
            status="completed",
            admin_user_id=current_user.id  # Track which admin granted this
        )
        
        db.add(transaction)
        await db.commit()
        await db.refresh(transaction)
        
        logger.info(
            f"Manual referral commission granted: ${commission_amount} to user {user_id} "
            f"by admin {current_user.username} (transaction: {transaction.id})"
        )
        
        return {
            "success": True,
            "message": "Referral commission granted successfully",
            "commission_details": {
                "user_id": user_id,
                "username": user.username,
                "amount": commission_amount,
                "description": description,
                "old_balance": float(old_balance),
                "new_balance": float(user.wallet_balance),
                "transaction_id": transaction.id,
                "granted_by": current_user.username,
                "granted_at": transaction.created_at.isoformat()
            }
        }
        
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid commission amount format"
        )
    except Exception as e:
        logger.error(f"Error granting referral commission: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to grant referral commission: {str(e)}"
        )


@router.get("/analytics/trends", response_model=Dict[str, Any])
async def get_referral_trends(
    current_user: User = Depends(get_current_admin),
    db: AsyncSession = Depends(get_async_db),
    days: int = 30
):
    """
    Get referral system trend analysis over time
    
    Features:
    - Daily referral sign-up trends
    - Commission payment trends
    - Growth rate analysis
    - Comparative metrics
    """
    try:
        from datetime import datetime, timedelta
        
        # Generate date range for analysis
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Daily referral sign-ups
        stmt_daily_referrals = select(
            func.date(User.created_at).label('date'),
            func.count(User.id).label('count')
        ).where(
            User.referred_by.isnot(None),
            User.created_at >= start_date
        ).group_by(
            func.date(User.created_at)
        ).order_by(
            func.date(User.created_at)
        )
        
        result_daily_referrals = await db.execute(stmt_daily_referrals)
        daily_referrals = result_daily_referrals.all()
        
        # Daily commission payments
        stmt_daily_commissions = select(
            func.date(Transaction.created_at).label('date'),
            func.sum(Transaction.amount).label('total'),
            func.count(Transaction.id).label('count')
        ).where(
            Transaction.type == TransactionType.referral_commission,
            Transaction.created_at >= start_date
        ).group_by(
            func.date(Transaction.created_at)
        ).order_by(
            func.date(Transaction.created_at)
        )
        
        result_daily_commissions = await db.execute(stmt_daily_commissions)
        daily_commissions = result_daily_commissions.all()
        
        # Format trend data
        referral_trends = [
            {
                "date": date.isoformat(),
                "referral_count": count
            }
            for date, count in daily_referrals
        ]
        
        commission_trends = [
            {
                "date": date.isoformat(),
                "total_amount": float(total),
                "transaction_count": count
            }
            for date, total, count in daily_commissions
        ]
        
        # Calculate summary metrics
        total_period_referrals = sum(item["referral_count"] for item in referral_trends)
        total_period_commissions = sum(item["total_amount"] for item in commission_trends)
        avg_daily_referrals = total_period_referrals / days if days > 0 else 0
        avg_daily_commissions = total_period_commissions / days if days > 0 else 0
        
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "days": days
            },
            "trends": {
                "daily_referrals": referral_trends,
                "daily_commissions": commission_trends
            },
            "summary": {
                "total_referrals": total_period_referrals,
                "total_commissions": total_period_commissions,
                "avg_daily_referrals": round(avg_daily_referrals, 2),
                "avg_daily_commissions": round(avg_daily_commissions, 2)
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting referral trends: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get referral trends"
        ) 