---
/**
 * @file dashboard/earn.astro
 * @description Dashboard earn page - tasks and card management
 */

import DashboardLayout from '../../layouts/DashboardLayout.astro';
import QueryClientWrapper from '../../components/react/QueryClientWrapper';
import EarnTabContent from '../../components/react/EarnTabContent';

// Page metadata
const title = "VIPVerse Dashboard - Earn";
const description = "Complete tasks and manage cards to earn rewards";
---

<DashboardLayout title={title} description={description} activeTab="earn">
  <!-- Earn Tab Content (React Island with Query Client) -->
  <QueryClientWrapper client:load>
    <EarnTabContent />
  </QueryClientWrapper>
</DashboardLayout>
