import { TelegramVerifiedData, UserPreferences, isTelegramVerifiedData } from '../types/storage';
import { encryptedSessionStorage, encryptedLocalStorage } from './encryptedStorage';

export const sessionData = {
  setTelegramData: async (data: TelegramVerifiedData) => {
    await encryptedSessionStorage.setItem('telegram_verified_data', data, 24); // 24 hours expiration
  },

  getTelegramData: async (): Promise<TelegramVerifiedData | null> => {
    try {
      const data =
        await encryptedSessionStorage.getItem<TelegramVerifiedData>('telegram_verified_data');
      return data && isTelegramVerifiedData(data) ? data : null;
    } catch {
      return null;
    }
  },

  clearTelegramData: () => {
    encryptedSessionStorage.removeItem('telegram_verified_data');
  },
};

export const userPreferences = {
  setTheme: async (theme: UserPreferences['theme']) => {
    await encryptedLocalStorage.setItem('user_theme', theme);
  },

  getTheme: async (): Promise<UserPreferences['theme']> => {
    const theme = await encryptedLocalStorage.getItem<UserPreferences['theme']>('user_theme');
    return theme || 'dark';
  },

  setLanguage: async (lang: string) => {
    await encryptedLocalStorage.setItem('user_language', lang);
  },

  getLanguage: async (): Promise<string> => {
    const lang = await encryptedLocalStorage.getItem<string>('user_language');
    return lang || 'en';
  },

  clear: () => {
    encryptedLocalStorage.removeItem('user_theme');
    encryptedLocalStorage.removeItem('user_language');
  },
};
