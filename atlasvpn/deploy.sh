#!/bin/bash

# Exit on error
set -e

echo "🚀 Starting deployment..."

# Go to frontend directory
cd /opt/atlasvpn/frontend

# Install dependencies if needed
echo "📦 Installing dependencies..."
pnpm install

# Build the application
echo "🔨 Building application..."
NODE_ENV=production pnpm build

# Copy build files to web root
echo "📂 Copying files to web root..."
sudo mkdir -p /var/www/atlasvpn/frontend
sudo cp -R dist/* /var/www/atlasvpn/frontend/

# Set proper permissions
echo "🔒 Setting permissions..."
sudo chown -R www-data:www-data /var/www/atlasvpn/frontend
sudo chmod -R 755 /var/www/atlasvpn/frontend

# Restart Nginx
echo "🔄 Restarting Nginx..."
sudo systemctl restart nginx

echo "✅ Deployment completed successfully!"
echo "🌐 Your application is now live at: https://dev.atlasvip.cloud"
