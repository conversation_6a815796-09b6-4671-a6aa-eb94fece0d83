import React, { useRef, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { createPortal } from 'react-dom';
import {
  UserCircleIcon,
  WalletIcon,
  ArrowLeftStartOnRectangleIcon,
} from '@heroicons/react/24/outline';
import { useCurrentUserQuery, useLogoutMutation } from '../hooks/authHooks';
import { useDashboardStatsQuery } from '../hooks/dashboardHooks';
import Avatar3DViewer from './Avatar3DViewer';
import type { User, TabId } from '../types';
import { formatCurrency, capitalize } from '../utils/format';
import { FaBolt } from 'react-icons/fa';

export interface HeaderProps {
  activeTab?: TabId;
  showBackButton?: boolean;
  title?: string;
  className?: string;
  style?: React.CSSProperties;
}

interface UserMenuProps {
  show: boolean;
  onClose: () => void;
  onNavigate: (path: string) => void;
}

const UserMenu: React.FC<UserMenuProps> = ({ show, onClose, onNavigate }) => {
  const { data: user } = useCurrentUserQuery();
  const { mutate: logoutUser, isPending: isLoggingOut } = useLogoutMutation();
  const { data: stats, isLoading: isLoadingStats } = useDashboardStatsQuery();

  if (!show || !user) return null;

  const handleLogout = async () => {
    logoutUser(undefined, {
      onSuccess: () => {
        onClose();
      },
      onError: error => {
        console.error('Logout failed:', error);
        onClose();
      },
    });
  };

  return createPortal(
    <div className="fixed inset-0 z-[100]" onClick={onClose}>
      <div
        className="fixed right-4 top-20 w-64 bg-black/90 backdrop-blur-md rounded-xl border border-accent-500/20 shadow-xl overflow-hidden"
        onClick={e => e.stopPropagation()}
      >
        <div className="h-1 w-full bg-gradient-to-r from-accent-500/5 via-accent-500/30 to-accent-500/5"></div>

        <div className="p-4">
          <div className="flex items-center space-x-3">
            <Avatar3DViewer
              filename={`${user?.telegram_photo_url || 'Lovely Angel'}.webp`}
              size={{ width: 57, height: 57 }}
              className="transform transition-all"
            />
            <div>
              <div className="font-display font-medium text-white">{user?.username}</div>
              <div className="text-sm text-white/60">{user?.email || 'No email set'}</div>
              <div className="text-xs text-accent-500 mt-1">
                Balance: ${formatCurrency(stats?.wallet_balance || 0)}
              </div>
            </div>
          </div>
        </div>
        <div className="p-2 space-y-1">
          <button
            onClick={() => {
              onNavigate('/profile');
              onClose();
            }}
            className="w-full flex items-center px-4 py-2 text-sm text-white/80 hover:bg-white/5 hover:text-white rounded-lg transition-colors"
          >
            <UserCircleIcon className="w-5 h-5 mr-3 text-accent-400/70" />
            Profile Settings
          </button>
          <button
            onClick={() => {
              onNavigate('/wallet');
              onClose();
            }}
            className="w-full flex items-center px-4 py-2 text-sm text-white/80 hover:bg-white/5 hover:text-white rounded-lg transition-colors"
          >
            <WalletIcon className="w-5 h-5 mr-3 text-accent-400/70" />
            Wallet
          </button>
          <div className="h-px bg-accent-500/10 my-1" />
          <button
            onClick={handleLogout}
            disabled={isLoggingOut}
            className="w-full flex items-center px-4 py-2 text-sm text-red-400 hover:bg-red-500/10 rounded-lg transition-colors disabled:opacity-50"
          >
            <ArrowLeftStartOnRectangleIcon className="w-5 h-5 mr-3" />
            {isLoggingOut ? 'Logging out...' : 'Logout'}
          </button>
        </div>
      </div>
    </div>,
    document.body
  );
};

const Header: React.FC<HeaderProps> = ({ activeTab, className, style }) => {
  const { data: user, isLoading: isUserLoading } = useCurrentUserQuery();
  const { data: stats, isLoading: isLoadingStats } = useDashboardStatsQuery({ enabled: !!user });
  const navigate = useNavigate();
  const [showUserMenu, setShowUserMenu] = useState(false);

  // Function to format tab name for subtitle
  const formatTabName = (tab: string): string => {
    if (!tab) return '';
    const formatted = tab.replace(/-/g, ' ');
    return capitalize(formatted);
  };

  return (
    <>
      <header
        className={`flex items-center justify-between py-2 px-4 ${className || ''}`}
        style={style}
      >
        {/* Thinner Shine Effect */}
        <div className="absolute top-0 inset-x-0 h-0.5 bg-gradient-to-r from-transparent via-accent-500/15 to-transparent">
          <div className="absolute inset-0 flex justify-center">
            {/* Left-moving shine - Reduced size */}
            <div className="w-1/3 h-full relative overflow-hidden">
              <div className="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-transparent via-white/20 to-transparent animate-shine-left" />
            </div>
            {/* Right-moving shine - Reduced size */}
            <div className="w-1/3 h-full relative overflow-hidden">
              <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shine-right" />
            </div>
          </div>
        </div>

        {/* Title and Subtitle */}
        <div className="flex flex-col">
          <div className="text-white font-display font-semibold tracking-wide text-base leading-tight">
            VIPVerse
          </div>
          <div className="text-xs text-white/60 leading-tight mt-0.5">
            {formatTabName(activeTab || '')}
          </div>
        </div>

        {/* Right side content: Stats and Profile Button */}
        <div className="flex items-center gap-3">
          {user && (
            /* Vertical Column for Stats */
            <div className="flex flex-col items-end text-right mr-1">
              {/* Balance */}
              <div className="text-xs text-white/60 leading-tight">Balance</div>
              <div className="text-sm font-medium text-accent-300 leading-tight">
                ${formatCurrency(stats?.wallet_balance || 0)}
              </div>

              {/* Profit per Hour */}
              {stats?.total_passive_hourly_income && stats.total_passive_hourly_income > 0 && (
                <div className="flex items-center justify-end gap-1 text-[10px] bg-accent-500/10 px-1 py-0 rounded-sm mt-0.5">
                  <FaBolt size={8} className="text-accent-300" />
                  <span className="text-accent-300 leading-none">
                    {formatCurrency(stats.total_passive_hourly_income)}/h
                  </span>
                </div>
              )}
            </div>
          )}

          <button
            onClick={() => setShowUserMenu(prev => !prev)}
            disabled={isUserLoading}
            className="relative overflow-hidden rounded-full border-2 border-accent-500/30 hover:border-accent-500/50 shadow-md hover:shadow-lg hover:shadow-accent-500/10 transition-all duration-300 disabled:opacity-50"
          >
            {user ? (
              <Avatar3DViewer
                filename={`${user?.telegram_photo_url || 'Lovely Angel'}.webp`}
                size={{ width: 40, height: 40 }}
                className="transform hover:scale-105 transition-all"
              />
            ) : isUserLoading ? (
              <div className="w-10 h-10 flex items-center justify-center bg-accent-500/10 text-white">
                ...
              </div>
            ) : (
              <div className="w-10 h-10 flex items-center justify-center bg-accent-500/10 text-white">
                ?
              </div>
            )}
          </button>
        </div>
      </header>

      <UserMenu show={showUserMenu} onClose={() => setShowUserMenu(false)} onNavigate={navigate} />
    </>
  );
};

export default Header;
