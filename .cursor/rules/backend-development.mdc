---
globs: backend/**/*.py
description: Backend development rules for FastAPI and Redis integration
---

# Backend Development Rules

## 🔴 Redis Usage Rules

**ALWAYS** use the shared Redis client from [backend/config/redis_config.py](mdc:backend/config/redis_config.py):

```python
from backend.config.redis_config import get_redis_client

# ✅ Correct way
redis_client = get_redis_client()
await redis_client.set("key", "value")

# ❌ Never create new Redis connections directly
```

## 🔵 Database Rules

1. Use [backend/database.py](mdc:backend/database.py) for all database connections
2. Environment variables with SQLite fallback pattern
3. Always use async database operations

## 🟢 API Endpoint Rules

1. All API routes must be prefixed with `/api`
2. Use FastAPI dependency injection for Redis and database
3. Include proper error handling and status codes
4. Use Pydantic models for request/response validation

## 🟡 Service Integration

1. Initialize services in FastAPI lifespan events in [backend/main.py](mdc:backend/main.py)
2. Use dependency injection for service access
3. Implement proper connection pooling and cleanup

## 🟠 Authentication Rules

1. Use [backend/auth.py](mdc:backend/auth.py) for all auth operations
2. Telegram WebApp authentication via `initData` validation
3. Session management with Redis for token storage
4. CSRF protection (disable in development only)
description:
globs:
alwaysApply: false
---
