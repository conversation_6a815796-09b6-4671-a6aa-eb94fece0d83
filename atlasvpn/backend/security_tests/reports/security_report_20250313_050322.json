{"timestamp": "2025-03-13T05:03:22.722865", "total_tests": 2, "passed": 2, "failed": 0, "success_rate": 100.0, "results": [{"script": "test_security_headers.py", "start_time": "2025-03-13T05:02:46.995297", "success": true, "output": "\n================================================================================\n                    SECURITY HEADERS AND BOT DETECTION TEST                     \n================================================================================\n\n\nTesting security headers on main page...\n2025-03-13 05:02:50,602 - INFO - Testing security headers for: http://localhost:8000\n✓ All required security headers are present\n\nTesting Content-Security-Policy header...\n2025-03-13 05:02:50,630 - WARNING - CSP allows unsafe inline scripts\n⚠ CSP allows unsafe inline scripts - this can reduce XSS protection\n✓ Content-Security-Policy is properly configured\n\nTesting security headers on API endpoints...\n2025-03-13 05:02:50,630 - INFO - Testing security headers for: http://localhost:8000/api/tasks/available\n✓ Endpoint /api/tasks/available has all required security headers\n2025-03-13 05:02:50,680 - INFO - Testing security headers for: http://localhost:8000/api/security/verify-captcha\n✗ Endpoint /api/security/verify-captcha is missing headers: Content-Security-Policy, X-XSS-Protection, X-Content-Type-Options, X-Frame-Options, Referrer-Policy, Permissions-Policy, Cache-Control\n2025-03-13 05:02:50,695 - INFO - Testing security headers for: http://localhost:8000/auth/token\n✗ Endpoint /auth/token is missing headers: Content-Security-Policy, X-XSS-Protection, X-Content-Type-Options, X-Frame-Options, Referrer-Policy, Permissions-Policy, Cache-Control\n\nTesting bot detection...\n2025-03-13 05:02:50,719 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)': 404\n2025-03-13 05:02:50,731 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)': 404\n2025-03-13 05:02:50,745 - INFO - Bot test with User-Agent 'python-requests/2.25.1': 404\n2025-03-13 05:02:50,767 - INFO - Bot test with User-Agent 'Wget/1.20.3 (linux-gnu)': 404\n2025-03-13 05:02:50,776 - INFO - Bot test with User-Agent 'curl/7.68.0': 404\n⚠ Legitimate search engine bots blocked: Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)\n⚠ Automation tools allowed without CAPTCHA: python-requests/2.25.1, Wget/1.20.3 (linux-gnu), curl/7.68.0\n✗ Bot detection has issues\n\nDetailed Bot Detection Results:\n  Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html): ✓ OK\n  Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm): ✓ OK\n  python-requests/2.25.1: ✓ OK\n  Wget/1.20.3 (linux-gnu): ✓ OK\n  curl/7.68.0: ✓ OK\n", "error": "", "duration": 4.581714391708374}, {"script": "test_rate_limit_captcha.py", "start_time": "2025-03-13T05:02:51.577723", "success": true, "output": "\n================================================================================\n                         RATE LIMITING AND CAPTCHA TEST                         \n================================================================================\n\n\nTesting login rate limiting...\n2025-03-13 05:02:54,734 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts\n2025-03-13 05:02:54,787 - INFO - Attempt 1: Status 500\n2025-03-13 05:02:55,577 - INFO - Attempt 2: Status 500\n2025-03-13 05:02:55,812 - INFO - Attempt 3: Status 500\n2025-03-13 05:02:56,585 - INFO - Attempt 4: Status 500\n2025-03-13 05:02:56,811 - INFO - Attempt 5: Status 500\n2025-03-13 05:02:57,560 - INFO - Attempt 6: Status 500\n2025-03-13 05:02:57,787 - INFO - Attempt 7: Status 500\n2025-03-13 05:02:58,559 - INFO - Attempt 8: Status 500\n2025-03-13 05:02:58,772 - INFO - Attempt 9: Status 500\n2025-03-13 05:02:59,557 - INFO - Attempt 10: Status 429\n2025-03-13 05:02:59,765 - INFO - Attempt 11: Status 429\n2025-03-13 05:03:00,560 - INFO - Attempt 12: Status 429\n✓ Rate limiting triggered appropriately (after 10 attempts)\n✓ Login rate limiting is working\n\nRate limit headers for login attempts:\n  Attempt 1: OK - Headers: {}\n  Attempt 2: OK - Headers: {}\n  Attempt 3: OK - Headers: {}\n  Attempt 4: OK - Headers: {}\n  Attempt 5: OK - Headers: {}\n  Attempt 6: OK - Headers: {}\n  Attempt 7: OK - Headers: {}\n  Attempt 8: OK - Headers: {}\n  Attempt 9: OK - Headers: {}\n  Attempt 10: BLOCKED - Headers: {'retry-after': '3600', 'x-ratelimit-limit': '10', 'x-ratelimit-remaining': '0', 'x-ratelimit-reset': '3600', 'x-ratelimit-window': '300', 'x-ratelimit-type': 'login'}\n  Attempt 11: BLOCKED - Headers: {'retry-after': '3600', 'x-ratelimit-limit': '10', 'x-ratelimit-remaining': '0', 'x-ratelimit-reset': '3600', 'x-ratelimit-window': '300', 'x-ratelimit-type': 'login'}\n  Attempt 12: BLOCKED - Headers: {'retry-after': '3600', 'x-ratelimit-limit': '10', 'x-ratelimit-remaining': '0', 'x-ratelimit-reset': '3600', 'x-ratelimit-window': '300', 'x-ratelimit-type': 'login'}\n\nTesting rate limiting for API endpoints...\n\nTesting rate limiting for /api/tasks/available...\n2025-03-13 05:03:00,765 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts\n2025-03-13 05:03:00,809 - INFO - Attempt 1: Status 401\n2025-03-13 05:03:01,558 - INFO - Attempt 2: Status 401\n2025-03-13 05:03:01,806 - INFO - Attempt 3: Status 401\n2025-03-13 05:03:02,555 - INFO - Attempt 4: Status 401\n2025-03-13 05:03:02,787 - INFO - Attempt 5: Status 401\n2025-03-13 05:03:03,554 - INFO - Attempt 6: Status 401\n2025-03-13 05:03:03,801 - INFO - Attempt 7: Status 401\n2025-03-13 05:03:04,559 - INFO - Attempt 8: Status 401\n2025-03-13 05:03:04,790 - INFO - Attempt 9: Status 401\n2025-03-13 05:03:05,558 - INFO - Attempt 10: Status 429\n✓ Rate limiting triggered appropriately (after 10 attempts)\n✓ Rate limiting is working for /api/tasks/available\n\nTesting rate limiting for /api/tasks/analytics...\n2025-03-13 05:03:05,760 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts\n2025-03-13 05:03:05,789 - INFO - Attempt 1: Status 401\n2025-03-13 05:03:06,579 - INFO - Attempt 2: Status 401\n2025-03-13 05:03:06,817 - INFO - Attempt 3: Status 401\n2025-03-13 05:03:07,555 - INFO - Attempt 4: Status 401\n2025-03-13 05:03:07,785 - INFO - Attempt 5: Status 401\n2025-03-13 05:03:08,561 - INFO - Attempt 6: Status 401\n2025-03-13 05:03:08,795 - INFO - Attempt 7: Status 401\n2025-03-13 05:03:09,554 - INFO - Attempt 8: Status 401\n2025-03-13 05:03:09,789 - INFO - Attempt 9: Status 401\n2025-03-13 05:03:10,570 - INFO - Attempt 10: Status 429\n✓ Rate limiting triggered appropriately (after 10 attempts)\n✓ Rate limiting is working for /api/tasks/analytics\n\nTesting rate limiting for /auth/register...\n2025-03-13 05:03:10,771 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts\n2025-03-13 05:03:10,787 - INFO - Attempt 1: Status 405\n2025-03-13 05:03:11,554 - INFO - Attempt 2: Status 405\n2025-03-13 05:03:11,765 - INFO - Attempt 3: Status 429\n2025-03-13 05:03:12,554 - INFO - Attempt 4: Status 429\n2025-03-13 05:03:12,767 - INFO - Attempt 5: Status 429\n2025-03-13 05:03:13,609 - INFO - Attempt 6: Status 429\n2025-03-13 05:03:13,829 - INFO - Attempt 7: Status 429\n2025-03-13 05:03:14,473 - INFO - Attempt 8: Status 429\n2025-03-13 05:03:14,716 - INFO - Attempt 9: Status 429\n2025-03-13 05:03:14,943 - INFO - Attempt 10: Status 429\n⚠ Rate limiting triggered too early (after 3 attempts)\n✓ Rate limiting is working for /auth/register\n\nTesting Cloudflare CAPTCHA integration...\n2025-03-13 05:03:15,455 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token\n2025-03-13 05:03:15,466 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior\n2025-03-13 05:03:15,481 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register\n2025-03-13 05:03:15,497 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior\n2025-03-13 05:03:15,506 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login\n2025-03-13 05:03:15,515 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior\n⚠ No CAPTCHA detected for login endpoint\n⚠ No CAPTCHA detected for register endpoint\n⚠ No CAPTCHA detected for admin_login endpoint\n\nSimulating distributed attack...\n2025-03-13 05:03:15,527 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent\n2025-03-13 05:03:18,938 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored\n✗ Distributed attack was not effectively blocked (block rate: 0.00%)\n\nSample distributed attack results:\n  Request from IP ***************: ALLOWED\n  Request from IP ***************: ALLOWED\n  Request from IP ***************: ALLOWED\n  Request from IP **************: ALLOWED\n  Request from IP *************: ALLOWED\n", "error": "", "duration": 31.141491174697876}]}