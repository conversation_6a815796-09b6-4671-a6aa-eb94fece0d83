/**
 * Store exports - Central export point for all Zustand stores
 */

// UI Store
export { useUIStore, type Theme } from './uiStore';

// Task Store
export { useTaskStore } from './taskStore';

// Chat Store
export { useChatStore } from './chatStore';

// UI Chat Store
export { useUIChatStore, type ChatViewMode } from './uiChatStore';

// Default export
import { useUIStore } from './uiStore';
export default useUIStore;