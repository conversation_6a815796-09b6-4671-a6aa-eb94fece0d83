import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  <PERSON>a<PERSON>oi<PERSON>,
  <PERSON>aArrow<PERSON>p,
  Fa<PERSON><PERSON><PERSON>,
  <PERSON>a<PERSON>olt,
  FaChessRook,
  FaCheckCircle,
  FaCalendarDay,
  FaTasks,
  FaGamepad,
} from 'react-icons/fa';
// import { useAuthStore } from '../stores/authStore'; // Commented out, refreshAuth might not be needed or handled differently
import {
  useStartTaskMutation,
  useVerifyTaskMutation,
  useClaimRewardMutation,
} from '../hooks/taskHooks';
import { useUpgradeCardMutation, useBuyCardMutation } from '../hooks/cardHooks';
import LoadingSpinner from './LoadingSpinner';
import { formatCurrency } from '../utils/format';
import CardDetailSlide from './CardDetailSlide';
import { isLowEndDevice } from '../utils/performance';
import { Task, TaskType, TaskStatus } from '../types/task';
import TaskDetailSlide from './TaskDetailSlide';
import { TaskTabComponent } from './TaskTab';
import CardTab from './CardTab';
import { queryClient } from '../queryClient'; // Import queryClient
// Import necessary types
import {
  UserCard,
  UpgradeResult,
  UnifiedCardResponse,
  CardCatalog as GameCardCatalog,
} from '../types/cardTypes';
import type { User } from '../types';

// Rarity color map for border and shadow - memoized to avoid recreation
const rarityColors = {
  common: 'border-gray-400 shadow-gray-300/10',
  rare: 'border-blue-400 shadow-blue-300/20',
  epic: 'border-purple-500 shadow-purple-400/30',
  legendary: 'border-yellow-400 shadow-yellow-300/40',
  mythic: 'border-pink-400 shadow-pink-300/50',
} as const;

// Define keys based on the above object
type RarityKey = keyof typeof rarityColors;

// Rarity text color map - memoized to avoid recreation
const rarityTextColors = {
  common: 'text-gray-300',
  rare: 'text-blue-300',
  epic: 'text-purple-300',
  legendary: 'text-yellow-300',
  mythic: 'text-pink-300',
} as const;

// Define section type for the tab
type EarnSection = 'cards' | 'tasks' | 'games';

// Placeholder for SegmentedControl props and component if not existing
interface SegmentedControlOption {
  id: string;
  label: string;
  icon?: React.ComponentType<any>; // Allow icon component
}

interface SegmentedControlProps {
  options: SegmentedControlOption[];
  active: string;
  onChange: (id: string) => void; // Specify string type for id
}

// Basic placeholder component implementation if needed
const SegmentedControl: React.FC<SegmentedControlProps> = React.memo(
  ({ options, active, onChange }) => (
    <div className="flex bg-black/30 rounded-full p-0.5 border border-white/5">
      {options.map(option => (
        <button
          key={option.id}
          onClick={() => onChange(option.id)}
          className={`flex-1 py-1.5 px-3 rounded-full text-xs font-medium transition-all flex items-center justify-center gap-1 ${
            active === option.id
              ? 'bg-white/10 text-white shadow-inner'
              : 'text-white/60 hover:text-white hover:bg-white/5'
          }`}
        >
          {option.icon && (
            <option.icon
              className={`w-3.5 h-3.5 ${active === option.id ? 'text-purple-300' : 'text-white/50'}`}
            />
          )}
          <span>{option.label}</span>
        </button>
      ))}
    </div>
  )
);
SegmentedControl.displayName = 'SegmentedControl';

interface EarnTabProps {
  cardTabProps: any;
  taskTabProps: any;
  isLoadingTasks: boolean;
  isLoadingCards: boolean;
  user?: User;
  onTabChange?: (tabId: string) => void;
}

const EarnTab: React.FC<EarnTabProps> = ({
  cardTabProps,
  taskTabProps,
  isLoadingTasks,
  isLoadingCards,
  user,
  onTabChange,
}) => {
  // const { refreshAuth } = useAuthStore(); // Commented out

  const [activeSection, setActiveSection] = useState<EarnSection>('cards');

  // --- Card Store (for mutations) ---
  const upgradeCardMutation = useUpgradeCardMutation();
  const buyCardMutation = useBuyCardMutation();

  // --- Task Store (for mutations) ---
  const startTaskMutation = useStartTaskMutation();
  const verifyTaskMutation = useVerifyTaskMutation();
  const claimRewardMutation = useClaimRewardMutation();

  const [selectedUnifiedCard, setSelectedUnifiedCard] = useState<UnifiedCardResponse | null>(null);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const isLowEnd = useMemo(() => isLowEndDevice(), []);

  const contentAnimationConfig = useMemo(
    () => ({
      initial: { opacity: 0, y: isLowEnd ? 5 : 10 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: isLowEnd ? -5 : -10 },
      transition: { duration: isLowEnd ? 0.15 : 0.2, ease: 'easeOut' },
    }),
    [isLowEnd]
  );

  // --- Card/Task selection handlers ---
  const handleCardSelect = useCallback((card: UnifiedCardResponse) => {
    console.log('[EarnTab] Card selected:', card);
    setSelectedUnifiedCard(card);
  }, []);
  const handleTaskSelect = useCallback((task: Task) => setSelectedTask(task), []);
  const handleCloseSlides = useCallback(() => {
    setSelectedUnifiedCard(null);
    setSelectedTask(null);
  }, []);

  // --- Profit calculations ---
  const totalAccumulatedProfit = useMemo(
    () =>
      (cardTabProps.unifiedCards ?? []).reduce(
        (sum: number, card: UnifiedCardResponse) =>
          sum + (card.is_owned && card.user_card ? card.user_card.accumulated_profit : 0),
        0
      ),
    [cardTabProps.unifiedCards]
  );
  const totalHourlyProfit = useMemo(
    () =>
      (cardTabProps.unifiedCards ?? []).reduce(
        (sum: number, card: UnifiedCardResponse) =>
          sum + (card.is_owned && card.user_card ? card.user_card.current_hourly_profit : 0),
        0
      ),
    [cardTabProps.unifiedCards]
  );
  const ownedCards = useMemo(
    () =>
      (cardTabProps.unifiedCards ?? []).filter(
        (card: UnifiedCardResponse) => card.is_owned && card.user_card
      ),
    [cardTabProps.unifiedCards]
  );
  const availableCards = useMemo(
    () => (cardTabProps.unifiedCards ?? []).filter((card: UnifiedCardResponse) => !card.is_owned),
    [cardTabProps.unifiedCards]
  );
  const allCardsOwned = useMemo(() => {
    return (
      (cardTabProps.unifiedCards ?? []).length > 0 &&
      (cardTabProps.unifiedCards ?? []).filter((card: UnifiedCardResponse) => !card.is_owned)
        .length === 0
    );
  }, [cardTabProps.unifiedCards]);

  const segmentedControlOptions = useMemo<SegmentedControlOption[]>(
    () => [
      { id: 'cards', label: 'Cards', icon: FaChessRook },
      { id: 'tasks', label: 'Tasks', icon: FaTasks },
      { id: 'games', label: 'Games', icon: FaGamepad },
    ],
    []
  );

  const gamesPlaceholder = useMemo(
    () => (
      <motion.div key="games" {...contentAnimationConfig} className="text-center py-10">
        <FaGamepad className="text-5xl text-purple-400/60 mx-auto mb-4" />
        <p className="text-white/70">Game integrations are coming soon!</p>
        <p className="text-sm text-white/50">Stay tuned for exciting play-to-earn opportunities.</p>
      </motion.div>
    ),
    [contentAnimationConfig]
  );

  const renderErrorState = useCallback(() => {
    if (activeSection === 'cards' && !cardTabProps.unifiedCards) {
      return (
        <div className="text-center text-red-400 py-10">
          Failed to load cards. Please try again later.
        </div>
      );
    }
    if (activeSection === 'tasks' && !taskTabProps.tasks) {
      return (
        <div className="text-center text-red-400 py-10">
          Failed to load tasks. Please try again later.
        </div>
      );
    }
    return null;
  }, [activeSection, cardTabProps.unifiedCards, taskTabProps.tasks]);

  const handleUpgrade = useCallback(
    async (cardId: number) => {
      try {
        const result = await upgradeCardMutation.mutateAsync(cardId);
        // Optionally refetch unifiedCards or invalidate queries here
        return { success: true, updatedCard: result };
      } catch (error: any) {
        return { success: false, error: error?.message || 'Upgrade failed' };
      }
    },
    [upgradeCardMutation]
  );

  const handleBuy = useCallback(
    async (cardCatalogId: number) => {
      try {
        const result = await buyCardMutation.mutateAsync(cardCatalogId);
        return {
          success: true,
          newCard: result.new_card,
          newWalletBalance: result.new_wallet_balance,
        };
      } catch (error: any) {
        return { success: false, error: error?.message || 'Purchase failed' };
      }
    },
    [buyCardMutation]
  );

  useEffect(() => {
    if (!selectedUnifiedCard) return;
    // Find the updated card in the latest unifiedCards
    const updated = (cardTabProps.unifiedCards ?? []).find((c: UnifiedCardResponse) => c.id === selectedUnifiedCard.id);
    // Only update if the reference or content has changed
    if (updated && updated !== selectedUnifiedCard) {
      setSelectedUnifiedCard(updated);
    }
  }, [cardTabProps.unifiedCards, selectedUnifiedCard]);

  const renderContent = () => {
    if (activeSection === 'cards') {
      if (isLoadingCards && cardTabProps.unifiedCards.length === 0) {
        return (
          <motion.div
            key="cards-loader"
            {...contentAnimationConfig}
            className="flex justify-center items-center py-10"
          >
            <LoadingSpinner size="lg" />
          </motion.div>
        );
      }
      return <CardTab {...cardTabProps} onCardSelect={handleCardSelect} />;
    }

    if (activeSection === 'tasks') {
      if (isLoadingTasks && taskTabProps.tasks.length === 0) {
        return (
          <motion.div
            key="tasks-loader"
            {...contentAnimationConfig}
            className="flex justify-center items-center py-10"
          >
            <LoadingSpinner size="lg" />
          </motion.div>
        );
      }
      return (
        <motion.div key="tasks" {...contentAnimationConfig}>
          <TaskTabComponent {...taskTabProps} onTaskSelect={handleTaskSelect} />
        </motion.div>
      );
    }

    if (activeSection === 'games') {
      return gamesPlaceholder;
    }
    return null;
  };

  return (
    <div className="pb-4 min-h-[60vh] earn-tab-marker">
      <div className="p-4 sticky top-0 z-10 bg-black/80 backdrop-blur-md">
        <SegmentedControl
          options={segmentedControlOptions}
          active={activeSection}
          onChange={id => setActiveSection(id as EarnSection)}
        />
      </div>

      <AnimatePresence mode="wait">
        <motion.div
          key={activeSection}
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="px-1"
        >
          {renderErrorState()}
          {renderContent()}
        </motion.div>
      </AnimatePresence>

      <AnimatePresence>
        {selectedUnifiedCard &&
          ((() => {
            console.log('[EarnTab] Rendering CardDetailSlide with:', selectedUnifiedCard);
            return null;
          })(),
          (
            <CardDetailSlide
              unifiedCardData={selectedUnifiedCard}
              isOpen={!!selectedUnifiedCard}
              onClose={handleCloseSlides}
              onUpgrade={handleUpgrade}
              onBuy={handleBuy}
              onCardUpdated={() => {}}
            />
          ))}
        {selectedTask && (
          <TaskDetailSlide
            task={selectedTask}
            isOpen={!!selectedTask}
            onClose={handleCloseSlides}
            onTaskUpdated={() => {}}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default React.memo(EarnTab);
