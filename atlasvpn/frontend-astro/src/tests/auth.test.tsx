import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import React from 'react';
import { useCurrentUserQuery, useTelegramLoginMutation } from '../api/hooks';
import * as telegramUtils from '../utils/telegram';
import type { User } from '../api/types';

// Mock the API client
const mockApi = {
  post: vi.fn(),
  get: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
};

vi.mock('../api', () => ({
  default: mockApi,
}));

// Mock Telegram utilities
vi.mock('../utils/telegram', () => ({
  getTelegramData: vi.fn(),
  initializeTelegramWebApp: vi.fn(),
  isTelegramWebApp: vi.fn(),
  triggerHapticFeedback: vi.fn(),
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

const mockUser: User = {
  id: 1,
  username: 'testuser',
  telegram_id: 12345,
  email: '<EMAIL>',
  wallet_balance: 0,
  total_passive_hourly_income: 0,
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T00:00:00Z',
};

describe('Authentication Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.clearAllTimers();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('useCurrentUserQuery', () => {
    it('should return authenticated user when session is valid', async () => {
      // Arrange
      (mockApi.post as any).mockResolvedValueOnce({
        data: {
          status: 'authenticated',
          data: mockUser,
        },
      });

      // Act
      const { result } = renderHook(() => useCurrentUserQuery(), {
        wrapper: createWrapper(),
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual(mockUser);
      expect(mockApi.post).toHaveBeenCalledWith(
        '/auth/verify-session',
        {},
        expect.objectContaining({
          signal: expect.any(AbortSignal),
          timeout: 12000,
        })
      );
    });

    it('should return undefined on 401 error', async () => {
      // Arrange
      (mockApi.post as any).mockRejectedValueOnce({
        response: { status: 401 },
      });

      // Act
      const { result } = renderHook(() => useCurrentUserQuery(), {
        wrapper: createWrapper(),
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toBeUndefined();
    });

    it('should handle request timeout gracefully', async () => {
      // Arrange
      (mockApi.post as any).mockRejectedValueOnce({
        name: 'AbortError',
      });

      // Act
      const { result } = renderHook(() => useCurrentUserQuery(), {
        wrapper: createWrapper(),
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Request timeout');
    });

    it('should have correct React Query configuration', () => {
      // Act
      const { result } = renderHook(() => useCurrentUserQuery(), {
        wrapper: createWrapper(),
      });

      // Assert - Check query configuration
      expect(result.current.isStale).toBe(false);
      expect(result.current.isFetching).toBe(true);
    });
  });

  describe('useTelegramLoginMutation', () => {
    it('should handle successful login for existing user', async () => {
      // Arrange
      (mockApi.post as any).mockResolvedValueOnce({
        data: {
          data: mockUser,
        },
      });

      // Act
      const { result } = renderHook(() => useTelegramLoginMutation(), {
        wrapper: createWrapper(),
      });

      // Assert
      await waitFor(() => {
        expect(result.current.mutate).toBeDefined();
      });

      // Trigger mutation
      result.current.mutate({
        init_data: 'test_init_data',
        auth_method: 'telegram',
      });

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual({
        isNewUser: false,
        user: mockUser,
      });

      expect(mockApi.post).toHaveBeenCalledWith(
        '/auth/telegram/login',
        {
          init_data: 'test_init_data',
          auth_method: 'telegram',
        },
        expect.objectContaining({
          signal: expect.any(AbortSignal),
          timeout: 7000,
        })
      );
    });

    it('should handle new user scenario (404 response)', async () => {
      // Arrange
      const telegramData = { user_id: '12345', username: 'newuser' };
      (mockApi.post as any).mockRejectedValueOnce({
        response: {
          status: 404,
          data: { telegram_data: telegramData },
        },
      });

      // Act
      const { result } = renderHook(() => useTelegramLoginMutation(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        init_data: 'test_init_data',
        auth_method: 'telegram',
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual({
        isNewUser: true,
        telegram_data: telegramData,
      });
    });

    it('should handle network errors properly', async () => {
      // Arrange
      (mockApi.post as any).mockRejectedValueOnce({
        message: 'Network Error',
      });

      // Act
      const { result } = renderHook(() => useTelegramLoginMutation(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        init_data: 'test_init_data',
        auth_method: 'telegram',
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Network Error');
    });

    it('should handle timeout errors', async () => {
      // Arrange
      (mockApi.post as any).mockRejectedValueOnce({
        name: 'AbortError',
      });

      // Act
      const { result } = renderHook(() => useTelegramLoginMutation(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        init_data: 'test_init_data',
        auth_method: 'telegram',
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toBe('Request timeout');
    });

    it('should handle server errors', async () => {
      // Arrange
      (mockApi.post as any).mockRejectedValueOnce({
        response: {
          status: 500,
          data: { message: 'Internal Server Error' },
        },
      });

      // Act
      const { result } = renderHook(() => useTelegramLoginMutation(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        init_data: 'test_init_data',
        auth_method: 'telegram',
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toContain('Server error');
    });

    it('should update cache on successful login', async () => {
      // Arrange
      (mockApi.post as any).mockResolvedValueOnce({
        data: {
          data: mockUser,
        },
      });

      const queryClient = new QueryClient();
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
      );

      // Act
      const { result } = renderHook(() => useTelegramLoginMutation(), {
        wrapper,
      });

      result.current.mutate({
        init_data: 'test_init_data',
        auth_method: 'telegram',
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      // Check if cache was updated
      const cachedData = queryClient.getQueryData(['currentUser']);
      expect(cachedData).toEqual(mockUser);
    });

    it('should handle rate limiting errors', async () => {
      // Arrange
      (mockApi.post as any).mockRejectedValueOnce({
        response: {
          status: 429,
          data: { message: 'Too Many Requests' },
        },
      });

      // Act
      const { result } = renderHook(() => useTelegramLoginMutation(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        init_data: 'test_init_data',
        auth_method: 'telegram',
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toContain('rate limit');
    });

    it('should handle validation errors', async () => {
      // Arrange
      (mockApi.post as any).mockRejectedValueOnce({
        response: {
          status: 400,
          data: { message: 'Invalid init_data' },
        },
      });

      // Act
      const { result } = renderHook(() => useTelegramLoginMutation(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        init_data: 'invalid_data',
        auth_method: 'telegram',
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isError).toBe(true);
      });

      expect(result.current.error?.message).toContain('Invalid');
    });

    it('should handle successful signup for new user', async () => {
      // Arrange
      const telegramData = { user_id: '12345', username: 'newuser' };
      (mockApi.post as any).mockResolvedValueOnce({
        data: {
          data: mockUser,
        },
      });

      // Mock Telegram utilities
      vi.mocked(telegramUtils.getTelegramData).mockReturnValue({
        initData: 'test_init_data',
        user: telegramData,
      } as any);

      // Act
      const { result } = renderHook(() => useTelegramLoginMutation(), {
        wrapper: createWrapper(),
      });

      result.current.mutate({
        init_data: 'test_init_data',
        auth_method: 'telegram',
      });

      // Assert
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true);
      });

      expect(result.current.data).toEqual({
        isNewUser: false,
        user: mockUser,
      });
    });
  });
});