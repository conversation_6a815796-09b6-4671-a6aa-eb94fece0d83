---
alwaysApply: true
description: AtlasVPN Telegram Mini App project structure and development guidelines
---

# AtlasVPN Telegram Mini App - Project Rules

## 🏗️ Project Architecture

This is a **Telegram Mini App** built with:
- **Backend**: FastAPI ([backend/main.py](mdc:backend/main.py))
- **Frontend**: Astro + React ([frontend-astro/](mdc:frontend-astro/))
- **Database**: PostgreSQL + Redis
- **Proxy**: Nginx ([nginx/conf.d/app.atlasvip.cloud.conf](mdc:nginx/conf.d/app.atlasvip.cloud.conf))
- **Orchestration**: Docker Compose ([docker-compose.yml](mdc:docker-compose.yml))

## 🔧 Key Configuration Files

- **Docker Compose**: [docker-compose.yml](mdc:docker-compose.yml) - Main orchestration
- **Redis Config**: [backend/config/redis_config.py](mdc:backend/config/redis_config.py) - Connection pool
- **Database**: [backend/database.py](mdc:backend/database.py) - PostgreSQL setup
- **API Client**: [frontend-astro/src/utils/apiClient.ts](mdc:frontend-astro/src/utils/apiClient.ts) - HTTP client
- **Nginx Config**: [nginx/conf.d/app.atlasvip.cloud.conf](mdc:nginx/conf.d/app.atlasvip.cloud.conf) - Reverse proxy
- **Astro Config**: [frontend-astro/astro.config.mjs](mdc:frontend-astro/astro.config.mjs) - Frontend build

## 🌐 Domain and URLs

- **Production Domain**: `app.atlasvip.cloud`
- **Backend API**: `https://app.atlasvip.cloud/api`
- **Frontend Dev Server**: Internal port 3005
- **Backend Server**: Internal port 8000

## 📋 Development Rules

### When Working on Backend:
1. Always use the shared Redis client from [backend/config/redis_config.py](mdc:backend/config/redis_config.py)
2. Database connections use environment variables with SQLite fallback
3. API endpoints must be prefixed with `/api`
4. Use FastAPI lifespan events for service initialization

### When Working on Frontend:
1. React components go in [frontend-astro/src/components/react/](mdc:frontend-astro/src/components/react/)
2. API calls use [frontend-astro/src/utils/apiClient.ts](mdc:frontend-astro/src/utils/apiClient.ts)
3. Telegram WebApp SDK integration in [frontend-astro/src/utils/telegram.ts](mdc:frontend-astro/src/utils/telegram.ts)
4. HMR is disabled for Telegram compatibility

### When Working on Nginx:
1. Always proxy `/src/` and `/node_modules/` for development
2. Include WebSocket upgrade headers for any WS endpoints
3. Set proper CSP for Telegram domains: `frame-ancestors https://*.telegram.org`
4. Remove `X-Frame-Options` for iframe embedding

## 🐳 Docker Commands

- **Status**: `docker-compose ps`
- **Logs**: `docker-compose logs [service]`
- **Restart**: `docker-compose restart [service]`
- **Live Logs**: `docker-compose logs -f [service]`

## 🔍 Common Issues

1. **503 Errors**: Restart frontend: `docker-compose restart frontend`
2. **Redis Connection**: Check [backend/config/redis_config.py](mdc:backend/config/redis_config.py) pool config
3. **Telegram Embedding**: Ensure no `X-Frame-Options` in nginx
4. **CSRF Errors**: Disable CSRF middleware in development
5. **WebSocket Failures**: Check HMR is disabled in astro.config.mjs

## 🧪 Testing

- **Unit Tests**: [frontend-astro/src/tests/](mdc:frontend-astro/src/tests/)
- **Integration Tests**: Focus on auth flow and API connectivity
- **Telegram Testing**: Look for `[Telegram.WebView]` console messages
