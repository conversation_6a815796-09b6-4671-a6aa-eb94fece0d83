import type { Card, CardPurchase, CardProfit } from '~/types'

/**
 * Modern cards composable using Nuxt 4 data fetching patterns
 * Optimized for Telegram Mini App with proper caching and error handling
 */
export const useCards = () => {
  // Default fetch options optimized for Telegram Mini App
  const defaultOptions = {
    server: false, // Client-side only for Telegram Mini App
    retry: 3,
    retryDelay: 1000,
    timeout: 15000,
    onRequestError({ error }: { error: any }) {
      console.error('[Cards] Request error:', error)
    },
    onResponseError({ response }: { response: any }) {
      console.error('[Cards] Response error:', response.status, response.statusText)
    }
  }

  // Purchase state (local state for UI feedback)
  const isPurchasing = ref(false)
  const purchaseError = ref<string | null>(null)
  const claimingProfits = ref(new Set<number>())

  // Available cards with modern useFetch
  const { 
    data: availableCards, 
    pending: isLoadingAvailable, 
    error: availableError, 
    refresh: refreshAvailable 
  } = useFetch<Card[]>('/api/cards/available', {
    ...defaultOptions,
    key: 'available-cards',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // User's owned cards with modern useFetch
  const { 
    data: ownedCards, 
    pending: isLoadingOwned, 
    error: ownedError, 
    refresh: refreshOwned 
  } = useFetch<Card[]>('/api/cards/owned', {
    ...defaultOptions,
    key: 'owned-cards',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // All cards (unified view) with modern useFetch
  const { 
    data: allCards, 
    pending: isLoadingAll, 
    error: allError, 
    refresh: refreshAll 
  } = useFetch<Card[]>('/api/cards/all', {
    ...defaultOptions,
    key: 'all-cards',
    default: () => [],
    transform: (data: any) => data?.data || data || []
  })

  // Purchase a card with optimistic updates
  const purchaseCard = async (cardId: number): Promise<CardPurchase> => {
    if (isPurchasing.value) throw new Error('Purchase already in progress')

    isPurchasing.value = true
    purchaseError.value = null

    // Optimistic update - move card from available to owned
    const cardIndex = availableCards.value?.findIndex(c => c.id === cardId) ?? -1
    let originalCard: Card | null = null
    
    if (cardIndex !== -1 && availableCards.value) {
      originalCard = availableCards.value[cardIndex]
      // Remove from available
      availableCards.value.splice(cardIndex, 1)
      // Add to owned (if ownedCards is loaded)
      if (ownedCards.value) {
        ownedCards.value.push({ ...originalCard, is_owned: true })
      }
    }

    try {
      const response = await $fetch<{ data: CardPurchase }>('/api/cards/purchase', {
        method: 'POST',
        body: { card_id: cardId }
      })
      
      // Refresh all card data to get the actual state
      await Promise.all([
        refreshAvailable(),
        refreshOwned(),
        refreshAll()
      ])
      
      return response.data
    } catch (error: any) {
      console.error('[Cards] Failed to purchase card:', error)
      purchaseError.value = error.message || 'Failed to purchase card'
      
      // Revert optimistic update on error
      if (originalCard && cardIndex !== -1) {
        if (availableCards.value) {
          availableCards.value.splice(cardIndex, 0, originalCard)
        }
        if (ownedCards.value) {
          const ownedIndex = ownedCards.value.findIndex(c => c.id === cardId)
          if (ownedIndex !== -1) {
            ownedCards.value.splice(ownedIndex, 1)
          }
        }
      }
      
      throw error
    } finally {
      isPurchasing.value = false
    }
  }

  // Claim profits from all cards
  const claimAllProfits = async (): Promise<CardProfit[]> => {
    try {
      const response = await $fetch<{ data: CardProfit[] }>('/api/cards/claim-all-profits', {
        method: 'POST'
      })
      
      // Refresh owned cards to get updated profit data
      await refreshOwned()
      
      return response.data
    } catch (error: any) {
      console.error('[Cards] Failed to claim all profits:', error)
      throw error
    }
  }

  // Claim profit from specific card
  const claimCardProfit = async (cardId: number): Promise<CardProfit> => {
    if (claimingProfits.value.has(cardId)) throw new Error('Already claiming profit for this card')

    claimingProfits.value.add(cardId)

    try {
      const response = await $fetch<{ data: CardProfit }>(`/api/cards/${cardId}/claim-profit`, {
        method: 'POST'
      })
      
      // Update card profit in local state
      const cardIndex = ownedCards.value?.findIndex(c => c.id === cardId) ?? -1
      if (cardIndex !== -1 && ownedCards.value) {
        ownedCards.value[cardIndex] = {
          ...ownedCards.value[cardIndex],
          accumulated_profit: 0,
          last_profit_claim: new Date().toISOString()
        }
      }
      
      return response.data
    } catch (error: any) {
      console.error('[Cards] Failed to claim card profit:', error)
      throw error
    } finally {
      claimingProfits.value.delete(cardId)
    }
  }

  // Upgrade a card
  const upgradeCard = async (cardId: number): Promise<Card> => {
    try {
      const response = await $fetch<{ data: Card }>(`/api/cards/${cardId}/upgrade`, {
        method: 'POST'
      })
      
      // Refresh owned cards to get updated data
      await refreshOwned()
      
      return response.data
    } catch (error: any) {
      console.error('[Cards] Failed to upgrade card:', error)
      throw error
    }
  }

  // Refresh all card data
  const refreshAllCards = async () => {
    await Promise.all([
      refreshAvailable(),
      refreshOwned(),
      refreshAll()
    ])
  }

  // Computed properties
  const totalCardsOwned = computed(() => ownedCards.value?.length || 0)

  const totalProfitAvailable = computed(() => 
    ownedCards.value?.reduce((total, card) => total + (card.accumulated_profit || 0), 0) || 0
  )

  const totalHourlyIncome = computed(() => 
    ownedCards.value?.reduce((total, card) => total + (card.hourly_profit || 0), 0) || 0
  )

  const cardsWithProfits = computed(() => 
    ownedCards.value?.filter(card => (card.accumulated_profit || 0) > 0) || []
  )

  const canClaimProfits = computed(() => cardsWithProfits.value.length > 0)

  const cardsByCategory = computed(() => {
    const categories: Record<string, Card[]> = {}
    allCards.value?.forEach(card => {
      const category = card.category || 'Other'
      if (!categories[category]) {
        categories[category] = []
      }
      categories[category].push(card)
    })
    return categories
  })

  // Utility functions
  const formatProfit = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const getCardRarity = (card: Card): string => {
    if (card.price >= 10000) return 'Legendary'
    if (card.price >= 5000) return 'Epic'
    if (card.price >= 1000) return 'Rare'
    return 'Common'
  }

  const getTimeUntilNextProfit = (card: Card): number => {
    if (!card.last_profit_claim) return 0
    const lastClaim = new Date(card.last_profit_claim)
    const nextClaim = new Date(lastClaim.getTime() + 60 * 60 * 1000) // 1 hour
    const now = new Date()
    return Math.max(0, nextClaim.getTime() - now.getTime())
  }

  return {
    // State (reactive from useFetch)
    availableCards: readonly(availableCards),
    ownedCards: readonly(ownedCards),
    allCards: readonly(allCards),
    isLoadingAvailable: readonly(isLoadingAvailable),
    isLoadingOwned: readonly(isLoadingOwned),
    isLoadingAll: readonly(isLoadingAll),
    availableError: readonly(availableError),
    ownedError: readonly(ownedError),
    allError: readonly(allError),
    isPurchasing: readonly(isPurchasing),
    purchaseError: readonly(purchaseError),
    claimingProfits: readonly(claimingProfits),

    // Actions
    refreshAvailable,
    refreshOwned,
    refreshAll,
    refreshAllCards,
    purchaseCard,
    claimAllProfits,
    claimCardProfit,
    upgradeCard,

    // Computed
    totalCardsOwned,
    totalProfitAvailable,
    totalHourlyIncome,
    cardsWithProfits,
    canClaimProfits,
    cardsByCategory,

    // Utilities
    formatProfit,
    getCardRarity,
    getTimeUntilNextProfit
  }
}
