/**
 * @file BottomNavigation.tsx
 * @description Bottom navigation component for dashboard tabs
 * React island for handling navigation interactions and animations
 */

import React, { useMemo, memo, useCallback } from 'react';
import { FaHome, FaCoins, FaBolt, FaWallet, FaUserFriends, FaGlobe } from 'react-icons/fa';

// Navigation tab types
type BottomNavTabId = 'home' | 'earn' | 'friends' | 'wallet' | 'premium';
type NavTabId = BottomNavTabId | 'verse';

interface NavItem {
  id: NavTabId;
  icon: React.ReactNode;
  label: string;
  path: string;
}

export interface BottomNavigationProps {
  activeTab: BottomNavTabId;
  onTabChange?: (tabId: BottomNavTabId) => void;
  className?: string;
  style?: React.CSSProperties;
}

// Navigation items configuration
const NAV_ITEMS: NavItem[] = [
  {
    id: 'home',
    icon: <FaHome className="w-4 h-4" />,
    label: 'Home',
    path: '/dashboard/home',
  },
  {
    id: 'earn',
    icon: <FaCoins className="w-4 h-4" />,
    label: 'Earn',
    path: '/dashboard/earn',
  },
  {
    id: 'friends',
    icon: <FaUserFriends className="w-4 h-4" />,
    label: 'Friends',
    path: '/dashboard/friends',
  },
  {
    id: 'wallet',
    icon: <FaWallet className="w-4 h-4" />,
    label: 'Wallet',
    path: '/dashboard/wallet',
  },
  {
    id: 'premium',
    icon: <FaBolt className="w-4 h-4" />,
    label: 'Premium',
    path: '/dashboard/premium',
  },
  {
    id: 'verse',
    icon: <FaGlobe className="w-4 h-4" />,
    label: 'Verse',
    path: '/verse',
  },
];

// Styles for tab items based on active state
const getTabItemStyles = (isActive: boolean) => ({
  container: isActive
    ? 'text-white'
    : 'text-white/50 hover:text-white/80 transition-colors duration-300',
  icon: isActive
    ? 'text-accent-400 mb-1 relative z-10 filter drop-shadow-[0_0_3px_rgba(168,85,247,0.4)]'
    : 'text-white/60 mb-1 transition-colors duration-300 group-hover:text-white/80',
  label: isActive
    ? 'text-xs font-medium text-transparent bg-clip-text bg-gradient-to-r from-accent-200 to-accent-400'
    : 'text-xs font-medium text-white/60 group-hover:text-white/80 transition-colors duration-300',
});

const BottomNavigation: React.FC<BottomNavigationProps> = memo(
  ({ activeTab, onTabChange, className = '', style }) => {
    // Handle navigation clicks
    const handleTabClick = useCallback(
      (item: NavItem) => {
        if (item.id === 'verse') {
          // Navigate directly to verse page
          window.location.href = item.path;
        } else if (item.id !== activeTab) {
          // For dashboard tabs, use Astro navigation
          window.location.href = item.path;
        }
      },
      [activeTab]
    );

    // Pre-calculate styles based on active tab
    const tabStyles = useMemo(() => {
      return NAV_ITEMS.map(item => ({
        id: item.id,
        isActive: item.id !== 'verse' && activeTab === item.id,
        className: `relative flex flex-col items-center justify-center min-w-[3rem] py-1
                 transition-all duration-200 ${
                   item.id !== 'verse' && activeTab === item.id
                     ? 'text-accent-400'
                     : 'text-white/60 hover:text-white'
                 }`,
      }));
    }, [activeTab]);

    return (
      <nav 
        className={`relative ${className}`}
        style={style}
        role="navigation"
        aria-label="Main navigation"
      >
        <div className="relative h-full">
          {/* Subtle shine effect */}
          <div className="absolute top-0 inset-x-0 h-0.5 bg-gradient-to-r from-transparent via-accent-500/10 to-transparent">
            <div className="absolute inset-0 flex justify-center">
              <div className="w-1/3 h-full relative overflow-hidden">
                <div className="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-transparent via-white/10 to-transparent animate-shine-left" />
              </div>
              <div className="w-1/3 h-full relative overflow-hidden">
                <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shine-right" />
              </div>
            </div>
          </div>

          <div className="container mx-auto px-2 h-full">
            <div className="flex justify-around items-center h-full">
              {NAV_ITEMS.map(item => {
                const isActive = item.id !== 'verse' && item.id === activeTab;
                const tabItemStyles = getTabItemStyles(isActive);

                return (
                  <button
                    key={item.id}
                    className={`relative flex flex-col items-center justify-center h-full w-full group ${tabItemStyles.container}`}
                    onClick={() => handleTabClick(item)}
                    aria-selected={isActive}
                    aria-label={`Navigate to ${item.label}`}
                    style={{ transition: 'background-color 0.25s ease' }}
                  >
                    {/* Active indicator background */}
                    {isActive && (
                      <div className="absolute inset-0 bg-gradient-to-t from-accent-500/15 via-transparent to-transparent rounded-lg opacity-100 transition-opacity duration-300" />
                    )}

                    {/* Icon and label container */}
                    <div className="relative flex flex-col items-center justify-center">
                      <span className={tabItemStyles.icon}>{item.icon}</span>
                      
                      {/* Active glowing indicator */}
                      {isActive && (
                        <div className="absolute -bottom-2 left-1/2 -translate-x-1/2 w-1 h-1 bg-accent-300 rounded-full shadow-[0_0_6px_2px_theme(colors.accent.300 / 0.7)]" />
                      )}
                    </div>
                    <span className={`${tabItemStyles.label} relative z-10`}>{item.label}</span>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
      </nav>
    );
  }
);

BottomNavigation.displayName = 'BottomNavigation';

export default BottomNavigation;
