declare global {
  interface Window {
    Telegram?: {
      WebApp?: TelegramWebApp;
    };
  }
}

export interface TelegramWebApp {
  initData: string;
  initDataUnsafe: WebAppInitData;
  version: string;
  platform: 'ios' | 'android' | 'web';
  colorScheme: string;
  themeParams: ThemeParams;
  isExpanded: boolean;
  viewportHeight: number;
  viewportStableHeight: number;
  headerColor: string;
  backgroundColor: string;
  isClosingConfirmationEnabled: boolean;
  BackButton: WebAppBackButton;
  MainButton: WebAppMainButton;
  SettingsButton: WebAppSettingsButton;
  HapticFeedback: {
    impact: (style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft') => void;
    notification: (type: 'success' | 'error' | 'warning') => void;
  };
  CloudStorage: WebAppCloudStorage;
  ViewportManager: WebAppViewportManager;
  FullscreenManager: WebAppFullscreenManager;
  HomeScreenManager: WebAppHomeScreenManager;
  LocationManager: WebAppLocationManager;
  ScanQrManager: WebAppScanQrManager;
  EmojiManager: WebAppEmojiManager;
  FileManager: WebAppFileManager;
  ShareManager: WebAppShareManager;
  InvoiceManager: WebAppInvoiceManager;
  contentSafeAreaInset: { top: number; right: number; bottom: number; left: number };
  isVersionAtLeast(version: string): boolean;
  setHeaderColor(color: string): void;
  setBackgroundColor(color: string): void;
  enableClosingConfirmation(): void;
  disableClosingConfirmation(): void;
  onEvent(eventType: WebAppEventType, eventHandler: Function): void;
  offEvent(eventType: WebAppEventType, eventHandler: Function): void;
  sendData(data: any): void;
  ready(): void;
  expand(): void;
  close(): void;
  openLink(url: string): void;
  showPopup(params: PopupParams): void;
  showAlert(message: string): void;
  showConfirm(message: string): Promise<boolean>;
  switchInlineQuery(query: string, choose_chat_types?: string[]): void;
  shareMessage(messageOrId: PreparedInlineMessage | string): Promise<boolean>;
  savePreparedInlineMessage(params: {
    user_id: number;
    result: InlineQueryResultArticle;
    allow_user_chats?: boolean;
    allow_bot_chats?: boolean;
    allow_group_chats?: boolean;
    allow_channel_chats?: boolean;
  }): Promise<PreparedInlineMessage>;
  disableVerticalSwipes(): void;
  enableVerticalSwipes(): void;
}

export interface WebAppInitData {
  query_id?: string;
  user?: WebAppUser;
  receiver?: WebAppUser;
  chat?: WebAppChat;
  chat_type?: string;
  chat_instance?: string;
  start_param?: string; // Added missing start_param field. Parameter passed via startapp link.
  can_send_after?: number;
  auth_date: number;
  hash: string;
  device_id?: string;
  version?: string;
}

export interface WebAppUser {
  id: number;
  is_bot?: boolean;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
  is_premium?: boolean;
  photo_url?: string;
  platform?: 'ios' | 'android' | 'web';
}

export interface WebAppChat {
  id: number;
  type: string;
  title: string;
  username?: string;
  photo_url?: string;
}

export interface ThemeParams {
  bg_color: string;
  text_color: string;
  hint_color: string;
  link_color: string;
  button_color: string;
  button_text_color: string;
  secondary_bg_color: string;
}

export interface WebAppBackButton {
  isVisible: boolean;
  onClick(callback: Function): void;
  offClick(callback: Function): void;
  show(): void;
  hide(): void;
}

export interface WebAppMainButton {
  text: string;
  color: string;
  textColor: string;
  isVisible: boolean;
  isActive: boolean;
  isProgressVisible: boolean;
  setText(text: string): void;
  onClick(callback: Function): void;
  offClick(callback: Function): void;
  show(): void;
  hide(): void;
  enable(): void;
  disable(): void;
  showProgress(leaveActive: boolean): void;
  hideProgress(): void;
}

export interface WebAppHapticFeedback {
  impactOccurred(style: 'light' | 'medium' | 'heavy' | 'rigid' | 'soft'): void;
  notificationOccurred(type: 'error' | 'success' | 'warning'): void;
  selectionChanged(): void;
}

export interface PopupParams {
  title?: string;
  message: string;
  buttons?: Array<{
    id: string;
    type?: 'ok' | 'close' | 'cancel' | 'destructive';
    text: string;
  }>;
}

// Additional managers and types
export interface WebAppFullscreenManager {
  isFullscreen: boolean;
  isSupported: boolean;
  requestFullscreen(): Promise<void>;
  exitFullscreen(): Promise<void>;
}

export interface WebAppHomeScreenManager {
  isSupported: boolean;
  isAdded: boolean;
  addToHomeScreen(): Promise<void>;
  checkHomeScreenStatus(): Promise<{ isSupported: boolean; isAdded: boolean }>;
}

export interface WebAppLocationManager {
  isAvailable: boolean;
  requestLocation(): Promise<{ latitude: number; longitude: number }>;
  stopWatchingLocation(): void;
}

export interface WebAppScanQrManager {
  isAvailable: boolean;
  showScanQrPopup(params: { text?: string }): Promise<string>;
  closeScanQrPopup(): void;
}

export interface WebAppEmojiManager {
  isSupported: boolean;
  setEmojiStatus(emoji_id: string): Promise<void>;
  requestEmojiStatusAccess(): Promise<boolean>;
}

export interface WebAppFileManager {
  isSupported: boolean;
  downloadFile(file_id: string): Promise<Blob>;
}

export interface MessageEntity {
  type:
    | 'text_link'
    | 'text_mention'
    | 'bold'
    | 'italic'
    | 'underline'
    | 'strikethrough'
    | 'code'
    | 'pre';
  offset: number;
  length: number;
  url?: string;
  user?: WebAppUser;
  language?: string;
}

export interface PreparedInlineMessage {
  message_text: string;
  parse_mode?: 'HTML' | 'MarkdownV2';
  disable_web_page_preview?: boolean;
  link_preview_options?: {
    is_disabled?: boolean;
    url?: string;
    prefer_large_media?: boolean;
    prefer_small_media?: boolean;
    show_above_text?: boolean;
  };
  entities?: MessageEntity[];
  reply_markup?: {
    inline_keyboard: Array<
      Array<{
        text: string;
        url?: string;
        callback_data?: string;
      }>
    >;
  };
}

export interface WebAppShareData {
  url?: string;
  text?: string;
  title?: string;
  files?: File[];
}

export interface WebAppShareManager {
  shareMessage(message: PreparedInlineMessage): Promise<boolean>;
  shareData(data: WebAppShareData): Promise<boolean>;
}

export interface WebAppCloudStorage {
  setItem(key: string, value: string): Promise<void>;
  getItem(key: string): Promise<string | null>;
  removeItem(key: string): Promise<void>;
  getKeys(): Promise<string[]>;
  removeItems(keys: string[]): Promise<void>;
  clear(): Promise<void>;
}

export interface WebAppInvoiceManager {
  isSupported: boolean;
  openInvoice(url: string): Promise<'paid' | 'cancelled' | 'failed' | 'pending'>;
}

export interface WebAppViewportManager {
  isStable: boolean;
  currentHeight: number;
  stableHeight: number;
  safeAreaInset: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
  contentSafeAreaInset: {
    top: number;
    right: number;
    bottom: number;
    left: number;
  };
}

export interface WebAppSettingsButton {
  isVisible: boolean;
  onClick(callback: Function): void;
  offClick(callback: Function): void;
  show(): void;
  hide(): void;
}

// Event types for onEvent and offEvent
export type WebAppEventType =
  | 'viewportChanged'
  | 'backButtonClicked'
  | 'settingsButtonClicked'
  | 'mainButtonClicked'
  | 'invoiceClosed'
  | 'popupClosed'
  | 'qrTextReceived'
  | 'clipboardTextReceived'
  | 'themeChanged'
  | 'activated'
  | 'deactivated'
  | 'contentSafeAreaChanged'
  | 'fullscreenChanged'
  | 'fullscreenFailed'
  | 'homeScreenAdded'
  | 'homeScreenChecked'
  | 'emojiStatusSet'
  | 'emojiStatusFailed'
  | 'emojiStatusAccessRequested'
  | 'locationRequested'
  | 'locationReceived';

export type { TelegramWebApp as Telegram };

export interface InlineQueryResultArticle {
  type: 'article';
  id: string;
  title: string;
  description?: string;
  url?: string;
  hide_url?: boolean;
  thumb_url?: string;
  thumb_width?: number;
  thumb_height?: number;
  input_message_content: {
    message_text: string;
    parse_mode?: 'HTML' | 'MarkdownV2';
    disable_web_page_preview?: boolean;
  };
  reply_markup?: {
    inline_keyboard: Array<
      Array<{
        text: string;
        url?: string;
        callback_data?: string;
      }>
    >;
  };
}
