-- Drop tables that are no longer needed
DROP TABLE IF EXISTS task_verifications;
DROP TABLE IF EXISTS social_tasks;

-- Add platform fields to tasks table if they don't exist
ALTER TABLE tasks ADD COLUMN platform_url TEXT;
ALTER TABLE tasks ADD COLUMN platform_id VARCHAR(100);
ALTER TABLE tasks ADD COLUMN verify_key VARCHAR(100);
ALTER TABLE tasks ADD COLUMN max_verification_attempts INTEGER DEFAULT 3;

-- Create index for platform lookups
CREATE INDEX IF NOT EXISTS idx_task_platform ON tasks(platform_id, type);