/**
 * Simplified Secure Storage for Zustand
 * Lightweight alternative to encrypted storage for essential state persistence
 */

import type { StateStorage } from 'zustand/middleware';

// Simple encryption/decryption using base64 and basic obfuscation
const STORAGE_KEY_PREFIX = 'vipverse_';
const OBFUSCATION_KEY = 'atlas_vpn_secure_2024';

// Simple obfuscation function
function obfuscate(text: string): string {
  let result = '';
  for (let i = 0; i < text.length; i++) {
    const charCode = text.charCodeAt(i) ^ OBFUSCATION_KEY.charCodeAt(i % OBFUSCATION_KEY.length);
    result += String.fromCharCode(charCode);
  }
  return btoa(result);
}

// Simple deobfuscation function
function deobfuscate(obfuscated: string): string {
  try {
    const decoded = atob(obfuscated);
    let result = '';
    for (let i = 0; i < decoded.length; i++) {
      const charCode = decoded.charCodeAt(i) ^ OBFUSCATION_KEY.charCodeAt(i % OBFUSCATION_KEY.length);
      result += String.fromCharCode(charCode);
    }
    return result;
  } catch {
    return '';
  }
}

// Create secure storage adapter
function createSecureStorage(useSessionStorage = false): StateStorage {
  const storage = useSessionStorage ? sessionStorage : localStorage;
  
  return {
    getItem: (name: string): string | null => {
      try {
        const key = STORAGE_KEY_PREFIX + name;
        const obfuscatedData = storage.getItem(key);
        if (!obfuscatedData) return null;
        
        const deobfuscatedData = deobfuscate(obfuscatedData);
        return deobfuscatedData || null;
      } catch (error) {
        console.warn(`[SecureStorage] Failed to get item: ${name}`, error);
        return null;
      }
    },
    
    setItem: (name: string, value: string): void => {
      try {
        const key = STORAGE_KEY_PREFIX + name;
        const obfuscatedData = obfuscate(value);
        storage.setItem(key, obfuscatedData);
      } catch (error) {
        console.error(`[SecureStorage] Failed to set item: ${name}`, error);
      }
    },
    
    removeItem: (name: string): void => {
      try {
        const key = STORAGE_KEY_PREFIX + name;
        storage.removeItem(key);
      } catch (error) {
        console.error(`[SecureStorage] Failed to remove item: ${name}`, error);
      }
    },
  };
}

// Export storage creators
export const createSecureJSONStorage = (useSessionStorage = false) => {
  return () => createSecureStorage(useSessionStorage);
};

export const secureLocalStorage = createSecureJSONStorage(false);
export const secureSessionStorage = createSecureJSONStorage(true);

export default {
  createSecureJSONStorage,
  secureLocalStorage,
  secureSessionStorage,
};