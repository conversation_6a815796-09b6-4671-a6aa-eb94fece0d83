#!/usr/bin/env python
"""
Security Headers Test
-------------------
Test security headers across all endpoints.
"""
import logging
from typing import Dict, Any, List, Tuple
from test_utils import (
    print_header,
    print_success,
    print_error,
    print_warning,
    print_info,
    log_request_details,
    log_error_with_traceback,
    verify_security_headers,
    make_request,
    get_critical_endpoints,
    log_test_result,
    log_environment_info
)
from test_config import API_BASE_URL, REQUIRED_HEADERS

logger = logging.getLogger(__name__)

def test_security_headers() -> Dict[str, Any]:
    """Test security headers for all endpoints"""
    print_header("Testing Security Headers")
    
    # Log environment info for debugging
    log_environment_info()
    
    # Setup results dictionary
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "missing_headers": {},
        "invalid_values": {},
        "errors": []
    }
    
    # Get all critical endpoints
    endpoints = get_critical_endpoints()
    print_info(f"Testing {len(endpoints)} endpoints for security headers")
    results["total_tests"] = len(endpoints)
    
    # Test each endpoint
    for endpoint in endpoints:
        # Create URL for endpoint (replace placeholders)
        url = f"{API_BASE_URL}{endpoint.replace('{task_id}', '1').replace('{panel_id}', '1').replace('{subscription_url}', 'test').replace('{code}', 'test')}"
        
        try:
            # Make request
            success, response = make_request(url)
            
            if not success:
                results["failed_tests"] += 1
                results["errors"].append(f"Failed to connect to {url}: {response.get('error')}")
                continue
            
            # Log request and response details
            log_request_details(None, response)
            
            # Verify security headers
            is_valid, header_results = verify_security_headers(response["headers"], REQUIRED_HEADERS)
            
            if is_valid:
                results["successful_tests"] += 1
                print_success(f"Security headers verified for {endpoint}")
            else:
                results["failed_tests"] += 1
                print_error(f"Security headers failed for {endpoint}")
                
                # Record missing headers
                if header_results["missing_headers"]:
                    results["missing_headers"][endpoint] = header_results["missing_headers"]
                
                # Record invalid values
                if header_results["invalid_values"]:
                    results["invalid_values"][endpoint] = header_results["invalid_values"]
                
                # Log detailed failure information
                for detail in header_results["details"]:
                    print_warning(f"  {detail}")
            
            # Log test result
            log_test_result(
                f"Security Headers - {endpoint}",
                is_valid,
                header_results if not is_valid else None
            )
            
        except Exception as e:
            results["failed_tests"] += 1
            error_msg = f"Error testing {endpoint}: {str(e)}"
            results["errors"].append(error_msg)
            print_error(error_msg)
            log_error_with_traceback(e, f"Testing endpoint {endpoint}")
    
    # Calculate success rate
    success_rate = (results["successful_tests"] / results["total_tests"] * 100) if results["total_tests"] > 0 else 0
    
    # Print summary
    print_header("Security Headers Test Summary")
    print_info(f"Total Endpoints Tested: {results['total_tests']}")
    print_info(f"Successful Tests: {results['successful_tests']}")
    print_info(f"Failed Tests: {results['failed_tests']}")
    print_info(f"Success Rate: {success_rate:.2f}%")
    
    if results["errors"]:
        print_warning("\nErrors Encountered:")
        for error in results["errors"]:
            print_warning(f"  {error}")
    
    if results["missing_headers"]:
        print_warning("\nMissing Headers:")
        for endpoint, headers in results["missing_headers"].items():
            print_warning(f"  {endpoint}:")
            for header in headers:
                print_warning(f"    - {header}")
    
    if results["invalid_values"]:
        print_warning("\nInvalid Header Values:")
        for endpoint, invalid_headers in results["invalid_values"].items():
            print_warning(f"  {endpoint}:")
            for header in invalid_headers:
                print_warning(
                    f"    - {header['header']}: "
                    f"expected '{header['expected']}', got '{header['actual']}'"
                )
    
    return results

if __name__ == "__main__":
    test_security_headers() 