{"tables": {"daily_task_streaks": {"columns": ["id", "user_id", "current_streak", "longest_streak", "last_check_in", "total_check_ins", "current_cycle_day", "created_at", "first_check_time"], "primary_keys": ["id"], "indexes": [{"name": "idx_user_cycle", "columns": ["user_id", "current_cycle_day", "last_check_in"], "unique": 0}, {"name": "idx_user_streak", "columns": ["user_id", "last_check_in"], "unique": 0}], "foreign_keys": [{"referred_table": "users", "referred_columns": ["id"], "constrained_columns": ["user_id"]}], "issues": []}, "marzban_panels": {"columns": ["id", "name", "api_url", "admin_username", "admin_password", "is_active", "created_at"], "primary_keys": ["id"], "indexes": [{"name": "ix_marzban_panels_id", "columns": ["id"], "unique": 0}], "foreign_keys": [], "issues": []}, "migrations_history": {"columns": ["id", "migration_name", "applied_at", "success", "error_message"], "primary_keys": ["id"], "indexes": [], "foreign_keys": [], "issues": ["No indexes defined"]}, "package_panel_association": {"columns": ["package_id", "panel_id"], "primary_keys": [], "indexes": [], "foreign_keys": [{"referred_table": "vpn_packages", "referred_columns": ["id"], "constrained_columns": ["package_id"]}, {"referred_table": "marzban_panels", "referred_columns": ["id"], "constrained_columns": ["panel_id"]}], "issues": ["No primary key defined", "No indexes defined"]}, "reward_revocations": {"columns": ["id", "task_completion_id", "revoked_at", "reward_type", "revoked_amount", "reason", "wallet_balance_after"], "primary_keys": ["id"], "indexes": [{"name": "ix_reward_revocations_id", "columns": ["id"], "unique": 0}], "foreign_keys": [{"referred_table": "task_completions", "referred_columns": ["id"], "constrained_columns": ["task_completion_id"]}], "issues": []}, "social_tasks": {"columns": ["id", "task_id", "platform", "platform_id", "platform_url", "required_duration", "verification_type", "created_at", "is_active", "verify_key", "auto_complete_after", "max_verification_attempts", "last_verification_time", "verification_interval"], "primary_keys": ["id"], "indexes": [{"name": "ix_social_tasks_id", "columns": ["id"], "unique": 0}], "foreign_keys": [{"referred_table": "tasks", "referred_columns": ["id"], "constrained_columns": ["task_id"]}], "issues": []}, "system_configs": {"columns": ["id", "key", "value", "description", "updated_at"], "primary_keys": ["id"], "indexes": [], "foreign_keys": [], "issues": ["No indexes defined"]}, "task_completions": {"columns": ["id", "user_id", "task_id", "completed_at", "current_progress", "is_claimed", "claimed_at", "status", "last_verified_at", "auto_verify_at", "streak_day", "verification_attempts"], "primary_keys": ["id"], "indexes": [{"name": "idx_task_completions_status", "columns": ["status"], "unique": 0}, {"name": "idx_task_completions_task_id", "columns": ["task_id"], "unique": 0}, {"name": "idx_task_completions_user_id", "columns": ["user_id"], "unique": 0}, {"name": "idx_task_verification_completion", "columns": ["task_id", "status", "last_verified_at"], "unique": 0}, {"name": "idx_user_completion", "columns": ["user_id", "is_claimed", "status"], "unique": 0}, {"name": "idx_user_task_status", "columns": ["user_id", "task_id", "status"], "unique": 0}, {"name": "ix_task_completions_id", "columns": ["id"], "unique": 0}], "foreign_keys": [{"referred_table": "users", "referred_columns": ["id"], "constrained_columns": ["user_id"]}, {"referred_table": "tasks", "referred_columns": ["id"], "constrained_columns": ["task_id"]}], "issues": []}, "task_pack_tasks": {"columns": ["pack_id", "task_id", "order"], "primary_keys": ["pack_id", "task_id"], "indexes": [{"name": "idx_pack_task_order", "columns": ["pack_id", "order"], "unique": 0}], "foreign_keys": [{"referred_table": "task_packs", "referred_columns": ["id"], "constrained_columns": ["pack_id"]}, {"referred_table": "tasks", "referred_columns": ["id"], "constrained_columns": ["task_id"]}], "issues": []}, "task_packs": {"columns": ["id", "name", "description", "reward_package_id", "is_lifetime", "auto_renewal", "required_duration", "is_active", "created_at"], "primary_keys": ["id"], "indexes": [], "foreign_keys": [{"referred_table": "vpn_packages", "referred_columns": ["id"], "constrained_columns": ["reward_package_id"]}], "issues": ["No indexes defined"]}, "task_verifications": {"columns": ["id", "task_completion_id", "verified_at", "status", "platform_data", "next_verification"], "primary_keys": ["id"], "indexes": [{"name": "ix_task_verifications_id", "columns": ["id"], "unique": 0}], "foreign_keys": [{"referred_table": "task_completions", "referred_columns": ["id"], "constrained_columns": ["task_completion_id"]}], "issues": []}, "tasks": {"columns": ["id", "name", "description", "type", "target_value", "reward_type", "reward_value", "verification_frequency", "allow_reward_revocation", "minimum_duration", "is_active", "daily_multiplier", "streak_requirement", "auto_verify_delay", "verification_type", "created_at"], "primary_keys": ["id"], "indexes": [{"name": "idx_task_reward", "columns": ["reward_type", "reward_value"], "unique": 0}, {"name": "idx_task_type_active", "columns": ["type", "is_active"], "unique": 0}, {"name": "idx_task_verification", "columns": ["type", "verification_type", "is_active"], "unique": 0}], "foreign_keys": [], "issues": []}, "transactions": {"columns": ["id", "type", "amount", "created_at", "user_id", "reseller_id", "subscription_id", "package_name", "package_price", "package_data_limit", "package_expire_days", "description", "balance_after", "status"], "primary_keys": ["id"], "indexes": [{"name": "ix_transactions_id", "columns": ["id"], "unique": 0}], "foreign_keys": [{"referred_table": "users", "referred_columns": ["id"], "constrained_columns": ["user_id"]}, {"referred_table": "users", "referred_columns": ["id"], "constrained_columns": ["reseller_id"]}, {"referred_table": "vpn_subscriptions", "referred_columns": ["id"], "constrained_columns": ["subscription_id"]}], "issues": []}, "users": {"columns": ["id", "telegram_id", "username", "hashed_password", "role", "wallet_balance", "created_at", "first_name", "last_name", "email", "is_active", "telegram_photo_url", "discount_percent", "marzban_username", "marzban_subscription_url", "referred_by", "referral_code", "device_platform", "device_id", "last_login"], "primary_keys": ["id"], "indexes": [{"name": "ix_users_id", "columns": ["id"], "unique": 0}, {"name": "ix_users_telegram_id", "columns": ["telegram_id"], "unique": 1}, {"name": "ix_users_username", "columns": ["username"], "unique": 1}], "foreign_keys": [{"referred_table": "users", "referred_columns": ["id"], "constrained_columns": ["referred_by"]}], "issues": []}, "vpn_packages": {"columns": ["id", "name", "data_limit", "expire_days", "price", "description", "created_at", "is_active"], "primary_keys": ["id"], "indexes": [{"name": "ix_vpn_packages_id", "columns": ["id"], "unique": 0}], "foreign_keys": [], "issues": []}, "vpn_subscriptions": {"columns": ["id", "user_id", "package_id", "marzban_username", "subscription_url", "created_at", "expires_at", "data_limit", "data_used", "is_active"], "primary_keys": ["id"], "indexes": [{"name": "ix_vpn_subscriptions_id", "columns": ["id"], "unique": 0}], "foreign_keys": [{"referred_table": "users", "referred_columns": ["id"], "constrained_columns": ["user_id"]}, {"referred_table": "vpn_packages", "referred_columns": ["id"], "constrained_columns": ["package_id"]}], "issues": []}}, "security": {"vulnerabilities": [], "timing_attacks": [], "injection_tests": [{"test": "User login", "pattern": "' OR '1'='1", "status": "potentially_vulnerable", "execution_time": 0.000412}, {"test": "User login", "pattern": "'; DROP TABLE users; --", "status": "potentially_vulnerable", "execution_time": 0.000487}, {"test": "User login", "pattern": "' UNION SELECT password_hash FROM users; --", "status": "potentially_vulnerable", "execution_time": 0.000153}, {"test": "User login", "pattern": "admin'--", "status": "potentially_vulnerable", "execution_time": 0.000138}, {"test": "User login", "pattern": "' OR id IS NOT NULL; --", "status": "potentially_vulnerable", "execution_time": 0.00015}, {"test": "User login", "pattern": "'; UPDATE users SET role='admin'--", "status": "potentially_vulnerable", "execution_time": 0.000179}, {"test": "User login", "pattern": "' AND (SELECT sleep(1))='", "status": "potentially_vulnerable", "execution_time": 0.000191}, {"test": "User login", "pattern": "' AND (SELECT COUNT(*) FROM users)>0--", "status": "potentially_vulnerable", "execution_time": 0.000133}, {"test": "User login", "pattern": "'; BEGIN TRANSACTION; ROLLBACK; --", "status": "potentially_vulnerable", "execution_time": 0.000217}, {"test": "Task search", "pattern": "' OR '1'='1", "status": "potentially_vulnerable", "execution_time": 0.00046}, {"test": "Task search", "pattern": "'; DROP TABLE users; --", "status": "potentially_vulnerable", "execution_time": 0.000214}, {"test": "Task search", "pattern": "' UNION SELECT password_hash FROM users; --", "status": "potentially_vulnerable", "execution_time": 0.000156}, {"test": "Task search", "pattern": "admin'--", "status": "potentially_vulnerable", "execution_time": 0.000148}, {"test": "Task search", "pattern": "' OR id IS NOT NULL; --", "status": "potentially_vulnerable", "execution_time": 0.000187}, {"test": "Task search", "pattern": "'; UPDATE users SET role='admin'--", "status": "potentially_vulnerable", "execution_time": 0.000206}, {"test": "Task search", "pattern": "' AND (SELECT sleep(1))='", "status": "potentially_vulnerable", "execution_time": 0.000277}, {"test": "Task search", "pattern": "' AND (SELECT COUNT(*) FROM users)>0--", "status": "potentially_vulnerable", "execution_time": 0.000158}, {"test": "Task search", "pattern": "'; BEGIN TRANSACTION; ROLLBACK; --", "status": "potentially_vulnerable", "execution_time": 0.000143}, {"test": "Profile lookup", "pattern": "' OR '1'='1", "status": "potentially_vulnerable", "execution_time": 0.000361}, {"test": "Profile lookup", "pattern": "'; DROP TABLE users; --", "status": "potentially_vulnerable", "execution_time": 0.00022}, {"test": "Profile lookup", "pattern": "' UNION SELECT password_hash FROM users; --", "status": "potentially_vulnerable", "execution_time": 0.000215}, {"test": "Profile lookup", "pattern": "admin'--", "status": "potentially_vulnerable", "execution_time": 0.000269}, {"test": "Profile lookup", "pattern": "' OR id IS NOT NULL; --", "status": "potentially_vulnerable", "execution_time": 0.000185}, {"test": "Profile lookup", "pattern": "'; UPDATE users SET role='admin'--", "status": "potentially_vulnerable", "execution_time": 0.000162}, {"test": "Profile lookup", "pattern": "' AND (SELECT sleep(1))='", "status": "potentially_vulnerable", "execution_time": 0.000203}, {"test": "Profile lookup", "pattern": "' AND (SELECT COUNT(*) FROM users)>0--", "status": "potentially_vulnerable", "execution_time": 0.00021}, {"test": "Profile lookup", "pattern": "'; BEGIN TRANSACTION; ROLLBACK; --", "status": "potentially_vulnerable", "execution_time": 0.000251}], "password_security": {}, "access_control": {"public_tables": ["marzban_panels", "migrations_history", "package_panel_association", "reward_revocations", "social_tasks", "system_configs", "task_pack_tasks", "task_packs", "task_verifications", "tasks", "vpn_packages"], "role_based_tables": ["users"]}, "data_exposure": []}, "data_integrity": {"duplicate_records": {"migrations_history": 3}, "orphaned_records": {"transactions_to_users": 55, "transactions_to_vpn_subscriptions": 118, "users_to_users": 95}}, "performance": {"tables_without_indexes": ["migrations_history", "package_panel_association", "system_configs", "task_packs"]}, "timestamp": "2025-01-02T06:31:49.476368"}