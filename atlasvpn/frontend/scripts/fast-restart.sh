#!/bin/bash

# Fast restart script for VIPVerse frontend development

echo "🚀 VIPVerse Fast Restart"
echo "========================"

# Kill existing processes
echo "Stopping existing processes..."
pkill -f "vite.*3000" || true
sleep 2

# Clean Vite cache
echo "Cleaning Vite cache..."
rm -rf node_modules/.vite/deps*
rm -rf dist

# Start development server
echo "Starting optimized development server..."
NODE_ENV=development pnpm dev

echo "✅ Development server started!"
echo "🌐 Frontend: https://dev.atlasvip.cloud/"
echo "🔧 Backend: https://dev.atlasvip.cloud:2083/" 