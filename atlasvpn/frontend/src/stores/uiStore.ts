import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { createSecureJSONStorage } from '../utils/secureZustandStorage';

export interface UiState {
  // Theme
  currentTheme: 'light' | 'dark';
  setTheme: (theme: 'light' | 'dark') => void;

  // Welcome states
  showWelcomeMessage: boolean;
  dismissWelcomeMessage: () => void;
  showWelcomeBackSplash: boolean;
  setShowWelcomeBackSplash: (show: boolean) => void;
  lastSplashCheck: number; // Timestamp

  // Visuals
  backgroundVisible: boolean;
  setBackgroundVisible: (visible: boolean) => void;

  // Layout/Navigation
  homeTabScrollPosition: number;
  setHomeTabScrollPosition: (position: number) => void;

  // App lifecycle
  isBackgrounded: boolean;
  setIsBackgrounded: (isBackgrounded: boolean) => void;

  // Potentially other UI states like global loading indicators if not tied to specific queries
}

export const useUiStore = create<UiState>()(
  persist(
    (set, get) => ({
      // Initial State
      currentTheme: 'dark',
      showWelcomeMessage: true,
      showWelcomeBackSplash: false,
      lastSplashCheck: 0,
      backgroundVisible: true,
      homeTabScrollPosition: 0,
      isBackgrounded: false,

      // Actions
      setTheme: theme => {
        set({ currentTheme: theme });
        if (theme === 'dark') {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      },
      dismissWelcomeMessage: () => set({ showWelcomeMessage: false }),
      setShowWelcomeBackSplash: show =>
        set({ showWelcomeBackSplash: show, lastSplashCheck: Date.now() }),
      setBackgroundVisible: visible => set({ backgroundVisible: visible }),
      setHomeTabScrollPosition: position => set({ homeTabScrollPosition: position }),
      setIsBackgrounded: isBackgrounded => set({ isBackgrounded }),
    }),
    {
      name: 'atlas-ui-storage', // Unique name for encrypted localStorage
      storage: createJSONStorage(() => createSecureJSONStorage()()),
      // Persist all states defined in UiState by default,
      // or specify a partialize function if needed:
      // partialize: (state) => ({
      //   currentTheme: state.currentTheme,
      //   showWelcomeMessage: state.showWelcomeMessage,
      //   // ... etc.
      // }),
    }
  )
);

// Initialize theme on load
const initialTheme = useUiStore.getState().currentTheme;
if (initialTheme === 'dark') {
  document.documentElement.classList.add('dark');
} else {
  document.documentElement.classList.remove('dark');
}
