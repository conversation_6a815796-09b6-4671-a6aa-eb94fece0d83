import redis.asyncio as redis
import os
import logging
from dotenv import load_dotenv
import time
import asyncio
from typing import Dict, Any, Optional, List
from urllib.parse import urlparse

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class RedisClient:
    def __init__(self):
        # Parse configuration from REDIS_URL
        redis_url = os.getenv("REDIS_URL", "redis://atlasvpn-redis:6379/0")
        parsed_url = urlparse(redis_url)
        
        self.redis_host = parsed_url.hostname or "localhost"
        self.redis_port = parsed_url.port or 6379
        self.redis_password = parsed_url.password
        self.redis_db = int(parsed_url.path.strip('/') ) if parsed_url.path else 0
        self.max_connections = int(os.getenv("REDIS_MAX_CONNECTIONS", "10"))
        self.connection_timeout = int(os.getenv("REDIS_CONNECTION_TIMEOUT", "5"))
        
        # Track client status
        self.is_connected = False
        self.last_error = None
        self.client = None
        self.health_check_interval = 30  # seconds
        self._health_check_task = None
        self._active_connections = {}
        self._connection_count = 0
        self._connection_lock = asyncio.Lock()
        
        logger.info(f"Redis client initialized with host={self.redis_host}, port={self.redis_port}, "
                   f"db={self.redis_db}, max_connections={self.max_connections}")
        
    async def connect(self):
        """Connect to Redis server with connection pooling"""
        try:
            if self.client is not None:
                logger.info("Redis client already connected")
                return
                
            # Create Redis client with connection pool
            pool = redis.ConnectionPool(
                host=self.redis_host,
                port=self.redis_port,
                password=self.redis_password,
                db=self.redis_db,
                max_connections=self.max_connections
            )
            
            self.client = redis.Redis(
                connection_pool=pool,
                socket_timeout=self.connection_timeout,
                socket_connect_timeout=self.connection_timeout,
                socket_keepalive=True,
                health_check_interval=15,  # automatic health check every 15 seconds
                retry_on_timeout=True,
                retry_on_error=[redis.ConnectionError]
            )
            
            # Verify connection
            await self.client.ping()
            self.is_connected = True
            self.last_error = None
            
            # Start health check
            self._start_health_check()
            
            logger.info("Successfully connected to Redis server")
        except Exception as e:
            self.is_connected = False
            self.last_error = str(e)
            logger.error(f"Failed to connect to Redis: {str(e)}")
            if self.client:
                await self.client.close()
                self.client = None
            raise
            
    async def initialize(self):
        """Initialize Redis client - alias for connect for compatibility"""
        await self.connect()
        return self.client
        
    async def get_client(self):
        """Get the Redis client instance, connecting if needed"""
        if not self.client:
            await self.connect()
        return self.client
        
    async def close(self):
        """Close Redis connection and clean up resources"""
        try:
            # Stop health check
            if self._health_check_task and not self._health_check_task.done():
                self._health_check_task.cancel()
                try:
                    await self._health_check_task
                except asyncio.CancelledError:
                    pass
                
            # Close client
            if self.client:
                await self.client.close()
                self.client = None
                
            self.is_connected = False
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {str(e)}")
            
    def _start_health_check(self):
        """Start background task for health checking"""
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        
    async def _health_check_loop(self):
        """Background task to periodically check Redis connection health"""
        try:
            while True:
                await asyncio.sleep(self.health_check_interval)
                try:
                    if self.client:
                        # Check if connection is still working
                        await self.client.ping()
                        
                        # Get pool stats
                        pool_stats = await self.get_connection_stats()
                        if pool_stats["active_connections"] > (self.max_connections * 0.8):
                            logger.warning(f"Redis connection pool nearing capacity: {pool_stats}")
                except Exception as e:
                    logger.error(f"Redis health check failed: {str(e)}")
                    self.is_connected = False
                    self.last_error = str(e)
                    # Try to reconnect
                    try:
                        if self.client:
                            await self.client.close()
                        self.client = None
                        await self.connect()
                    except Exception as reconnect_error:
                        logger.error(f"Redis reconnection failed: {str(reconnect_error)}")
        except asyncio.CancelledError:
            logger.info("Redis health check loop cancelled")
        except Exception as e:
            logger.error(f"Unexpected error in Redis health check loop: {str(e)}")
            
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics"""
        stats = {
            "active_connections": 0,
            "max_connections": self.max_connections,
            "is_connected": self.is_connected,
            "last_error": self.last_error
        }
        
        if self.client and hasattr(self.client, "connection_pool"):
            pool = self.client.connection_pool
            if hasattr(pool, "max_connections"):
                stats["max_connections"] = pool.max_connections
            
            # Get active connection count from pool if possible
            if hasattr(pool, "_in_use_connections"):
                stats["active_connections"] = len(pool._in_use_connections)
                
        return stats
        
    async def get(self, key: str) -> Optional[bytes]:
        """Get value from Redis"""
        if not self.client:
            await self.connect()
        return await self.client.get(key)
        
    async def set(self, key: str, value: Any, expiry: Optional[int] = None) -> bool:
        """Set value in Redis with optional expiry in seconds"""
        if not self.client:
            await self.connect()
        return await self.client.set(key, value, ex=expiry)
        
    async def delete(self, key: str) -> int:
        """Delete key from Redis"""
        if not self.client:
            await self.connect()
        return await self.client.delete(key)
        
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis"""
        if not self.client:
            await self.connect()
        return await self.client.exists(key) > 0
        
    async def keys(self, pattern: str) -> List[str]:
        """Get keys matching pattern"""
        if not self.client:
            await self.connect()
        keys = await self.client.keys(pattern)
        return [k.decode('utf-8') for k in keys]
        
    async def incr(self, key: str) -> int:
        """Increment value in Redis"""
        if not self.client:
            await self.connect()
        return await self.client.incr(key)
        
    async def expire(self, key: str, seconds: int) -> bool:
        """Set expiry on key"""
        if not self.client:
            await self.connect()
        return await self.client.expire(key, seconds)

    async def config_set(self, parameter: str, value: str):
        """Set Redis configuration parameters"""
        if not self.client:
            await self.connect()
        return await self.client.config_set(parameter, value)

    async def config_get(self, parameter: str):
        """Get Redis configuration parameters"""
        if not self.client:
            await self.connect()
        return await self.client.config_get(parameter)

    async def setex(self, key: str, time: int, value: str):
        """Set a key-value pair in Redis with expiration"""
        if not self.client:
            await self.connect()
        return await self.client.setex(key, time, value)

    async def ttl(self, key: str):
        """Get time to live for a key"""
        if not self.client:
            await self.connect()
        return await self.client.ttl(key)

    async def decr(self, key: str):
        """Decrement a key's value"""
        if not self.client:
            await self.connect()
        return await self.client.decr(key)