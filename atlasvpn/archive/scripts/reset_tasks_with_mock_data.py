#!/usr/bin/env python
"""
Reset Tasks with Mock Data

This script erases all existing tasks and creates new sample tasks with complete data
including platform URLs for YouTube, Instagram, Telegram, and website visits.

Usage:
    python reset_tasks_with_mock_data.py
    
Options:
    --force    Skip confirmation prompt
"""
import sys
import os
from pathlib import Path
import asyncio
import logging
from datetime import datetime, timedelta
import json
import time
import traceback
from typing import Dict, List, Optional, Any, Tuple
import random
import string

# Add backend directory to Python path
sys.path.append(str(Path(__file__).parent.parent))

# Import database and models
from database import init_db, get_db_context, engine
from models import Task, TaskType, RewardType, TaskCompletion, TaskStatus, Base
from sqlalchemy import select, delete, func

# Set up logs directory
SCRIPT_DIR = Path(__file__).parent
LOGS_DIR = SCRIPT_DIR / "logs"
LOGS_DIR.mkdir(exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOGS_DIR / 'reset_tasks.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TaskResetManager:
    """Manages resetting tasks and creating mock data"""
    
    def __init__(self):
        """Initialize the task reset manager"""
        self.stats = {
            "tasks_deleted": 0,
            "completions_deleted": 0,
            "tasks_created": 0
        }
    
    def get_task_count(self):
        """Get the count of tasks in the database"""
        try:
            with get_db_context() as db:
                return db.query(func.count(Task.id)).scalar()
        except Exception as e:
            logger.error(f"Error counting tasks: {str(e)}")
            return 0
    
    def delete_all_tasks(self, force=False):
        """Delete all existing tasks and their completions"""
        try:
            # Check how many tasks exist
            task_count = self.get_task_count()
            logger.info(f"Found {task_count} existing tasks")
            
            # Ask for confirmation if there are many tasks and force is not enabled
            if task_count > 10 and not force:
                confirm = input(f"WARNING: You are about to delete {task_count} tasks. Continue? (y/N): ")
                if confirm.lower() != 'y':
                    logger.info("Task deletion cancelled by user")
                    return False
            
            with get_db_context() as db:
                # First delete all task completions
                result = db.execute(delete(TaskCompletion))
                self.stats["completions_deleted"] = result.rowcount
                logger.info(f"Deleted {self.stats['completions_deleted']} task completions")
                
                # Then delete all tasks
                result = db.execute(delete(Task))
                self.stats["tasks_deleted"] = result.rowcount
                logger.info(f"Deleted {self.stats['tasks_deleted']} tasks")
                
                db.commit()
            return True
        except Exception as e:
            logger.error(f"Error deleting tasks: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def create_mock_tasks(self):
        """Create mock tasks with complete data"""
        try:
            with get_db_context() as db:
                # Create YouTube video view task
                youtube_task = Task(
                    name="Watch YouTube Tutorial",
                    description="Watch our tutorial video on YouTube to learn about VPN usage",
                    type=TaskType.YOUTUBE_VIEW,
                    reward_type=RewardType.WALLET_BONUS,
                    reward_value=5.0,
                    target_value=1,
                    is_active=True,
                    platform_url="https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # Real YouTube URL
                    platform_id="dQw4w9WgXcQ",
                    verify_key="VPN123",  # The verification code shown in the video
                    max_verification_attempts=3,
                    verification_cooldown=60
                )
                db.add(youtube_task)
                
                # Create Instagram view task
                instagram_task = Task(
                    name="Check Our Instagram Post",
                    description="View our latest Instagram post to learn about new features",
                    type=TaskType.INSTAGRAM_VIEW,
                    reward_type=RewardType.WALLET_BONUS,
                    reward_value=3.0,
                    target_value=1,
                    is_active=True,
                    platform_url="https://www.instagram.com/p/CxYzAbcDEf/",  # Mock Instagram post URL
                    platform_id="CxYzAbcDEf",
                    verify_key="INSTA456",  # The verification code shown in the post
                    max_verification_attempts=3,
                    verification_cooldown=60
                )
                db.add(instagram_task)
                
                # Create Instagram follow task
                instagram_follow_task = Task(
                    name="Follow Us on Instagram",
                    description="Follow our Instagram page to stay updated",
                    type=TaskType.INSTAGRAM_FOLLOW,
                    reward_type=RewardType.WALLET_BONUS,
                    reward_value=10.0,
                    target_value=1,
                    is_active=True,
                    platform_url="https://www.instagram.com/ourcompany/",  # Mock Instagram profile URL
                    platform_id="ourcompany",
                    verify_key="",  # No verification code needed for follow tasks
                    max_verification_attempts=3,
                    verification_cooldown=60
                )
                db.add(instagram_follow_task)
                
                # Create Telegram channel task
                telegram_task = Task(
                    name="Join Our Telegram Channel",
                    description="Join our Telegram channel for updates and support",
                    type=TaskType.TELEGRAM_CHANNEL,
                    reward_type=RewardType.WALLET_BONUS,
                    reward_value=15.0,
                    target_value=1,
                    is_active=True,
                    platform_url="https://t.me/our_channel",  # Real Telegram channel URL format
                    platform_id="our_channel",
                    verify_key="",  # No verification key needed for Telegram
                    max_verification_attempts=3,
                    verification_cooldown=60
                )
                db.add(telegram_task)
                
                # Create Twitter follow task
                twitter_task = Task(
                    name="Follow Us on Twitter",
                    description="Follow our Twitter account for news and updates",
                    type=TaskType.TWITTER_FOLLOW,
                    reward_type=RewardType.WALLET_BONUS,
                    reward_value=8.0,
                    target_value=1,
                    is_active=True,
                    platform_url="https://twitter.com/ourcompany",  # Mock Twitter URL
                    platform_id="ourcompany",
                    verify_key="",  # No verification code needed for follow tasks
                    max_verification_attempts=3,
                    verification_cooldown=60
                )
                db.add(twitter_task)
                
                # Create Website visit task with required_duration
                # Note: We don't directly set social_task as it's not a field in the Task model
                website_task = Task(
                    name="Explore Our Website",
                    description="Visit our website and stay for at least 30 seconds to learn more",
                    type=TaskType.WEBSITE_VISIT,
                    reward_type=RewardType.WALLET_BONUS,
                    reward_value=5.0,
                    target_value=1,
                    is_active=True,
                    platform_url="https://ourwebsite.com/features",  # Mock website URL
                    platform_id="features_page",
                    verify_key="",  # No verification key needed for website visits
                    max_verification_attempts=3,
                    verification_cooldown=60
                )
                db.add(website_task)
                
                # Create Referral task
                referral_task = Task(
                    name="Refer a Friend",
                    description="Invite friends to join our platform and earn rewards",
                    type=TaskType.REFERRAL,
                    reward_type=RewardType.WALLET_BONUS,
                    reward_value=20.0,
                    target_value=5,  # Need 5 referrals to complete
                    is_active=True,
                    platform_url="",  # No platform URL needed for referrals
                    platform_id="referral_program",
                    verify_key="",  # No verification key needed
                    max_verification_attempts=3,
                    verification_cooldown=60
                )
                db.add(referral_task)
                
                # Create Daily check-in task
                daily_task = Task(
                    name="Daily Check-in",
                    description="Check in daily to earn rewards and build streak",
                    type=TaskType.DAILY_CHECKIN,
                    reward_type=RewardType.WALLET_BONUS,
                    reward_value=2.0,
                    target_value=1,
                    is_active=True,
                    platform_url="",  # No platform URL needed for daily check-in
                    platform_id="daily_checkin",
                    verify_key="",  # No verification key needed
                    max_verification_attempts=3,
                    verification_cooldown=60,
                    cycle_length=7,  # 7 days in one cycle
                    daily_rewards=json.dumps([2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 15.0]),  # Increasing rewards
                    cycle_bonus_reward=10.0,  # Bonus for completing full cycle
                    reset_streak_after_hours=48
                )
                db.add(daily_task)
                
                db.commit()
                
                # Count created tasks
                task_count = db.execute(select(func.count()).select_from(Task)).scalar()
                self.stats["tasks_created"] = task_count
                logger.info(f"Created {task_count} mock tasks")
                
            return True
        except Exception as e:
            logger.error(f"Error creating mock tasks: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def print_report(self):
        """Print final report"""
        logger.info("=" * 50)
        logger.info("Task Reset Report")
        logger.info("=" * 50)
        logger.info(f"Tasks deleted: {self.stats['tasks_deleted']}")
        logger.info(f"Completions deleted: {self.stats['completions_deleted']}")
        logger.info(f"New tasks created: {self.stats['tasks_created']}")
        logger.info("=" * 50)

def main():
    """Main function to run the task reset"""
    try:
        logger.info("Starting task reset with mock data...")
        
        # Check for force flag
        force = "--force" in sys.argv
        
        # Initialize database
        init_db()
        logger.info("Database initialized")
        
        # Create task reset manager
        reset_manager = TaskResetManager()
        
        # Delete existing tasks
        if reset_manager.delete_all_tasks(force=force):
            logger.info("Successfully deleted existing tasks")
        else:
            logger.error("Failed to delete existing tasks")
            return
        
        # Create mock tasks
        if reset_manager.create_mock_tasks():
            logger.info("Successfully created mock tasks")
        else:
            logger.error("Failed to create mock tasks")
            return
        
        # Print report
        reset_manager.print_report()
        
        logger.info("Task reset completed successfully")
    except Exception as e:
        logger.error(f"Error in main function: {str(e)}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    main() 