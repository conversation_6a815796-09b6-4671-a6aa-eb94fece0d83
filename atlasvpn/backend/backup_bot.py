import os
import time
import logging
import subprocess
import asyncio
import glob
from datetime import datetime
from telegram.ext import Application
from telegram import Bo<PERSON>
from telegram.error import TelegramError
from dotenv import load_dotenv

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='logs/backup_bot.log'
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv('.env')
BOT_TOKEN = os.getenv('BOT_TOKEN')
ADMIN_ID = os.getenv('ADMIN_ID')

if not BOT_TOKEN or not ADMIN_ID:
    logger.error("BOT_TOKEN and ADMIN_ID must be set in .env file")
    raise ValueError("BOT_TOKEN and ADMIN_ID must be set in .env file")

# Convert ADMIN_ID to integer
try:
    ADMIN_ID = int(ADMIN_ID)
except ValueError:
    logger.error(f"ADMIN_ID must be a number, got: {ADMIN_ID}")
    raise ValueError(f"ADMIN_ID must be a number, got: {ADMIN_ID}")

def cleanup_old_backups():
    """Remove any leftover backup files"""
    try:
        backup_dir = 'backups'
        if os.path.exists(backup_dir):
            files = glob.glob(os.path.join(backup_dir, 'project_backup_*.zip'))
            for f in files:
                try:
                    os.remove(f)
                    logger.info(f"Cleaned up old backup: {f}")
                except Exception as e:
                    logger.error(f"Error cleaning up {f}: {str(e)}")
    except Exception as e:
        logger.error(f"Error during cleanup: {str(e)}")

def create_backup():
    """Create backup using the shell script"""
    try:
        # Make sure the backup directory exists
        os.makedirs('backups', exist_ok=True)
        
        # Clean up any old backups first
        cleanup_old_backups()
        
        # Check if project.sh exists
        if not os.path.exists('./project.sh'):
            logger.error("Backup script project.sh not found")
            raise FileNotFoundError("Backup script project.sh not found")
        
        # Make sure the script is executable
        os.chmod('./project.sh', 0o755)
        
        # Run the backup script
        result = subprocess.run(['./project.sh'], 
                              capture_output=True, 
                              text=True)
        
        if result.returncode != 0:
            error_msg = f"Backup script failed: {result.stderr}"
            logger.error(error_msg)
            raise Exception(error_msg)
            
        # Get the backup file
        backup_dir = 'backups'
        files = glob.glob(os.path.join(backup_dir, 'project_backup_*.zip'))
        
        if not files:
            error_msg = "No backup files found after running backup script"
            logger.error(error_msg)
            raise Exception(error_msg)
            
        if len(files) > 1:
            logger.warning(f"Multiple backup files found: {files}")
            
        logger.info(f"Backup created successfully: {files[0]}")
        return files[0]  # Return the first backup file
        
    except Exception as e:
        logger.error(f"Error creating backup: {str(e)}")
        raise

async def send_backup(app: Application, file_path: str):
    """Send the backup file to admin and delete it"""
    try:
        if not os.path.exists(file_path):
            raise Exception(f"Backup file not found: {file_path}")
            
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # Size in MB
        
        if file_size > 50:  # If file is larger than 50MB (Telegram limit is 50MB)
            logger.warning(f"Backup file too large for Telegram: {file_size:.2f}MB")
            await app.bot.send_message(
                chat_id=ADMIN_ID,
                text=f"⚠️ Backup file is too large to send via Telegram: {file_size:.2f}MB\n"\
                     f"💾 Path: {file_path}"
            )
            return
            
        with open(file_path, 'rb') as file:
            # Send a message before the file
            timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            message = f"📦 New backup created\n"\
                     f"🕒 Time: {timestamp}\n"\
                     f"📍 Size: {file_size:.2f} MB"
            
            await app.bot.send_message(chat_id=ADMIN_ID, text=message)
            
            # Send the file
            await app.bot.send_document(
                chat_id=ADMIN_ID,
                document=file,
                filename=os.path.basename(file_path),
                caption="✅ Backup file attached"
            )
            
            logger.info(f"Backup sent successfully: {file_path}")
            
    except TelegramError as e:
        logger.error(f"Telegram error when sending backup: {str(e)}")
        await app.bot.send_message(
            chat_id=ADMIN_ID,
            text=f"❌ Telegram error when sending backup: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Error sending backup: {str(e)}")
        try:
            await app.bot.send_message(
                chat_id=ADMIN_ID,
                text=f"❌ Error sending backup: {str(e)}"
            )
        except:
            logger.error("Failed to send error message to Telegram")
    finally:
        # Always try to clean up, even if sending fails
        try:
            cleanup_old_backups()
        except Exception as e:
            logger.error(f"Error during cleanup after sending: {str(e)}")

async def main():
    # Create logs directory
    os.makedirs('logs', exist_ok=True)
    
    # Create backups directory
    os.makedirs('backups', exist_ok=True)
    
    # Clean up any old backups on startup
    cleanup_old_backups()
    
    logger.info("Backup bot started")
    
    # Initialize the application
    application = Application.builder().token(BOT_TOKEN).build()
    
    try:
        # Send initial message
        await application.bot.send_message(
            chat_id=ADMIN_ID,
            text="🤖 Backup bot is now running!\n"\
                "I will send you project backups every 60 minutes."
        )
    except TelegramError as e:
        logger.error(f"Failed to send initial message: {str(e)}")
    
    while True:
        try:
            # Create and send backup
            backup_file = create_backup()
            await send_backup(application, backup_file)
            
            # Wait for 60 minutes (3600 seconds) between backups
            logger.info("Waiting 60 minutes until next backup...")
            await asyncio.sleep(3600)
            
        except Exception as e:
            error_msg = f"❌ Error during backup process: {str(e)}"
            logger.error(error_msg)
            try:
                await application.bot.send_message(chat_id=ADMIN_ID, text=error_msg)
            except:
                logger.error("Failed to send error message to Telegram")
            
            # Wait 5 minutes before retrying after an error
            logger.info("Waiting 5 minutes before retrying...")
            await asyncio.sleep(300)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Backup bot stopped by user")
    except Exception as e:
        logger.critical(f"Critical error in backup bot: {str(e)}")
