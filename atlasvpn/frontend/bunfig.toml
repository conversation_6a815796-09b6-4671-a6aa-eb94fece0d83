[install]
# Configure package installation behavior
cache = true
exact = false
production = false
optional = true
dev = true

# Package resolution and performance
peer = true
auto = "bun"
registry = "https://registry.npmjs.org/"

[install.lockfile]
# Enable lockfile for reproducible builds
save = true
print = "yarn"

[run]
# Run script configuration
bun = true
shell = "bash"

[test]
# Test configuration
root = "./src"
preload = ["./test/setup.ts"]

[bundler]
# Development bundler settings
sourcemap = "inline"
minify = false
target = "browser" 