# Nginx Configuration for React to Astro Migration

## Overview

This document describes the nginx configuration updates made to support both React and Astro frontends during the migration period, allowing for A/B testing and gradual migration.

## Configuration Changes Made

### 1. Fixed Port Mismatches
- **Issue**: React frontend was running on port 3000 but nginx was proxying to port 3003
- **Fix**: Updated all React frontend proxy configurations to use port 3000
- **Impact**: Fixed WebSocket HMR connections for React development

### 2. Added Astro Frontend Support
- **Port**: Astro development server runs on port 3005
- **Access Path**: `/astro/*` routes proxy to the Astro frontend
- **Example**: `https://dev.atlasvip.cloud/astro/dashboard/home`

### 3. WebSocket Configuration Fixes
- **React HMR**: `/__vite_hmr` → `localhost:3000`
- **Astro HMR**: `/astro/__vite_hmr` → `localhost:3005`
- **Backend WebSocket**: `/ws/` → `localhost:8000` (fixed connection upgrade headers)

### 4. Routing Strategy

#### Current Setup:
```
https://dev.atlasvip.cloud/                    → React Frontend (port 3000)
https://dev.atlasvip.cloud/astro/              → Astro Frontend (port 3005)
https://dev.atlasvip.cloud/api/                → Backend API (port 8000)
https://dev.atlasvip.cloud/ws/                 → WebSocket (port 8000)
https://dev.atlasvip.cloud/docs                → API Documentation (port 8000)
```

#### Path Rewriting:
- `/astro/dashboard/home` → proxied to Astro server as `/dashboard/home`
- `/astro/_astro/` → proxied to Astro server for static assets

## Port Allocation

| Service | Port | Status | Access |
|---------|------|--------|--------|
| React Frontend | 3000 | Production | `https://dev.atlasvip.cloud/` |
| Astro Frontend | 3005 | Development/Testing | `https://dev.atlasvip.cloud/astro/` |
| Backend API | 8000 | Production | `https://dev.atlasvip.cloud/api/` |

## Development Commands

### React Frontend (Current Production)
```bash
cd frontend
pnpm run dev  # Runs on port 3000
```

### Astro Frontend (Migration Testing)
```bash
cd frontend-astro
pnpm run dev  # Runs on port 3005
```

## Testing the Configuration

### 1. Test React Frontend
```bash
curl -I https://dev.atlasvip.cloud/
# Should return 200 OK
```

### 2. Test Astro Frontend
```bash
curl -I https://dev.atlasvip.cloud/astro/dashboard/home
# Should return 200 OK
```

### 3. Test WebSocket Connections
```bash
# React HMR WebSocket
curl -I -H "Upgrade: websocket" -H "Connection: upgrade" https://dev.atlasvip.cloud/__vite_hmr

# Astro HMR WebSocket
curl -I -H "Upgrade: websocket" -H "Connection: upgrade" https://dev.atlasvip.cloud/astro/__vite_hmr
```

## Security Considerations

### Headers Applied:
- **HSTS**: Strict-Transport-Security with preload
- **CSP**: Content Security Policy allowing necessary resources
- **XSS Protection**: X-XSS-Protection enabled
- **Content Type**: X-Content-Type-Options nosniff
- **Referrer Policy**: strict-origin-when-cross-origin

### File Access Restrictions:
- Hidden files (`.env`, `.git`, etc.) are blocked
- Environment files are explicitly denied
- Sensitive file patterns are blocked

## Performance Optimizations

### Compression:
- Gzip enabled for text/css/js/json/xml files
- Minimum compression size: 256 bytes
- Compression level: 6

### Caching:
- Development: No caching (`no-cache, no-store, must-revalidate`)
- Static assets: 30-day cache for Astro `_astro/` files
- API responses: No caching for dynamic content

## Migration Strategy

### Phase 1: Parallel Development ✅
- Both React and Astro frontends running simultaneously
- Astro accessible via `/astro/` path
- Independent development and testing

### Phase 2: A/B Testing (Next)
- Route specific users to Astro frontend
- Compare performance and functionality
- Gradual traffic migration

### Phase 3: Full Migration (Future)
- Switch main domain to Astro frontend
- Deprecate React frontend
- Update all references

## Troubleshooting

### Common Issues:

1. **WebSocket Connection Failed**
   - Check if development servers are running on correct ports
   - Verify nginx proxy headers for WebSocket upgrade

2. **404 on Astro Routes**
   - Ensure Astro server is running on port 3005
   - Check nginx rewrite rules for `/astro/` prefix

3. **CSS/JS Assets Not Loading**
   - Verify static asset proxy configuration
   - Check browser developer tools for 404s

### Log Locations:
- Nginx access log: `/var/log/nginx/access.log`
- Nginx error log: `/var/log/nginx/error.log`
- Astro dev server: Terminal output
- React dev server: Terminal output

## Backup and Rollback

### Configuration Backup:
```bash
# Backup created automatically:
/etc/nginx/sites-available/atlas-vpn-site.conf.backup-YYYYMMDD_HHMMSS
```

### Rollback Process:
```bash
# Restore backup
sudo cp /etc/nginx/sites-available/atlas-vpn-site.conf.backup-* /etc/nginx/sites-available/atlas-vpn-site.conf

# Test configuration
sudo nginx -t

# Reload nginx
sudo systemctl reload nginx
```

## Next Steps

1. **Complete Phase 2 Migration Tasks**:
   - Migrate remaining React components to Astro
   - Implement authentication middleware
   - Set up proper error handling

2. **Performance Testing**:
   - Compare load times between React and Astro
   - Test WebSocket performance
   - Monitor resource usage

3. **User Testing**:
   - Internal testing via `/astro/` path
   - Gather feedback on functionality
   - Identify any missing features

## Package Manager Updates

- **Astro Frontend**: Updated to use `pnpm` for consistency
- **React Frontend**: Already using `pnpm`
- **Commands**: All npm references replaced with pnpm in scripts
