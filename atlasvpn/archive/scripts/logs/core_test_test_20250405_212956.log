
Starting Core Task API Tests...
2025-04-05 21:30:22,539 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 21:30:22,542 - INFO - Retrieved CSRF token for admin login
2025-04-05 21:30:27,530 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 21:30:27,593 - INFO - Admin login successful
2025-04-05 21:30:27,669 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-05 21:30:27,671 - INFO - Retrieved CSRF token for user login
2025-04-05 21:30:28,684 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-05 21:30:28,685 - INFO - User login successful
2025-04-05 21:30:28,813 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 21:30:28,814 - INFO - Created YOUTUBE_VIEW task with ID: 384
2025-04-05 21:30:28,890 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-05 21:30:28,933 - INFO - Created TELEGRAM_CHANNEL task with ID: 385
2025-04-05 21:30:29,558 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/384/start "HTTP/1.1 200 OK"
2025-04-05 21:30:29,601 - INFO - Started task 384
2025-04-05 21:30:29,809 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/384/verify "HTTP/1.1 200 OK"
2025-04-05 21:30:29,853 - INFO - Verified task 384: True
2025-04-05 21:30:31,732 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/384/verify "HTTP/1.1 200 OK"
2025-04-05 21:30:31,733 - INFO - Verified task 384: True
2025-04-05 21:30:31,811 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/384/claim "HTTP/1.1 200 OK"
2025-04-05 21:30:31,849 - INFO - Claimed reward for task 384
2025-04-05 21:30:31,850 - INFO - YouTube task flow completed successfully
2025-04-05 21:30:31,925 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/385/start "HTTP/1.1 200 OK"
2025-04-05 21:30:32,455 - INFO - Started task 385
2025-04-05 21:30:32,829 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/385/verify "HTTP/1.1 200 OK"
2025-04-05 21:30:32,870 - INFO - Verified task 385: True
2025-04-05 21:30:34,786 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/385/claim "HTTP/1.1 200 OK"
2025-04-05 21:30:34,829 - INFO - Claimed reward for task 385
2025-04-05 21:30:34,830 - INFO - Telegram task flow completed successfully
2025-04-05 21:30:34,924 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/384/toggle "HTTP/1.1 200 OK"
2025-04-05 21:30:35,455 - INFO - Toggled task 384 active status to False
2025-04-05 21:30:35,705 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/384/toggle "HTTP/1.1 200 OK"
2025-04-05 21:30:35,745 - INFO - Toggled task 384 active status to True
2025-04-05 21:30:35,853 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/384/start "HTTP/1.1 400 Bad Request"
2025-04-05 21:30:35,893 - ERROR - Failed to start task 384: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
2025-04-05 21:30:35,893 - INFO - Admin functions tested successfully
  Setup completed 
  Running tests...

Task API Test Report
       Test Summary       
┏━━━━━━━━━━━━━━┳━━━━━━━━━┓
┃ Metric       ┃ Value   ┃
┡━━━━━━━━━━━━━━╇━━━━━━━━━┩
│ Total Tests  │ 3       │
│ Passed       │ 3       │
│ Failed       │ 0       │
│ Success Rate │ 100.00% │
└──────────────┴─────────┘

Report saved to /root/project/backend/scripts/logs/task_api_test_report_20250405_213035.json
