import { useState } from 'react'
import { useQ<PERSON>y, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { UserCheck, Users, DollarSign, Search, Plus } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface ReferralUser {
  id: number
  username: string
  email: string
  referral_code: string
  referred_by_id?: number
  referred_by_username?: string
  direct_referrals: number
  total_commissions_earned: number
  created_at: string
  last_login?: string
  is_active: boolean
}

interface ReferralStats {
  total_users: number
  users_with_referrals: number
  referral_penetration_rate: number
  total_commissions_paid: number
  avg_commission_per_referral: number
  top_referrers: Array<{
    user_id: number
    username: string
    referral_count: number
  }>
}

export function Referrals() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedUser, setSelectedUser] = useState<ReferralUser | null>(null)
  const [commissionDialogOpen, setCommissionDialogOpen] = useState(false)
  const [treeDialogOpen, setTreeDialogOpen] = useState(false)
  const [commissionAmount, setCommissionAmount] = useState('')
  const [commissionDescription, setCommissionDescription] = useState('')
  
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const { data: referralUsers, isLoading: isUsersLoading } = useQuery<ReferralUser[]>({
    queryKey: ['admin-referrals'],
    queryFn: () => api.get('/admin/referrals/').then(res => res.data)
  })

  const { data: referralStats, isLoading: isStatsLoading } = useQuery<ReferralStats>({
    queryKey: ['admin-referral-stats'],
    queryFn: () => api.get('/admin/referrals/stats').then(res => res.data)
  })

  const { data: referralTree } = useQuery({
    queryKey: ['admin-referral-tree', selectedUser?.id],
    queryFn: () => api.get(`/admin/referrals/${selectedUser?.id}/tree`).then(res => res.data),
    enabled: !!selectedUser && treeDialogOpen
  })

  const grantCommissionMutation = useMutation({
    mutationFn: (data: { amount: number; description: string }) =>
      api.post(`/admin/referrals/${selectedUser?.id}/commission`, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-referrals'] })
      queryClient.invalidateQueries({ queryKey: ['admin-referral-stats'] })
      setCommissionDialogOpen(false)
      setCommissionAmount('')
      setCommissionDescription('')
      toast({
        title: "Success",
        description: "Referral commission granted successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to grant commission",
        variant: "destructive",
      })
    }
  })

  const filteredUsers = referralUsers?.filter(user =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.referral_code.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleGrantCommission = () => {
    const amount = parseFloat(commissionAmount)
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: "Error",
        description: "Please enter a valid commission amount",
        variant: "destructive",
      })
      return
    }

    grantCommissionMutation.mutate({
      amount,
      description: commissionDescription || `Manual commission granted by admin`
    })
  }

  const StatCard = ({ 
    title, 
    value, 
    description, 
    icon: Icon, 
    isLoading 
  }: { 
    title: string
    value: string | number
    description: string
    icon: any
    isLoading: boolean
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-4 w-32" />
          </div>
        ) : (
          <>
            <div className="text-2xl font-bold">{value}</div>
            <p className="text-xs text-muted-foreground">{description}</p>
          </>
        )}
      </CardContent>
    </Card>
  )

  if (isUsersLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Referrals</h1>
          <p className="text-gray-600">Manage referral system</p>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Referrals</h1>
          <p className="text-gray-600">Manage referral system and commissions</p>
        </div>
      </div>

      {/* Referral Statistics */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Referral Statistics</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Users"
            value={referralStats?.total_users || 0}
            description="All registered users"
            icon={Users}
            isLoading={isStatsLoading}
          />
          <StatCard
            title="Referred Users"
            value={referralStats?.users_with_referrals || 0}
            description={`${referralStats?.referral_penetration_rate || 0}% penetration rate`}
            icon={UserCheck}
            isLoading={isStatsLoading}
          />
          <StatCard
            title="Total Commissions"
            value={`$${referralStats?.total_commissions_paid?.toFixed(2) || '0.00'}`}
            description="Total paid out"
            icon={DollarSign}
            isLoading={isStatsLoading}
          />
          <StatCard
            title="Avg Commission"
            value={`$${referralStats?.avg_commission_per_referral?.toFixed(2) || '0.00'}`}
            description="Per referral"
            icon={DollarSign}
            isLoading={isStatsLoading}
          />
        </div>
      </div>

      {/* Tabs for different views */}
      <Tabs defaultValue="users" className="space-y-4">
        <TabsList>
          <TabsTrigger value="users">All Users</TabsTrigger>
          <TabsTrigger value="top-referrers">Top Referrers</TabsTrigger>
        </TabsList>

        <TabsContent value="users" className="space-y-4">
          {/* Search */}
          <Card>
            <CardContent className="p-6">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search by username, email, or referral code..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardContent>
          </Card>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle>Users & Referrals ({filteredUsers?.length || 0})</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Referral Code</TableHead>
                    <TableHead>Referred By</TableHead>
                    <TableHead>Direct Referrals</TableHead>
                    <TableHead>Commissions Earned</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers?.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{user.username}</div>
                          <div className="text-sm text-gray-600">{user.email}</div>
                          <div className="text-xs text-gray-500">
                            {user.is_active ? (
                              <Badge variant="default">Active</Badge>
                            ) : (
                              <Badge variant="secondary">Inactive</Badge>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                          {user.referral_code}
                        </code>
                      </TableCell>
                      <TableCell>
                        {user.referred_by_username || (
                          <span className="text-gray-500">Direct signup</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{user.direct_referrals}</Badge>
                      </TableCell>
                      <TableCell>
                        <span className="font-medium">
                          ${user.total_commissions_earned.toFixed(2)}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedUser(user)
                              setTreeDialogOpen(true)
                            }}
                          >
                            <UserCheck className="h-4 w-4 mr-2" />
                            View Tree
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedUser(user)
                              setCommissionDialogOpen(true)
                            }}
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Grant Commission
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredUsers && filteredUsers.length === 0 && (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No users found</h3>
                  <p className="text-gray-600">
                    {searchTerm 
                      ? 'Try adjusting your search.' 
                      : 'No users in the system yet.'}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="top-referrers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Top Referrers</CardTitle>
              <CardDescription>Users with the most direct referrals</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {referralStats?.top_referrers.map((referrer, index) => (
                  <div key={referrer.user_id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center font-bold">
                          #{index + 1}
                        </div>
                      </div>
                      <div>
                        <div className="font-medium">{referrer.username}</div>
                        <div className="text-sm text-gray-600">User ID: {referrer.user_id}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-lg">{referrer.referral_count}</div>
                      <div className="text-sm text-gray-600">referrals</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Grant Commission Dialog */}
      <Dialog open={commissionDialogOpen} onOpenChange={setCommissionDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Grant Commission</DialogTitle>
            <DialogDescription>
              Grant referral commission to {selectedUser?.username}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="amount">Commission Amount ($)</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                value={commissionAmount}
                onChange={(e) => setCommissionAmount(e.target.value)}
                placeholder="0.00"
              />
            </div>
            <div>
              <Label htmlFor="description">Description (Optional)</Label>
              <Input
                id="description"
                value={commissionDescription}
                onChange={(e) => setCommissionDescription(e.target.value)}
                placeholder="Manual commission grant"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setCommissionDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleGrantCommission}
              disabled={grantCommissionMutation.isPending || !commissionAmount}
            >
              {grantCommissionMutation.isPending ? 'Granting...' : 'Grant Commission'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Referral Tree Dialog */}
      <Dialog open={treeDialogOpen} onOpenChange={setTreeDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Referral Tree - {selectedUser?.username}</DialogTitle>
            <DialogDescription>
              View the referral network for this user
            </DialogDescription>
          </DialogHeader>
          {referralTree && (
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="font-medium">{referralTree.username}</div>
                <div className="text-sm text-gray-600">Code: {referralTree.referral_code}</div>
                <div className="text-sm text-gray-600">
                  {referralTree.direct_referrals.length} direct referrals
                </div>
              </div>
              
              <div className="space-y-2">
                <h4 className="font-medium">Direct Referrals:</h4>
                {referralTree.direct_referrals.length > 0 ? (
                  referralTree.direct_referrals.map((referral: any) => (
                    <div key={referral.user_id} className="p-3 border rounded-lg ml-4">
                      <div className="font-medium">{referral.username}</div>
                      <div className="text-sm text-gray-600">{referral.email}</div>
                      <div className="text-sm text-gray-600">
                        {referral.second_level_count} second-level referrals
                      </div>
                      <div className="text-xs text-gray-500">
                        Joined: {new Date(referral.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 ml-4">No direct referrals yet.</p>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
