---
/**
 * @file Header.astro
 * @description Dashboard header component converted from React to Astro
 * Static header with user info and navigation - interactive parts as React islands
 */

export interface Props {
  activeTab?: 'home' | 'earn' | 'friends' | 'wallet' | 'premium';
  showBackButton?: boolean;
  title?: string;
  class?: string;
  style?: string;
}

const { 
  activeTab, 
  showBackButton = false, 
  title, 
  class: className = '',
  style = ''
} = Astro.props;

// Get user data from middleware/locals
const user = (Astro.locals as any).user;

// Import React components for interactive parts
import UserMenu from '../react/UserMenu';

// Function to format tab name for subtitle
const formatTabName = (tab: string): string => {
  if (!tab) return '';
  const formatted = tab.replace(/-/g, ' ');
  return formatted.charAt(0).toUpperCase() + formatted.slice(1);
};

const displayTitle = title || (activeTab ? formatTabName(activeTab) : 'Dashboard');
---

<header 
  class={`flex items-center justify-between py-2 px-4 ${className}`}
  style={style}
>
  <!-- Thinner Shine Effect -->
  <div class="absolute top-0 inset-x-0 h-0.5 bg-gradient-to-r from-transparent via-accent-500/15 to-transparent">
    <div class="absolute inset-0 flex justify-center">
      <!-- Left-moving shine - Reduced size -->
      <div class="w-1/3 h-full relative overflow-hidden">
        <div class="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-transparent via-white/20 to-transparent animate-shine-left" />
      </div>
      <!-- Right-moving shine - Reduced size -->
      <div class="w-1/3 h-full relative overflow-hidden">
        <div class="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shine-right" />
      </div>
    </div>
  </div>

  <!-- Left side: Back button or logo -->
  <div class="flex items-center space-x-3">
    {showBackButton ? (
      <button 
        onclick="history.back()"
        class="p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors"
        aria-label="Go back"
      >
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>
    ) : (
      <div class="flex items-center space-x-2">
        <div class="w-8 h-8 rounded-full bg-gradient-to-br from-accent-500 to-purple-600 flex items-center justify-center">
          <span class="text-white font-bold text-sm">V</span>
        </div>
        <div>
          <h1 class="text-lg font-semibold text-white leading-tight">VIPVerse</h1>
          {activeTab && (
            <p class="text-xs text-white/60 leading-tight">{displayTitle}</p>
          )}
        </div>
      </div>
    )}
  </div>

  <!-- Right side: User menu (React Island for interactivity) -->
  <div class="flex items-center space-x-3">
    <UserMenu 
      user={user}
      client:load
    />
  </div>
</header>

<!-- Header-specific animations -->
<style>
  @keyframes shine-left {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
  }
  
  @keyframes shine-right {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }
  
  .animate-shine-left {
    animation: shine-left 3s ease-in-out infinite;
    animation-delay: 0s;
  }
  
  .animate-shine-right {
    animation: shine-right 3s ease-in-out infinite;
    animation-delay: 1.5s;
  }
  
  /* Ensure header stays on top */
  header {
    z-index: 50;
  }
  
  /* Backdrop blur support */
  @supports (backdrop-filter: blur(12px)) {
    header {
      backdrop-filter: blur(12px);
    }
  }
</style>
