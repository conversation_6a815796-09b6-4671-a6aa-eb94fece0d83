"""Add cycle_day column to task_completions table"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import MetaData, Table, text
from database import engine

def main():
    # Get metadata
    metadata = MetaData()
    metadata.reflect(bind=engine)
    
    # Get task_completions table
    task_completions = Table('task_completions', metadata, extend_existing=True)
    
    # Add cycle_day column if it doesn't exist
    if 'cycle_day' not in task_completions.columns:
        print("Adding cycle_day column to task_completions table...")
        with engine.connect() as conn:
            conn.execute(text('ALTER TABLE task_completions ADD COLUMN cycle_day INTEGER'))
            conn.commit()
        print("Column added successfully!")
    else:
        print("cycle_day column already exists")

if __name__ == '__main__':
    main() 