import React from 'react';
import FriendsTab from './FriendsTab';
import { useCurrentUserQuery } from '../hooks/authHooks';
import LoadingSpinner from './LoadingSpinner';

const FriendsTabWrapper: React.FC = () => {
  const { data: user, isLoading: isLoadingUser } = useCurrentUserQuery();

  if (isLoadingUser) {
    return (
      <div className="flex justify-center items-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <FriendsTab
      isLoadingReferrals={false}
      user={user || undefined}
    />
  );
};

export default FriendsTabWrapper; 