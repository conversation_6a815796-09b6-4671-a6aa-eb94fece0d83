/**
 * @file api/hooks/referrals.ts
 * @description Referral-related React Query hooks
 */

import { useQuery } from '@tanstack/react-query';
import type {
  ReferralInfo,
  ReferredUser,
  QueryOptions
} from '../types';
import { referralService } from '../services';

// Query Keys
export const REFERRAL_QUERY_KEYS = {
  REFERRAL_INFO: ['referrals', 'info'],
  REFERRED_USERS: ['referrals', 'users']
} as const;

// Default query options
const DEFAULT_QUERY_OPTIONS: QueryOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  retry: false
};

// Referral Hooks
export function useReferralInfoQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: REFERRAL_QUERY_KEYS.REFERRAL_INFO,
    queryFn: () => referralService.getReferralInfo(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function useReferredUsersQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: REFERRAL_QUERY_KEYS.REFERRED_USERS,
    queryFn: () => referralService.getReferredUsers(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}