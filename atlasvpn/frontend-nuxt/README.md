# AtlasVPN Frontend - Nuxt.js Migration

This is the complete migration of the AtlasVPN frontend from React to Nuxt.js 3, providing a modern, performant, and maintainable codebase for the Telegram Mini App.

## 🚀 Migration Overview

The frontend has been completely migrated from React to Nuxt.js 3 with the following improvements:

- **Modern Framework**: Nuxt.js 3 with Vue 3 Composition API
- **Better Performance**: Optimized bundle splitting and caching
- **Enhanced DX**: Better TypeScript support and development tools
- **Telegram Integration**: Full Telegram WebApp API compatibility
- **PWA Support**: Service worker and offline functionality
- **Responsive Design**: Mobile-first approach with Tailwind CSS

## 📁 Project Structure

```
frontend-nuxt/
├── assets/                 # Static assets (CSS, images, fonts)
│   ├── css/               # Global stylesheets
│   ├── img/               # Images and icons
│   └── music/             # Audio files
├── components/            # Vue components
│   ├── ui/               # Reusable UI components
│   ├── layout/           # Layout components
│   ├── dashboard/        # Dashboard-specific components
│   └── verse/            # 3D world components
├── composables/          # Vue composables (hooks)
│   ├── useApi.ts         # API client composable
│   ├── useAuth.ts        # Authentication composable
│   ├── useTelegram.ts    # Telegram WebApp composable
│   └── ...
├── layouts/              # Nuxt layouts
│   ├── default.vue       # Default layout
│   └── dashboard.vue     # Dashboard layout
├── middleware/           # Route middleware
│   ├── auth.ts           # Authentication middleware
│   └── telegram.ts       # Telegram validation middleware
├── pages/                # File-based routing
│   ├── index.vue         # Landing page
│   ├── dashboard/        # Dashboard pages
│   └── verse/            # 3D world pages
├── plugins/              # Nuxt plugins
│   ├── pinia.client.ts   # Pinia store initialization
│   ├── telegram.client.ts # Telegram WebApp initialization
│   └── performance.client.ts # Performance monitoring
├── public/               # Static files
│   ├── avatars/          # User avatars
│   ├── img/              # Public images
│   ├── manifest.json     # PWA manifest
│   └── sw.js             # Service worker
├── server/               # Server-side code
│   └── api/              # Server API routes
├── stores/               # Pinia stores
│   ├── auth.ts           # Authentication store
│   ├── user.ts           # User data store
│   ├── tasks.ts          # Tasks management store
│   └── ...
├── types/                # TypeScript type definitions
│   └── index.ts          # Global types
├── utils/                # Utility functions
│   ├── api.ts            # API utilities
│   ├── telegram.ts       # Telegram utilities
│   ├── performance.ts    # Performance utilities
│   └── ...
├── nuxt.config.ts        # Nuxt configuration
├── tailwind.config.js    # Tailwind CSS configuration
├── package.json          # Dependencies and scripts
└── Dockerfile            # Docker configuration
```

## 🛠 Technology Stack

### Core Framework
- **Nuxt.js 3**: Vue.js meta-framework with SSR/SPA capabilities
- **Vue 3**: Progressive JavaScript framework with Composition API
- **TypeScript**: Type-safe JavaScript development
- **Pinia**: State management for Vue.js

### UI & Styling
- **Tailwind CSS**: Utility-first CSS framework
- **Nuxt UI**: Pre-built Vue components
- **Headless UI**: Unstyled, accessible UI components
- **Heroicons**: Beautiful hand-crafted SVG icons

### Development Tools
- **Vite**: Fast build tool and dev server
- **ESLint**: Code linting and formatting
- **PostCSS**: CSS processing and optimization

### Telegram Integration
- **Telegram WebApp API**: Native Telegram Mini App features
- **Haptic Feedback**: Touch feedback for better UX
- **Theme Integration**: Automatic theme detection

### Performance & PWA
- **Service Worker**: Offline functionality and caching
- **Bundle Splitting**: Optimized code splitting
- **Image Optimization**: Lazy loading and WebP support
- **Performance Monitoring**: Real-time performance tracking

## 🚀 Getting Started

### Prerequisites
- Node.js 20+ (LTS recommended)
- pnpm (package manager)
- Docker & Docker Compose (for deployment)

### Local Development

1. **Install dependencies**:
   ```bash
   cd frontend-nuxt
   pnpm install
   ```

2. **Start development server**:
   ```bash
   pnpm dev
   ```

3. **Access the application**:
   - Local: http://localhost:3005
   - Network: Available on your local network

### Environment Variables

Create a `.env` file in the project root:

```env
# API Configuration
NUXT_PUBLIC_API_URL=https://app.atlasvip.cloud/api
NUXT_PUBLIC_WS_BASE_URL=wss://app.atlasvip.cloud/ws

# Telegram Configuration
NUXT_PUBLIC_TELEGRAM_WEBAPP_URL=https://app.atlasvip.cloud
NUXT_PUBLIC_TELEGRAM_BOT_USERNAME=your_bot_username

# Environment
NODE_ENV=development
```

## 🐳 Docker Deployment

The application is containerized and deployed using Docker Compose with Traefik as a reverse proxy.

### Docker Configuration

The `Dockerfile` uses Node.js 20 Alpine and pnpm for optimal performance:

```dockerfile
FROM node:20-alpine
WORKDIR /app
RUN npm install -g pnpm@latest
COPY package*.json pnpm-lock.yaml* ./
RUN pnpm install --frozen-lockfile
COPY . .
EXPOSE 3005
CMD ["pnpm", "dev"]
```

### Traefik Integration

The application is configured with Traefik labels for:
- SSL termination with Let's Encrypt
- Automatic routing based on domain
- Telegram WebApp headers for iframe compatibility
- Development file serving (HMR, assets)

### Deployment Commands

```bash
# Stop existing containers
docker-compose down

# Start all services
docker-compose up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f frontend
```

## 📱 Telegram Mini App Integration

### WebApp API Features
- **User Authentication**: Automatic user detection from Telegram
- **Theme Integration**: Respects user's Telegram theme
- **Haptic Feedback**: Touch feedback for interactions
- **Viewport Management**: Automatic viewport expansion
- **Main Button**: Telegram's native main button
- **Back Button**: Navigation integration

### Security Headers
The application includes proper CSP headers for Telegram iframe compatibility:

```javascript
"Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://telegram.org; frame-ancestors https://web.telegram.org https://*.telegram.org;"
```

## 🎯 Key Features Migrated

### ✅ Authentication System
- Telegram WebApp authentication
- JWT token management
- Session persistence
- Role-based access control

### ✅ Dashboard & Analytics
- Real-time statistics
- Interactive charts and graphs
- Performance metrics
- User activity tracking

### ✅ Task Management System
- Task creation and completion
- Progress tracking
- Reward distribution
- Daily check-ins and streaks

### ✅ VPN Management
- Package selection and purchase
- Subscription management
- Server status monitoring
- Usage analytics

### ✅ Wallet & Transactions
- Balance management
- Transaction history
- Payment processing
- Referral rewards

### ✅ Social Features
- Friend invitations
- Referral system
- Social sharing
- Leaderboards

### ✅ 3D Virtual World (AtlasVerse)
- Three.js integration
- Avatar customization
- Interactive environments
- Real-time rendering

### ✅ UI Components Library
- Reusable Vue components
- Consistent design system
- Accessibility features
- Mobile-optimized layouts

## 🔧 Development Guidelines

### Code Style
- Use TypeScript for all new code
- Follow Vue 3 Composition API patterns
- Use Pinia for state management
- Implement proper error handling

### Component Structure
```vue
<template>
  <!-- Template with proper accessibility -->
</template>

<script setup lang="ts">
// Composition API with TypeScript
import { ref, computed, onMounted } from 'vue'

// Props and emits with proper typing
interface Props {
  title: string
  items: Item[]
}

const props = defineProps<Props>()
const emit = defineEmits<{
  select: [item: Item]
}>()

// Reactive state
const isLoading = ref(false)
const selectedItem = ref<Item | null>(null)

// Computed properties
const filteredItems = computed(() => {
  // Logic here
})

// Lifecycle hooks
onMounted(() => {
  // Initialization
})
</script>

<style scoped>
/* Component-specific styles */
</style>
```

### State Management
Use Pinia stores for complex state:

```typescript
export const useUserStore = defineStore('user', () => {
  const user = ref<User | null>(null)
  const isAuthenticated = computed(() => !!user.value)
  
  const login = async (credentials: LoginCredentials) => {
    // Login logic
  }
  
  const logout = () => {
    user.value = null
  }
  
  return {
    user: readonly(user),
    isAuthenticated,
    login,
    logout
  }
})
```

## 🚀 Performance Optimizations

### Bundle Optimization
- Manual chunk splitting for better caching
- Tree shaking for unused code elimination
- Minification and compression
- Critical resource preloading

### Runtime Performance
- Lazy loading for routes and components
- Image optimization with WebP support
- Service worker for offline functionality
- Performance monitoring and metrics

### Caching Strategy
- Static assets cached with long TTL
- API responses cached with appropriate TTL
- Service worker implements cache-first strategy
- Browser caching optimized

## 🧪 Testing

### Unit Testing
```bash
# Run unit tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Generate coverage report
pnpm test:coverage
```

### E2E Testing
```bash
# Run end-to-end tests
pnpm test:e2e

# Run tests in headless mode
pnpm test:e2e:headless
```

## 📊 Monitoring & Analytics

### Performance Monitoring
- Web Vitals tracking
- Bundle size monitoring
- Runtime performance metrics
- Error tracking and reporting

### User Analytics
- Page view tracking
- User interaction events
- Conversion funnel analysis
- A/B testing support

## 🔒 Security

### Content Security Policy
- Strict CSP headers for XSS protection
- Telegram-specific iframe allowances
- Resource loading restrictions

### Input Validation
- Client-side validation with server verification
- XSS prevention through proper escaping
- CSRF protection with tokens

### Authentication Security
- JWT token validation
- Secure session management
- Rate limiting for API endpoints

## 🚀 Deployment Checklist

- [ ] Environment variables configured
- [ ] SSL certificates valid
- [ ] Database migrations applied
- [ ] Static assets optimized
- [ ] Service worker updated
- [ ] Performance metrics baseline established
- [ ] Error monitoring configured
- [ ] Backup procedures verified

## 📞 Support & Maintenance

### Common Issues
1. **Module not found errors**: Run `pnpm install` to ensure all dependencies are installed
2. **Build failures**: Check TypeScript errors and fix them
3. **Telegram integration issues**: Verify WebApp script is loaded and domain is whitelisted
4. **Performance issues**: Use the performance monitoring tools to identify bottlenecks

### Maintenance Tasks
- Regular dependency updates
- Performance monitoring and optimization
- Security vulnerability scanning
- Database cleanup and optimization
- Log rotation and cleanup

## 🎉 Migration Success

The migration from React to Nuxt.js has been completed successfully with:

- ✅ **100% Feature Parity**: All original features migrated
- ✅ **Improved Performance**: Faster loading and better UX
- ✅ **Better Developer Experience**: Modern tooling and TypeScript
- ✅ **Enhanced Telegram Integration**: Full WebApp API support
- ✅ **PWA Capabilities**: Offline functionality and app-like experience
- ✅ **Production Ready**: Deployed and accessible at https://app.atlasvip.cloud

The new Nuxt.js frontend provides a solid foundation for future development and scaling of the AtlasVPN platform.

## 📋 Quick Reference

### Development Commands
```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview

# Run linting
pnpm lint

# Run type checking
pnpm typecheck
```

### Docker Commands
```bash
# Build and start all services
docker-compose up -d

# Stop all services
docker-compose down

# Restart frontend only
docker-compose restart frontend

# View frontend logs
docker-compose logs -f frontend

# Check service status
docker-compose ps
```

### Useful URLs
- **Production**: https://app.atlasvip.cloud
- **Traefik Dashboard**: https://app.atlasvip.cloud:8080
- **API Documentation**: https://app.atlasvip.cloud/docs
- **Local Development**: http://localhost:3005

---

**Migration completed successfully! 🎉**

For questions or support, please refer to the documentation above or contact the development team.
