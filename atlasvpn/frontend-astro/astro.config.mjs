// @ts-check
import { defineConfig } from 'astro/config';
import react from '@astrojs/react';
import tailwind from '@astrojs/tailwind';

// https://astro.build/config
export default defineConfig({
  devToolbar: {
    enabled: false, // Disable dev toolbar to avoid 503 errors
  },
  server: {
    port: 3005, // REQUIRED: Must run on port 3005
    host: true, // Allow external connections
  },
  integrations: [
    react({
      experimentalReactChildren: true,
    }),
    tailwind({
      applyBaseStyles: false,
    }),
  ],
  output: 'static',
  build: {
    assets: 'assets',
  },
  vite: {
    server: {
      host: true,
      port: 3005,
      hmr: false, // Disable HMR WebSocket for now to avoid connection issues
      allowedHosts: ['app.atlasvip.cloud', 'localhost', '127.0.0.1'],
    },
    define: {
      global: 'globalThis',
    },
    optimizeDeps: {
      include: ['react', 'react-dom', '@tanstack/react-query', 'zustand'],
      exclude: ['@tanstack/react-query-devtools'],
    },
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            'react-vendor': ['react', 'react-dom'],
            'query-vendor': ['@tanstack/react-query'],
            'three-vendor': ['three', '@react-three/fiber', '@react-three/drei'],
            'animation-vendor': ['framer-motion'],
            'telegram-vendor': ['@telegram-apps/sdk-react'],
          },
        },
      },
    },
  },
});
