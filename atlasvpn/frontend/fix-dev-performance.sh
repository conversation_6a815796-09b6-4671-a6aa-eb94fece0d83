#!/bin/bash

# 🚀 VIPVerse Development Performance Fix Script
# This script fixes the 100+ second loading issue in dev mode

set -e

echo "🔧 Starting VIPVerse Development Performance Fix..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the frontend directory
if [ ! -f "package.json" ]; then
    print_error "Please run this script from the frontend directory"
    exit 1
fi

print_status "Step 1: Backing up production environment..."
if [ -f ".env" ]; then
    cp .env .env.production.backup
    print_success "Production .env backed up to .env.production.backup"
fi

print_status "Step 2: Creating localhost development environment..."
cat > .env << 'EOF'
# VIPVerse Frontend Environment Configuration - LOCAL DEVELOPMENT
VITE_API_URL=http://localhost:8000
VITE_WS_BASE_URL=ws://localhost:8000/ws
VITE_ENVIRONMENT=development
VITE_APP_NAME=VIPVerse
VITE_APP_VERSION=1.0.0
VITE_DEBUG=true
VITE_TELEGRAM_BOT_NAME=@VIPVerseBot
EOF
print_success "Created localhost .env configuration"

print_status "Step 3: Killing existing Vite processes..."
pkill -f vite || true
sleep 2
print_success "Stopped existing Vite processes"

print_status "Step 4: Clearing Vite dependency cache..."
rm -rf node_modules/.vite/deps*
rm -rf node_modules/.vite/registry.json
print_success "Cleared Vite cache"

print_status "Step 5: Checking backend availability..."
if curl -s http://localhost:8000/docs > /dev/null; then
    print_success "Backend is running on localhost:8000"
else
    print_warning "Backend not accessible on localhost:8000"
    print_warning "Make sure to start the backend with: cd ../backend && source venv/bin/activate && uvicorn main:app --reload --host 0.0.0.0 --port 8000"
fi

print_status "Step 6: Starting optimized development server..."
echo ""
echo "🚀 Starting Vite with optimized configuration..."
echo "   - Heavy dependencies excluded from optimization"
echo "   - Localhost URLs configured"
echo "   - HMR properly configured for local development"
echo ""

# Start the dev server
pnpm run dev

print_success "Development server started!"
echo ""
echo "📊 Expected Performance:"
echo "   - Initial load: 3-8 seconds (was 100+ seconds)"
echo "   - Dependency bundling: 1-2 seconds (was 7+ seconds)"
echo "   - Authentication: Reliable (was failing)"
echo ""
echo "🌐 Access your app at: http://localhost:3000"
echo "📚 API docs available at: http://localhost:8000/docs"
echo ""
echo "💡 Tips:"
echo "   - Use 'pnpm run dev:fast' for ultra-fast startup"
echo "   - Use 'pnpm run dev:minimal' for minimal mode"
echo "   - Performance monitor will show in top-right corner" 