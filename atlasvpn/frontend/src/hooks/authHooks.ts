import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { fetchData, mutateData, api } from '../utils/apiClient';
import { type User } from '../types';
import type { ApiResponse } from '../types';

// Local type definitions
export interface VerifySessionData {
  status: string;
  data?: any;
}

export interface LoginParams {
  username: string;
  password: string;
}

export interface TelegramLoginParams {
  init_data: string;
  auth_method: 'telegram';
}

export interface AuthError extends Error {
  response?: {
    data?: {
      message?: string;
      detail?: string;
    };
    status?: number;
  };
}

// Single source of truth for current user query
const CURRENT_USER_QUERY_KEY = ['currentUser', { endpoint: '/auth/verify-session' }] as const;

export function useCurrentUserQuery() {
  return useQuery({
    queryKey: CURRENT_USER_QUERY_KEY,
    queryFn: async (context): Promise<VerifySessionData> => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.log('[Auth] Request timeout - aborting');
        controller.abort();
      }, 15000); // Increased from 5000 to 15000 for dev mode
      
      try {
        console.log('[Auth] Starting session verification...');
        
        // Create axios config with abort signal
        const axiosConfig = { 
          signal: controller.signal,
          timeout: 12000 // Increased from 4000 to 12000 for dev mode
        };
        
        // Make direct API call with timeout handling
        const response = await api.post<VerifySessionData>('/auth/verify-session', {}, axiosConfig);
        
        clearTimeout(timeoutId);
        console.log('[Auth] Session verification successful');
        return response.data as VerifySessionData;
      } catch (error: any) {
        clearTimeout(timeoutId);
        
        // Better error logging
        console.log('[Auth] Session verification error:', {
          message: error.message,
          status: error?.response?.status,
          code: error?.code,
          name: error?.name,
          isAborted: error.name === 'AbortError' || error.code === 'ERR_CANCELED'
        });
        
        // Handle abort/timeout gracefully
        if (error.name === 'AbortError' || error.code === 'ERR_CANCELED' || error.message?.includes('timeout')) {
          console.warn('[Auth] Session verification was canceled/timed out, treating as unauthenticated');
          return { status: 'unauthenticated', data: null } as VerifySessionData;
        }
        
        // Handle 401 responses gracefully - expected for unauthenticated users
        if (error?.response?.status === 401 || error?.status === 401) {
          console.log('[Auth] User is unauthenticated (401)');
          return { status: 'unauthenticated', data: null } as VerifySessionData;
        }
        
        // For network errors or server issues, also return unauthenticated to prevent hanging
        if (error?.code === 'NETWORK_ERROR' || error?.response?.status >= 500) {
          console.warn('[Auth] Network/server error, treating as unauthenticated:', error.message);
          return { status: 'unauthenticated', data: null } as VerifySessionData;
        }
        
        // For other errors, also return unauthenticated to prevent hanging
        console.warn('[Auth] Treating error as unauthenticated:', error.message);
        return { status: 'unauthenticated', data: null } as VerifySessionData;
      }
    },
    staleTime: 5 * 60 * 1000, // Increased from 2 minutes to 5 minutes
    gcTime: 10 * 60 * 1000, // Increased from 5 minutes to 10 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    retry: false,
    retryOnMount: false,
    networkMode: 'online',
    // React 19 optimization: prevent excessive re-renders
    structuralSharing: false,
    select: (verifySessionData: VerifySessionData): User | null => {
      if (verifySessionData?.status === 'authenticated' && verifySessionData.data) {
        return verifySessionData.data as User;
      }
      return null;
    },
  });
}

export function useLoginMutation() {
  const queryClient = useQueryClient();
  return useMutation<User, AuthError, LoginParams>({
    mutationFn: async (loginVariables: LoginParams) => {
      const response = await api.auth.login({
        username: loginVariables.username,
        password: loginVariables.password
      });
      
      return response.data;
    },
    onSuccess: (user) => {
      // Update the cache with authenticated user
      const verifyData: VerifySessionData = {
        status: 'authenticated',
        data: user,
      };
      queryClient.setQueryData(CURRENT_USER_QUERY_KEY, verifyData);
    },
    onError: (error) => {
      console.error('[Login] Authentication failed:', error);
      // Ensure we don't leave stale auth data
      queryClient.setQueryData(CURRENT_USER_QUERY_KEY, { status: 'unauthenticated', data: null });
    },
  });
}

export function useTelegramLoginMutation() {
  const queryClient = useQueryClient();
  
  type TelegramLoginResult = {
    isNewUser: boolean;
    user?: User;
    telegram_data?: any;
  };
  
  return useMutation<TelegramLoginResult, AuthError, TelegramLoginParams>({
    mutationFn: async (variables: TelegramLoginParams): Promise<TelegramLoginResult> => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout for login
      
      try {
        console.log('[TelegramLogin] Starting login process');
        
        const response = await api.auth.telegramLogin({
          init_data: variables.init_data,
          auth_method: variables.auth_method
        });
        
        clearTimeout(timeoutId);
        
        // If we get a successful response, it means existing user
        if (response?.data) {
          console.log('[TelegramLogin] Existing user login successful');
          return {
            isNewUser: false,
            user: response.data
          };
        }
        
        throw new Error('Invalid response from server');
      } catch (error: any) {
        clearTimeout(timeoutId);
        console.error('[TelegramLogin] Error:', error);
        
        // Handle timeout
        if (error.name === 'AbortError' || error.message?.includes('timeout')) {
          throw new Error('Login request timed out. Please try again.');
        }
        
        // Handle specific error cases
        if (error?.response?.status === 404) {
          console.log('[TelegramLogin] User not found - new user');
          return {
            isNewUser: true,
            telegram_data: variables.init_data
          };
        }
        
        // Simplify error handling for other cases
        if (error?.response?.status === 401) {
          throw new Error('Telegram authentication failed');
        }
        if (error?.response?.status === 400) {
          throw new Error('Invalid Telegram data');
        }
        
        // Network or server errors
        if (error?.code === 'NETWORK_ERROR' || error?.response?.status >= 500) {
          throw new Error('Server error. Please try again later.');
        }
        
        throw error;
      }
    },
    onSuccess: (data: TelegramLoginResult) => {
      if (data?.user) {
        // Existing user - update cache immediately
        const verifyData: VerifySessionData = {
          status: 'authenticated',
          data: data.user,
        };
        queryClient.setQueryData(CURRENT_USER_QUERY_KEY, verifyData);
        console.log('[TelegramLogin] Updated auth cache for existing user');
        
        // Clear any stale setup data
        sessionStorage.removeItem('telegram_setup_data');
      } else if (data?.isNewUser && data.telegram_data) {
        // New user - store setup data
        sessionStorage.setItem(
          'telegram_setup_data',
          JSON.stringify({ 
            telegramData: data.telegram_data, 
            timestamp: Date.now() 
          })
        );
        console.log('[TelegramLogin] Stored setup data for new user');
        
        // Ensure auth cache reflects unauthenticated state
        queryClient.setQueryData(CURRENT_USER_QUERY_KEY, { status: 'unauthenticated', data: null });
      }
    },
    onError: (error) => {
      console.error('[TelegramLogin] Login failed:', error);
      // Ensure we don't leave stale auth data
      queryClient.setQueryData(CURRENT_USER_QUERY_KEY, { status: 'unauthenticated', data: null });
    },
  });
}

export function useLogoutMutation() {
  const queryClient = useQueryClient();
  return useMutation<unknown, AuthError, void>({
    mutationFn: async () => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout for logout
      
      try {
        const result = await api.auth.logout();
        clearTimeout(timeoutId);
        return result;
      } catch (error: any) {
        clearTimeout(timeoutId);
        // Even if logout fails on server, we still want to clear local state
        console.warn('[Logout] Server logout failed, but clearing local state:', error);
        return null;
      }
    },
    onSuccess: () => {
      // Clear all auth-related data
      queryClient.setQueryData(CURRENT_USER_QUERY_KEY, { status: 'unauthenticated', data: null });
      queryClient.removeQueries({ queryKey: CURRENT_USER_QUERY_KEY });
      sessionStorage.removeItem('telegram_setup_data');
      localStorage.removeItem('isFreshLogin');
      console.log('[Logout] Cleared auth state');
    },
    onError: () => {
      // Even on error, clear local auth state
      queryClient.setQueryData(CURRENT_USER_QUERY_KEY, { status: 'unauthenticated', data: null });
      queryClient.removeQueries({ queryKey: CURRENT_USER_QUERY_KEY });
      sessionStorage.removeItem('telegram_setup_data');
      localStorage.removeItem('isFreshLogin');
      console.log('[Logout] Cleared auth state after error');
    },
  });
}

export function useRefreshAuthMutation() {
  const queryClient = useQueryClient();
  return useMutation<User, AuthError, void>({
    mutationFn: async () => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout
      
      try {
        const response = await api.auth.refreshToken(); // Use the correct method name
        clearTimeout(timeoutId);
        return response.data;
      } catch (error: any) {
        clearTimeout(timeoutId);
        
        if (error.name === 'AbortError') {
          throw new Error('Refresh request timed out');
        }
        
        throw error;
      }
    },
    onSuccess: (user) => {
      const verifyData: VerifySessionData = {
        status: 'authenticated',
        data: user,
      };
      queryClient.setQueryData(CURRENT_USER_QUERY_KEY, verifyData);
      console.log('[RefreshAuth] Updated auth cache');
    },
    onError: (error) => {
      console.error('[RefreshAuth] Refresh failed:', error);
      // On refresh failure, treat as unauthenticated
      queryClient.setQueryData(CURRENT_USER_QUERY_KEY, { status: 'unauthenticated', data: null });
    },
  });
}
