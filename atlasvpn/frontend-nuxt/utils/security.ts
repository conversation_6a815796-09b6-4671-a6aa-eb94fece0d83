/**
 * Security utilities for input validation, sanitization, and protection
 */

/**
 * Sanitize HTML content to prevent XSS attacks
 * @param html - HTML string to sanitize
 * @returns Sanitized HTML string
 */
export function sanitizeHtml(html: string): string {
  if (typeof window === 'undefined') return html;

  // Create a temporary div element
  const temp = document.createElement('div');
  temp.textContent = html;
  return temp.innerHTML;
}

/**
 * Escape HTML special characters
 * @param text - Text to escape
 * @returns Escaped text
 */
export function escapeHtml(text: string): string {
  const map: Record<string, string> = {
    '&': '&amp;',
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#39;',
    '/': '&#x2F;',
  };

  return text.replace(/[&<>"'/]/g, (s) => map[s]);
}

/**
 * Validate email format
 * @param email - Email to validate
 * @returns True if valid email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

/**
 * Validate username format
 * @param username - Username to validate
 * @returns True if valid username format
 */
export function isValidUsername(username: string): boolean {
  // Username should be 3-20 characters, alphanumeric and underscores only
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  return usernameRegex.test(username);
}

/**
 * Validate password strength
 * @param password - Password to validate
 * @returns Password strength score (0-4) and requirements
 */
export function validatePasswordStrength(password: string): {
  score: number;
  requirements: {
    length: boolean;
    lowercase: boolean;
    uppercase: boolean;
    number: boolean;
    special: boolean;
  };
  feedback: string[];
} {
  const requirements = {
    length: password.length >= 8,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    number: /\d/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  };

  const score = Object.values(requirements).filter(Boolean).length;
  
  const feedback: string[] = [];
  if (!requirements.length) feedback.push('Password must be at least 8 characters long');
  if (!requirements.lowercase) feedback.push('Password must contain at least one lowercase letter');
  if (!requirements.uppercase) feedback.push('Password must contain at least one uppercase letter');
  if (!requirements.number) feedback.push('Password must contain at least one number');
  if (!requirements.special) feedback.push('Password must contain at least one special character');

  return { score, requirements, feedback };
}

/**
 * Generate a secure random string
 * @param length - Length of the random string
 * @param charset - Character set to use
 * @returns Random string
 */
export function generateSecureRandom(
  length: number = 32,
  charset: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
): string {
  let result = '';
  
  if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
    // Use crypto.getRandomValues for secure random generation
    const array = new Uint8Array(length);
    window.crypto.getRandomValues(array);
    
    for (let i = 0; i < length; i++) {
      result += charset[array[i] % charset.length];
    }
  } else {
    // Fallback to Math.random (less secure)
    for (let i = 0; i < length; i++) {
      result += charset[Math.floor(Math.random() * charset.length)];
    }
  }
  
  return result;
}

/**
 * Hash a string using SHA-256 (if available)
 * @param text - Text to hash
 * @returns Promise that resolves to hash string
 */
export async function hashString(text: string): Promise<string> {
  if (typeof window === 'undefined' || !window.crypto?.subtle) {
    // Fallback: simple hash function (not cryptographically secure)
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }

  try {
    const encoder = new TextEncoder();
    const data = encoder.encode(text);
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  } catch (error) {
    console.warn('Failed to hash string:', error);
    return text; // Return original text as fallback
  }
}

/**
 * Validate URL format and check for suspicious patterns
 * @param url - URL to validate
 * @returns True if URL appears safe
 */
export function isValidUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    
    // Check for allowed protocols
    const allowedProtocols = ['http:', 'https:', 'mailto:', 'tel:'];
    if (!allowedProtocols.includes(urlObj.protocol)) {
      return false;
    }
    
    // Check for suspicious patterns
    const suspiciousPatterns = [
      /javascript:/i,
      /data:/i,
      /vbscript:/i,
      /file:/i,
    ];
    
    return !suspiciousPatterns.some(pattern => pattern.test(url));
  } catch (error) {
    return false;
  }
}

/**
 * Rate limiting utility
 */
export class RateLimiter {
  private attempts: Map<string, number[]> = new Map();

  /**
   * Check if action is allowed based on rate limit
   * @param key - Unique key for the action
   * @param maxAttempts - Maximum attempts allowed
   * @param windowMs - Time window in milliseconds
   * @returns True if action is allowed
   */
  isAllowed(key: string, maxAttempts: number = 5, windowMs: number = 60000): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(timestamp => now - timestamp < windowMs);
    
    if (validAttempts.length >= maxAttempts) {
      return false;
    }
    
    // Add current attempt
    validAttempts.push(now);
    this.attempts.set(key, validAttempts);
    
    return true;
  }

  /**
   * Reset attempts for a key
   * @param key - Key to reset
   */
  reset(key: string): void {
    this.attempts.delete(key);
  }

  /**
   * Clear all attempts
   */
  clear(): void {
    this.attempts.clear();
  }
}

/**
 * Content Security Policy utilities
 */
export const csp = {
  /**
   * Generate a nonce for inline scripts/styles
   * @returns Random nonce string
   */
  generateNonce(): string {
    return generateSecureRandom(16);
  },

  /**
   * Check if CSP is supported
   * @returns True if CSP is supported
   */
  isSupported(): boolean {
    return typeof window !== 'undefined' && 'SecurityPolicyViolationEvent' in window;
  },

  /**
   * Report CSP violation
   * @param violation - Violation details
   */
  reportViolation(violation: any): void {
    console.warn('CSP Violation:', violation);
    // In production, you might want to send this to your logging service
  },
};

/**
 * Input sanitization utilities
 */
export const sanitize = {
  /**
   * Remove potentially dangerous characters from input
   * @param input - Input string to sanitize
   * @returns Sanitized string
   */
  input(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove angle brackets
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  },

  /**
   * Sanitize filename
   * @param filename - Filename to sanitize
   * @returns Sanitized filename
   */
  filename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9._-]/g, '_') // Replace special chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores
      .substring(0, 255); // Limit length
  },

  /**
   * Sanitize phone number
   * @param phone - Phone number to sanitize
   * @returns Sanitized phone number
   */
  phone(phone: string): string {
    return phone.replace(/[^\d+()-\s]/g, '');
  },
};

/**
 * Validate file upload
 * @param file - File to validate
 * @param options - Validation options
 * @returns Validation result
 */
export function validateFileUpload(
  file: File,
  options: {
    maxSize?: number; // in bytes
    allowedTypes?: string[];
    allowedExtensions?: string[];
  } = {}
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Check file size
  if (options.maxSize && file.size > options.maxSize) {
    errors.push(`File size exceeds ${options.maxSize} bytes`);
  }
  
  // Check file type
  if (options.allowedTypes && !options.allowedTypes.includes(file.type)) {
    errors.push(`File type ${file.type} is not allowed`);
  }
  
  // Check file extension
  if (options.allowedExtensions) {
    const extension = file.name.split('.').pop()?.toLowerCase();
    if (!extension || !options.allowedExtensions.includes(extension)) {
      errors.push(`File extension .${extension} is not allowed`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Create a rate limiter instance
 */
export const rateLimiter = new RateLimiter();

/**
 * Common validation patterns
 */
export const patterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s()-]{10,}$/,
  url: /^https?:\/\/.+/,
  ipv4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  ipv6: /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/,
  creditCard: /^\d{13,19}$/,
  postalCode: /^\d{5}(-\d{4})?$/,
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>]).{8,}$/,
};

/**
 * Validate input against common patterns
 * @param input - Input to validate
 * @param pattern - Pattern name or regex
 * @returns True if input matches pattern
 */
export function validatePattern(input: string, pattern: keyof typeof patterns | RegExp): boolean {
  const regex = typeof pattern === 'string' ? patterns[pattern] : pattern;
  return regex.test(input);
}
