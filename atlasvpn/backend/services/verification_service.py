from sqlalchemy.orm import Session
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, List
from models import Task, TaskCompletion, TaskVerification, TaskStatus, TaskType, User
from fastapi import Request
import json
import logging
from .telegram_service import TelegramService
import os
from fastapi import HTTPException
import traceback
from fastapi import status
from sqlalchemy.exc import SQLAlchemyError
import asyncio
from sqlalchemy import select, func

logger = logging.getLogger(__name__)

class VerificationError(Exception):
    def __init__(self, message: str, error_code: str):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class VerificationService:
    def __init__(self, db: Session):
        self.db = db
        self.telegram_service = TelegramService(db)
        self.is_test_mode = os.getenv("TEST_MODE", "false").lower() == "true"
        self.CLAIM_DELAY_MINUTES = 30  # Always use 30 min delay
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)  # Enable debug logging
        self._http_clients = []
        self._closing = False
        self._cleanup_lock = asyncio.Lock()

    async def initialize(self):
        """Initialize verification service"""
        try:
            # Reset the closing flag
            self._closing = False
            
            # Initialize Telegram service if needed
            if hasattr(self.telegram_service, 'initialize'):
                await self.telegram_service.initialize()
            
            self.logger.info("Verification service initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize verification service: {str(e)}")
            return False

    async def cleanup(self):
        """Clean up resources used by the verification service"""
        try:
            # Set closing flag to prevent new operations during cleanup
            self._closing = True
            
            # Use a lock to prevent multiple simultaneous cleanups
            async with self._cleanup_lock:
                # Close any active HTTP clients
                for client in self._http_clients:
                    try:
                        if hasattr(client, 'close') and callable(client.close):
                            await client.close()
                    except Exception as e:
                        self.logger.error(f"Error closing HTTP client: {str(e)}")
                
                # Clear the client list
                self._http_clients.clear()
                
                # Clean up Telegram service if needed
                if hasattr(self.telegram_service, 'cleanup') and callable(getattr(self.telegram_service, 'cleanup')):
                    await self.telegram_service.cleanup()
                
                self.logger.info("Verification service cleaned up successfully")
                return True
        except Exception as e:
            self.logger.error(f"Error during verification service cleanup: {str(e)}")
            return False

    async def needs_captcha(self, request: Request) -> bool:
        """
        Determine if CAPTCHA is needed for a specific request.
        
        Args:
            request: The request to check
            
        Returns:
            bool: True if CAPTCHA is needed, False otherwise
        """
        # Critical paths that would require CAPTCHA in production
        critical_paths = [
            '/auth/login', 
            '/auth/register', 
            # '/auth/admin/login',  # Admin login is now exempt from CAPTCHA
            '/payment/create'
        ]
        
        # Check if path matches any critical path
        path = request.url.path
        method = request.method
        
        # Always skip CAPTCHA for admin login
        if '/auth/admin/login' in path:
            self.logger.info(f"Admin login detected, skipping CAPTCHA: {path}")
            return False
        
        # Only require CAPTCHA for POST requests to critical paths
        if method != "POST":
            return False
            
        # Check if request is in test mode (for testing without CAPTCHA)
        if self.is_test_mode:
            user_agent = request.headers.get("user-agent", "")
            if "SecurityTestAgent" in user_agent:
                return False
        
        return any(path.endswith(critical) for critical in critical_paths)
    
    async def verify_captcha(self, token: str) -> bool:
        """
        Verify a CAPTCHA token against Cloudflare Turnstile or other CAPTCHA service.
        
        Args:
            token: The CAPTCHA token to verify
            
        Returns:
            bool: True if the token is valid, False otherwise
        """
        # In test mode, accept test tokens
        if self.is_test_mode:
            if token in ["valid_token_for_testing", "bypass_captcha_in_test_mode"]:
                self.logger.info("Accepted test CAPTCHA token")
                return True
        
        # Check if token is a Cloudflare Turnstile token
        if token.startswith("0x4AAAAAAAA") or token.startswith("1x00000000"):
            self.logger.info(f"Verifying Cloudflare Turnstile test token: {token[:10]}...")
            # Accept test tokens in any environment for testing
            return True
        
        # Check if we have a secret key for verification
        captcha_secret = os.getenv("CLOUDFLARE_CAPTCHA_SECRET")
        if not captcha_secret:
            self.logger.warning("CLOUDFLARE_CAPTCHA_SECRET not configured, falling back to simple validation")
            # In absence of proper API verification, any non-empty token will pass
            return bool(token and len(token) > 20)
            
        try:
            # Log the verification attempt
            self.logger.info(f"Verifying Cloudflare Turnstile token: {token[:10]}...")
            
            # This would be replaced with an actual verification call to Cloudflare
            # For now, we'll consider any token valid for testing
            is_valid = True
            
            self.logger.info(f"CAPTCHA verification result: {is_valid}")
            return is_valid
            
        except Exception as e:
            self.logger.error(f"Error verifying CAPTCHA: {str(e)}")
            # Default to accepting in case of errors to avoid blocking users
            # In production, this would be changed to return False
            return True

    async def verify_task(
        self,
        task_id: int,
        user_id: int,
        verification_data: Optional[Dict] = None,
        request: Optional[Request] = None
    ) -> Dict:
        """Verify task completion with claim delay"""
        try:
            self.logger.debug(f"Starting task verification for task_id={task_id}, user_id={user_id}")
            self.logger.debug(f"Verification data: {verification_data}")

            # Get task and completion
            completion = self.db.query(TaskCompletion).filter(
                TaskCompletion.task_id == task_id,
                TaskCompletion.user_id == user_id
            ).first()

            if not completion:
                self.logger.error(f"Task completion not found for task_id={task_id}, user_id={user_id}")
                raise VerificationError("Task not started", "TASK_NOT_STARTED")

            # Load current verification data
            try:
                current_verification_data = json.loads(completion.verification_data) if completion.verification_data else {}
            except json.JSONDecodeError:
                current_verification_data = {}

            # Log current task status
            self.logger.debug(f"Current task status: {completion.status}")
            self.logger.debug(f"Current verification data: {current_verification_data}")

            # Check task status
            if completion.status == TaskStatus.COMPLETED:
                return {
                    "success": True,
                    "status": TaskStatus.COMPLETED,
                    "message": "Task is already completed"
                }
            elif completion.status == TaskStatus.FAILED:
                raise VerificationError("Task has failed", "TASK_FAILED")
            elif completion.status != TaskStatus.PENDING:
                raise VerificationError("Invalid task status for verification", "INVALID_TASK_STATUS")

            task = completion.task
            self.logger.debug(f"Task type: {task.type}")

            # Create verification record
            verification = TaskVerification(
                task_completion_id=completion.id,
                status=TaskStatus.PENDING,
                ip_address=str(request.client.host) if request else None,
                user_agent=request.headers.get("user-agent") if request else None,
                verification_method=self._get_verification_method(task.type),
                verification_data=json.dumps(verification_data) if verification_data else None
            )
            self.db.add(verification)

            # Handle different task types
            is_verified = False
            verification_message = ""
            
            if task.type == TaskType.YOUTUBE_VIEW:
                if not verification_data:
                    return {
                        "success": False,
                        "status": TaskStatus.PENDING,
                        "message": "Verification code required",
                        "required_format": {
                            "code": "string"
                        }
                    }
                
                # Check for either 'code' or 'verification_code'
                code = verification_data.get('code') or verification_data.get('verification_code')
                if not code:
                    return {
                        "success": False,
                        "status": TaskStatus.PENDING,
                        "message": "Verification code required",
                        "required_format": {
                            "code": "string"
                        }
                    }
                
                is_verified = code == task.verify_key
                verification_message = "Verification code validated" if is_verified else "Invalid verification code"

            elif task.type == TaskType.TELEGRAM_CHANNEL:
                if not task.platform_id:
                    raise VerificationError("Invalid task configuration: missing platform_id", "INVALID_TASK")
                
                # Use channel_id from verification data if provided
                channel_id = verification_data.get("channel_id") if verification_data else task.platform_id
                result = await self.telegram_service.verify_channel_membership(channel_id, user_id)
                is_verified = result.get("success", False)
                verification_message = result.get("message", "")

            elif task.type == TaskType.INSTAGRAM_FOLLOW:
                if not verification_data or 'username' not in verification_data:
                    return {
                        "success": False,
                        "status": TaskStatus.PENDING,
                        "message": "Username required for verification",
                        "required_format": {
                            "username": "string"
                        }
                    }

                # In test mode, auto-verify follow tasks
                if self.is_test_mode:
                    is_verified = True
                    verification_message = "Test mode: Follow verification successful"
                else:
                    # Normal mode verification
                    is_verified = True  # For now, just accept the username
                    verification_message = "Username submitted for verification"

            elif task.type == TaskType.WEBSITE_VISIT:
                if not completion.started_at:
                    self.logger.warning(f"Task {task_id} (WEBSITE_VISIT) has no started_at time.")
                    is_verified = False
                    verification_message = "Task start time missing, cannot verify duration."
                else:
                    # Assume required_duration is stored in task.config or a dedicated field
                    # Using getattr with a default value for safety
                    required_duration = getattr(task, 'required_duration', 20) # Default to 20s if not set
                    
                    # Ensure required_duration is numeric
                    try:
                        required_duration = float(required_duration)
                        if required_duration < 0: required_duration = 20.0 # Handle negative values
                    except (ValueError, TypeError):
                        self.logger.warning(f"Invalid required_duration '{required_duration}' for task {task.id}. Defaulting to 20s.")
                        required_duration = 20.0

                    # Ensure both datetimes are timezone-aware (UTC) for correct calculation
                    now_utc = datetime.now(timezone.utc)
                    started_at_utc = completion.started_at
                    if started_at_utc.tzinfo is None:
                       started_at_utc = started_at_utc.replace(tzinfo=timezone.utc) # Assume UTC if naive

                    elapsed_seconds = (now_utc - started_at_utc).total_seconds()
                    
                    self.logger.debug(f"Website Visit Check: Task={task.id}, Required={required_duration}s, Elapsed={elapsed_seconds:.2f}s")

                    if elapsed_seconds >= required_duration:
                        is_verified = True
                        verification_message = "Website visit duration requirement met."
                    else:
                        is_verified = False
                        remaining_seconds = required_duration - elapsed_seconds
                        verification_message = f"Website visit duration not met. Please wait {int(remaining_seconds)} more seconds."
                        # Consider adding remaining_seconds to the response if needed by frontend later

            elif task.type == TaskType.REFERRAL:
                return await self._verify_referral_task(task, completion, verification_data)

            # Update verification data
            if verification_data:
                current_verification_data.update(verification_data)
            current_verification_data["is_verified"] = is_verified
            current_verification_data["last_verified_at"] = datetime.utcnow().isoformat()

            # Update completion status
            try:
                # First update verification data and is_verified flag
                completion.verification_data = json.dumps(current_verification_data)
                completion.is_verified = is_verified
                self.db.flush()  # Flush changes to ensure is_verified is set

                # Then update other fields if verified
                if is_verified:
                    verification.status = TaskStatus.COMPLETED
                    completion.status = TaskStatus.COMPLETED
                    completion.completed_at = datetime.utcnow()
                    completion.claim_available_at = datetime.utcnow() + timedelta(minutes=self.CLAIM_DELAY_MINUTES)

                self.db.commit()
                self.logger.info(f"Task {task_id} verification completed. Is verified: {is_verified}")

                return {
                    "success": is_verified,
                    "status": completion.status,
                    "message": verification_message,
                    "claim_available_at": completion.claim_available_at.isoformat() if completion.claim_available_at else None
                }

            except SQLAlchemyError as e:
                self.logger.error(f"Database error updating verification: {str(e)}")
                self.db.rollback()
                raise VerificationError("Failed to update verification status", "DB_ERROR")

        except VerificationError as e:
            self.logger.error(f"Verification error: {e.message} ({e.error_code})")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": e.message, "code": e.error_code}
            )
        except Exception as e:
            self.logger.error(f"Error verifying task: {str(e)}")
            self.logger.error(traceback.format_exc())
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail={"message": "Internal server error during task verification", "error": str(e)}
            )

    def _get_verification_method(self, task_type: TaskType) -> str:
        """Get verification method based on task type"""
        if task_type == TaskType.TELEGRAM_CHANNEL:
            return "api"  # Uses Telegram API
        elif task_type in [TaskType.YOUTUBE_VIEW, TaskType.INSTAGRAM_VIEW, TaskType.WEBSITE_VISIT]:
            return "code"  # Uses verification code
        elif task_type in [TaskType.INSTAGRAM_FOLLOW, TaskType.TWITTER_FOLLOW]:
            return "manual"  # Manual username verification
        elif task_type == TaskType.REFERRAL:
            return "code"  # Uses referral code
        return "auto"  # Default to automatic

    def _get_next_verification_time(self, task_type: TaskType) -> Optional[datetime]:
        """Get next verification time based on task type"""
        if task_type in [TaskType.YOUTUBE_VIEW, TaskType.INSTAGRAM_VIEW]:
            return datetime.utcnow() + timedelta(minutes=10)
        elif task_type in [TaskType.INSTAGRAM_FOLLOW, TaskType.TWITTER_FOLLOW]:
            return datetime.utcnow() + timedelta(hours=1)
        return None

    async def _verify_by_type(
        self,
        task: Task,
        completion: TaskCompletion,
        verification_data: Optional[Dict]
    ) -> Dict:
        """Verify task based on its type"""
        
        # In test mode, auto-verify with correct code
        if self.is_test_mode:
            if verification_data and verification_data.get('verification_code') == 'TEST123':
                return {"success": True}
            elif not verification_data:
                return {
                    "success": False,
                    "status": TaskStatus.PENDING,
                    "message": "Verification code required",
                    "error_code": "CODE_REQUIRED"
                }
            else:
                return {
                    "success": False,
                    "status": TaskStatus.PENDING,
                    "message": "Invalid verification code",
                    "error_code": "INVALID_CODE"
                }
        
        # Handle Telegram channel verification
        if task.type == TaskType.TELEGRAM_CHANNEL:
            if not task.platform_id:
                raise VerificationError("Invalid task configuration", "INVALID_TASK")
            return await self.telegram_service.verify_channel_membership(task.platform_id, completion.user_id)
        
        # Handle view tasks (YouTube/Instagram)
        elif task.type in [TaskType.YOUTUBE_VIEW, TaskType.INSTAGRAM_VIEW]:
            return await self._verify_view_task(task, completion, verification_data)
        
        # Handle follow tasks (Instagram/Twitter)
        elif task.type in [TaskType.INSTAGRAM_FOLLOW, TaskType.TWITTER_FOLLOW]:
            return await self._verify_follow_task(task, completion, verification_data)
        
        # Handle referral tasks
        elif task.type == TaskType.REFERRAL:
            return await self._verify_referral_task(task, completion, verification_data)
        
        # Auto-complete other tasks
        return {"success": True}

    async def _verify_view_task(
        self,
        task: Task,
        completion: TaskCompletion,
        verification_data: Optional[Dict]
    ) -> Dict:
        """Verify view tasks with code and time check"""
        if not verification_data:
            raise VerificationError("Verification code required", "CODE_REQUIRED")
            
        # Check for either 'code' or 'verification_code' in the data
        code = verification_data.get('code') or verification_data.get('verification_code')
        if not code:
            raise VerificationError("Verification code required", "CODE_REQUIRED")
            
        if code != task.verify_key:
            return {
                "success": False,
                "status": TaskStatus.PENDING,
                "message": "Invalid verification code",
                "error_code": "INVALID_CODE"
            }
            
        # Skip time check in test mode
        if not self.is_test_mode:
            # Check time requirement
            if completion.last_verified_at:
                time_passed = datetime.utcnow() - completion.last_verified_at
                if time_passed < timedelta(minutes=10):
                    remaining = timedelta(minutes=10) - time_passed
                    return {
                        "success": False,
                        "status": TaskStatus.PENDING,
                        "message": f"Please wait {int(remaining.total_seconds() / 60)} more minutes",
                        "error_code": "TIME_REMAINING",
                        "verification_time_left": int(remaining.total_seconds())
                    }
                return {"success": True}
            
            # First verification, start timer
            completion.last_verified_at = datetime.utcnow()
            return {
                "success": False,
                "status": TaskStatus.PENDING,
                "message": "Code verified. Please wait 10 minutes to complete verification.",
                "error_code": "WAITING_PERIOD",
                "verification_time_left": 600  # 10 minutes in seconds
            }
        
        # In test mode, return success immediately
        return {"success": True}

    async def _verify_follow_task(
        self,
        task: Task,
        completion: TaskCompletion,
        verification_data: Optional[Dict]
    ) -> Dict:
        """Verify follow tasks with username and time check"""
        if not verification_data or "username" not in verification_data:
            raise VerificationError("Username required", "USERNAME_REQUIRED")
            
        # Check time requirement
        if completion.last_verified_at:
            time_passed = datetime.utcnow() - completion.last_verified_at
            if time_passed < timedelta(hours=1):
                remaining = timedelta(hours=1) - time_passed
                return {
                    "success": False,
                    "error": f"Please wait {int(remaining.total_seconds() / 3600)} more hours"
                }
            return {"success": True}
        
        # First verification, start timer
        completion.last_verified_at = datetime.utcnow()
        return {
            "success": False,
            "error": "Username verified. Please wait 1 hour to complete verification."
        }

    async def _verify_referral_task(
        self,
        task: Task,
        completion: TaskCompletion,
        verification_data: Optional[Dict] = None
    ) -> Dict:
        """Verify a referral task by checking the actual referral count."""
        try:
            # Get user's referral count
            stmt = select(func.count(User.id)).where(
                User.referred_by == completion.user_id,
                User.is_active == True
            )
            result = await self.db.execute(stmt)
            active_referrals = result.scalar_one_or_none() or 0
            
            # Check if user has reached the target
            target_achieved = active_referrals >= task.target_value
            
            # Update progress in task completion
            completion.current_progress = active_referrals
            
            # Set task as completed if target achieved
            success = False
            message = ""
            
            if target_achieved:
                success = True
                completion.status = TaskStatus.COMPLETED
                message = f"Congratulations! You've reached your referral target of {task.target_value} active referrals."
            else:
                remaining = task.target_value - active_referrals
                message = f"You need {remaining} more active referrals to complete this task."
            
            # Save changes
            await self.db.commit()
            
            return {
                "success": success,
                "status": completion.status,
                "message": message,
                "current_progress": active_referrals,
                "target": task.target_value
            }
            
        except Exception as e:
            self.logger.error(f"Error verifying referral task: {str(e)}")
            await self.db.rollback()
            return {
                "success": False,
                "status": TaskStatus.PENDING,
                "message": f"Error verifying referral task: {str(e)}",
                "error_code": "VERIFICATION_ERROR"
        }

    def get_verification_history(self, completion_id: int) -> List[TaskVerification]:
        """Get verification history for a task completion"""
        return self.db.query(TaskVerification).filter(
            TaskVerification.task_completion_id == completion_id
        ).order_by(TaskVerification.created_at.desc()).all()

    def can_verify(self, completion: TaskCompletion) -> bool:
        """Check if a task can be verified"""
        if not completion.last_verified_at:
            return True
            
        latest_verification = self.db.query(TaskVerification).filter(
            TaskVerification.task_completion_id == completion.id
        ).order_by(TaskVerification.created_at.desc()).first()
        
        if not latest_verification or not latest_verification.next_verification:
            return True
            
        return datetime.utcnow() >= latest_verification.next_verification 