"""Base schemas and utilities for all schema modules"""
from pydantic import BaseModel, Field, ConfigDict, field_validator, ValidationInfo
from typing import Any, List, Dict
from enum import Enum
import re
import html

class RoleEnum(str, Enum):
    """User role enumeration"""
    admin = "admin"
    reseller = "reseller"
    user = "user"

    @classmethod
    def from_str(cls, value: str) -> 'RoleEnum':
        try:
            return cls(value.lower())
        except ValueError:
            raise ValueError(f"Invalid role value: {value}")

class BaseModelConfig(BaseModel):
    """Base model with common configuration and validation"""
    model_config = ConfigDict(
        from_attributes=True,
        str_strip_whitespace=True,
        validate_assignment=True,
        extra='forbid'
    )

    @field_validator('*')
    @classmethod
    def sanitize_strings(cls, v: Any, info: ValidationInfo) -> Any:
        """Sanitize string inputs to prevent XSS and other security issues"""
        if isinstance(v, str):
            # Remove HTML tags
            v = re.sub(r'<[^>]*?>', '', v)
            # Escape special characters
            v = html.escape(v)
            # Remove potentially dangerous patterns
            v = re.sub(r'javascript:', '', v, flags=re.IGNORECASE)
            v = re.sub(r'data:', '', v, flags=re.IGNORECASE)
            v = re.sub(r'vbscript:', '', v, flags=re.IGNORECASE)
            # Remove null bytes
            v = v.replace('\x00', '')
            # Normalize whitespace
            v = ' '.join(v.split())
        elif isinstance(v, dict):
            return {k: cls.sanitize_strings(val, info) for k, val in v.items()}
        elif isinstance(v, list):
            return [cls.sanitize_strings(item, info) for item in v]
        return v 