import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useCurrentUserQuery } from '../hooks/authHooks';
import { secureTelegramStorage } from '../utils/secureTelegramStorage';
import LoadingSpinner from '../components/LoadingSpinner';
import type { User } from '../types';

interface ProtectedRouteProps {
  children: React.ReactNode | ((props: { user: User }) => React.ReactNode);
}

// Base route for any authenticated user
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { data: user, isLoading, isFetching, isError, error } = useCurrentUserQuery();
  const location = useLocation();

  // User is authenticated if we have user data
  const isAuthenticated = !!user;

  // Only show loading during initial load, not background fetching
  if (isLoading) {
    console.log('[ProtectedRoute] Rendering LoadingSpinner because query is initially loading');
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-white/70 text-sm">Verifying authentication...</p>
        </div>
      </div>
    );
  }

  // Handle error state from the query, e.g., redirect to login or show error message
  // For now, if error and no user, treat as unauthenticated
  if (isError && !user) {
    console.error(
      '[ProtectedRoute] Error fetching user and no cached user, redirecting to login:',
      error
    );
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  if (!isAuthenticated) {
    console.log('[ProtectedRoute] Not authenticated, redirecting to login.');
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  if (typeof children === 'function') {
    // Pass the user data to the children function if it expects it.
    // Ensure user is not null here, though isAuthenticated should guard this.
    if (user) {
      return <>{children({ user })}</>;
    }
    // Fallback or error if user is null but was expected (should ideally not happen if isAuthenticated is true)
    console.error(
      '[ProtectedRoute] User is null despite being authenticated. This should not happen.'
    );
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

// Simple route for new user setup
export const NewUserRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { data: user, isLoading, isFetching, isError, error } = useCurrentUserQuery();
  const location = useLocation();
  const [isActuallyNewUser, setIsActuallyNewUser] = useState<boolean | null>(null);

  const isAuthenticated = !!user;

  // Check if user needs setup using secure storage
  useEffect(() => {
    const checkNewUserStatus = async () => {
      try {
        const hasSetupData = await secureTelegramStorage.hasAnyData();
        setIsActuallyNewUser(hasSetupData);
      } catch (error) {
        console.error('Error checking new user status:', error);
        setIsActuallyNewUser(false);
      }
    };

    checkNewUserStatus();
  }, []);

  // TODO: Determine isNewUser status. This might come from user object (e.g. user.isNewUser)
  // or from secure storage as set by useTelegramLoginMutation.
  // For now, we'll assume new user setup requires authentication first.

  if (isLoading || isActuallyNewUser === null) {
    console.log(
      '[NewUserRoute] Rendering LoadingOverlay because query is loading or checking setup status:',
      { isLoading, isActuallyNewUser }
    );
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-white/70 text-sm">Checking user setup status...</p>
        </div>
      </div>
    );
  }

  if (isError && !user) {
    console.error('[NewUserRoute] Error fetching user, redirecting to home:', error);
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // Must be authenticated to even consider new user setup page
  if (!isAuthenticated) {
    console.log('[NewUserRoute] Not authenticated, redirecting to home.');
    return <Navigate to="/" replace />;
  }

  // If authenticated AND identified as a new user needing setup
  if (isAuthenticated && isActuallyNewUser) {
    return <>{children}</>;
  }

  // If authenticated but NOT a new user (or setup complete), redirect from new-user-setup page
  if (isAuthenticated && !isActuallyNewUser && location.pathname === '/new-user-setup') {
    console.log(
      '[NewUserRoute] Authenticated but not a new user, or setup complete. Redirecting from /new-user-setup to home.'
    );
    return <Navigate to="/" replace />;
  }

  // If trying to access a different route that is wrapped by NewUserRoute but isn't a new user, allow it (should not happen with current routing)
  // This case is more of a safeguard if NewUserRoute were to wrap more than just /new-user-setup
  if (isAuthenticated && !isActuallyNewUser) {
    return <Navigate to="/" replace />; // Or some other appropriate redirect
  }

  // Fallback redirect if conditions are not met (e.g. somehow not authenticated and not loading)
  return <Navigate to="/" replace />;
};
