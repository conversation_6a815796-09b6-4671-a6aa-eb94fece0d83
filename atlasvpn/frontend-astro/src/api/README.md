# API Client Documentation

This directory contains the centralized API client for the AtlasVPN Astro frontend application.

## Structure

```
src/api/
├── index.ts          # Main exports - import everything from here
├── client.ts         # Core API client (axios + fetch implementations)
├── services.ts       # API service methods organized by domain
├── hooks.ts          # React Query hooks for data fetching
├── types.ts          # TypeScript interfaces and types
├── utils.ts          # Utilities, constants, and helper functions
└── README.md         # This documentation
```

## Quick Start

### Basic Usage

```typescript
// Import everything you need from the main index
import { 
  api, 
  apiClient, 
  authService, 
  useCurrentUserQuery,
  API_CONSTANTS 
} from '@/api';

// Use the axios instance directly
const response = await api.get('/users/me');

// Use service methods
const user = await authService.getCurrentUser();

// Use React Query hooks in components
function UserProfile() {
  const { data: user, isLoading } = useCurrentUserQuery();
  
  if (isLoading) return <div>Loading...</div>;
  return <div>Hello, {user?.username}!</div>;
}
```

### Server-Side Usage (Astro)

```typescript
// In Astro components or API routes
import { createServerApiClient } from '@/api';

// Create server-side client with cookies
const serverApi = createServerApiClient(Astro.request);
const user = await serverApi.get('/users/me');
```

## Core Components

### 1. API Client (`client.ts`)

Provides two main implementations:

- **Axios Instance (`api`)**: For general API calls with interceptors
- **APIClient Class (`apiClient`)**: For Telegram WebApp authentication
- **Server Client (`createServerApiClient`)**: For server-side requests

```typescript
import { api, apiClient, createServerApiClient } from '@/api';

// Axios instance with interceptors
const response = await api.get('/endpoint');

// Telegram WebApp authenticated requests
const data = await apiClient.request('GET', '/protected-endpoint');

// Server-side with cookies
const serverApi = createServerApiClient(request);
```

### 2. Services (`services.ts`)

Organized API methods by domain:

```typescript
import { 
  authService, 
  userService, 
  cardService, 
  taskService,
  referralService,
  vpnService,
  chatService,
  systemService 
} from '@/api';

// Authentication
await authService.login({ username, password });
await authService.telegramLogin({ initData });

// User operations
const stats = await userService.getStats();
await userService.updateProfile({ name: 'New Name' });

// Cards
const cards = await cardService.getAll();
await cardService.buy({ cardId: 1 });

// Tasks
const tasks = await taskService.getAvailable();
await taskService.start({ taskId: 1 });

// And more...
```

### 3. React Query Hooks (`hooks.ts`)

Ready-to-use hooks for components:

```typescript
import { 
  useCurrentUserQuery,
  useUserStatsQuery,
  useTasksQuery,
  useBuyCardMutation,
  QUERY_KEYS 
} from '@/api';

function Dashboard() {
  // Queries
  const { data: user } = useCurrentUserQuery();
  const { data: stats } = useUserStatsQuery();
  const { data: tasks } = useTasksQuery();
  
  // Mutations
  const buyCard = useBuyCardMutation();
  
  const handleBuyCard = (cardId: number) => {
    buyCard.mutate({ cardId });
  };
  
  return (
    <div>
      <h1>Welcome, {user?.username}</h1>
      <p>Balance: {stats?.balance}</p>
      {/* ... */}
    </div>
  );
}
```

### 4. Types (`types.ts`)

Comprehensive TypeScript definitions:

```typescript
import type { 
  User, 
  UserStats, 
  Card, 
  Task, 
  ApiResponse,
  LoginParams,
  PaginatedResponse 
} from '@/api';

// Use in your components
interface Props {
  user: User;
  stats: UserStats;
}
```

### 5. Utilities (`utils.ts`)

Helper functions and constants:

```typescript
import { 
  API_CONSTANTS,
  handleApiError,
  createQueryKey,
  urlUtils,
  dataUtils 
} from '@/api';

// Constants
console.log(API_CONSTANTS.STATUS_CODES.UNAUTHORIZED); // 401

// Error handling
try {
  await api.get('/endpoint');
} catch (error) {
  const message = handleApiError(error);
  console.error(message);
}

// Query keys
const userKey = createQueryKey.user.stats();

// URL utilities
const url = urlUtils.combineUrl(baseUrl, '/endpoint');
const query = urlUtils.buildQueryString({ page: 1, limit: 10 });
```

## Authentication

### Telegram WebApp Authentication

The API client automatically handles Telegram WebApp authentication:

```typescript
import { apiClient } from '@/api';

// Automatically authenticates with Telegram WebApp data
const response = await apiClient.request('GET', '/protected-endpoint');
```

### Traditional Authentication

```typescript
import { authService, useCurrentUserQuery } from '@/api';

// Login
const result = await authService.login({ 
  username: '<EMAIL>', 
  password: 'password' 
});

// Check authentication status
function AuthGuard({ children }) {
  const { data: user, isLoading } = useCurrentUserQuery();
  
  if (isLoading) return <div>Loading...</div>;
  if (!user) return <div>Please log in</div>;
  
  return children;
}
```

## Error Handling

The API client provides comprehensive error handling:

```typescript
import { handleApiError, APIError } from '@/api';

try {
  await api.get('/endpoint');
} catch (error) {
  if (error instanceof APIError) {
    console.log('Status:', error.status);
    console.log('Message:', error.message);
  }
  
  // Get user-friendly error message
  const message = handleApiError(error);
  toast.error(message);
}
```

## Query Keys and Caching

Use the provided query key factory for consistent caching:

```typescript
import { createQueryKey, QUERY_KEYS } from '@/api';
import { useQueryClient } from '@tanstack/react-query';

function Component() {
  const queryClient = useQueryClient();
  
  // Invalidate specific queries
  const invalidateUserStats = () => {
    queryClient.invalidateQueries({ 
      queryKey: createQueryKey.user.stats() 
    });
  };
  
  // Prefetch data
  const prefetchTasks = () => {
    queryClient.prefetchQuery({
      queryKey: QUERY_KEYS.TASKS.AVAILABLE,
      queryFn: () => taskService.getAvailable()
    });
  };
}
```

## Environment Configuration

The API client automatically detects the environment and configures URLs:

- **Development**: Uses `localhost:8000` for API calls
- **Production**: Uses the current domain or `PUBLIC_API_URL`
- **Server-side**: Uses environment variables or defaults

## Migration from Legacy Code

**✅ MIGRATION COMPLETED**: All legacy hook files have been successfully migrated to the centralized API structure.

The following files have been consolidated into `/src/api/hooks.ts`:
- `src/hooks/authHooks.ts` → Authentication hooks
- `src/hooks/taskHooks.ts` → Task hooks and utilities
- `src/hooks/transactionHooks.ts` → Transaction hooks and utilities
- `src/hooks/useApiQueries.ts` → General API queries
- `src/hooks/vpnHooks.ts` → VPN hooks and utilities

For detailed migration instructions, see the [Migration Guide](../../MIGRATION_GUIDE.md).

### Quick Migration Steps:
1. Update imports from `../hooks/*` to `../api/hooks`
2. Check for renamed hooks (e.g., `useCardsQuery` → `useAllCardsQuery`)
3. Import types from `../api/types`
4. Utilize new utility functions and enhanced error handling

```typescript
// Old way
// Use centralized API imports:
import { api, useCurrentUserQuery } from '@/api';
```

The new structure maintains backward compatibility while providing better organization and type safety.

## Best Practices

1. **Import from main index**: Always import from `@/api` for consistency
2. **Use services for complex logic**: Don't put business logic in components
3. **Leverage React Query**: Use the provided hooks for automatic caching and synchronization
4. **Handle errors gracefully**: Use the error handling utilities
5. **Type everything**: Take advantage of the comprehensive type definitions
6. **Use query keys**: Leverage the query key factory for cache management

## Contributing

When adding new API endpoints:

1. Add types to `types.ts`
2. Add service methods to `services.ts`
3. Add React Query hooks to `hooks.ts`
4. Update query keys in `utils.ts` if needed
5. Export everything from `index.ts`

## File Structure

The API layer is organized as follows:

- `src/api/hooks.ts` → All API hooks (consolidated)
- `src/api/services.ts` → Service layer implementations  
- `src/api/types.ts` → TypeScript definitions
- `src/api/client.ts` → HTTP client configuration
- `src/api/utils.ts` → Utilities and constants

## Migration from Legacy Structure

1. Update imports from `../hooks/*` to `../api/hooks`
2. Use the new consolidated hook names (see hooks.ts for available exports)
3. Update type imports to use `../api/types`