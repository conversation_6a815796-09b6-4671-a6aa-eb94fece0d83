#!/usr/bin/env python3
import sqlite3
import os

def fix_user_schema():
    """Remove is_verified column from users table if it exists"""
    db_path = 'users.db'
    
    if not os.path.exists(db_path):
        print('Database does not exist')
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if is_verified column exists in users table
        cursor.execute('PRAGMA table_info(users)')
        columns = cursor.fetchall()
        
        has_is_verified = any(col[1] == 'is_verified' for col in columns)
        print(f'Database exists. Has is_verified column: {has_is_verified}')
        
        if has_is_verified:
            print('Dropping is_verified column from users table...')
            # SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
            cursor.execute('BEGIN TRANSACTION')
            
            # Create new table without is_verified
            cursor.execute('''
                CREATE TABLE users_new (
                    id INTEGER PRIMARY KEY,
                    telegram_id INTEGER UNIQUE,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    hashed_password VARCHAR(255),
                    role VARCHAR(20) DEFAULT 'user' NOT NULL,
                    wallet_balance FLOAT DEFAULT 0.0 NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP NOT NULL,
                    first_name VARCHAR(50),
                    last_name VARCHAR(50),
                    email VARCHAR(100),
                    is_active BOOLEAN DEFAULT 1 NOT NULL,
                    telegram_photo_url TEXT,
                    discount_percent FLOAT DEFAULT 0.0,
                    marzban_username VARCHAR(100),
                    marzban_subscription_url TEXT,
                    referred_by INTEGER,
                    referral_code VARCHAR(8) UNIQUE NOT NULL,
                    device_platform VARCHAR(50),
                    device_id VARCHAR(50),
                    last_login DATETIME,
                    last_passive_claim_at DATETIME,
                    FOREIGN KEY (referred_by) REFERENCES users(id)
                )
            ''')
            
            # Copy data from old table to new table (excluding is_verified)
            cursor.execute('''
                INSERT INTO users_new 
                SELECT id, telegram_id, username, hashed_password, role, wallet_balance, 
                       created_at, first_name, last_name, email, is_active, telegram_photo_url,
                       discount_percent, marzban_username, marzban_subscription_url, 
                       referred_by, referral_code, device_platform, device_id, 
                       last_login, last_passive_claim_at
                FROM users
            ''')
            
            # Drop old table and rename new table
            cursor.execute('DROP TABLE users')
            cursor.execute('ALTER TABLE users_new RENAME TO users')
            
            cursor.execute('COMMIT')
            print('Successfully removed is_verified column from users table')
        else:
            print('is_verified column not found in users table - no action needed')
    
    except Exception as e:
        cursor.execute('ROLLBACK')
        print(f'Error fixing schema: {e}')
    finally:
        conn.close()

if __name__ == '__main__':
    fix_user_schema() 