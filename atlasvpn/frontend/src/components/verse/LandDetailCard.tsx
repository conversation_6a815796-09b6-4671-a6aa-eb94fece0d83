/**
 * @file LandDetailCard.tsx
 * @description UI component to display details of a selected land plot with glassmorphism style.
 */

import React from 'react';
import { motion } from 'framer-motion';
import { LandPlot } from '../../utils/mockVerseData';

interface LandDetailCardProps {
  land: LandPlot;
  onClose: () => void;
  onPaint: (land: LandPlot) => void;
}

// --- Helper functions (Keep these as they are independent of UI library) ---
function formatRarity(rarity: LandPlot['rarity']) {
  switch (rarity) {
    case 'common':
      return 'Common';
    case 'uncommon':
      return 'Uncommon';
    case 'rare':
      return 'Rare';
    case 'epic':
      return 'Epic';
    case 'legendary':
      return 'Legendary';
    default:
      return 'Unknown';
  }
}

function formatStatus(status: LandPlot['status']) {
  switch (status) {
    case 'owned':
      return 'Owned';
    case 'for_sale':
      return 'For Sale';
    case 'unclaimed':
      return 'Unclaimed';
    default:
      return 'Unknown';
  }
}

// Helper for rarity text color (using Tailwind classes)
function getRarityColorClass(rarity: LandPlot['rarity']): string {
  switch (rarity) {
    case 'common':
      return 'text-gray-300';
    case 'uncommon':
      return 'text-emerald-400';
    case 'rare':
      return 'text-blue-400';
    case 'epic':
      return 'text-purple-400';
    case 'legendary':
      return 'text-amber-400';
    default:
      return 'text-gray-500';
  }
}

// --- Component Implementation ---
export function LandDetailCard({
  land,
  onClose,
  onPaint,
}: LandDetailCardProps): React.ReactElement {
  return (
    <motion.div
      initial={{ opacity: 0, y: 40, scale: 0.95, filter: 'blur(8px)' }}
      animate={{ opacity: 1, y: 0, scale: 1, filter: 'blur(0px)' }}
      exit={{ opacity: 0, y: 40, scale: 0.95, filter: 'blur(8px)' }}
      transition={{ type: 'spring', stiffness: 320, damping: 28, mass: 0.7 }}
      className="bg-black/40 backdrop-blur-md border border-white/10 rounded-lg shadow-lg text-white overflow-hidden"
    >
      {/* Header */}
      <div className="flex items-start justify-between p-4 border-b border-white/10">
        <div>
          <h3 className="text-lg font-semibold leading-tight text-gray-100">{land.name}</h3>
          <p className="text-xs text-gray-400">ID: {land.id}</p>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white transition-colors ml-2 p-1 -mt-1 -mr-1 rounded-full hover:bg-white/10 font-bold"
          aria-label="Close"
        >
          X
        </button>
      </div>

      {/* Content */}
      <div className="p-4 space-y-3 text-sm">
        <div className="flex items-center justify-between">
          <span className="text-gray-400">Rarity:</span>
          <span className={`font-medium capitalize ${getRarityColorClass(land.rarity)}`}>
            {formatRarity(land.rarity)}
          </span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-gray-400">Size:</span>
          <span className="text-gray-200">{land.angularSize.toFixed(1)}&deg;</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-gray-400">Coordinates:</span>
          <span className="text-gray-200">
            ({land.lat.toFixed(2)}, {land.lon.toFixed(2)})
          </span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-gray-400">Status:</span>
          <span className="font-medium text-gray-200">{formatStatus(land.status)}</span>
        </div>
        {land.status === 'owned' && land.owner && (
          <div className="flex items-center justify-between">
            <span className="text-gray-400">Owner:</span>
            <span className="text-gray-200 truncate max-w-[120px]">{land.owner}</span>
          </div>
        )}
        {land.status === 'for_sale' && land.price !== undefined && (
          <div className="flex items-center justify-between">
            <span className="text-gray-400">Price:</span>
            <span className="font-semibold text-gray-100">
              {land.price.toLocaleString()} Tokens
            </span>
          </div>
        )}
      </div>

      {/* Footer with placeholder actions */}
      <div className="px-4 pb-4 pt-2">
        {/* Opportunity or premium message */}
        {land.status === 'for_sale' && land.price !== undefined && land.price <= 20 && (
          <div className="mb-2 text-xs text-yellow-300 font-semibold text-center">
            Opportunity: This land is a rare bargain!
          </div>
        )}
        {land.status === 'for_sale' && land.price !== undefined && land.price >= 900 && (
          <div className="mb-2 text-xs text-amber-400 font-semibold text-center">
            Premium: High-value land plot.
          </div>
        )}
        {land.status === 'for_sale' && (
          <button
            className="w-full px-3 py-1.5 text-sm font-medium rounded-md bg-purple-600 hover:bg-purple-700 disabled:opacity-50 transition-colors"
            disabled
          >
            Buy Now
          </button>
        )}
        {land.status === 'unclaimed' && (
          <button
            className="w-full px-3 py-1.5 text-sm font-medium rounded-md bg-blue-600 hover:bg-blue-700 disabled:opacity-50 transition-colors"
            disabled
          >
            Claim Plot
          </button>
        )}
        {land.status === 'owned' && (
          <div className="flex space-x-2">
            <button
              className="flex-1 px-3 py-1.5 text-sm font-medium rounded-md border border-white/20 bg-white/5 hover:bg-white/10 disabled:opacity-50 transition-colors"
              disabled
            >
              View Details
            </button>
            <button
              className="flex-1 px-3 py-1.5 text-sm font-medium rounded-md bg-accent-500 hover:bg-accent-600 transition-colors"
              onClick={() => onPaint(land)}
            >
              Paint Land
            </button>
          </div>
        )}
      </div>
    </motion.div>
  );
}
