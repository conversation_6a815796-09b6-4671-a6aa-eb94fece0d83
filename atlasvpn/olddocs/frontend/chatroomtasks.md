# Public & Private Chat System for Verse Page — Implementation Plan (v2 - Privacy Focus)

## Prompt & Overview

Implement public and private chat systems. **Key Requirement:** Store all chat history, but **hide usernames** in all chat displays (public and private interfaces) for privacy. Use avatars or generic identifiers instead. Usernames should only be visible when searching for a user to start a *new* private chat.

-   **Public Chat:** Visible to all. Preview at bottom, glassy input, opens full dialog. 3D pauses when open. Usernames hidden.
-   **Private Chat:** Direct user-to-user messages. UI for selecting users (search shows usernames, chat view hides them), separate chat views, persistence. Usernames hidden in chat view.
-   **General:** Modular, modern, glassy, mobile-friendly UI. Backend: FastAPI, SQLAlchemy, async DB. Pause 3D for any full chat view.

---

## 1. Frontend: Modular Chat Components

### A. UI/UX Structure

-   **Mini Chat Hub (Verse Bottom Bar):**
    -   Shows preview of the *last active chat* (public OR private). Indicates sender generically (e.g., avatar).
    -   Buttons/Tabs: "Public Chat", "Private Messages".
    -   `GlassyMessageInput` relevant to selected view.
    -   <PERSON><PERSON> to open the full chat interface.
-   **Full Chat Interface (Modal/Overlay):**
    -   Pauses 3D rendering/animation.
    -   Tabs/Sidebar: "Public Chat", "Private Conversations".
        -   **Public Chat View:** Shows history. Messages displayed with generic sender identification (e.g., Avatar + "User 1234" or similar derived from `sender_id`, consistent per user). Input at bottom.
        -   **Private Chat View:**
            -   Lists ongoing private conversations (e.g., "Chat with [Avatar/User ID]").
            -   Button/Search (`UserSearch`) to start *new* private conversation. **This search MUST show usernames** to allow selection, but the username is *not* shown once the chat thread is open.
            -   Displays selected private chat history. Messages identified by sender (e.g., "Me" vs. "Them" via alignment/styling and avatar). Input at bottom.
    -   Modern, glassy UI. Close button.
-   **Modularity:**
    -   `ChatHubPreview` component.
    -   `FullChatDialog` component.
    -   `PublicChatView` component.
    -   `PrivateChatListView` component.
    -   `PrivateChatThreadView` component.
    -   `GlassyMessageInput` component.
    -   `UserSearch` component (shows usernames for selection).

### B. State & Data Flow

-   Zustand/Context state:
    -   Public messages (`ChatMessageData[]`).
    -   Private conversations map (`{[partnerUserId: number]: ChatMessageData[]}`).
    -   Current chat view context (public, private list, specific private thread `partnerUserId`).
    -   Input state per context.
    -   `isChatOpen` flag (pauses 3D).
    -   User search results (`{id: number, username: string, avatar?: string}[]`).
    -   Map of `user_id` to display info (avatar, generated alias) fetched as needed.
-   WebSocket for real-time messages (containing `sender_id` but not `sender_username`).
-   REST API for initial history, conversation list, user search.

---

## 2. Backend: FastAPI Public & Private Chat

### A. Models (`models.py`)

`ChatMessage` model stores necessary data, **excluding** `sender_username` as it won't be displayed directly in chat views. Frontend will map `sender_id` to display info (avatar/alias) if needed.

```python
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text, Index
from sqlalchemy.orm import relationship
from datetime import datetime

# Assuming User model exists with id, username, avatar_url etc.
# from .user import User # Example import

class ChatMessage(Base):
    __tablename__ = 'chat_messages'
    id = Column(Integer, primary_key=True, index=True)
    sender_id = Column(Integer, ForeignKey('users.id'), nullable=False, index=True)
    # sender_username = Column(String(50), nullable=False) # REMOVED for privacy in display
    receiver_id = Column(Integer, ForeignKey('users.id'), nullable=True, index=True) # NULL for public
    content = Column(Text, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, index=True)

    # Relationships still useful for potential backend logic or admin views
    sender = relationship('User', foreign_keys=[sender_id], back_populates='sent_messages')
    receiver = relationship('User', foreign_keys=[receiver_id], back_populates='received_messages')

    __table_args__ = (
        Index('ix_private_message_pair', 'sender_id', 'receiver_id', 'created_at'),
        Index('ix_public_messages', 'receiver_id', 'created_at', postgresql_where=(receiver_id.is_(None))),
    )

# In your User model, add relationships back:
# sent_messages = relationship("ChatMessage", foreign_keys=[ChatMessage.sender_id], back_populates="sender")
# received_messages = relationship("ChatMessage", foreign_keys=[ChatMessage.receiver_id], back_populates="receiver")
```
*Run Alembic migration after removing `sender_username`.*

### B. WebSocket Endpoint (FastAPI)

-   Authenticates user, maps `user_id` to `WebSocket`.
-   Message payload contains `sender_id` but **not** `sender_username`.

**Example Structure Update:**
```python
# routers/chat_router.py (Key Changes)
# ... imports ...

active_connections: Dict[int, WebSocket] = {}

async def send_personal_message(message: dict, user_id_target: int):
    # ... (no change needed in sending logic itself) ...

async def broadcast_message(message: dict, sender_id: int):
    # ... (no change needed in sending logic itself) ...

@router.websocket('/ws/chat/{token}')
async def websocket_endpoint(websocket: WebSocket, token: str, db: AsyncSession = Depends(get_async_db)):
    # --- Authentication ---
    # user_id = get_current_user_id(token) # Use your real auth
    # --- Placeholder Auth ---
    try:
        user_id = int(token) # DANGEROUS: Use real auth!
        # Verify user exists
        user_exists = await db.scalar(select(User.id).where(User.id == user_id))
        if not user_exists: await websocket.close(code=1008); return
    except ValueError: await websocket.close(code=1008); return
    # --- End Placeholder ---

    await websocket.accept()
    active_connections[user_id] = websocket
    print(f"User {user_id} connected. Total: {len(active_connections)}")

    try:
        while True:
            data = await websocket.receive_json()
            content = data.get('content')
            receiver_user_id = data.get('receiver_id') # null/None for public

            if not content or len(content) > 1000: continue # Basic validation

            # --- Save Message (No sender_username) ---
            msg = ChatMessage(
                sender_id=user_id,
                receiver_id=receiver_user_id,
                content=content
            )
            db.add(msg)
            await db.commit()
            await db.refresh(msg)

            # --- Prepare message data (No sender_username) ---
            message_data = {
                'id': msg.id,
                'sender_id': msg.sender_id,
                'receiver_id': msg.receiver_id,
                'content': msg.content,
                'created_at': msg.created_at.isoformat(),
                'is_public': receiver_user_id is None,
            }

            # --- Send/Broadcast ---
            if msg.receiver_id is None: # Public
                await broadcast_message(message_data, user_id)
            else: # Private
                await send_personal_message(message_data, msg.receiver_id) # To recipient
                await send_personal_message(message_data, user_id) # Back to sender

    # ... except/finally for disconnect and cleanup ...
    finally:
        if user_id in active_connections: del active_connections[user_id]
        # ...
```

### C. REST Endpoints

-   Return `sender_id` in message data, **not** `sender_username`.
-   User search endpoint **must** return `username` (and potentially avatar) for selection purposes.

**Example Updates:**
```python
# routers/chat_router.py (REST API Key Changes)

# --- Helper Function Update ---
def msg_to_dict(m: ChatMessage):
    return {
        'id': m.id,
        'sender_id': m.sender_id,
        # 'sender_username': m.sender_username, # REMOVED
        'receiver_id': m.receiver_id,
        'content': m.content,
        'created_at': m.created_at.isoformat(),
        'is_public': m.receiver_id is None,
    }

@router.get('/public-chat/messages')
async def get_public_messages(limit: int = 50, db: AsyncSession = Depends(get_async_db)):
    # ... query logic ...
    messages = result.scalars().all()
    return [msg_to_dict(m) for m in reversed(messages)] # Uses updated helper

@router.get('/private-chat/{other_user_id}/messages')
async def get_private_messages(#... params ...
):
    # ... query logic ...
    messages = result.scalars().all()
    return [msg_to_dict(m) for m in reversed(messages)] # Uses updated helper

@router.get('/private-chat/conversations')
async def get_conversations(#... params ...
):
    # ... query logic to get partner_ids ...
    if not partner_ids: return []
    # Fetch user details (id, username, avatar etc.) needed for the list view
    users_result = await db.execute(
        select(User.id, User.username, User.avatar_url) # Fetch needed fields
        .where(User.id.in_(partner_ids))
    )
    # Return data needed for the conversation list display (incl. username for identification here)
    return [
        {"id": u.id, "username": u.username, "avatar_url": u.avatar_url}
        for u in users_result.mappings().all() # Use mappings for dict-like results
    ]

@router.get('/users/search')
async def search_users(query: str, limit: int = 10, db: AsyncSession = Depends(get_async_db)):
    if not query: return []
    result = await db.execute(
        select(User.id, User.username, User.avatar_url) # Select fields needed for search results
        .where(User.username.ilike(f"%{query}%"))
        .limit(limit)
    )
    # Return username for selection purposes
    return [
        {"id": u.id, "username": u.username, "avatar_url": u.avatar_url}
        for u in result.mappings().all()
    ]

# NEW Endpoint (Optional but Recommended): Get user display info
@router.get('/users/display-info')
async def get_user_display_info(user_ids: List[int] = Query(...), db: AsyncSession = Depends(get_async_db)):
    """ Fetches minimal display info (like avatar) for a list of user IDs. """
    if not user_ids: return {}
    # Limit the number of IDs per request
    if len(user_ids) > 100: raise HTTPException(status_code=400, detail="Too many user IDs requested.")

    result = await db.execute(
        select(User.id, User.avatar_url) # Fetch only needed display info
        .where(User.id.in_(user_ids))
    )
    # Return a map of user_id -> { avatar_url: ... }
    return {u.id: {"avatar_url": u.avatar_url} for u in result.mappings().all()}

```
*Added an optional `/users/display-info` endpoint for frontend to fetch avatars based on IDs efficiently.*

---

## 3. Backend Review & Async Engine Advice

-   **(No change)** Use `asyncpg` for production.

---

## 4. Integration Steps

-   **Backend:**
    -   Update `ChatMessage` model (remove `sender_username`), update/add relationships in `User` model, run Alembic migration.
    -   Update WebSocket endpoint: remove username logic, use `sender_id`.
    -   Update REST endpoints: remove username from message data, ensure user search returns usernames for selection. Add `/users/display-info` endpoint.
    -   Ensure robust authentication.
    -   Configure `asyncpg`.
-   **Frontend:**
    -   Create/Update chat components.
    -   Modify UI to display avatars/generic IDs instead of usernames in chat views.
    -   Use `UserSearch` component which displays usernames for selection.
    -   Connect WebSocket, handle messages using `sender_id`.
    -   Use REST APIs for history/conversations. Use `/users/search` for finding users.
    -   Optionally use `/users/display-info` to fetch avatar URLs based on `sender_id`s present in fetched messages.
    -   Implement state management, including `isChatOpen` for pausing 3D.

---

## 5. Summary Table

| Area             | Recommendation/Action                                        | Status     |
|------------------|--------------------------------------------------------------|------------|
| DB Engine        | asyncpg (PostgreSQL) for prod                                | Consistent |
| Models           | `ChatMessage` updated (no username), `User` relationships    | **Updated**|
| WebSocket        | Payload uses `sender_id` only                                | **Updated**|
| REST API         | Payloads use `sender_id`. User search returns usernames.     | **Updated**|
| Frontend UI      | Hide usernames in chat views, show in search only.           | **Updated**|
| 3D Pause         | Pause 3D when full chat dialog is open                       | Consistent |
| User Auth        | Critical for WS/REST                                         | Detail     |
| User Display     | Frontend maps `sender_id` to avatar/alias                    | **New Detail**|

---

## 6. Notes

-   Hiding usernames significantly impacts UI/UX for identification. Using avatars is strongly recommended.
-   The distinction between showing usernames in search vs. hiding in chat is a key design decision to enable functionality while meeting privacy goals.
-   Ensure the `User` model has necessary fields like `avatar_url`.

---

*This file contains the updated plan focusing on user privacy by hiding usernames in chat displays.* 