from typing import Dict, Any

RATE_LIMIT_CONFIG = {
    # Authentication limits - Less strict
    "login": {
        "attempts": 10,  # Increased from 5
        "window": 300,  # 5 minutes
        "description": "Regular login attempts",
        "strict": True,
        "block_duration": 1800  # Reduced from 3600 (30 minutes instead of 1 hour)
    },
    "admin_login": {
        "attempts": 5,  # Increased from 3
        "window": 1800,  # 30 minutes
        "description": "Admin login attempts",
        "strict": True,
        "block_duration": 3600  # Reduced from 7200 (1 hour instead of 2 hours)
    },
    "telegram_login": {
        "attempts": 5001,  # Increased from 10 to accommodate multiple requests during login flow
        "window": 300,
        "description": "Telegram login attempts",
        "strict": False,  # Changed to false to be less restrictive
        "block_duration": 300  # Reduced from 1800 to 5 minutes
    },
    
    # Registration limits
    "register": {
        "attempts": 3,  # Allow 3 attempts
        "window": 3600,  # 1 hour
        "description": "User registration",
        "strict": True,
        "block_duration": 43200  # Reduced from 86400 (12 hours instead of 24 hours)
    },
    
    # Task limits
    "task_verification": {
        "attempts": 5,  # Increased from 3
        "window": 60,  # 1 minute
        "description": "Task verification attempts",
        "strict": True,
        "block_duration": 180  # Reduced from 300 (3 minutes instead of 5 minutes)
    },
    "task_claim": {
        "attempts": 5,  # Increased from 3
        "window": 60,
        "description": "Task reward claims",
        "strict": True,
        "block_duration": 300  # 5 minutes
    },
    "daily_checkin": {
        "attempts": 100,  # Increased from 60
        "window": 3600,  # 1 hour
        "description": "Daily task check-ins",
        "strict": True,
        "block_duration": 180  # Reduced from 300 (3 minutes instead of 5 minutes)
    },
    
    # API general limits
    "api_general": {
        "attempts": 100,  # Decreased to test rate limiting
        "window": 60,
        "description": "General API requests",
        "block_duration": 300  # 5 minutes
    },
    "api_sensitive": {
        "attempts": 50,  # Increased from 20
        "window": 60,
        "description": "Sensitive API endpoints",
        "strict": True,
        "block_duration": 600  # 10 minutes
    },
    # Add specific limit for task endpoints
    "api_tasks": {
        "attempts": 10,  # Set to 10 attempts
        "window": 60,
        "description": "Tasks API endpoints",
        "strict": True,
        "block_duration": 300  # 5 minutes
    },
    # Add separate limit for analytics endpoints
    "api_analytics": {
        "attempts": 10,  # Set to 10 attempts
        "window": 60,
        "description": "Analytics API endpoints",
        "strict": True,
        "block_duration": 300  # 5 minutes
    }
}

def get_limit_type(path: str, method: str) -> str:
    """Determine rate limit type based on request path and method"""
    path = path.lower()
    method = method.upper()
    
    # Authentication endpoints
    if path.startswith("/auth/"):
        if "token" in path:
            return "login"
        if "admin/login" in path:
            return "admin_login"
        if "register" in path:
            return "register"
        # Specific handling for telegram login endpoint
        if path == "/auth/telegram/login":
            return "telegram_login"
        if "telegram" in path:
            return "telegram_login" if "login" in path else "register"
    
    # Specific Task endpoints that need rate limiting
    if path == "/api/tasks/available":
        return "api_tasks"
    
    # Analytics endpoints
    if path == "/api/tasks/analytics":
        return "api_analytics"
            
    # General Task endpoints
    if path.startswith("/api/tasks/"):
        if "verify" in path:
            return "task_verification"
        if "claim" in path:
            return "task_claim"
        if "daily/check-in" in path or "checkin" in path:
            return "daily_checkin"
        if method in ["POST", "PUT", "DELETE"]:
            return "api_sensitive"
        return "api_general"
            
    # Admin endpoints
    if any(s in path for s in ["/admin/", "/api/admin/"]):
        return "api_sensitive"
        
    # Default API limits
    if path.startswith("/api/"):
        return "api_general"
        
    return None 