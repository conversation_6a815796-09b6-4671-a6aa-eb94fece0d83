---
description: 
globs: 
alwaysApply: false
---
# Frontend Architecture Guide

## Project Structure

This is a **Telegram Mini App** built with React 19 + Vite 6, optimized for mobile WebView performance.

### Core Architecture

- **Framework**: React 19 with TypeScript
- **Build Tool**: Vite 6 with SWC for fast compilation
- **Styling**: Tailwind CSS 4 with custom design system
- **State Management**: Zustand + TanStack Query
- **Routing**: React Router v7
- **3D Graphics**: Three.js + React Three Fiber (performance-critical)

### Key Files

- **Entry Point**: [frontend/src/main.tsx](mdc:frontend/src/main.tsx) - React 19 root with QueryClient
- **App Component**: [frontend/src/App.tsx](mdc:frontend/src/App.tsx) - Main routing and auth logic
- **Main Layout**: [frontend/src/components/DashboardLayout.tsx](mdc:frontend/src/components/DashboardLayout.tsx) - Core dashboard with tab navigation
- **Vite Config**: [frontend/vite.config.ts](mdc:frontend/vite.config.ts) - Optimized build configuration
- **Package Config**: [frontend/package.json](mdc:frontend/package.json) - Dependencies and scripts

### Page Structure

- **Landing**: [frontend/src/pages/LandingPage.tsx](mdc:frontend/src/pages/LandingPage.tsx) - Telegram auth entry
- **Dashboard Tabs**: Home, Earn, Premium, Wallet, Friends
- **3D Verse**: [frontend/src/pages/VersePage.tsx](mdc:frontend/src/pages/VersePage.tsx) - Interactive 3D world
- **Profile**: [frontend/src/pages/ProfilePage.tsx](mdc:frontend/src/pages/ProfilePage.tsx) - User settings

### Performance Considerations

- **Bundle Size**: Target <300KB per chunk for Telegram WebView
- **Lazy Loading**: All heavy components are lazy-loaded
- **3D Optimization**: Three.js components are performance-critical
- **Mobile First**: Optimized for mobile WebView constraints

### Development Commands

```bash
# Development server
pnpm run dev

# Production build with analysis
pnpm run build:analyze

# Bundle analysis (check dist/stats.html)
pnpm run build:analyze
```

### Critical Performance Rules

1. **Avoid large chunks** - Use dynamic imports for heavy features
2. **Optimize 3D components** - Three.js is the biggest performance bottleneck
3. **Lazy load non-critical** - Only load what's immediately needed
4. **Mobile-first design** - Telegram WebView has limited resources
