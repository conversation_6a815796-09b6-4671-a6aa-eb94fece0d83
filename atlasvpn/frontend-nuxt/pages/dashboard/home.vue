<template>
  <div class="home-tab">
    <!-- Welcome Message -->
    <WelcomeMessage
      :is-new-user="isNewUser"
      :username="userDisplayName"
    />

    <!-- Welcome Section -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <div>
          <h1 class="text-2xl font-bold text-white">
            Welcome back, {{ userDisplayName }}!
          </h1>
          <p class="text-gray-400 text-sm">
            {{ currentTime }}
          </p>
        </div>
        <div class="flex items-center space-x-2">
          <UButton
            @click="refreshData"
            :loading="isRefreshing"
            variant="ghost"
            size="sm"
            icon="i-heroicons-arrow-path"
          />
        </div>
      </div>
    </div>

    <!-- Main Stats Grid -->
    <div class="grid grid-cols-2 gap-3 mb-6">
      <CyberpunkStatDisplay
        label="Balance"
        :value="formattedBalance"
        icon="i-heroicons-wallet"
        color="green"
        :animate="true"
      />

      <CyberpunkStatDisplay
        label="VPN Services"
        :value="activeSubscriptions.toString()"
        icon="i-heroicons-shield-check"
        color="purple"
        :show-circle="true"
        :circle-progress="dataUsagePercentage"
      />

      <CyberpunkStatDisplay
        label="Total Earned"
        :value="formatCurrency(totalCardEarnings)"
        icon="i-heroicons-currency-dollar"
        color="gold"
        :animate="true"
      />

      <CyberpunkStatDisplay
        label="Hourly Rate"
        :value="formatCurrency(hourlyPassiveIncome) + '/h'"
        icon="i-heroicons-clock"
        color="blue"
      />
    </div>

    <!-- Profit Card -->
    <div v-if="totalCardEarnings > 0" class="mb-6">
      <ProfitCard
        :profit="totalCardEarnings"
        :is-claiming="isClaimingProfit"
        @claim="handleClaimProfit"
      />
    </div>

    <!-- Income Projections -->
    <div class="mb-6">
      <IncomeProjectionsCard
        :subscriptions="activeSubscriptions"
        :balance="walletBalance"
        :hourly-rate="hourlyPassiveIncome"
      />
    </div>

    <!-- Data Usage Card -->
    <div v-if="hasActiveSubscription" class="card mb-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-white">Data Usage</h3>
        <div class="text-sm text-gray-400">
          {{ formattedDataUsage.used }} / {{ formattedDataUsage.total }}
        </div>
      </div>
      
      <div class="mb-3">
        <div class="w-full bg-gray-700 rounded-full h-2">
          <div 
            class="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
            :style="{ width: `${dataUsagePercentage}%` }"
          ></div>
        </div>
      </div>
      
      <div class="text-sm text-gray-400">
        {{ Math.round(dataUsagePercentage) }}% used
      </div>
    </div>

    <!-- Quick Actions -->
    <div class="card mb-6">
      <h3 class="text-lg font-semibold text-white mb-4">Quick Actions</h3>
      <div class="grid grid-cols-2 gap-3">
        <UButton
          @click="navigateTo('/dashboard/earn')"
          variant="outline"
          class="h-12 flex-col"
        >
          <Icon name="i-heroicons-currency-dollar" class="w-5 h-5 mb-1" />
          <span class="text-xs">Earn</span>
        </UButton>
        
        <UButton
          @click="navigateTo('/dashboard/premium')"
          variant="outline"
          class="h-12 flex-col"
        >
          <Icon name="i-heroicons-star" class="w-5 h-5 mb-1" />
          <span class="text-xs">Premium</span>
        </UButton>
        
        <UButton
          @click="navigateTo('/dashboard/friends')"
          variant="outline"
          class="h-12 flex-col"
        >
          <Icon name="i-heroicons-user-group" class="w-5 h-5 mb-1" />
          <span class="text-xs">Friends</span>
        </UButton>
        
        <UButton
          @click="navigateTo('/verse')"
          variant="outline"
          class="h-12 flex-col"
        >
          <Icon name="i-heroicons-cube" class="w-5 h-5 mb-1" />
          <span class="text-xs">Verse</span>
        </UButton>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="card">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-white">Recent Activity</h3>
        <UButton
          @click="navigateTo('/dashboard/wallet')"
          variant="ghost"
          size="sm"
          class="text-purple-400"
        >
          View All
        </UButton>
      </div>
      
      <div v-if="recentTransactions.length > 0" class="space-y-3">
        <div
          v-for="transaction in recentTransactions"
          :key="transaction.id"
          class="flex items-center justify-between py-2"
        >
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center">
              <Icon 
                :name="getTransactionIcon(transaction.type)" 
                class="w-4 h-4 text-gray-300" 
              />
            </div>
            <div>
              <div class="text-sm font-medium text-white">
                {{ getTransactionTitle(transaction.type) }}
              </div>
              <div class="text-xs text-gray-400">
                {{ formatDate(transaction.created_at) }}
              </div>
            </div>
          </div>
          <div class="text-sm font-medium" :class="transaction.amount >= 0 ? 'text-green-400' : 'text-red-400'">
            {{ transaction.amount >= 0 ? '+' : '' }}${{ Math.abs(transaction.amount).toFixed(2) }}
          </div>
        </div>
      </div>
      
      <div v-else class="text-center py-8 text-gray-400">
        <Icon name="i-heroicons-document-text" class="w-12 h-12 mx-auto mb-2 opacity-50" />
        <p>No recent activity</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { format } from 'date-fns'

definePageMeta({
  middleware: 'auth',
  layout: 'dashboard'
})

// SEO
useHead({
  title: 'Dashboard - AtlasVPN'
})

const userStore = useUserStore()
const vpnStore = useVPNStore()
const { refreshDashboard } = useDashboard()

const isRefreshing = ref(false)
const isClaimingProfit = ref(false)
const currentTime = ref('')

// Computed properties from stores
const userDisplayName = computed(() => userStore.userDisplayName)
const isNewUser = computed(() => userStore.isNewUser)
const formattedBalance = computed(() => userStore.formattedBalance)
const walletBalance = computed(() => userStore.walletBalance)
const activeSubscriptions = computed(() => userStore.activeSubscriptions)
const hasActiveSubscription = computed(() => vpnStore.hasActiveSubscription)
const dataUsagePercentage = computed(() => userStore.dataUsagePercentage)
const formattedDataUsage = computed(() => userStore.formattedDataUsage)
const recentTransactions = computed(() => userStore.recentTransactions)
const totalCardEarnings = computed(() => userStore.totalCardEarnings)
const hourlyPassiveIncome = computed(() => userStore.hourlyPassiveIncome)

// Update current time
const updateTime = () => {
  const now = new Date()
  currentTime.value = format(now, 'EEEE, MMMM d, yyyy')
}

// Refresh all data
const refreshData = async () => {
  isRefreshing.value = true
  try {
    await refreshDashboard()
  } catch (error) {
    console.error('Failed to refresh data:', error)
  } finally {
    isRefreshing.value = false
  }
}

// Handle profit claiming
const handleClaimProfit = async () => {
  isClaimingProfit.value = true
  try {
    // In a real app, this would call the API to claim profits
    await new Promise(resolve => setTimeout(resolve, 1000))

    const uiStore = useUiStore()
    uiStore.showSuccessToast('Profits claimed successfully!')
  } catch (error) {
    console.error('Failed to claim profit:', error)
    const uiStore = useUiStore()
    uiStore.showErrorToast('Failed to claim profits')
  } finally {
    isClaimingProfit.value = false
  }
}

// Format currency helper
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

// Transaction helpers
const getTransactionIcon = (type: string) => {
  const icons: Record<string, string> = {
    subscription_purchase: 'i-heroicons-shield-check',
    subscription_renewal: 'i-heroicons-arrow-path',
    wallet_topup: 'i-heroicons-plus',
    referral_commission: 'i-heroicons-user-group',
    admin_adjustment: 'i-heroicons-cog-6-tooth',
    card_profit_claim: 'i-heroicons-gift',
    card_level_up: 'i-heroicons-arrow-up',
    card_purchase: 'i-heroicons-shopping-cart'
  }
  return icons[type] || 'i-heroicons-document-text'
}

const getTransactionTitle = (type: string) => {
  const titles: Record<string, string> = {
    subscription_purchase: 'VPN Purchase',
    subscription_renewal: 'VPN Renewal',
    wallet_topup: 'Wallet Top-up',
    referral_commission: 'Referral Bonus',
    admin_adjustment: 'Admin Adjustment',
    card_profit_claim: 'Card Profit',
    card_level_up: 'Card Upgrade',
    card_purchase: 'Card Purchase'
  }
  return titles[type] || 'Transaction'
}

const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'MMM d, HH:mm')
}

// Initialize
onMounted(() => {
  updateTime()
  // Update time every minute
  setInterval(updateTime, 60000)
  
  // Load initial data
  refreshData()
})
</script>
