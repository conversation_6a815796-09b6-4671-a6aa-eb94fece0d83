/**
 * UI Store - Lightweight Zustand store for UI state management
 * Migrated from original frontend with simplified storage
 */

import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

export type Theme = 'light' | 'dark' | 'auto';

interface UIState {
  // Theme management
  theme: Theme;
  setTheme: (theme: Theme) => void;
  
  // Welcome messages
  hasSeenWelcomeMessage: boolean;
  setHasSeenWelcomeMessage: (seen: boolean) => void;
  
  // Background visibility
  isBackgroundVisible: boolean;
  setBackgroundVisible: (visible: boolean) => void;
  
  // Home tab scroll position
  homeTabScrollPosition: number;
  setHomeTabScrollPosition: (position: number) => void;
  
  // App background status
  isAppInBackground: boolean;
  setAppInBackground: (inBackground: boolean) => void;
  
  // Reset function
  resetUI: () => void;
}

const initialState = {
  theme: 'auto' as Theme,
  hasSeenWelcomeMessage: false,
  isBackgroundVisible: true,
  homeTabScrollPosition: 0,
  isAppInBackground: false,
};

export const useUIStore = create<UIState>()(
  persist(
    (set) => ({
      ...initialState,
      
      setTheme: (theme: Theme) => set({ theme }),
      
      setHasSeenWelcomeMessage: (seen: boolean) => 
        set({ hasSeenWelcomeMessage: seen }),
      
      setBackgroundVisible: (visible: boolean) => 
        set({ isBackgroundVisible: visible }),
      
      setHomeTabScrollPosition: (position: number) => 
        set({ homeTabScrollPosition: position }),
      
      setAppInBackground: (inBackground: boolean) => 
        set({ isAppInBackground: inBackground }),
      
      resetUI: () => set(initialState),
    }),
    {
      name: 'ui-store',
      storage: createJSONStorage(() => localStorage),
      partialize: (state: UIState) => ({
        theme: state.theme,
        hasSeenWelcomeMessage: state.hasSeenWelcomeMessage,
        isBackgroundVisible: state.isBackgroundVisible,
        homeTabScrollPosition: state.homeTabScrollPosition,
      }),
    }
  )
);

// Export store instance for direct access if needed
export default useUIStore;