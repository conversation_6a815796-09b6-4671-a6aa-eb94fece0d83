<template>
  <div class="earn-tab">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-white mb-2">Earn Rewards</h1>
      <p class="text-gray-400">Complete tasks and earn coins</p>
    </div>

    <!-- Daily Streak Card -->
    <div class="mb-6">
      <DailyStreakCard
        :streak="dailyStreak"
        :is-checking-in="isCheckingIn"
        @check-in="handleDailyCheckIn"
      />
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-2 gap-4 mb-6">
      <div class="stat-card">
        <div class="text-2xl font-bold text-white mb-1">
          {{ totalTasksCompleted }}
        </div>
        <div class="text-xs text-gray-400">Tasks Completed</div>
      </div>
      
      <div class="stat-card">
        <div class="text-2xl font-bold text-green-400 mb-1">
          ${{ totalRewardsEarned.toFixed(2) }}
        </div>
        <div class="text-xs text-gray-400">Total Earned</div>
      </div>
    </div>

    <!-- Task Categories -->
    <div class="mb-6">
      <div class="flex space-x-2 mb-4">
        <UButton
          v-for="category in taskCategories"
          :key="category"
          @click="selectedCategory = category"
          :variant="selectedCategory === category ? 'solid' : 'outline'"
          size="sm"
          class="capitalize"
        >
          {{ category }}
        </UButton>
      </div>
    </div>

    <!-- Tasks List -->
    <div class="space-y-4">
      <!-- Loading State -->
      <div v-if="isLoadingTasks" class="space-y-4">
        <div v-for="i in 3" :key="i" class="card animate-pulse">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gray-700 rounded-lg"></div>
            <div class="flex-1">
              <div class="h-4 bg-gray-700 rounded mb-2"></div>
              <div class="h-3 bg-gray-700 rounded w-2/3"></div>
            </div>
            <div class="w-20 h-8 bg-gray-700 rounded"></div>
          </div>
        </div>
      </div>

      <!-- Tasks -->
      <div v-else-if="filteredTasks.length > 0" class="space-y-4">
        <TaskCard
          v-for="task in filteredTasks"
          :key="task.id"
          :task="task"
          :is-starting="isTaskCompleting(task.id)"
          :is-claiming="isTaskClaiming(task.id)"
          @start="completeTask(task.id)"
          @claim="claimReward(task.id)"
          @click="handleTaskClick"
        />
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <Icon name="i-heroicons-clipboard-document-list" class="w-16 h-16 mx-auto mb-4 text-gray-600" />
        <h3 class="text-lg font-medium text-white mb-2">No tasks available</h3>
        <p class="text-gray-400">Check back later for new earning opportunities</p>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="tasksError" class="card border-red-500/20 bg-red-500/5">
      <div class="flex items-center space-x-3">
        <Icon name="i-heroicons-exclamation-triangle" class="w-6 h-6 text-red-400" />
        <div>
          <h4 class="text-red-400 font-medium">Failed to load tasks</h4>
          <p class="text-red-300 text-sm">{{ tasksError }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  middleware: 'auth',
  layout: 'dashboard'
})

// SEO
useHead({
  title: 'Earn Rewards - AtlasVPN'
})

const taskStore = useTaskStore()
const { 
  fetchTasks, 
  fetchDailyStreak, 
  completeTask: completeTaskAction, 
  claimTaskReward, 
  dailyCheckIn 
} = useTasks()

const selectedCategory = ref('all')
const isCheckingIn = ref(false)

// Computed properties from store
const tasks = computed(() => taskStore.tasks)
const dailyStreak = computed(() => taskStore.dailyStreak)
const isLoadingTasks = computed(() => taskStore.isLoading)
const tasksError = computed(() => taskStore.error)
const currentStreak = computed(() => taskStore.currentStreak)
const canCheckIn = computed(() => taskStore.canCheckIn)
const nextReward = computed(() => taskStore.nextReward)
const totalTasksCompleted = computed(() => taskStore.totalTasksCompleted)
const totalRewardsEarned = computed(() => taskStore.totalRewardsEarned)

// Task categories
const taskCategories = computed(() => {
  const categories = ['all', ...new Set(tasks.value.map(task => task.category))]
  return categories
})

// Filtered tasks
const filteredTasks = computed(() => {
  if (selectedCategory.value === 'all') {
    return tasks.value
  }
  return tasks.value.filter(task => task.category === selectedCategory.value)
})

// Task operations
const completeTask = async (taskId: number) => {
  try {
    await completeTaskAction(taskId)
  } catch (error: any) {
    console.error('Failed to complete task:', error)
  }
}

const claimReward = async (taskId: number) => {
  try {
    await claimTaskReward(taskId)
  } catch (error: any) {
    console.error('Failed to claim reward:', error)
  }
}

const handleDailyCheckIn = async () => {
  isCheckingIn.value = true
  try {
    await dailyCheckIn()
  } catch (error: any) {
    console.error('Failed to check in:', error)
  } finally {
    isCheckingIn.value = false
  }
}

const handleTaskClick = (task: any) => {
  console.log('Task clicked:', task)
  // Could open task details modal or navigate to task page
}

// Helper functions
const isTaskCompleting = (taskId: number) => taskStore.isTaskCompleting(taskId)
const isTaskClaiming = (taskId: number) => taskStore.isTaskClaiming(taskId)

// Initialize
onMounted(async () => {
  await Promise.all([
    fetchTasks(),
    fetchDailyStreak()
  ])
})
</script>
