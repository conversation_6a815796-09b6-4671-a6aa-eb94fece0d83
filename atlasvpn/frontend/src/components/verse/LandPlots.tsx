/**
 * @file LandPlots.tsx
 * @description R3F component that renders individual meshes for each land plot, ensuring clickability.
 */

// @ts-nocheck
import React, { useRef, useEffect, useMemo, useState } from 'react';
import * as THREE from 'three';
import { useFrame, useThree, extend } from '@react-three/fiber';
import { LandPlot } from '../../utils/mockVerseData';
import { Text } from '@react-three/drei';
import { motion } from 'framer-motion';

// Extend Three.js objects for JSX usage
extend(THREE);

interface LandPlotsProps {
  lands: Array<LandPlot & { position: [number, number, number]; size: [number, number] }>;
  onHover: (landId: string | null) => void;
  onClick: (land: LandPlot) => void;
  hoveredLandId: string | null;
  selectedLandId: string | null;
  isPerformanceMode: boolean;
  isAppVisible: boolean;
  currentUser: any;
}

// Constants
const basePlotHeight = 0.1; // Box thickness
const baseSurfaceOffset = 0.05; // Offset ABOVE sphere surface (positive)
const scaleFactor = 0.13; // Angular size to world scale
const baseColor = new THREE.Color('#fffff0');
const performanceColor = new THREE.Color('#aaaaaa'); // Gray color for performance mode

// Global texture cache
const textureCache = new Map<string, THREE.Texture>();

// Individual Land Plot Component
const LandPlotMesh = ({
  land,
  onHover,
  onClick,
  isHovered,
  isSelected,
  isPerformanceMode,
  isAppVisible,
  currentUser,
}: {
  land: LandPlot & { position: [number, number, number]; size: [number, number] };
  onHover: (landId: string | null) => void;
  onClick: (land: LandPlot) => void;
  isHovered: boolean;
  isSelected: boolean;
  isPerformanceMode: boolean;
  isAppVisible: boolean;
  currentUser: any;
}) => {
  const meshRef = useRef<THREE.Mesh>(null!);
  const materialRef = useRef<THREE.MeshStandardMaterial>(null!);
  const timeRef = useRef(0);
  const initialPositionRef = useRef<THREE.Vector3 | null>(null);

  // Load paint texture if available - using caching strategy
  const paintTexture = useMemo(() => {
    // Skip texture loading in performance mode
    if (isPerformanceMode || !land.paintData) return null;

    // Check cache first
    if (textureCache.has(land.paintData)) {
      return textureCache.get(land.paintData)!;
    }

    // If not in cache, load it
    const texture = new THREE.TextureLoader().load(land.paintData);
    texture.minFilter = THREE.LinearFilter;
    texture.magFilter = THREE.LinearFilter;
    textureCache.set(land.paintData, texture);
    return texture;
  }, [land.paintData, isPerformanceMode]);

  // Update mesh material when paint texture changes or performance mode changes
  useEffect(() => {
    if (!meshRef.current || !materialRef.current) return;

    if (isPerformanceMode) {
      // Performance mode - use simple gray material
      materialRef.current.map = null;
      materialRef.current.emissiveMap = null;
      materialRef.current.color.set(performanceColor);
      materialRef.current.emissive.set('#333333');
      materialRef.current.emissiveIntensity = 0.1;
      materialRef.current.opacity = 0.7;
    } else if (paintTexture) {
      // Regular mode with texture
      materialRef.current.map = paintTexture;
      materialRef.current.emissiveMap = paintTexture;
      materialRef.current.color.set(baseColor);
      materialRef.current.emissive.set(baseColor);
      materialRef.current.emissiveIntensity = 0.8;
      materialRef.current.opacity = isHovered || isSelected ? 0.95 : 0.7;
    } else {
      // Regular mode without texture
      materialRef.current.map = null;
      materialRef.current.emissiveMap = null;
      materialRef.current.color.set(baseColor);
      materialRef.current.emissive.set(baseColor);
      materialRef.current.emissiveIntensity = 0.8;
      materialRef.current.opacity = isHovered || isSelected ? 0.95 : 0.7;
    }
    materialRef.current.needsUpdate = true;
  }, [paintTexture, isPerformanceMode, isHovered, isSelected]);

  // Add a frame-based opacity transition to smooth out hover effects
  useFrame((_, delta) => {
    if (!materialRef.current || isPerformanceMode) return;

    // Target opacity based on hover/selected state
    const targetOpacity = isHovered || isSelected ? 0.95 : 0.7;

    // Only apply transition if opacity needs to change
    if (Math.abs(materialRef.current.opacity - targetOpacity) > 0.01) {
      // Use faster lerp for opacity changes to match position changes
      materialRef.current.opacity = THREE.MathUtils.lerp(
        materialRef.current.opacity,
        targetOpacity,
        delta * 6
      );
    }
  });

  // Define visual properties
  const scale = land.angularSize * scaleFactor;
  const raiseAmount = isSelected ? 0.5 : isHovered ? 0.3 : 0;

  // Store the initial position once calculated
  useEffect(() => {
    if (!meshRef.current) return;

    // Calculate the initial position on the sphere
    const worldPos = new THREE.Vector3(...land.position).normalize();
    const initialPos = worldPos.multiplyScalar(16 + baseSurfaceOffset);
    initialPositionRef.current = initialPos.clone();

    // Set initial position
    meshRef.current.position.copy(initialPos);
    meshRef.current.lookAt(0, 0, 0);
  }, [land.position]);

  // Animation for hover and selection with stability check
  useFrame((_, delta) => {
    if (!meshRef.current || !initialPositionRef.current) return;

    // In performance mode, always ensure lands are at their base position
    if (isPerformanceMode) {
      if (meshRef.current.position.distanceToSquared(initialPositionRef.current) > 0.01) {
        meshRef.current.position.copy(initialPositionRef.current);
        meshRef.current.lookAt(0, 0, 0);
      }
      return;
    }

    // Skip animation when app is not visible
    if (!isAppVisible) return;

    // Update time for animation
    timeRef.current += delta;
    const floatOffset = Math.sin(timeRef.current * 0.5) * 0.005;

    // Get the direction vector from center to land
    const dir = initialPositionRef.current.clone().normalize();

    // Calculate target height with hover effect
    const targetHeight =
      16 + baseSurfaceOffset + raiseAmount + (isHovered || isSelected ? floatOffset : 0);

    // Calculate new position
    const newPos = dir.multiplyScalar(targetHeight);

    // Set position with lerp for smooth transition
    // Use faster transition when returning to base position to prevent the jumping effect
    const lerpSpeed = !isHovered && !isSelected ? delta * 6 : delta * 3;
    meshRef.current.position.lerp(newPos, lerpSpeed);

    // Always look at center
    meshRef.current.lookAt(0, 0, 0);

    // Safety check to prevent land plots from drifting too far
    const distFromOrigin = meshRef.current.position.length();
    if (distFromOrigin < 15 || distFromOrigin > 18) {
      console.log(`[Debug] Land ${land.id} position reset - abnormal distance: ${distFromOrigin}`);
      meshRef.current.position.copy(initialPositionRef.current);
      meshRef.current.lookAt(0, 0, 0);
    }
  });

  // Determine if land is owned by current user
  const isOwnedByUser =
    currentUser && (land.owner === currentUser.id || land.owner === currentUser.username);

  return (
    <group>
      <mesh
        ref={meshRef}
        renderOrder={10}
        onPointerOver={() => {
          onHover(land.id);
          document.body.style.cursor = 'pointer';
        }}
        onPointerOut={() => {
          onHover(null);
          document.body.style.cursor = 'default';
        }}
        onClick={e => {
          e.stopPropagation();
          onClick(land);
        }}
      >
        <boxGeometry args={[scale, scale, basePlotHeight]} />
        <meshStandardMaterial
          ref={materialRef}
          color={isPerformanceMode ? performanceColor : baseColor}
          emissive={isPerformanceMode ? '#333333' : baseColor}
          emissiveIntensity={isPerformanceMode ? 0.1 : 0.8}
          metalness={0.1}
          roughness={0.4}
          transparent
          opacity={0.7}
          map={!isPerformanceMode ? paintTexture || null : null}
          emissiveMap={!isPerformanceMode ? paintTexture || null : null}
          side={THREE.FrontSide}
          depthTest={false}
        />
      </mesh>

      {/* Simple ownership indicator - visible even in performance mode */}
      {isOwnedByUser && (
        <mesh position={[0, 0, basePlotHeight / 2 + 0.4]}>
          <coneGeometry args={[scale * 0.2, 0.5, isPerformanceMode ? 4 : 8]} />
          <meshBasicMaterial color={isPerformanceMode ? '#aaaaaa' : '#facc15'} />
        </mesh>
      )}
    </group>
  );
};

// Main component with optimized rendering
function LandPlots({
  lands,
  onHover,
  onClick,
  hoveredLandId,
  selectedLandId,
  isPerformanceMode,
  isAppVisible,
  currentUser,
}: LandPlotsProps): React.ReactElement {
  // Clean up unused textures - only in normal mode
  useEffect(() => {
    if (isPerformanceMode) return;

    // Get the set of active texture URLs
    const activeTextures = new Set(lands.map(l => l.paintData).filter(Boolean) as string[]);

    // Remove unused textures from cache
    textureCache.forEach((texture, url) => {
      if (!activeTextures.has(url)) {
        texture.dispose();
        textureCache.delete(url);
      }
    });

    // Cleanup on unmount
    return () => {
      if (!isPerformanceMode) {
        textureCache.forEach(texture => texture.dispose());
        textureCache.clear();
      }
    };
  }, [lands, isPerformanceMode]);

  // Only separate lands in normal mode
  const interactiveLands = useMemo(() => {
    if (isPerformanceMode) return [];

    return lands.filter(
      land =>
        land.id === hoveredLandId ||
        land.id === selectedLandId ||
        (currentUser && (land.owner === currentUser.id || land.owner === currentUser.username))
    );
  }, [lands, hoveredLandId, selectedLandId, currentUser, isPerformanceMode]);

  const regularLands = useMemo(() => {
    if (isPerformanceMode) return lands; // In performance mode, all lands are regular

    return lands.filter(
      land =>
        land.id !== hoveredLandId &&
        land.id !== selectedLandId &&
        !(currentUser && (land.owner === currentUser.id || land.owner === currentUser.username))
    );
  }, [lands, hoveredLandId, selectedLandId, currentUser, isPerformanceMode]);

  return (
    <group>
      {/* Render all lands (in performance mode) or regular lands (in normal mode) */}
      {regularLands.map(land => (
        <LandPlotMesh
          key={land.id}
          land={land}
          onHover={onHover}
          onClick={onClick}
          isHovered={hoveredLandId === land.id}
          isSelected={selectedLandId === land.id}
          isPerformanceMode={isPerformanceMode}
          isAppVisible={isAppVisible}
          currentUser={currentUser}
        />
      ))}

      {/* Render interactive lands on top (only in normal mode) */}
      {!isPerformanceMode &&
        interactiveLands.map(land => (
          <LandPlotMesh
            key={land.id}
            land={land}
            onHover={onHover}
            onClick={onClick}
            isHovered={hoveredLandId === land.id}
            isSelected={selectedLandId === land.id}
            isPerformanceMode={isPerformanceMode}
            isAppVisible={isAppVisible}
            currentUser={currentUser}
          />
        ))}
    </group>
  );
}

export default React.memo(LandPlots);
