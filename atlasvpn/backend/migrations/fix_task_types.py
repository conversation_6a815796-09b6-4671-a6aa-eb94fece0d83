"""Fix task type values in database to match enum case."""
from sqlalchemy import text
from models import TaskType

def upgrade(connection):
    """Update task type values to match enum case."""
    # Map of old values to new values
    type_mapping = {
        'telegram_channel': TaskType.TELEGRAM_CHANNEL.value,
        'instagram_follow': TaskType.INSTAGRAM_FOLLOW.value,
        'twitter_follow': TaskType.TWITTER_FOLLOW.value,
        'youtube_view': TaskType.YOUTUBE_VIEW.value,
        'website_visit': TaskType.WEBSITE_VISIT.value,
        'daily_checkin': TaskType.DAILY_CHECKIN.value,
        'referral': TaskType.REFERRAL.value
    }
    
    # Update tasks table
    for old_value, new_value in type_mapping.items():
        connection.execute(
            text("UPDATE tasks SET type = :new_value WHERE LOWER(type) = LOWER(:old_value)"),
            {"new_value": new_value, "old_value": old_value}
        )

def downgrade(connection):
    """Revert task type values to lowercase."""
    # Map of new values to old values (lowercase)
    type_mapping = {
        TaskType.TELEGRAM_CHANNEL.value: 'telegram_channel',
        TaskType.INSTAGRAM_FOLLOW.value: 'instagram_follow',
        TaskType.TWITTER_FOLLOW.value: 'twitter_follow',
        TaskType.YOUTUBE_VIEW.value: 'youtube_view',
        TaskType.WEBSITE_VISIT.value: 'website_visit',
        TaskType.DAILY_CHECKIN.value: 'daily_checkin',
        TaskType.REFERRAL.value: 'referral'
    }
    
    # Update tasks table
    for new_value, old_value in type_mapping.items():
        connection.execute(
            text("UPDATE tasks SET type = :old_value WHERE type = :new_value"),
            {"old_value": old_value, "new_value": new_value}
        ) 