<template>
  <div 
    class="loading-skeleton animate-pulse rounded"
    :class="[
      sizeClasses,
      'bg-gray-800'
    ]"
    :style="customStyle"
  />
</template>

<script setup lang="ts">
interface Props {
  width?: string | number
  height?: string | number
  size?: 'sm' | 'md' | 'lg' | 'xl'
  rounded?: boolean
  circle?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  rounded: true,
  circle: false
})

const sizeClasses = computed(() => {
  if (props.circle) {
    const circleSize = {
      sm: 'w-8 h-8',
      md: 'w-12 h-12',
      lg: 'w-16 h-16',
      xl: 'w-20 h-20'
    }
    return `${circleSize[props.size]} rounded-full`
  }

  if (props.width || props.height) {
    return props.rounded ? 'rounded' : ''
  }

  const sizes = {
    sm: 'h-4',
    md: 'h-6',
    lg: 'h-8',
    xl: 'h-12'
  }
  
  return `${sizes[props.size]} w-full ${props.rounded ? 'rounded' : ''}`
})

const customStyle = computed(() => {
  const style: Record<string, string> = {}
  
  if (props.width) {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }
  
  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
  }
  
  return style
})
</script>
