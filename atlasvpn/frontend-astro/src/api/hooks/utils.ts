/**
 * @file api/hooks/utils.ts
 * @description Utility functions for transaction handling and formatting
 */

import { useCallback } from 'react';
import { APIError } from '../client';

// Transaction Utility Functions
export const getTransactionDetails = (type: string) => {
  switch (type) {
    case 'wallet_topup':
      return { 
        icon: 'arrow-up-circle', 
        color: 'text-green-400', 
        label: 'Wallet Top-up',
        isPositive: true 
      };
    case 'subscription_purchase':
      return { 
        icon: 'arrow-down-circle', 
        color: 'text-red-400', 
        label: 'VPN Purchase',
        isPositive: false 
      };
    case 'subscription_renewal':
      return { 
        icon: 'arrow-down-circle', 
        color: 'text-red-400', 
        label: 'VPN Renewal',
        isPositive: false 
      };
    case 'referral_commission':
      return { 
        icon: 'arrow-up-circle', 
        color: 'text-green-400', 
        label: 'Referral Bonus',
        isPositive: true 
      };
    case 'card_profit_claim':
      return { 
        icon: 'arrow-up-circle', 
        color: 'text-yellow-400', 
        label: 'Card Profit Claim',
        isPositive: true 
      };
    case 'card_level_up':
      return { 
        icon: 'arrow-down-circle', 
        color: 'text-red-400', 
        label: 'Card Level Up',
        isPositive: false 
      };
    case 'card_purchase':
      return { 
        icon: 'arrow-down-circle', 
        color: 'text-red-400', 
        label: 'Card Purchase',
        isPositive: false 
      };
    case 'admin_adjustment':
      return { 
        icon: 'arrow-up-circle', 
        color: 'text-blue-400', 
        label: 'Admin Adjustment',
        isPositive: true 
      };
    default:
      const label = type.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase());
      return { 
        icon: 'banknotes', 
        color: 'text-gray-400', 
        label: label,
        isPositive: false 
      };
  }
};

export const formatTransactionAmount = (amount: number, isPositive: boolean): string => {
  const sign = isPositive ? '+' : '-';
  const absAmount = Math.abs(amount);
  
  if (absAmount >= 1000000) {
    return `${sign}${(absAmount / 1000000).toFixed(1)}M`;
  } else if (absAmount >= 1000) {
    return `${sign}${(absAmount / 1000).toFixed(1)}K`;
  } else {
    return `${sign}${absAmount.toFixed(2)}`;
  }
};

export const formatTransactionDate = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
  
  if (diffInHours < 1) {
    const diffInMinutes = Math.floor(diffInHours * 60);
    return `${diffInMinutes}m ago`;
  } else if (diffInHours < 24) {
    return `${Math.floor(diffInHours)}h ago`;
  } else if (diffInHours < 48) {
    return 'Yesterday';
  } else {
    return date.toLocaleDateString(undefined, {
      month: 'short',
      day: 'numeric'
    });
  }
};

// API Error Handler Hook
export const useApiErrorHandler = () => {
  return useCallback((error: unknown): string => {
    if (error instanceof APIError) {
      switch (error.status) {
        case 401:
          return 'Authentication required. Please log in again.';
        case 403:
          return 'Access denied. You do not have permission for this action.';
        case 404:
          return 'Resource not found.';
        case 409:
          return 'Conflict: The resource already exists or is in use.';
        case 422:
          return 'Invalid data provided. Please check your input.';
        case 429:
          return 'Too many requests. Please try again later.';
        case 500:
          return 'Server error. Please try again later.';
        case 502:
          return 'Service temporarily unavailable. Please try again.';
        case 503:
          return 'Service is currently down for maintenance.';
        default:
          return error.message || 'An unexpected error occurred.';
      }
    }

    if (error instanceof Error) {
      // Handle network errors
      if (error.message.includes('Network Error') || error.message.includes('fetch')) {
        return 'Network connection failed. Please check your internet connection.';
      }
      return error.message;
    }

    return 'An unexpected error occurred.';
  }, []);
};