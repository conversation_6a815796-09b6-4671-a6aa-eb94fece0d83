/**
 * HTML utilities for DOM manipulation and HTML processing
 */

/**
 * Safely parse HTML string and extract text content
 * @param html - HTML string to parse
 * @returns Plain text content
 */
export function stripHtml(html: string): string {
  if (typeof window === 'undefined') {
    // Server-side: simple regex-based stripping (not perfect but safe)
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
      .replace(/<[^>]*>/g, '')
      .replace(/&nbsp;/g, ' ')
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .trim();
  }

  // Client-side: use DOM parser for accurate stripping
  const temp = document.createElement('div');
  temp.innerHTML = html;
  return temp.textContent || temp.innerText || '';
}

/**
 * Truncate HTML content while preserving tags
 * @param html - HTML string to truncate
 * @param maxLength - Maximum length of text content
 * @param suffix - Suffix to add when truncated
 * @returns Truncated HTML
 */
export function truncateHtml(html: string, maxLength: number, suffix: string = '...'): string {
  const textContent = stripHtml(html);
  
  if (textContent.length <= maxLength) {
    return html;
  }

  // Simple truncation - in production, you might want a more sophisticated approach
  const truncatedText = textContent.substring(0, maxLength - suffix.length) + suffix;
  return truncatedText;
}

/**
 * Extract links from HTML content
 * @param html - HTML string to parse
 * @returns Array of link objects
 */
export function extractLinks(html: string): Array<{ href: string; text: string; title?: string }> {
  if (typeof window === 'undefined') return [];

  const temp = document.createElement('div');
  temp.innerHTML = html;
  const links = temp.querySelectorAll('a[href]');
  
  return Array.from(links).map(link => ({
    href: link.getAttribute('href') || '',
    text: link.textContent || '',
    title: link.getAttribute('title') || undefined,
  }));
}

/**
 * Extract images from HTML content
 * @param html - HTML string to parse
 * @returns Array of image objects
 */
export function extractImages(html: string): Array<{ src: string; alt?: string; title?: string }> {
  if (typeof window === 'undefined') return [];

  const temp = document.createElement('div');
  temp.innerHTML = html;
  const images = temp.querySelectorAll('img[src]');
  
  return Array.from(images).map(img => ({
    src: img.getAttribute('src') || '',
    alt: img.getAttribute('alt') || undefined,
    title: img.getAttribute('title') || undefined,
  }));
}

/**
 * Clean HTML by removing dangerous elements and attributes
 * @param html - HTML string to clean
 * @returns Cleaned HTML string
 */
export function cleanHtml(html: string): string {
  if (typeof window === 'undefined') {
    // Server-side: remove potentially dangerous elements
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
      .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '') // Remove event handlers
      .replace(/javascript:/gi, ''); // Remove javascript: URLs
  }

  // Client-side: use DOM manipulation for more precise cleaning
  const temp = document.createElement('div');
  temp.innerHTML = html;

  // Remove dangerous elements
  const dangerousElements = temp.querySelectorAll('script, style, iframe, object, embed');
  dangerousElements.forEach(el => el.remove());

  // Remove dangerous attributes
  const allElements = temp.querySelectorAll('*');
  allElements.forEach(el => {
    // Remove event handler attributes
    Array.from(el.attributes).forEach(attr => {
      if (attr.name.startsWith('on') || attr.value.includes('javascript:')) {
        el.removeAttribute(attr.name);
      }
    });
  });

  return temp.innerHTML;
}

/**
 * Convert relative URLs to absolute URLs
 * @param html - HTML string with potentially relative URLs
 * @param baseUrl - Base URL to resolve relative URLs against
 * @returns HTML with absolute URLs
 */
export function makeUrlsAbsolute(html: string, baseUrl: string): string {
  if (typeof window === 'undefined') return html;

  const temp = document.createElement('div');
  temp.innerHTML = html;

  // Convert relative src attributes
  const elementsWithSrc = temp.querySelectorAll('[src]');
  elementsWithSrc.forEach(el => {
    const src = el.getAttribute('src');
    if (src && !src.startsWith('http') && !src.startsWith('//')) {
      el.setAttribute('src', new URL(src, baseUrl).href);
    }
  });

  // Convert relative href attributes
  const elementsWithHref = temp.querySelectorAll('[href]');
  elementsWithHref.forEach(el => {
    const href = el.getAttribute('href');
    if (href && !href.startsWith('http') && !href.startsWith('//') && !href.startsWith('#')) {
      el.setAttribute('href', new URL(href, baseUrl).href);
    }
  });

  return temp.innerHTML;
}

/**
 * Add target="_blank" to external links
 * @param html - HTML string to process
 * @param currentDomain - Current domain to identify external links
 * @returns HTML with external links opening in new tab
 */
export function addTargetBlankToExternalLinks(html: string, currentDomain?: string): string {
  if (typeof window === 'undefined') return html;

  const temp = document.createElement('div');
  temp.innerHTML = html;
  const links = temp.querySelectorAll('a[href]');

  const domain = currentDomain || window.location.hostname;

  links.forEach(link => {
    const href = link.getAttribute('href');
    if (href) {
      try {
        const url = new URL(href, window.location.origin);
        if (url.hostname !== domain) {
          link.setAttribute('target', '_blank');
          link.setAttribute('rel', 'noopener noreferrer');
        }
      } catch (error) {
        // Invalid URL, skip
      }
    }
  });

  return temp.innerHTML;
}

/**
 * Highlight text in HTML content
 * @param html - HTML string to process
 * @param searchText - Text to highlight
 * @param className - CSS class for highlighted text
 * @returns HTML with highlighted text
 */
export function highlightText(html: string, searchText: string, className: string = 'highlight'): string {
  if (!searchText.trim()) return html;

  const regex = new RegExp(`(${escapeRegExp(searchText)})`, 'gi');
  return html.replace(regex, `<mark class="${className}">$1</mark>`);
}

/**
 * Escape special regex characters
 * @param string - String to escape
 * @returns Escaped string
 */
function escapeRegExp(string: string): string {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * Get text content length from HTML
 * @param html - HTML string
 * @returns Length of text content
 */
export function getTextLength(html: string): number {
  return stripHtml(html).length;
}

/**
 * Check if HTML contains only whitespace
 * @param html - HTML string to check
 * @returns True if HTML contains only whitespace
 */
export function isEmptyHtml(html: string): boolean {
  const textContent = stripHtml(html);
  return textContent.trim().length === 0;
}

/**
 * Convert HTML to markdown (basic conversion)
 * @param html - HTML string to convert
 * @returns Markdown string
 */
export function htmlToMarkdown(html: string): string {
  return html
    // Headers
    .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
    .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
    .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')
    .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '#### $1\n\n')
    .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '##### $1\n\n')
    .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '###### $1\n\n')
    
    // Bold and italic
    .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
    .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
    .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
    .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')
    
    // Links
    .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')
    
    // Images
    .replace(/<img[^>]*src="([^"]*)"[^>]*alt="([^"]*)"[^>]*>/gi, '![$2]($1)')
    .replace(/<img[^>]*src="([^"]*)"[^>]*>/gi, '![]($1)')
    
    // Line breaks
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<\/p>/gi, '\n\n')
    
    // Lists
    .replace(/<ul[^>]*>/gi, '')
    .replace(/<\/ul>/gi, '\n')
    .replace(/<ol[^>]*>/gi, '')
    .replace(/<\/ol>/gi, '\n')
    .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')
    
    // Code
    .replace(/<code[^>]*>(.*?)<\/code>/gi, '`$1`')
    .replace(/<pre[^>]*>(.*?)<\/pre>/gi, '```\n$1\n```\n')
    
    // Remove remaining HTML tags
    .replace(/<[^>]*>/g, '')
    
    // Clean up extra whitespace
    .replace(/\n{3,}/g, '\n\n')
    .trim();
}

/**
 * Wrap text nodes in HTML with a span
 * @param html - HTML string to process
 * @param className - CSS class for the span
 * @returns HTML with wrapped text nodes
 */
export function wrapTextNodes(html: string, className: string): string {
  if (typeof window === 'undefined') return html;

  const temp = document.createElement('div');
  temp.innerHTML = html;

  function wrapTextNodesRecursive(node: Node) {
    if (node.nodeType === Node.TEXT_NODE && node.textContent?.trim()) {
      const span = document.createElement('span');
      span.className = className;
      span.textContent = node.textContent;
      node.parentNode?.replaceChild(span, node);
    } else {
      Array.from(node.childNodes).forEach(wrapTextNodesRecursive);
    }
  }

  Array.from(temp.childNodes).forEach(wrapTextNodesRecursive);
  return temp.innerHTML;
}

/**
 * Get reading time estimate for HTML content
 * @param html - HTML string
 * @param wordsPerMinute - Average reading speed (default: 200)
 * @returns Reading time in minutes
 */
export function getReadingTime(html: string, wordsPerMinute: number = 200): number {
  const textContent = stripHtml(html);
  const wordCount = textContent.split(/\s+/).filter(word => word.length > 0).length;
  return Math.ceil(wordCount / wordsPerMinute);
}
