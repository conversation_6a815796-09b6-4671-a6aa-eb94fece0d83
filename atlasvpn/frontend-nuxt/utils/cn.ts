import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

/**
 * Utility function to merge Tailwind CSS classes with clsx
 * This is commonly used in component libraries to allow conditional classes
 * and proper Tailwind class merging without conflicts
 * 
 * @param inputs - Class values to merge
 * @returns Merged class string
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

/**
 * Create a variant-based class name generator
 * Useful for creating component variants with different styles
 * 
 * @param base - Base classes that are always applied
 * @param variants - Object containing variant configurations
 * @param defaultVariants - Default variant values
 * @returns Function that generates class names based on variants
 */
export function createVariants<T extends Record<string, Record<string, string>>>(
  base: string,
  variants: T,
  defaultVariants?: Partial<{ [K in keyof T]: keyof T[K] }>
) {
  return (props?: Partial<{ [K in keyof T]: keyof T[K] }>) => {
    const mergedProps = { ...defaultVariants, ...props };
    
    const variantClasses = Object.entries(mergedProps)
      .map(([key, value]) => {
        const variant = variants[key as keyof T];
        return variant?.[value as keyof typeof variant] || '';
      })
      .filter(Boolean);
    
    return cn(base, ...variantClasses);
  };
}

/**
 * Conditional class utility
 * Applies classes based on conditions
 * 
 * @param condition - Boolean condition
 * @param trueClasses - Classes to apply when condition is true
 * @param falseClasses - Classes to apply when condition is false
 * @returns Conditional classes
 */
export function conditionalClass(
  condition: boolean,
  trueClasses: string,
  falseClasses: string = ''
): string {
  return condition ? trueClasses : falseClasses;
}

/**
 * Focus ring utility for accessibility
 * Provides consistent focus styling across the application
 * 
 * @param variant - Focus ring variant
 * @returns Focus ring classes
 */
export function focusRing(variant: 'default' | 'inset' | 'none' = 'default'): string {
  const variants = {
    default: 'focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-gray-900',
    inset: 'focus:outline-none focus:ring-2 focus:ring-inset focus:ring-purple-500',
    none: 'focus:outline-none',
  };
  
  return variants[variant];
}

/**
 * Screen reader only utility
 * Hides content visually but keeps it accessible to screen readers
 */
export const srOnly = 'sr-only';

/**
 * Not screen reader only utility
 * Shows content that was previously hidden from screen readers
 */
export const notSrOnly = 'not-sr-only';

/**
 * Transition utility classes
 * Provides consistent transitions across the application
 */
export const transitions = {
  default: 'transition-all duration-200 ease-in-out',
  fast: 'transition-all duration-150 ease-in-out',
  slow: 'transition-all duration-300 ease-in-out',
  colors: 'transition-colors duration-200 ease-in-out',
  transform: 'transition-transform duration-200 ease-in-out',
  opacity: 'transition-opacity duration-200 ease-in-out',
} as const;

/**
 * Animation utility classes
 * Provides consistent animations across the application
 */
export const animations = {
  spin: 'animate-spin',
  ping: 'animate-ping',
  pulse: 'animate-pulse',
  bounce: 'animate-bounce',
  fadeIn: 'animate-fade-in',
  fadeOut: 'animate-fade-out',
  slideIn: 'animate-slide-in',
  slideOut: 'animate-slide-out',
} as const;

/**
 * Shadow utility classes
 * Provides consistent shadows across the application
 */
export const shadows = {
  sm: 'shadow-sm',
  default: 'shadow',
  md: 'shadow-md',
  lg: 'shadow-lg',
  xl: 'shadow-xl',
  '2xl': 'shadow-2xl',
  inner: 'shadow-inner',
  none: 'shadow-none',
  glow: 'shadow-lg shadow-purple-500/25',
  glowBlue: 'shadow-lg shadow-blue-500/25',
  glowGreen: 'shadow-lg shadow-green-500/25',
} as const;

/**
 * Border utility classes
 * Provides consistent borders across the application
 */
export const borders = {
  default: 'border border-gray-700',
  primary: 'border border-purple-500/30',
  secondary: 'border border-blue-500/30',
  success: 'border border-green-500/30',
  warning: 'border border-yellow-500/30',
  error: 'border border-red-500/30',
  none: 'border-0',
} as const;

/**
 * Background utility classes
 * Provides consistent backgrounds across the application
 */
export const backgrounds = {
  primary: 'bg-purple-500',
  secondary: 'bg-blue-500',
  success: 'bg-green-500',
  warning: 'bg-yellow-500',
  error: 'bg-red-500',
  dark: 'bg-gray-900',
  darker: 'bg-gray-950',
  card: 'bg-gray-900/50 backdrop-blur-sm',
  glass: 'bg-white/5 backdrop-blur-md',
} as const;

/**
 * Text utility classes
 * Provides consistent text styling across the application
 */
export const textStyles = {
  heading: 'font-bold text-white',
  subheading: 'font-semibold text-gray-200',
  body: 'text-gray-300',
  caption: 'text-sm text-gray-400',
  muted: 'text-gray-500',
  primary: 'text-purple-400',
  secondary: 'text-blue-400',
  success: 'text-green-400',
  warning: 'text-yellow-400',
  error: 'text-red-400',
} as const;
