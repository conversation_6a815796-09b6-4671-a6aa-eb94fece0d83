from typing import Optional, Dict, Any, List, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, text
from redis import Redis
from models import SecurityLog, TokenBlacklist, User, Task
from schemas import SecurityLogCreate, TokenBlacklistSchema
import logging
import json
import asyncio
from collections import deque
import aiohttp
import os
from urllib.parse import quote
import re
from fastapi import Request, HTTPException, status

logger = logging.getLogger(__name__)

# Redis configuration
REDIS_CONFIG = {
    'maxmemory': '100mb',
    'maxmemory-policy': 'volatile-ttl'
}

# Telegram configuration
TELEGRAM_BOT_TOKEN = os.getenv("TELEGRAM_BOT_TOKEN")
TELEGRAM_ADMIN_CHAT_ID = os.getenv("TELEGRAM_ADMIN_CHAT_ID")

class SecurityService:
    """Security service with session tracking"""
    
    def __init__(self, db: Session, redis: Redis):
        self.db = db
        self.redis = redis
        self.test_mode = os.getenv("TEST_MODE", "false").lower() == "true"
        
        # Constants
        self.REDIS_BLACKLIST_PREFIX = "token_blacklist:"
        self.REDIS_SESSION_PREFIX = "user_sessions:"
        self.REDIS_SECURITY_STATS_PREFIX = "security_stats:"
        self.REDIS_BLACKLIST_TTL = 60 * 60 * 24 * 7  # 7 days
        self.MAX_SESSIONS_PER_USER = 5
        
        # SQL injection patterns
        self.sql_patterns = [
            r"(\b(union|select|insert|update|delete|drop|alter)\b.*?;)",
            r"(--.*$)",
            r"(\/\*.*?\*\/)",
            r"(;.*$)",
            r"('.*'.*')",
            r"(char\s*\(\s*\d+\s*\))",
        ]
        
        # Protected tables that require authentication
        self.protected_tables = {
            "marzban_panels": ["admin"],
            "system_configs": ["admin"],
            "task_packs": ["admin", "reseller"],
            "vpn_packages": ["admin", "reseller"],
            "reward_revocations": ["admin"],
            "social_tasks": ["admin"],
            "task_verifications": ["admin"],
            "tasks": ["admin"]
        }

    async def initialize(self):
        """Initialize Redis configuration"""
        try:
            # Get Redis client and test connection
            if hasattr(self.redis, 'get_client'):
                # This is our RedisClient wrapper
                redis_client = await self.redis.get_client()
                await redis_client.ping()
                logger.info("Security service: Redis connection verified")
            elif hasattr(self.redis, 'ping'):
                # This is a direct Redis client
                await self.redis.ping()
                logger.info("Security service: Direct Redis connection verified")
            else:
                logger.warning("Security service: Unknown Redis client type, skipping ping test")
            
            # Note: Redis config_set requires admin privileges and may not be available in Docker
            # We'll skip Redis configuration for now as it's not critical for functionality
            logger.info("Security service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize security service: {str(e)}")
            # Don't raise the exception to avoid breaking startup
            logger.warning("Security service will continue with limited Redis functionality")

    async def check_sql_injection(self, params: dict, body: Optional[str] = None) -> bool:
        """Check parameters for SQL injection patterns"""
        try:
            all_params = [*params.values()]
            if body:
                all_params.append(body)
            
            for param in all_params:
                param_str = str(param)
                for pattern in self.sql_patterns:
                    if re.search(pattern, param_str, re.IGNORECASE):
                        return False
            return True
        except Exception as e:
            logger.error(f"SQL injection check error: {str(e)}")
            return False

    async def check_table_access(self, table: str, user_role: str) -> bool:
        """Check if user has access to protected table"""
        if table in self.protected_tables:
            return user_role in self.protected_tables[table]
        return True

    async def validate_request_security(self, request: Request) -> None:
        """Validate request for security concerns"""
        try:
            # Get query parameters
            query_params = dict(request.query_params)
            
            # Check for SQL injection in query parameters
            if not await self.check_sql_injection(query_params):
                raise HTTPException(
                    status_code=400,
                    detail="Invalid input detected"
                )
            
            # Check for SQL injection in request body
            body = await request.body()
            body_str = body.decode() if body else ""
            if not await self.check_sql_injection({}, body_str):
                raise HTTPException(
                    status_code=400,
                    detail="Invalid input detected"
                )
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Security validation error: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail="Internal server error"
            )

    async def create_session(self, user_id: int, token: str, device_info: Optional[Dict] = None) -> bool:
        """Create new session with limits check"""
        if self.test_mode:
            return True

        try:
            # Check current session count
            session_pattern = f"{self.REDIS_SESSION_PREFIX}{user_id}:*"
            current_sessions = await self.redis.keys(session_pattern)
            
            if len(current_sessions) >= self.MAX_SESSIONS_PER_USER:
                # Remove oldest session
                oldest_session = None
                oldest_time = None
                
                for key in current_sessions:
                    session_data = await self.redis.get(key)
                    if session_data:
                        session = json.loads(session_data)
                        created_at = datetime.fromisoformat(session['created_at'])
                        if oldest_time is None or created_at < oldest_time:
                            oldest_time = created_at
                            oldest_session = key
                
                if oldest_session:
                    await self.redis.delete(oldest_session)

            # Store new session
            session_key = f"{self.REDIS_SESSION_PREFIX}{user_id}:{token}"
            session_data = {
                "token": token,
                "created_at": datetime.utcnow().isoformat(),
                "device_info": device_info or {},
                "last_activity": datetime.utcnow().isoformat()
            }

            # Store in Redis with TTL
            await self.redis.setex(
                session_key,
                self.REDIS_BLACKLIST_TTL,
                json.dumps(session_data)
            )

            return True

        except Exception as e:
            logger.error(f"Error creating session: {str(e)}")
            return False

    async def validate_session(self, user_id: int, token: str) -> bool:
        """Validate session and update last activity"""
        try:
            # Check if token is blacklisted
            if await self.is_token_blacklisted(token):
                return False

            # Check session in Redis
            session_key = f"{self.REDIS_SESSION_PREFIX}{user_id}:{token}"
            session_data = await self.redis.get(session_key)
            
            if not session_data:
                return False

            # Update last activity
            session = json.loads(session_data)
            session['last_activity'] = datetime.utcnow().isoformat()
            
            # Refresh session TTL and data
            await self.redis.setex(
                session_key,
                self.REDIS_BLACKLIST_TTL,
                json.dumps(session)
            )
            
            return True

        except Exception as e:
            logger.error(f"Error validating session: {str(e)}")
            return False

    async def revoke_session(self, user_id: int, token: str, reason: str = "user_logout") -> bool:
        """Revoke specific session"""
        try:
            # Add token to blacklist
            await self.blacklist_token(
                token=token,
                user_id=user_id,
                expires_at=datetime.utcnow() + timedelta(days=7),
                revocation_reason=reason,
                revoked_by_ip=None  # We don't have IP here
            )

            # Remove session from Redis
            session_key = f"{self.REDIS_SESSION_PREFIX}{user_id}:{token}"
            await self.redis.delete(session_key)

            return True

        except Exception as e:
            logger.error(f"Error revoking session: {str(e)}")
            return False

    async def revoke_all_sessions(self, user_id: int) -> bool:
        """Revoke all user sessions"""
        try:
            # Get all user sessions
            session_pattern = f"{self.REDIS_SESSION_PREFIX}{user_id}:*"
            sessions = await self.redis.keys(session_pattern)
            
            for session_key in sessions:
                # Extract token from key
                token = session_key.split(':')[-1]
                await self.revoke_session(user_id, token, "revoke_all")

            return True

        except Exception as e:
            logger.error(f"Error revoking all sessions: {str(e)}")
            return False

    async def get_active_sessions(self, user_id: int) -> List[Dict]:
        """Get list of user's active sessions"""
        try:
            sessions = []
            session_pattern = f"{self.REDIS_SESSION_PREFIX}{user_id}:*"
            keys = await self.redis.keys(session_pattern)
            
            for key in keys:
                session_data = await self.redis.get(key)
                if session_data:
                    session = json.loads(session_data)
                    sessions.append({
                        "token": session['token'][-8:],  # Last 8 chars for display
                        "created_at": session['created_at'],
                        "last_activity": session['last_activity'],
                        "device_info": session['device_info']
                    })

            return sorted(sessions, key=lambda x: x['last_activity'], reverse=True)

        except Exception as e:
            logger.error(f"Error getting active sessions: {str(e)}")
            return []

    async def cleanup_old_sessions(self):
        """Cleanup expired sessions"""
        try:
            # Redis handles TTL automatically
            pass
        except Exception as e:
            logger.error(f"Error cleaning up sessions: {str(e)}")

    async def blacklist_token(
        self,
        token: str,
        user_id: int,
        expires_at: datetime,
        revocation_reason: str,
        revoked_by_ip: Optional[str] = None
    ) -> bool:
        """Add token to blacklist, ensuring uniqueness."""
        try:
            # Check if token already exists in the database
            existing_entry = self.db.query(TokenBlacklist).filter(TokenBlacklist.token == token).first()
            if existing_entry:
                logger.warning(f"Token already blacklisted: {token[:10]}... Skipping database insertion.")
                # Optionally, ensure it's in Redis too
                redis_key = f"{self.REDIS_BLACKLIST_PREFIX}{token}"
                if not await self.redis.exists(redis_key):
                    ttl = int((existing_entry.expires_at - datetime.utcnow()).total_seconds())
                    if ttl > 0:
                        await self.redis.setex(redis_key, ttl, "1")
                return True # Indicate success as it's already blacklisted

            # If not existing, add to database
            blacklist_entry = TokenBlacklist(
                token=token,
                user_id=user_id,
                expires_at=expires_at,
                revoked_at=datetime.utcnow(),
                revocation_reason=revocation_reason,
                revoked_by_ip=revoked_by_ip or "unknown"
            )
            self.db.add(blacklist_entry)
            self.db.commit()
            
            # Add to Redis with TTL
            redis_key = f"{self.REDIS_BLACKLIST_PREFIX}{token}"
            ttl = int((expires_at - datetime.utcnow()).total_seconds())
            await self.redis.setex(redis_key, ttl, "1")
            
            # Log the event
            await self.log_security_event(
                action_type="token_blacklist",
                status="success",
                ip_address=revoked_by_ip or "unknown",
                risk_level="low",
                user_id=user_id,
                details={"token": token, "reason": revocation_reason}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error blacklisting token: {str(e)}")
            self.db.rollback()
            return False

    async def is_token_blacklisted(self, token: str) -> bool:
        """Check if token is blacklisted"""
        try:
            # Check Redis first
            redis_key = f"{self.REDIS_BLACKLIST_PREFIX}{token}"
            if await self.redis.exists(redis_key):
                return True
            
            # Check database as fallback
            blacklist_entry = self.db.query(TokenBlacklist).filter(
                TokenBlacklist.token == token,
                TokenBlacklist.expires_at > datetime.utcnow()
            ).first()
            
            if blacklist_entry:
                # Refresh Redis cache
                ttl = int((blacklist_entry.expires_at - datetime.utcnow()).total_seconds())
                if ttl > 0:
                    await self.redis.setex(redis_key, ttl, "1")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error checking token blacklist: {str(e)}")
            return True  # Fail secure - treat errors as blacklisted

    async def send_telegram_alert(self, message: str):
        """Send alert to Telegram for high-risk events"""
        if not TELEGRAM_BOT_TOKEN or not TELEGRAM_ADMIN_CHAT_ID:
            return

        try:
            url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
            async with aiohttp.ClientSession() as session:
                await session.post(url, json={
                    "chat_id": TELEGRAM_ADMIN_CHAT_ID,
                    "text": message,
                    "parse_mode": "HTML"
                })
        except Exception as e:
            logger.error(f"Error sending Telegram alert: {str(e)}")

    async def log_security_event(
        self,
        action_type: str,
        status: str,
        ip_address: str,
        risk_level: str,
        user_id: Optional[int] = None,
        user_agent: Optional[str] = None,
        details: Optional[Dict] = None,
        location_info: Optional[Dict] = None
    ) -> bool:
        """Log security event to database and Redis"""
        if self.test_mode:
            return True

        try:
            # Create event data
            event_data = {
                "action_type": action_type,
                "status": status,
                "ip_address": ip_address,
                "risk_level": risk_level,
                "user_id": user_id,
                "user_agent": user_agent,
                "details": details or {},
                "location_info": location_info or {},
                "timestamp": datetime.utcnow().isoformat()
            }

            # Store in Redis for real-time monitoring
            redis_key = f"security_event:{datetime.utcnow().timestamp()}"
            if self.redis:
                await self.redis.setex(
                    redis_key,
                    3600,  # 1 hour TTL
                    json.dumps(event_data)
                )

            # Update security stats
            if self.redis:
                await self._update_security_stats(
                    action_type,
                    status,
                    risk_level,
                    ip_address
                )

            # Send Telegram alert for high-risk events
            if risk_level == "high":
                alert_message = (
                    f"🚨 High Risk Security Event 🚨\n"
                    f"Action: {action_type}\n"
                    f"Status: {status}\n"
                    f"IP: {ip_address}\n"
                    f"User ID: {user_id or 'N/A'}\n"
                    f"Details: {json.dumps(details or {}, indent=2)}"
                )
                await self.send_telegram_alert(alert_message)

            # Create database log entry
            log_entry = SecurityLog(
                user_id=user_id,
                action_type=action_type,
                status=status,
                ip_address=ip_address,
                risk_level=risk_level,
                user_agent=user_agent,
                details=details,
                location_info=location_info
            )
            self.db.add(log_entry)
            self.db.commit()

            return True

        except Exception as e:
            logger.error(f"Error logging security event: {str(e)}")
            return False

    async def _update_security_stats(
        self,
        action_type: str,
        status: str,
        risk_level: str,
        ip_address: str
    ):
        """Update security statistics in Redis"""
        try:
            now = datetime.utcnow()
            date_key = now.strftime("%Y-%m-%d")
            hour_key = now.strftime("%Y-%m-%d-%H")

            # Keys for different time windows
            daily_key = f"{self.REDIS_SECURITY_STATS_PREFIX}daily:{date_key}"
            hourly_key = f"{self.REDIS_SECURITY_STATS_PREFIX}hourly:{hour_key}"
            ip_key = f"{self.REDIS_SECURITY_STATS_PREFIX}ip:{ip_address}:{date_key}"

            # Update daily stats
            daily_data = await self.redis.get(daily_key)
            if daily_data:
                daily_stats = json.loads(daily_data)
            else:
                daily_stats = {
                    "total": 0,
                    "by_action": {},
                    "by_status": {},
                    "by_risk": {},
                    "unique_ips": set()
                }

            # Update hourly stats
            hourly_data = await self.redis.get(hourly_key)
            if hourly_data:
                hourly_stats = json.loads(hourly_data)
            else:
                hourly_stats = {
                    "total": 0,
                    "by_action": {},
                    "by_status": {},
                    "by_risk": {},
                    "unique_ips": set()
                }

            # Update IP-specific stats
            ip_data = await self.redis.get(ip_key)
            if ip_data:
                ip_stats = json.loads(ip_data)
            else:
                ip_stats = {
                    "total": 0,
                    "by_action": {},
                    "by_status": {},
                    "by_risk": {}
                }

            # Update stats
            for stats in [daily_stats, hourly_stats, ip_stats]:
                stats["total"] = stats.get("total", 0) + 1
                stats["by_action"][action_type] = stats.get("by_action", {}).get(action_type, 0) + 1
                stats["by_status"][status] = stats.get("by_status", {}).get(status, 0) + 1
                stats["by_risk"][risk_level] = stats.get("by_risk", {}).get(risk_level, 0) + 1

            # Add IP to unique sets
            daily_stats["unique_ips"] = list(set(daily_stats.get("unique_ips", [])).union({ip_address}))
            hourly_stats["unique_ips"] = list(set(hourly_stats.get("unique_ips", [])).union({ip_address}))

            # Store updated stats
            await self.redis.setex(daily_key, 86400, json.dumps(daily_stats))  # 24 hours
            await self.redis.setex(hourly_key, 3600, json.dumps(hourly_stats))  # 1 hour
            await self.redis.setex(ip_key, 86400, json.dumps(ip_stats))  # 24 hours

        except Exception as e:
            logger.error(f"Error updating security stats: {str(e)}")
            # Don't raise exception to prevent blocking the main flow

    def _parse_user_agent(self, user_agent: str) -> Dict:
        """Parse user agent string for device info"""
        try:
            info = {
                "raw": user_agent,
                "is_mobile": any(x in user_agent.lower() for x in ['mobile', 'android', 'iphone']),
                "browser": "Unknown",
                "os": "Unknown"
            }
            
            # Basic browser detection
            browsers = {
                'Chrome': 'Chrome',
                'Firefox': 'Firefox',
                'Safari': 'Safari',
                'Edge': 'Edge',
                'Opera': 'Opera'
            }
            
            for browser, name in browsers.items():
                if browser in user_agent:
                    info['browser'] = name
                    break
            
            # Basic OS detection
            if 'Windows' in user_agent:
                info['os'] = 'Windows'
            elif 'Mac OS' in user_agent:
                info['os'] = 'MacOS'
            elif 'Linux' in user_agent:
                info['os'] = 'Linux'
            elif 'Android' in user_agent:
                info['os'] = 'Android'
            elif 'iPhone' in user_agent or 'iPad' in user_agent:
                info['os'] = 'iOS'
            
            return info
            
        except Exception:
            return {"raw": user_agent}

    async def get_security_stats(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        include_high_risk: bool = False
    ) -> Dict[str, Any]:
        """Get security statistics for time period"""
        try:
            if not start_date:
                start_date = datetime.utcnow() - timedelta(days=7)
            if not end_date:
                end_date = datetime.utcnow()

            stats = {
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                "daily_stats": {},
                "total_events": 0,
                "high_risk_events": 0,
                "unique_ips": 0
            }

            current = start_date
            while current <= end_date:
                date_key = current.strftime('%Y-%m-%d')
                
                # Get daily stats
                daily_stats = await self.redis.hgetall(f"{self.REDIS_SECURITY_STATS_PREFIX}daily:{date_key}")
                if daily_stats:
                    stats['daily_stats'][date_key] = daily_stats
                    stats['total_events'] += int(daily_stats.get('total', 0))
                    stats['high_risk_events'] += int(daily_stats.get('risk:high', 0)) + int(daily_stats.get('risk:critical', 0))
                
                # Get unique IPs
                unique_ips = await self.redis.scard(f"{self.REDIS_SECURITY_STATS_PREFIX}ips:{date_key}")
                stats['unique_ips'] += unique_ips
                
                # Get high-risk events if requested
                if include_high_risk:
                    high_risk = await self.redis.lrange(f"{self.REDIS_SECURITY_STATS_PREFIX}high_risk:{date_key}", 0, -1)
                    if high_risk:
                        stats['high_risk_events_detail'] = stats.get('high_risk_events_detail', [])
                        stats['high_risk_events_detail'].extend([json.loads(event) for event in high_risk])
                
                current += timedelta(days=1)

            return stats
            
        except Exception as e:
            logger.error(f"Error getting security stats: {str(e)}")
            return {}

    async def cleanup_expired_tokens(self) -> int:
        """Remove expired tokens from database and Redis"""
        try:
            # Remove from database
            result = self.db.query(TokenBlacklist).filter(
                TokenBlacklist.expires_at <= datetime.utcnow()
            ).delete()
            
            self.db.commit()
            
            # Redis cleanup is automatic via TTL
            return result
            
        except Exception as e:
            logger.error(f"Error cleaning up expired tokens: {str(e)}")
            self.db.rollback()
            raise

    async def get_user_security_logs(
        self,
        user_id: int,
        skip: int = 0,
        limit: int = 100,
        risk_level: Optional[str] = None,
        action_type: Optional[str] = None
    ) -> List[SecurityLog]:
        """Get security logs for a user"""
        try:
            query = self.db.query(SecurityLog).filter(SecurityLog.user_id == user_id)
            
            if risk_level:
                query = query.filter(SecurityLog.risk_level == risk_level)
            if action_type:
                query = query.filter(SecurityLog.action_type == action_type)
                
            return query.order_by(SecurityLog.created_at.desc()).offset(skip).limit(limit).all()
            
        except Exception as e:
            logger.error(f"Error retrieving security logs: {str(e)}")
            raise

    async def get_security_statistics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """Get security-related statistics"""
        try:
            query = self.db.query(SecurityLog)
            
            if start_date:
                query = query.filter(SecurityLog.created_at >= start_date)
            if end_date:
                query = query.filter(SecurityLog.created_at <= end_date)
                
            total_events = query.count()
            high_risk_events = query.filter(
                SecurityLog.risk_level.in_(['high', 'critical'])
            ).count()
            failed_logins = query.filter(
                and_(
                    SecurityLog.action_type == 'login',
                    SecurityLog.status == 'failure'
                )
            ).count()
            
            return {
                "total_events": total_events,
                "high_risk_events": high_risk_events,
                "failed_logins": failed_logins,
                "period_start": start_date,
                "period_end": end_date
            }
            
        except Exception as e:
            logger.error(f"Error getting security statistics: {str(e)}")
            raise 