#!/usr/bin/env python3
import sqlite3
import json
import os
import sys
from datetime import datetime

# Add backend directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from database import DATABASE_URL
except ImportError:
    DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///./atlasvpn.db')

# Allowed rarity values
VALID_RARITIES = ['common', 'rare', 'epic', 'legendary', 'mythic']
DEFAULT_RARITY = 'common'

# Sample card catalog data (Ensure rarities here are valid)
SAMPLE_CARDS = [
    {
        "name": "Bronze Miner",
        "rarity": "common",
        "description": "The Bronze Miner diligently harvests Atlas Points at a steady rate.",
        "level_profits_json": json.dumps([10, 15, 25, 40, 60]),
        "level_costs_json": json.dumps([0, 200, 500, 1200, 2500]),
        "max_level": 5,
        "image_url": "/img/cards/bronze-miner.jpg"
    },
    {
        "name": "Silver Explorer",
        "rarity": "rare", # Was uncommon, fixed
        "description": "The Silver Explorer ventures through the Atlas network, gathering points at an increased rate.",
        "level_profits_json": json.dumps([20, 35, 60, 100, 150]),
        "level_costs_json": json.dumps([0, 500, 1500, 3000, 6000]),
        "max_level": 5,
        "image_url": "/img/cards/silver-explorer.jpg"
    },
    {
        "name": "Gold Processor",
        "rarity": "rare",
        "description": "The Gold Processor efficiently converts network activity into Atlas Points.",
        "level_profits_json": json.dumps([40, 70, 120, 200, 300]),
        "level_costs_json": json.dumps([0, 1000, 3000, 7000, 15000]),
        "max_level": 5,
        "image_url": "/img/cards/gold-processor.jpg"
    },
    {
        "name": "Platinum Network",
        "rarity": "epic",
        "description": "The Platinum Network accelerates point generation through advanced algorithms.",
        "level_profits_json": json.dumps([80, 150, 250, 400, 600]),
        "level_costs_json": json.dumps([0, 2000, 6000, 15000, 35000]),
        "max_level": 5,
        "image_url": "/img/cards/platinum-network.jpg"
    },
    {
        "name": "Diamond Server",
        "rarity": "legendary",
        "description": "The Diamond Server represents the pinnacle of Atlas Point generation technology.",
        "level_profits_json": json.dumps([150, 300, 500, 800, 1200]),
        "level_costs_json": json.dumps([0, 5000, 15000, 40000, 100000]),
        "max_level": 5,
        "image_url": "/img/cards/diamond-server.jpg"
    }
]

def get_db_path():
    """Get the database path from the DATABASE_URL environment variable or use default"""
    try:
        from database import engine
        return engine.url.database
    except ImportError:
        db_url = DATABASE_URL
        if db_url.startswith('sqlite:///'):
            return db_url[len('sqlite:///'):]
        return './atlasvpn.db'

def check_card_catalog():
    """Check if card catalog entries exist, fix invalid rarities, and create samples if empty"""
    db_path = get_db_path()
    print(f"Using database at: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row # Use row factory for dictionary-like access
        cursor = conn.cursor()

        # --- 1. Ensure card_catalog table exists --- 
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='card_catalog'")
        if not cursor.fetchone():
            print("Card catalog table does not exist. Creating table...")
            cursor.execute("""
            CREATE TABLE card_catalog (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                rarity TEXT NOT NULL,
                description TEXT,
                level_profits_json TEXT NOT NULL,
                level_costs_json TEXT NOT NULL,
                max_level INTEGER NOT NULL,
                image_url TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """)
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_card_catalog_name ON card_catalog (name)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_card_catalog_rarity ON card_catalog (rarity)")
            conn.commit()
            print("Created card_catalog table.")

        # --- 2. Find and fix invalid rarities --- 
        print("\nChecking for invalid rarities...")
        # Create placeholders for the NOT IN clause
        invalid_rarity_placeholders = ",".join("?" * len(VALID_RARITIES))
        # Select cards whose rarity is NOT in the valid list
        sql_find_invalid = f"SELECT id, name, rarity FROM card_catalog WHERE rarity NOT IN ({invalid_rarity_placeholders})"
        
        cursor.execute(sql_find_invalid, VALID_RARITIES)
        invalid_cards = cursor.fetchall()
        updated_count = 0

        if invalid_cards:
            print("Found cards with invalid rarities to fix:")
            ids_to_fix = []
            for card_row in invalid_cards:
                card = dict(card_row)
                print(f"  -> Card ID {card['id']} ({card['name']}): Invalid rarity '{card['rarity']}'. Will be set to '{DEFAULT_RARITY}'.")
                ids_to_fix.append(card['id'])
            
            # Update all invalid cards in one go
            if ids_to_fix:
                update_placeholders = ",".join("?" * len(ids_to_fix))
                sql_update = f"UPDATE card_catalog SET rarity = ? WHERE id IN ({update_placeholders})"
                params = [DEFAULT_RARITY] + ids_to_fix
                cursor.execute(sql_update, params)
                updated_count = len(ids_to_fix)
                conn.commit()
                print(f"Fixed {updated_count} cards with invalid rarities.")
        else:
            print("No invalid rarities found.")

        # --- 3. Add sample cards if catalog is empty --- 
        cursor.execute("SELECT COUNT(*) FROM card_catalog")
        card_count = cursor.fetchone()['COUNT(*)'] # Access by column name due to row_factory
        print(f"\nFound {card_count} cards in the catalog after checks.")
        
        if card_count == 0:
            print("Catalog is empty. Adding sample cards...")
            added_count = 0
            for card in SAMPLE_CARDS:
                try:
                    # Ensure sample card rarity is valid before attempting insert
                    if card['rarity'] not in VALID_RARITIES:
                         print(f"  -> Warning: Sample card '{card['name']}' has invalid rarity '{card['rarity']}'. Skipping.")
                         continue
                         
                    cursor.execute("""
                    INSERT INTO card_catalog (
                        name, rarity, description, level_profits_json, level_costs_json, max_level, image_url
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        card["name"],
                        card["rarity"],
                        card["description"],
                        card["level_profits_json"],
                        card["level_costs_json"],
                        card["max_level"],
                        card["image_url"]
                    ))
                    added_count += 1
                except sqlite3.IntegrityError as e:
                    print(f"  -> Skipping card '{card['name']}' due to IntegrityError (likely already exists): {e}")
            
            if added_count > 0:
                conn.commit()
                print(f"Added {added_count} sample cards to the catalog.")
        else:
            print("Card catalog is not empty. No sample cards added.")
        
        # --- 4. Display the final catalog --- 
        cursor.execute("SELECT id, name, rarity FROM card_catalog ORDER BY id")
        final_cards = cursor.fetchall()
        print("\nFinal Cards in catalog:")
        if final_cards:
            for card_row in final_cards:
                card = dict(card_row)
                print(f"ID: {card['id']}, Name: {card['name']}, Rarity: {card['rarity']}")
        else:
             print("Catalog is empty.")
        
        conn.close()
        return True
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
        return False
    except Exception as e:
        print(f"Unexpected error: {e}")
        import traceback
        traceback.print_exc() # Print full traceback for unexpected errors
        return False

if __name__ == "__main__":
    print("Card Catalog Check & Fix Utility")
    print("================================")
    print(f"Running at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    success = check_card_catalog()
    if success:
        print("\nOperation completed successfully.")
        sys.exit(0)
    else:
        print("\nOperation failed.")
        sys.exit(1) 