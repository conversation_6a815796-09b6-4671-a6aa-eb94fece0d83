from sqlalchemy.orm import Session
from sqlalchemy import func, select
from typing import Optional, List, Dict
from datetime import datetime, timedelta, timezone
from models import (
    Task, TaskCompletion, TaskStatus,
    TaskType, RewardType, User, Transaction, TransactionType
)
from schemas import TaskCreate, TaskVerificationResult
import logging
import json
from .verification_service import VerificationService, VerificationError
import os
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

logger = logging.getLogger(__name__)

class TaskError(Exception):
    def __init__(self, message: str, error_code: str):
        self.message = message
        self.error_code = error_code
        super().__init__(self.message)

class TaskRewardError(TaskError):
    pass

class TaskService:
    """Enhanced task service with improved state management"""
    
    # Valid state transitions
    VALID_TRANSITIONS = {
        None: [TaskStatus.PENDING],
        TaskStatus.PENDING: [TaskStatus.ACTIVE, TaskStatus.FAILED],
        TaskStatus.ACTIVE: [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.PENDING],
        TaskStatus.COMPLETED: [TaskStatus.FAILED],  # Remove CLAIMED status
        TaskStatus.FAILED: [TaskStatus.PENDING]  # Allow retry from failed
    }
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.verification_service = VerificationService(db)

    def _validate_state_transition(self, current_state: Optional[TaskStatus], new_state: TaskStatus) -> bool:
        """Validate if a state transition is allowed"""
        if current_state not in self.VALID_TRANSITIONS:
            return False
        return new_state in self.VALID_TRANSITIONS[current_state]

    async def create_task(self, task_data: TaskCreate) -> Task:
        """Create a new task"""
        try:
            # Validate float values
            if task_data.reward_value is not None:
                if not isinstance(task_data.reward_value, (int, float)) or task_data.reward_value < 0 or task_data.reward_value > 1000:
                    raise ValueError("Reward value must be between 0 and 1000")
                task_data.reward_value = float(task_data.reward_value)

            if task_data.cycle_bonus_reward is not None:
                if not isinstance(task_data.cycle_bonus_reward, (int, float)) or task_data.cycle_bonus_reward < 0 or task_data.cycle_bonus_reward > 1000:
                    raise ValueError("Cycle bonus reward must be between 0 and 1000")
                task_data.cycle_bonus_reward = float(task_data.cycle_bonus_reward)

            # Create base task with only model fields
            task = Task(
                name=task_data.name,
                description=task_data.description,
                type=task_data.type,
                reward_type=task_data.reward_type,
                reward_value=task_data.reward_value,
                target_value=task_data.target_value,
                is_active=task_data.is_active,
                created_at=datetime.utcnow(),
                max_verification_attempts=task_data.max_verification_attempts,
                verification_cooldown=task_data.verification_cooldown,
            )
            
            # Set daily task specific fields
            if task_data.type == TaskType.DAILY_CHECKIN:
                task.cycle_length = task_data.cycle_length or 7
                task.daily_rewards = json.dumps(task_data.daily_rewards or [10, 15, 20, 25, 30, 35, 50])
                task.cycle_bonus_reward = task_data.cycle_bonus_reward or 100
                task.reset_streak_after_hours = task_data.reset_streak_after_hours or 48
            
            # Set platform fields if provided
            if task_data.platform_url:
                task.platform_url = task_data.platform_url
            if task_data.platform_id:
                task.platform_id = task_data.platform_id
            if task_data.verify_key:
                task.verify_key = task_data.verify_key
            
            self.db.add(task)
            await self.db.commit()
            await self.db.refresh(task)
            
            logger.info(f"Created task: {task.id} with type {task.type}")
            return task
            
        except ValueError as e:
            logger.error(f"Validation error: {str(e)}")
            raise TaskError(str(e), "VALIDATION_ERROR")
        except Exception as e:
            logger.error(f"Error creating task: {str(e)}")
            await self.db.rollback()
            raise TaskError("Failed to create task", "TASK_CREATE_FAILED")

    async def start_task(self, task_id: int, user_id: int) -> TaskCompletion:
        """Start a new task for the user with proper state management"""
        try:
            # Check if task exists and is active
            stmt_task = select(Task).where(Task.id == task_id)
            result_task = await self.db.execute(stmt_task)
            task = result_task.scalar_one_or_none()
            
            if not task:
                logger.warning(f"Start task failed: Task ID {task_id} not found.")
                raise TaskError("Task not found", "TASK_NOT_FOUND")
                
            if not task.is_active:
                logger.warning(f"Start task failed: Task ID {task_id} is inactive.")
                raise TaskError("Task is not active", "TASK_INACTIVE")

            # Check if user has any active/pending completion of this task
            stmt_existing = select(TaskCompletion).where(
                TaskCompletion.task_id == task_id,
                TaskCompletion.user_id == user_id,
                TaskCompletion.status.in_([TaskStatus.ACTIVE, TaskStatus.PENDING])
            )
            result_existing = await self.db.execute(stmt_existing)
            existing_completion = result_existing.scalar_one_or_none()
            
            if existing_completion:
                 logger.warning(f"Start task blocked for user {user_id}, task {task_id}: Task already in progress (status: {existing_completion.status}).")
                 # Re-fetch task details for the error message if needed
                 # await self.db.refresh(existing_completion.task) # Might be needed if task details aren't loaded
                 raise TaskError(f"Task '{task.name}' already in progress", "TASK_IN_PROGRESS")

            # Validate initial state transition
            if not self._validate_state_transition(None, TaskStatus.PENDING):
                # This shouldn't normally happen if logic is correct
                logger.error(f"Invalid initial state transition attempted for task {task_id}, user {user_id}")
                raise TaskError("Invalid initial state", "INVALID_STATE")

            # Create initial verification data based on task type
            initial_verification_data = {
                "is_verified": False,
                "last_verified_at": None
            }

            if task.type == TaskType.TELEGRAM_CHANNEL:
                initial_verification_data.update({
                    "channel_id": task.platform_id,
                    "username": None,
                    "membership_verified": False
                })
            elif task.type == TaskType.YOUTUBE_VIEW:
                initial_verification_data.update({
                    "verification_code": None
                })
            elif task.type == TaskType.INSTAGRAM_FOLLOW:
                initial_verification_data.update({
                    "username": None
                })
            elif task.type == TaskType.REFERRAL:
                initial_verification_data.update({
                    "referral_code": None
                })

            # Create new task completion with proper initialization
            completion = TaskCompletion(
                task_id=task_id,
                user_id=user_id,
                status=TaskStatus.PENDING,
                started_at=datetime.utcnow(),
                completed_at=None,
                verification_attempts=0,
                current_progress=0,
                is_claimed=False,
                claimed_at=None,
                last_verified_at=None,
                verification_data=json.dumps(initial_verification_data),
                verification_cooldown=task.verification_cooldown or 60,
                max_verification_attempts=task.max_verification_attempts or 3,
                is_verified=False,
                verification_method=None,
                verification_token=None,
                reward_amount=0.0,
                streak_day=1 if task.type == TaskType.DAILY_CHECKIN else None,
                cycle_day=1 if task.type == TaskType.DAILY_CHECKIN else None,
                reward_multiplier=1.0
            )
                
            # Add and commit in one transaction (async)
            try:
                self.db.add(completion)
                await self.db.commit()
                # Eagerly load the 'task' relationship during refresh
                await self.db.refresh(completion, attribute_names=['task'])

                logger.info(f"Task {task_id} started successfully for user {user_id} (Completion ID: {completion.id})")
                return completion

            except SQLAlchemyError as e:
                logger.error(f"Database error creating task completion for task {task_id}, user {user_id}: {str(e)}", exc_info=True)
                await self.db.rollback()
                
                # Check again if duplicate error or other DB issue
                stmt_check_again = select(TaskCompletion).where(
                    TaskCompletion.task_id == task_id,
                    TaskCompletion.user_id == user_id
                )
                result_check_again = await self.db.execute(stmt_check_again)
                existing_after_fail = result_check_again.scalar_one_or_none()
                                
                if existing_after_fail:
                    status_val = existing_after_fail.status.value if existing_after_fail.status else "UNKNOWN"
                    logger.warning(f"Task completion creation failed for user {user_id}, task {task_id}, but record now exists with status: {status_val}")
                    raise TaskError(f"Task already has a completion record with status: {status_val}", "TASK_ALREADY_EXISTS")
                else:
                    raise TaskError("Failed to create task completion due to database error", "DB_ERROR")

        except TaskError as task_err:
            logger.warning(f"TaskError starting task {task_id} for user {user_id}: {task_err.message} (Code: {task_err.error_code})")
            raise task_err # Re-raise specific TaskErrors
        except Exception as e:
            # Log unexpected errors
            logger.error(f"Unexpected error starting task {task_id} for user {user_id}: {str(e)}", exc_info=True)
            # Ensure rollback on unexpected errors
            try:
                await self.db.rollback()
            except Exception as rb_err:
                logger.error(f"Error during rollback after exception in start_task: {rb_err}")
            # Raise a generic start error
            raise TaskError(f"Failed to start task: {str(e)}", "START_ERROR")

    async def verify_task(self, task_id: int, user_id: int, verification_data: Optional[Dict] = None) -> Dict:
        """Verify task completion"""
        try:
            # Get task completion
            stmt_completion = select(TaskCompletion).where(
                TaskCompletion.task_id == task_id,
                TaskCompletion.user_id == user_id,
                TaskCompletion.status == TaskStatus.PENDING
            )
            result_completion = await self.db.execute(stmt_completion)
            completion = result_completion.scalar_one_or_none()
            
            if not completion:
                raise TaskError("Task not started or already completed", "TASK_NOT_FOUND")
            
            # In test mode, auto-verify with correct code
            if os.getenv("TEST_MODE", "false").lower() == "true":
                if verification_data and verification_data.get('verification_code') == 'TEST123':
                    completion.status = TaskStatus.COMPLETED
                    completion.completed_at = datetime.utcnow()
                    completion.is_verified = True
                    await self.db.commit()
                    return {
                        "success": True,
                        "status": TaskStatus.COMPLETED,
                        "message": "Task verified successfully"
                    }
                else:
                    return {
                        "success": False,
                        "status": TaskStatus.PENDING,
                        "message": "Invalid verification code",
                        "error_code": "INVALID_CODE"
                    }
            
            # Normal verification logic
            verification_service = VerificationService(self.db)
            return await verification_service.verify_task(task_id, user_id, verification_data)
            
        except TaskError:
            raise
        except Exception as e:
            logger.error(f"Error verifying task: {str(e)}")
            raise TaskError("Failed to verify task", "VERIFICATION_ERROR")

    async def claim_reward(self, task_id: int, user_id: int) -> TaskCompletion:
        """Claim reward for completed task"""
        try:
            # Fetch completion, EAGERLY LOADING task and user relationships
            stmt_completion = select(TaskCompletion).where(
                TaskCompletion.task_id == task_id,
                TaskCompletion.user_id == user_id,
                TaskCompletion.status == TaskStatus.COMPLETED,
                TaskCompletion.is_claimed == False
            ).options(
                selectinload(TaskCompletion.task), # Eager load Task
                selectinload(TaskCompletion.user)  # Eager load User
            ).with_for_update()
            result_completion = await self.db.execute(stmt_completion)
            completion = result_completion.scalar_one_or_none()
            
            if not completion:
                # Added logging for why claim might fail
                logger.warning(f"Claim reward failed for user {user_id}, task {task_id}: No eligible completion found (status=COMPLETED, is_claimed=False).")
                raise TaskRewardError("Task not completed or already claimed", "INVALID_CLAIM")

            # --- User and Task are now preloaded --- 
            user = completion.user 
            task = completion.task

            # Redundant user check, but kept for safety
            if not user:
                logger.error(f"Claim reward failed for task {task_id}: User {user_id} not found despite being loaded.")
                raise TaskRewardError("User not found", "USER_NOT_FOUND")
            if not task:
                 logger.error(f"Claim reward failed for user {user_id}: Task {task_id} not found despite being loaded.")
                 raise TaskRewardError("Task not found", "TASK_NOT_FOUND")

            # Process reward based on type
            reward_amount = task.reward_value
            if reward_amount is None:
                logger.warning(f"Task {task_id} has no reward value defined. Skipping reward.")
                reward_amount = 0.0 # Default to 0 if None
            else:
                 reward_amount = float(reward_amount) # Ensure it's a float

            if task.reward_type == RewardType.WALLET_BONUS:
                old_balance = user.wallet_balance
                user.wallet_balance += reward_amount
                new_balance = user.wallet_balance
                logger.info(f"Applied wallet bonus for user {user_id}, task {task_id}: {old_balance} -> {new_balance} (+{reward_amount})")
                self.db.add(user) # Add user to session since balance changed
            elif task.reward_type == RewardType.DATA_BONUS:
                # TODO: Handle data bonus logic here
                logger.info(f"Data bonus reward claimed for user {user_id}, task {task_id}. Value: {reward_amount} (Implementation Pending)")
                pass
            elif task.reward_type == RewardType.DAYS_BONUS:
                # TODO: Handle days bonus logic here
                logger.info(f"Days bonus reward claimed for user {user_id}, task {task_id}. Value: {reward_amount} (Implementation Pending)")
                pass
            else:
                logger.warning(f"Unknown reward type '{task.reward_type}' for task {task_id}. Skipping reward processing.")
                reward_amount = 0.0 # Ensure reward amount is 0 if type unknown

            # Create transaction record only if reward_amount > 0 or configured to log 0 rewards
            if reward_amount > 0:
                transaction = Transaction(
                    user_id=user_id,
                    type=TransactionType.task_reward,
                    amount=reward_amount,
                    description=f"Reward for completing Task {task_id}: {task.name}",
                    balance_after=user.wallet_balance if task.reward_type == RewardType.WALLET_BONUS else None
                )
                self.db.add(transaction)

            # Mark as claimed
            completion.is_claimed = True
            completion.claimed_at = datetime.now(timezone.utc) # Use timezone-aware now
            completion.reward_amount = reward_amount
            self.db.add(completion) # Add completion to session since it changed
            
            await self.db.commit()
            logger.info(f"Reward claimed successfully for user {user_id}, task {task_id}. Amount: {reward_amount}")
            return completion

        except TaskError:
            raise
        except Exception as e:
            logger.error(f"Unexpected error claiming reward for user {user_id}, task {task_id}: {str(e)}", exc_info=True)
            await self.db.rollback()
            raise TaskRewardError(f"Failed to claim reward: {str(e)}", "CLAIM_FAILED")

    async def get_task_analytics(self, user_id: Optional[int] = None) -> Dict:
        """Get task analytics for user or all users using aggregate queries"""
        try:

            # --- Total Completions --- 
            total_completions_stmt = select(func.count(TaskCompletion.id))
            if user_id:
                total_completions_stmt = total_completions_stmt.where(TaskCompletion.user_id == user_id)
            total_completions = (await self.db.execute(total_completions_stmt)).scalar_one()

            # --- Completed Tasks --- 
            completed_stmt = select(func.count(TaskCompletion.id)).where(
                TaskCompletion.status == TaskStatus.COMPLETED
            )
            if user_id:
                 completed_stmt = completed_stmt.where(TaskCompletion.user_id == user_id)
            completed_tasks = (await self.db.execute(completed_stmt)).scalar_one()

            completion_rate = (completed_tasks / total_completions * 100) if total_completions > 0 else 0

            # --- Reward Distribution --- 
            reward_dist_stmt = (
                select(Task.reward_type, func.count(TaskCompletion.id))
                .join(Task, Task.id == TaskCompletion.task_id)
                .where(TaskCompletion.status == TaskStatus.COMPLETED)
            )
            if user_id:
                 reward_dist_stmt = reward_dist_stmt.where(TaskCompletion.user_id == user_id)
            reward_dist_stmt = reward_dist_stmt.group_by(Task.reward_type)
            
            reward_results = (await self.db.execute(reward_dist_stmt)).all()
            reward_distribution = {rt.name: count for rt, count in reward_results} # Use enum member name as key
            # Ensure all reward types are present
            for rt_enum in RewardType:
                if rt_enum.name not in reward_distribution:
                    reward_distribution[rt_enum.name] = 0

            # --- Task Type Distribution --- 
            task_type_dist_stmt = (
                select(Task.type, func.count(TaskCompletion.id))
                .join(Task, Task.id == TaskCompletion.task_id)
                .where(TaskCompletion.status == TaskStatus.COMPLETED)
            )
            if user_id:
                task_type_dist_stmt = task_type_dist_stmt.where(TaskCompletion.user_id == user_id)
            task_type_dist_stmt = task_type_dist_stmt.group_by(Task.type)
            
            task_type_results = (await self.db.execute(task_type_dist_stmt)).all()
            task_type_distribution = {tt.name: count for tt, count in task_type_results} # Use enum member name as key
            # Ensure all task types are present
            for tt_enum in TaskType:
                if tt_enum.name not in task_type_distribution:
                    task_type_distribution[tt_enum.name] = 0

            # --- Daily Stats (Last 7 days) --- 
            daily_stats = []
            base_daily_stmt = select(func.count(TaskCompletion.id))
            if user_id:
                base_daily_stmt = base_daily_stmt.where(TaskCompletion.user_id == user_id)
                
            for i in range(7):
                date = datetime.now(timezone.utc).date() - timedelta(days=i)
                start_date = datetime.combine(date, datetime.min.time(), tzinfo=timezone.utc)
                end_date = datetime.combine(date, datetime.max.time(), tzinfo=timezone.utc)
                
                daily_stmt = base_daily_stmt.where(
                    TaskCompletion.completed_at >= start_date,
                    TaskCompletion.completed_at <= end_date
                )
                daily_completions = (await self.db.execute(daily_stmt)).scalar_one()
                
                daily_stats.append({
                    "date": date.isoformat(),
                    "completions": daily_completions
                })
            
            return {
                "total_completions": total_completions,
                "completed_tasks": completed_tasks,
                "completion_rate": round(completion_rate, 2),
                "reward_distribution": reward_distribution,
                "task_type_distribution": task_type_distribution,
                "daily_stats": daily_stats
            }
            
        except Exception as e:
            logger.error(f"Error getting task analytics: {str(e)}", exc_info=True)
            raise TaskError("Failed to get analytics", "ANALYTICS_FAILED")

    async def toggle_task_status(self, task_id: int) -> Task:
        """Toggle task active status"""
        try:
            stmt_task = select(Task).where(Task.id == task_id)
            result_task = await self.db.execute(stmt_task)
            task = result_task.scalar_one_or_none()
            if not task:
                raise TaskError("Task not found", "TASK_NOT_FOUND")
            
            task.is_active = not task.is_active
            await self.db.commit()
            await self.db.refresh(task)
            
            return task
            
        except TaskError:
            raise
        except Exception as e:
            logger.error(f"Error toggling task status: {str(e)}")
            await self.db.rollback()
            raise TaskError("Failed to toggle task status", "TOGGLE_FAILED")

    async def reset_verification(self, task_id: int, user_id: int) -> TaskCompletion:
        """Reset task verification attempts for a user"""
        try:
            stmt_completion = select(TaskCompletion).where(
                TaskCompletion.task_id == task_id,
                TaskCompletion.user_id == user_id,
                TaskCompletion.status.in_([TaskStatus.ACTIVE, TaskStatus.PENDING])
            )
            result_completion = await self.db.execute(stmt_completion)
            completion = result_completion.scalar_one_or_none()
            
            if not completion:
                raise TaskError("Task completion not found or not in progress", "COMPLETION_NOT_FOUND")
            
            completion.verification_attempts = 0
            completion.last_verified_at = None
            
            await self.db.commit()
            await self.db.refresh(completion)
            
            return completion
            
        except TaskError:
            raise
        except Exception as e:
            logger.error(f"Error resetting task verification: {str(e)}")
            await self.db.rollback()
            raise TaskError("Failed to reset verification", "RESET_FAILED")

    async def update_task_status(
        self,
        completion_id: int,
        new_status: TaskStatus,
        verification_result: Optional[Dict] = None
    ) -> TaskCompletion:
        """Update task status with validation"""
        try:
            stmt_completion = select(TaskCompletion).where(
                TaskCompletion.id == completion_id
            )
            result_completion = await self.db.execute(stmt_completion)
            completion = result_completion.scalar_one_or_none()
            
            if not completion:
                raise TaskError("Task completion not found", "COMPLETION_NOT_FOUND")

            # Validate state transition
            if not self._validate_state_transition(completion.status, new_status):
                raise TaskError(
                    f"Invalid status transition from {completion.status} to {new_status}",
                    "INVALID_TRANSITION"
                )

            # Update status and related fields
            completion.status = new_status
            if new_status == TaskStatus.COMPLETED:
                completion.completed_at = datetime.utcnow()
                if verification_result:
                    completion.verification_data = json.dumps(verification_result)
            elif new_status == TaskStatus.CLAIMED:
                completion.claimed_at = datetime.utcnow()
                completion.is_claimed = True

            await self.db.commit()
            await self.db.refresh(completion)
            return completion

        except TaskError:
            raise
        except Exception as e:
            logger.error(f"Error updating task status: {str(e)}")
            await self.db.rollback()
            raise TaskError("Failed to update task status", "UPDATE_ERROR") 