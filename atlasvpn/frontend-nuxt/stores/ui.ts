import { defineStore } from 'pinia'

export interface UiState {
  // Theme
  currentTheme: 'light' | 'dark'
  
  // Welcome states
  showWelcomeMessage: boolean
  showWelcomeBackSplash: boolean
  lastSplashCheck: number
  
  // Visuals
  backgroundVisible: boolean
  
  // Layout/Navigation
  homeTabScrollPosition: number
  
  // App lifecycle (not persisted)
  isBackgrounded: boolean
  
  // Loading states (not persisted)
  globalLoading: boolean
  
  // Modal states (not persisted)
  activeModal: string | null
  modalData: any
  
  // Toast notifications (not persisted)
  toasts: Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message?: string
    duration?: number
  }>
}

export const useUiStore = defineStore('ui', {
  state: (): UiState => ({
    // Persisted state
    currentTheme: 'dark' as 'light' | 'dark',
    showWelcomeMessage: true,
    showWelcomeBackSplash: false,
    lastSplashCheck: 0,
    backgroundVisible: true,
    homeTabScrollPosition: 0,

    // Non-persisted runtime state
    isBackgrounded: false,
    globalLoading: false,
    activeModal: null,
    modalData: null,
    toasts: []
  }),

  persist: {
    key: 'atlas-ui-store',
    storage: typeof window !== 'undefined' ? localStorage : undefined,
    pick: [
      'currentTheme',
      'showWelcomeMessage',
      'showWelcomeBackSplash',
      'lastSplashCheck',
      'backgroundVisible',
      'homeTabScrollPosition'
    ]
  },

  getters: {
    isDarkTheme: (state) => state.currentTheme === 'dark',
    hasActiveModal: (state) => !!state.activeModal,
    activeToasts: (state) => state.toasts.filter(toast => toast.duration !== 0)
  },

  actions: {
    // Theme actions
    setTheme(theme: 'light' | 'dark') {
      this.currentTheme = theme
      
      if (typeof document !== 'undefined') {
        if (theme === 'dark') {
          document.documentElement.classList.add('dark')
        } else {
          document.documentElement.classList.remove('dark')
        }
      }
    },

    toggleTheme() {
      this.setTheme(this.currentTheme === 'dark' ? 'light' : 'dark')
    },

    // Welcome actions
    dismissWelcomeMessage() {
      this.showWelcomeMessage = false
    },

    setShowWelcomeBackSplash(show: boolean) {
      this.showWelcomeBackSplash = show
      this.lastSplashCheck = Date.now()
    },

    // Visual actions
    setBackgroundVisible(visible: boolean) {
      this.backgroundVisible = visible
    },

    // Layout actions
    setHomeTabScrollPosition(position: number) {
      this.homeTabScrollPosition = position
    },

    // App lifecycle actions
    setIsBackgrounded(isBackgrounded: boolean) {
      this.isBackgrounded = isBackgrounded
    },

    // Loading actions
    setGlobalLoading(loading: boolean) {
      this.globalLoading = loading
    },

    // Modal actions
    openModal(modalId: string, data?: any) {
      this.activeModal = modalId
      this.modalData = data
    },

    closeModal() {
      this.activeModal = null
      this.modalData = null
    },

    // Toast actions
    addToast(toast: Omit<UiState['toasts'][0], 'id'>) {
      const id = Math.random().toString(36).substring(2, 11)
      const newToast = {
        id,
        duration: 5000, // Default 5 seconds
        ...toast
      }
      
      this.toasts.push(newToast)
      
      // Auto-remove toast after duration
      if (newToast.duration && newToast.duration > 0) {
        setTimeout(() => {
          this.removeToast(id)
        }, newToast.duration)
      }
      
      return id
    },

    removeToast(id: string) {
      const index = this.toasts.findIndex(toast => toast.id === id)
      if (index > -1) {
        this.toasts.splice(index, 1)
      }
    },

    clearAllToasts() {
      this.toasts = []
    },

    // Utility actions
    showSuccessToast(title: string, message?: string) {
      return this.addToast({ type: 'success', title, message })
    },

    showErrorToast(title: string, message?: string) {
      return this.addToast({ type: 'error', title, message })
    },

    showWarningToast(title: string, message?: string) {
      return this.addToast({ type: 'warning', title, message })
    },

    showInfoToast(title: string, message?: string) {
      return this.addToast({ type: 'info', title, message })
    },

    // Initialize theme on store creation
    initializeTheme() {
      if (typeof document !== 'undefined') {
        if (this.currentTheme === 'dark') {
          document.documentElement.classList.add('dark')
        } else {
          document.documentElement.classList.remove('dark')
        }
      }
    }
  }
})
