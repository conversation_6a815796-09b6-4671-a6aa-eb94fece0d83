# AtlasVPN Development Guide

## Quick Start (Recommended for Development)

The fastest way to develop and test changes:

```bash
# Start backend services (required for API calls)
docker-compose up -d traefik postgres redis backend

# Start frontend in development mode (instant startup)
./quick-dev.sh
```

This approach:
- ✅ Starts in **seconds** instead of 20+ minutes
- ✅ Supports **hot reload** and **fast refresh**
- ✅ No Docker rebuilds required for code changes
- ✅ Full **Nuxt DevTools** support
- ✅ Direct access to logs and debugging

## Development Approaches

### 1. Quick Development (Recommended)

**Use Case**: Daily development, testing changes, debugging

```bash
./quick-dev.sh
```

**Pros**:
- Instant startup (3-5 seconds)
- Hot reload works perfectly
- Full debugging capabilities
- No container rebuilds needed
- Direct access to Nuxt DevTools

**Cons**:
- Requires Node.js and pnpm installed locally
- Not identical to production environment

### 2. Docker Development Mode

**Use Case**: Testing Docker-specific issues, environment parity

```bash
./dev.sh frontend
```

**Pros**:
- Runs in Docker environment
- Volume mounts for hot reload
- Closer to production setup

**Cons**:
- Slower startup (2-5 minutes for initial build)
- Requires Docker rebuilds for dependency changes

### 3. Production Build

**Use Case**: Final testing before deployment, production builds

```bash
./dev.sh build
```

**Pros**:
- Identical to production
- Optimized bundle
- Full production testing

**Cons**:
- Very slow (20+ minutes)
- No hot reload
- Requires full rebuild for any changes

## Development Scripts

### Quick Development Script (`./quick-dev.sh`)

Runs Nuxt directly on the host machine:

```bash
./quick-dev.sh
```

- Frontend: http://localhost:3005
- Proxied (for Telegram): https://app.atlasvip.cloud

### Docker Development Script (`./dev.sh`)

Manages Docker-based development:

```bash
./dev.sh start      # Start full development environment
./dev.sh frontend   # Start only frontend in dev mode
./dev.sh backend    # Start only backend services
./dev.sh build      # Build for production
./dev.sh rebuild    # Rebuild dev container
./dev.sh logs       # Show frontend logs
./dev.sh stop       # Stop all services
./dev.sh clean      # Clean up containers
./dev.sh status     # Show service status
./dev.sh shell      # Open shell in container
./dev.sh test       # Run tests
```

## Performance Comparison

| Method | Startup Time | Hot Reload | Rebuild Time | Use Case |
|--------|-------------|------------|--------------|----------|
| Quick Dev | 3-5 seconds | ✅ Instant | N/A | Daily development |
| Docker Dev | 2-5 minutes | ✅ Fast | 2-5 minutes | Docker testing |
| Production | 20+ minutes | ❌ None | 20+ minutes | Final testing |

## Telegram Mini App Testing

### Development Testing

1. Start backend services:
   ```bash
   docker-compose up -d traefik postgres redis backend
   ```

2. Start frontend in development mode:
   ```bash
   ./quick-dev.sh
   ```

3. Test the Telegram authentication:
   - The app will be available at https://app.atlasvip.cloud
   - Automatic Telegram authentication should work
   - Check browser console for authentication logs

### Authentication Flow Testing

The updated implementation includes:

- ✅ Modern `@telegram-apps/sdk` integration
- ✅ Automatic authentication on app load
- ✅ Proper error handling and fallbacks
- ✅ Session verification and management
- ✅ New user detection and routing

## File Structure

```
atlasvpn/
├── quick-dev.sh              # Quick development script (recommended)
├── dev.sh                    # Docker development script
├── docker-compose.dev.yml    # Development Docker override
├── DEVELOPMENT.md            # This guide
└── frontend-nuxt/
    ├── Dockerfile.dev        # Development Dockerfile
    ├── .dockerignore.dev     # Development Docker ignore
    ├── composables/
    │   ├── useTelegram.ts    # Updated with modern SDK
    │   └── useAuth.ts        # Updated with auto-auth
    ├── pages/
    │   └── index.vue         # Updated with auto-auth flow
    └── plugins/
        └── telegram.client.ts # Updated with modern SDK
```

## Environment Variables

Development environment variables are automatically set by the scripts:

```bash
NODE_ENV=development
PORT=3005
NITRO_PORT=3005
NUXT_PUBLIC_API_URL=https://app.atlasvip.cloud/api
NUXT_PUBLIC_WS_BASE_URL=wss://app.atlasvip.cloud/ws
NUXT_PUBLIC_TELEGRAM_WEBAPP_URL=https://app.atlasvip.cloud
NUXT_DEVTOOLS=true
NUXT_DEV_SSR_LOGS=true
```

## Troubleshooting

### Frontend won't start
```bash
# Check if dependencies are installed
cd frontend-nuxt && pnpm install

# Check if backend services are running
docker-compose ps
```

### Telegram authentication not working
```bash
# Check browser console for errors
# Verify initData is available
# Check network requests to /api/auth/telegram
```

### Hot reload not working
```bash
# For quick-dev: Restart the script
./quick-dev.sh

# For Docker dev: Check volume mounts
./dev.sh logs
```

## Next Steps

1. **For daily development**: Use `./quick-dev.sh`
2. **For Docker testing**: Use `./dev.sh frontend`
3. **For production builds**: Use `./dev.sh build`
4. **For Telegram testing**: Test at https://app.atlasvip.cloud

The Telegram Mini App authentication should now work automatically when the app loads within a Telegram environment!
