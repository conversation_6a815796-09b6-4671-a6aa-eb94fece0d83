"""Remove task category column from tasks table."""
from sqlalchemy import text
import logging
from models import TaskType

logger = logging.getLogger(__name__)

def check_column_exists(connection, table_name, column_name):
    """Check if a column exists in SQLite table"""
    result = connection.execute(text(f"PRAGMA table_info({table_name})")).fetchall()
    return any(row[1] == column_name for row in result)

def get_type_for_category(category: str) -> str:
    """Map old categories to appropriate task types."""
    category_to_type = {
        'telegram_follow': TaskType.TELEGRAM_CHANNEL.value,
        'social_follow': TaskType.INSTAGRAM_FOLLOW.value,  # Default to Instagram if not specified
        'content_view': TaskType.YOUTUBE_VIEW.value,
        'link_visit': TaskType.WEBSITE_VISIT.value,
        'daily_check': TaskType.DAILY_CHECKIN.value,
        'task_pack': TaskType.TASK_PACK.value,
        'referral': TaskType.REFERRAL.value
    }
    return category_to_type.get(category.lower(), TaskType.TASK_PACK.value)

def upgrade(connection):
    """Remove the category column from tasks table after migrating data."""
    try:
        # First check if the column exists
        logger.info("Checking for category column in tasks table...")
        has_category = check_column_exists(connection, 'tasks', 'category')
        
        if has_category:
            logger.info("Found category column. Starting migration...")
            
            # First, get all tasks with their current types and categories
            tasks = connection.execute(text("SELECT id, type, category FROM tasks")).fetchall()
            logger.info(f"Found {len(tasks)} tasks to process")

            # Update task types based on categories where needed
            for task_id, task_type, category in tasks:
                if category:
                    # Only update if the current type doesn't match the expected type for the category
                    new_type = get_type_for_category(category)
                    logger.info(f"Updating task {task_id}: category '{category}' -> type '{new_type}'")
                    
                    connection.execute(
                        text("UPDATE tasks SET type = :new_type WHERE id = :task_id"),
                        {"new_type": new_type, "task_id": task_id}
                    )

            # Create new table without the category column
            logger.info("Creating new table without category column...")
            connection.execute(text("""
                CREATE TABLE tasks_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    type TEXT NOT NULL,
                    target_value INTEGER NOT NULL,
                    reward_type TEXT NOT NULL,
                    reward_value FLOAT NOT NULL,
                    verification_frequency INTEGER,
                    allow_reward_revocation BOOLEAN,
                    minimum_duration INTEGER,
                    is_active BOOLEAN DEFAULT TRUE,
                    daily_multiplier FLOAT,
                    streak_requirement INTEGER,
                    auto_verify_delay INTEGER,
                    verification_type TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """))

            # Copy data to new table
            logger.info("Copying data to new table...")
            connection.execute(text("""
                INSERT INTO tasks_new 
                SELECT id, name, description, type, target_value, reward_type, 
                       reward_value, verification_frequency, allow_reward_revocation,
                       minimum_duration, is_active, daily_multiplier, streak_requirement,
                       auto_verify_delay, verification_type, created_at
                FROM tasks
            """))

            # Drop old table and rename new one
            logger.info("Replacing old table with new one...")
            connection.execute(text("DROP TABLE tasks"))
            connection.execute(text("ALTER TABLE tasks_new RENAME TO tasks"))
            
            logger.info("Successfully removed category column from tasks table")
        else:
            logger.info("Category column not found in tasks table. Skipping...")
            
    except Exception as e:
        logger.error(f"Error in migration: {str(e)}")
        raise

def downgrade(connection):
    """Add back the category column if needed."""
    try:
        logger.info("Adding back category column...")
        # Add the category column
        connection.execute(text("ALTER TABLE tasks ADD COLUMN category TEXT"))
        
        # Map task types back to categories
        type_mappings = {
            TaskType.TELEGRAM_CHANNEL.value: 'telegram_follow',
            TaskType.INSTAGRAM_FOLLOW.value: 'social_follow',
            TaskType.TWITTER_FOLLOW.value: 'social_follow',
            TaskType.YOUTUBE_VIEW.value: 'content_view',
            TaskType.WEBSITE_VISIT.value: 'link_visit',
            TaskType.DAILY_CHECKIN.value: 'daily_check',
            TaskType.TASK_PACK.value: 'task_pack',
            TaskType.REFERRAL.value: 'referral'
        }
        
        # Update categories based on types
        for task_type, category in type_mappings.items():
            connection.execute(
                text("UPDATE tasks SET category = :category WHERE type = :type"),
                {"category": category, "type": task_type}
            )
            
        logger.info("Successfully restored category column")
    except Exception as e:
        logger.error(f"Error in downgrade: {str(e)}")
        raise 