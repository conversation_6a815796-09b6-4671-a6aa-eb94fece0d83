import { QueryClient } from '@tanstack/react-query';
import { QUERY_KEYS } from '../utils/constants';
import type { User } from '../types/api';

export interface SessionConfig {
  // Session timeout in minutes
  sessionTimeout: number;
  // Warning time before expiry in minutes
  warningTime: number;
  // Auto-refresh interval in minutes
  refreshInterval: number;
  // Storage key prefix
  storagePrefix: string;
}

const DEFAULT_CONFIG: SessionConfig = {
  sessionTimeout: 15, // 15 minutes
  warningTime: 2, // 2 minutes before expiry
  refreshInterval: 10, // Refresh every 10 minutes
  storagePrefix: 'atlasvpn_session_'
};

export class SessionManager {
  private config: SessionConfig;
  private queryClient: QueryClient;
  private refreshTimer: NodeJS.Timeout | null = null;
  private warningTimer: NodeJS.Timeout | null = null;
  private expiryTimer: NodeJS.Timeout | null = null;
  private isInitialized = false;
  
  // Event callbacks
  private onSessionExpired?: () => void;
  private onSessionWarning?: (minutesLeft: number) => void;
  private onSessionRefreshed?: (user: User) => void;
  
  constructor(queryClient: QueryClient, config: Partial<SessionConfig> = {}) {
    this.queryClient = queryClient;
    this.config = { ...DEFAULT_CONFIG, ...config };
  }
  
  /**
   * Initialize the session manager
   */
  public initialize(callbacks?: {
    onSessionExpired?: () => void;
    onSessionWarning?: (minutesLeft: number) => void;
    onSessionRefreshed?: (user: User) => void;
  }) {
    if (this.isInitialized) {
      console.warn('[SessionManager] Already initialized');
      return;
    }
    
    this.onSessionExpired = callbacks?.onSessionExpired;
    this.onSessionWarning = callbacks?.onSessionWarning;
    this.onSessionRefreshed = callbacks?.onSessionRefreshed;
    
    this.isInitialized = true;
    
    // Restore session from storage if available
    this.restoreSession();
    
    // Start session monitoring
    this.startSessionMonitoring();
    
    console.log('[SessionManager] Initialized with config:', this.config);
  }
  
  /**
   * Start a new session
   */
  public startSession(user: User) {
    const now = new Date();
    const expiryTime = new Date(now.getTime() + this.config.sessionTimeout * 60 * 1000);
    
    const sessionData = {
      user,
      startTime: now.toISOString(),
      lastActivity: now.toISOString(),
      expiryTime: expiryTime.toISOString(),
      isActive: true
    };
    
    this.saveSessionData(sessionData);
    this.updateQueryCache(user);
    this.scheduleSessionEvents(expiryTime);
    
    console.log('[SessionManager] Session started for user:', user.username);
  }
  
  /**
   * Update session activity
   */
  public updateActivity() {
    const sessionData = this.getSessionData();
    if (!sessionData || !sessionData.isActive) {
      return;
    }
    
    const now = new Date();
    const newExpiryTime = new Date(now.getTime() + this.config.sessionTimeout * 60 * 1000);
    
    sessionData.lastActivity = now.toISOString();
    sessionData.expiryTime = newExpiryTime.toISOString();
    
    this.saveSessionData(sessionData);
    this.scheduleSessionEvents(newExpiryTime);
  }
  
  /**
   * End the current session
   */
  public endSession() {
    this.clearSessionData();
    this.clearTimers();
    this.clearQueryCache();
    
    console.log('[SessionManager] Session ended');
  }
  
  /**
   * Check if session is valid
   */
  public isSessionValid(): boolean {
    const sessionData = this.getSessionData();
    if (!sessionData || !sessionData.isActive) {
      return false;
    }
    
    const now = new Date();
    const expiryTime = new Date(sessionData.expiryTime);
    
    return now < expiryTime;
  }
  
  /**
   * Get current session user
   */
  public getCurrentUser(): User | null {
    const sessionData = this.getSessionData();
    return sessionData?.user || null;
  }
  
  /**
   * Get session info
   */
  public getSessionInfo() {
    const sessionData = this.getSessionData();
    if (!sessionData) {
      return null;
    }
    
    const now = new Date();
    const expiryTime = new Date(sessionData.expiryTime);
    const lastActivity = new Date(sessionData.lastActivity);
    const startTime = new Date(sessionData.startTime);
    
    return {
      user: sessionData.user,
      isActive: sessionData.isActive,
      startTime,
      lastActivity,
      expiryTime,
      timeUntilExpiry: Math.max(0, expiryTime.getTime() - now.getTime()),
      sessionDuration: now.getTime() - startTime.getTime(),
      isValid: this.isSessionValid()
    };
  }
  
  /**
   * Refresh session from server
   */
  public async refreshSession(): Promise<boolean> {
    try {
      console.log('[SessionManager] Refreshing session from server');
      
      // Invalidate and refetch current user query
      await this.queryClient.invalidateQueries({ 
        queryKey: QUERY_KEYS.CURRENT_USER 
      });
      
      const user = await this.queryClient.fetchQuery({
        queryKey: QUERY_KEYS.CURRENT_USER
      }) as User | undefined;
      
      if (user) {
        this.startSession(user);
        this.onSessionRefreshed?.(user);
        console.log('[SessionManager] Session refreshed successfully');
        return true;
      } else {
        console.log('[SessionManager] Session refresh failed - not authenticated');
        this.endSession();
        return false;
      }
    } catch (error) {
      console.error('[SessionManager] Session refresh error:', error);
      this.endSession();
      return false;
    }
  }
  
  /**
   * Destroy the session manager
   */
  public destroy() {
    this.clearTimers();
    this.isInitialized = false;
    console.log('[SessionManager] Destroyed');
  }
  
  // Private methods
  
  private getStorageKey(key: string): string {
    return `${this.config.storagePrefix}${key}`;
  }
  
  private saveSessionData(data: any) {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(
        this.getStorageKey('data'),
        JSON.stringify(data)
      );
    } catch (error) {
      console.error('[SessionManager] Failed to save session data:', error);
    }
  }
  
  private getSessionData(): any {
    if (typeof window === 'undefined') return null;
    
    try {
      const data = localStorage.getItem(this.getStorageKey('data'));
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('[SessionManager] Failed to get session data:', error);
      return null;
    }
  }
  
  private clearSessionData() {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(this.getStorageKey('data'));
      localStorage.removeItem(this.getStorageKey('lastActivity'));
      localStorage.removeItem(this.getStorageKey('expiryTime'));
    } catch (error) {
      console.error('[SessionManager] Failed to clear session data:', error);
    }
  }
  
  private updateQueryCache(user: User) {
    this.queryClient.setQueryData(QUERY_KEYS.CURRENT_USER, user);
  }
  
  private clearQueryCache() {
    this.queryClient.setQueryData(QUERY_KEYS.CURRENT_USER, undefined);
    this.queryClient.removeQueries({ queryKey: QUERY_KEYS.CURRENT_USER });
  }
  
  private restoreSession() {
    const sessionData = this.getSessionData();
    if (!sessionData || !sessionData.isActive) {
      return;
    }
    
    if (this.isSessionValid()) {
      this.updateQueryCache(sessionData.user);
      const expiryTime = new Date(sessionData.expiryTime);
      this.scheduleSessionEvents(expiryTime);
      console.log('[SessionManager] Session restored from storage');
    } else {
      console.log('[SessionManager] Stored session expired, clearing');
      this.endSession();
    }
  }
  
  private scheduleSessionEvents(expiryTime: Date) {
    this.clearTimers();
    
    const now = new Date();
    const timeUntilExpiry = expiryTime.getTime() - now.getTime();
    const timeUntilWarning = timeUntilExpiry - (this.config.warningTime * 60 * 1000);
    
    // Schedule warning
    if (timeUntilWarning > 0) {
      this.warningTimer = setTimeout(() => {
        this.onSessionWarning?.(this.config.warningTime);
      }, timeUntilWarning);
    }
    
    // Schedule expiry
    if (timeUntilExpiry > 0) {
      this.expiryTimer = setTimeout(() => {
        console.log('[SessionManager] Session expired');
        this.endSession();
        this.onSessionExpired?.();
      }, timeUntilExpiry);
    }
  }
  
  private startSessionMonitoring() {
    // Auto-refresh timer
    this.refreshTimer = setInterval(() => {
      if (this.isSessionValid()) {
        this.refreshSession();
      }
    }, this.config.refreshInterval * 60 * 1000);
  }
  
  private clearTimers() {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
    
    if (this.warningTimer) {
      clearTimeout(this.warningTimer);
      this.warningTimer = null;
    }
    
    if (this.expiryTimer) {
      clearTimeout(this.expiryTimer);
      this.expiryTimer = null;
    }
  }
}

// Singleton instance
let sessionManagerInstance: SessionManager | null = null;

export const createSessionManager = (queryClient: QueryClient, config?: Partial<SessionConfig>): SessionManager => {
  if (sessionManagerInstance) {
    sessionManagerInstance.destroy();
  }
  
  sessionManagerInstance = new SessionManager(queryClient, config);
  return sessionManagerInstance;
};

export const getSessionManager = (): SessionManager | null => {
  return sessionManagerInstance;
};