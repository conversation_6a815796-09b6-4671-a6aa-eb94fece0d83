import { defineStore } from 'pinia'
import type { Task, TaskCompletion, DailyTaskStreak } from '~/types'

export interface TaskState {
  // Tasks data
  tasks: Task[]
  completions: TaskCompletion[]
  dailyStreak: DailyTaskStreak | null
  
  // Loading states
  isLoading: boolean
  isLoadingStreak: boolean
  
  // Error states
  error: string | null
  streakError: string | null
  
  // Operation states
  completingTasks: Set<number>
  claimingTasks: Set<number>
  
  // Cache timestamps
  lastFetch: number
  lastStreakFetch: number
}

export const useTaskStore = defineStore('tasks', {
  state: (): TaskState => ({
    // Tasks data
    tasks: [],
    completions: [],
    dailyStreak: null,
    
    // Loading states
    isLoading: false,
    isLoadingStreak: false,
    
    // Error states
    error: null,
    streakError: null,
    
    // Operation states
    completingTasks: new Set(),
    claimingTasks: new Set(),
    
    // Cache timestamps
    lastFetch: 0,
    lastStreakFetch: 0
  }),

  getters: {
    // Task filtering
    availableTasks: (state) => 
      state.tasks.filter(task => task.status === 'available'),
    
    completedTasks: (state) => 
      state.tasks.filter(task => task.status === 'completed' && !task.is_claimed),
    
    claimedTasks: (state) => 
      state.tasks.filter(task => task.is_claimed),
    
    pendingTasks: (state) => 
      state.tasks.filter(task => task.status === 'pending'),
    
    // Task statistics
    totalTasksCompleted: (state) => 
      state.tasks.filter(task => task.status === 'completed' || task.is_claimed).length,
    
    totalRewardsEarned: (state) => 
      state.tasks
        .filter(task => task.is_claimed)
        .reduce((total, task) => total + (task.reward_amount || 0), 0),
    
    // Task categories
    tasksByCategory: (state) => {
      const categories: Record<string, Task[]> = {}
      state.tasks.forEach(task => {
        if (!categories[task.category]) {
          categories[task.category] = []
        }
        categories[task.category].push(task)
      })
      return categories
    },
    
    // Daily streak
    canCheckIn: (state) => {
      if (!state.dailyStreak) return false
      const lastCheckIn = state.dailyStreak.last_checkin_date
      if (!lastCheckIn) return true
      
      const today = new Date().toDateString()
      const lastCheckInDate = new Date(lastCheckIn).toDateString()
      return today !== lastCheckInDate
    },
    
    currentStreak: (state) => state.dailyStreak?.current_streak || 0,
    
    nextReward: (state) => ({
      day: state.dailyStreak?.next_reward_day || 1,
      amount: state.dailyStreak?.next_reward_amount || 0
    }),
    
    // Cache status
    isDataStale: (state) => {
      const CACHE_TTL = 60 * 1000 // 1 minute
      return Date.now() - state.lastFetch > CACHE_TTL
    },
    
    isStreakDataStale: (state) => {
      const CACHE_TTL = 5 * 60 * 1000 // 5 minutes
      return Date.now() - state.lastStreakFetch > CACHE_TTL
    }
  },

  actions: {
    // Basic setters
    setTasks(tasks: Task[]) {
      this.tasks = tasks
      this.lastFetch = Date.now()
    },

    setCompletions(completions: TaskCompletion[]) {
      this.completions = completions
    },

    setDailyStreak(streak: DailyTaskStreak) {
      this.dailyStreak = streak
      this.lastStreakFetch = Date.now()
    },

    setLoading(loading: boolean) {
      this.isLoading = loading
    },

    setStreakLoading(loading: boolean) {
      this.isLoadingStreak = loading
    },

    setError(error: string | null) {
      this.error = error
    },

    setStreakError(error: string | null) {
      this.streakError = error
    },

    // Task operations
    updateTaskStatus(taskId: number, status: Task['status']) {
      const taskIndex = this.tasks.findIndex(task => task.id === taskId)
      if (taskIndex !== -1) {
        this.tasks[taskIndex] = {
          ...this.tasks[taskIndex],
          status,
          completed_at: status === 'completed' ? new Date().toISOString() : this.tasks[taskIndex].completed_at
        }
      }
    },

    markTaskClaimed(taskId: number) {
      const taskIndex = this.tasks.findIndex(task => task.id === taskId)
      if (taskIndex !== -1) {
        this.tasks[taskIndex] = {
          ...this.tasks[taskIndex],
          is_claimed: true,
          claimed_at: new Date().toISOString()
        }
      }
    },

    addCompletion(completion: TaskCompletion) {
      this.completions.push(completion)
    },

    updateCompletion(completionId: number, updates: Partial<TaskCompletion>) {
      const completionIndex = this.completions.findIndex(c => c.task_id === completionId)
      if (completionIndex !== -1) {
        this.completions[completionIndex] = {
          ...this.completions[completionIndex],
          ...updates
        }
      }
    },

    // Operation tracking
    setTaskCompleting(taskId: number, completing: boolean) {
      if (completing) {
        this.completingTasks.add(taskId)
      } else {
        this.completingTasks.delete(taskId)
      }
    },

    setTaskClaiming(taskId: number, claiming: boolean) {
      if (claiming) {
        this.claimingTasks.add(taskId)
      } else {
        this.claimingTasks.delete(taskId)
      }
    },

    // Utility actions
    getTaskById(taskId: number): Task | undefined {
      return this.tasks.find(task => task.id === taskId)
    },

    isTaskCompleting(taskId: number): boolean {
      return this.completingTasks.has(taskId)
    },

    isTaskClaiming(taskId: number): boolean {
      return this.claimingTasks.has(taskId)
    },

    // Sort tasks by priority
    sortTasksByPriority() {
      this.tasks.sort((a, b) => {
        const statusPriority: Record<string, number> = {
          pending: 0,
          completed: a.is_claimed ? 3 : 1,
          available: 2,
          failed: 4,
        }
        return statusPriority[a.status] - statusPriority[b.status]
      })
    },

    // Clear all data
    clearTasks() {
      this.tasks = []
      this.completions = []
      this.dailyStreak = null
      this.error = null
      this.streakError = null
      this.lastFetch = 0
      this.lastStreakFetch = 0
    },

    // Reset operation states
    resetOperationStates() {
      this.completingTasks.clear()
      this.claimingTasks.clear()
    }
  }
})
