---
globs: docker-compose.yml,nginx/**/*.conf,**/Dockerfile
description: Docker and Nginx configuration rules for containerized development
---

# Docker & Nginx Configuration Rules

## 🐳 Docker Compose Rules

1. **Service Dependencies**: Ensure proper `depends_on` and health checks
2. **Environment Variables**: Use `.env` files, never hardcode secrets
3. **Volume Mounts**: Mount config files from codebase, not copy to containers
4. **Port Mapping**: Internal services communicate via service names
5. **Health Checks**: All services must have proper health endpoints

## 🌐 Nginx Configuration Rules

### Telegram Mini App Requirements

1. **NO X-Frame-Options**: Remove for iframe embedding
2. **CSP Frame Ancestors**: Set `frame-ancestors https://*.telegram.org https://web.telegram.org`
3. **CORS Headers**: Allow Telegram domains for API requests

### Development Proxying

```nginx
# ✅ Required for dev file serving
location ~* ^/(src/|node_modules/) {
    proxy_pass http://frontend:3005;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

### WebSocket Support

```nginx
# ✅ For any WebSocket connections
proxy_http_version 1.1;
proxy_set_header Upgrade $http_upgrade;
proxy_set_header Connection "upgrade";
```

## 🔧 Dockerfile Best Practices

1. **Multi-stage builds** for production optimization
2. **Non-root users** for security
3. **Layer caching** with proper COPY order
4. **Health checks** in Dockerfile when needed
5. **Development vs Production** variants

## 🚨 Common Issues Prevention

1. **Redis Host**: Use `redis:6379`, not `localhost:6379`
2. **API Base URL**: Use service names internally, domain externally
3. **File Permissions**: Handle volume mount permissions properly
4. **SSL Certificates**: Mount certificates, don't copy into images
description:
globs:
alwaysApply: false
---
