import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import LoadingSpinner from './LoadingSpinner';
import { formatCurrency } from '../utils/format';
import {
  <PERSON>a<PERSON>oi<PERSON>,
  FaArrowUp,
  FaSpinner,
  FaBolt,
  FaChessRook,
  FaCheckCircle,
  FaPlus,
  FaArrowRight,
  FaFilter,
} from 'react-icons/fa';
import { UserCard, UnifiedCardResponse, CardCatalog as GameCardCatalog } from '../types/cardTypes';
import CardDetailSlide from './CardDetailSlide';
import { isLowEndDevice } from '../utils/performance';
import Section from './layout/Section';
import CornerGlowCard from './CornerGlowCard';

// --- Rarity Definitions ---

// Base colors used for mapping
const rarityColorBases = {
  common: { border: 'gray-400', shadow: 'gray-300', glow: '#9ca3af' },
  rare: { border: 'blue-400', shadow: 'blue-300', glow: '#60a5fa' },
  epic: { border: 'purple-500', shadow: 'purple-400', glow: '#a855f7' },
  legendary: { border: 'yellow-400', shadow: 'yellow-300', glow: '#facc15' },
  mythic: { border: 'pink-400', shadow: 'pink-300', glow: '#f472b6' },
} as const;

type RarityKey = keyof typeof rarityColorBases;

// Generate border classes with /20 opacity
const rarityBorderMap: Record<RarityKey, string> = Object.entries(rarityColorBases).reduce(
  (acc, [key, value]) => {
    acc[key as RarityKey] = `border-${value.border}/20`;
    return acc;
  },
  {} as Record<RarityKey, string>
);

// Generate shadow classes with /15 opacity
const rarityShadowMap: Record<RarityKey, string> = Object.entries(rarityColorBases).reduce(
  (acc, [key, value]) => {
    acc[key as RarityKey] = `shadow-${value.shadow}/15`;
    return acc;
  },
  {} as Record<RarityKey, string>
);

// Glow colors defined in base map

// Rarity text color map
const rarityTextColors = {
  common: 'text-gray-300',
  rare: 'text-blue-300',
  epic: 'text-purple-300',
  legendary: 'text-yellow-300',
  mythic: 'text-pink-300',
} as const;

// --- Prop Interfaces ---
interface CardTabProps {
  // State
  unifiedCards: UnifiedCardResponse[]; // New prop for unified card data
  loading: boolean;
  processingCardId: number | null;
  isClaimingAll: boolean;
  totalAccumulatedProfit: number;
  totalHourlyProfit: number;

  // Actions
  onClaimProfit: (userCardId: number) => void;
  onClaimAllProfits?: () => Promise<void>; // Make optional
  onUpgradeCard: (userCardId: number) => void;
  onPurchaseCard: (cardId: number) => void;
  onCardSelect: (card: UnifiedCardResponse) => void; // Changed to UnifiedCardResponse
}

// --- Main Component ---
const CardTab: React.FC<CardTabProps> = ({
  unifiedCards, // Use unifiedCards instead of userCards, cardCatalog
  loading,
  processingCardId,
  isClaimingAll,
  totalAccumulatedProfit,
  totalHourlyProfit,
  onClaimProfit,
  onClaimAllProfits,
  onUpgradeCard,
  onPurchaseCard,
  onCardSelect,
}) => {
  // All hooks must be called before any return
  const [activeInnerSection, setActiveInnerSection] = useState<'my-cards' | 'available-cards'>(
    'my-cards'
  );
  const userCards = useMemo(
    () =>
      (unifiedCards ?? [])
        .filter(card => card.is_owned && card.user_card)
        .map(card => {
          // Create UserCard structure from UnifiedCardResponse for compatibility
          const catalogInfo: GameCardCatalog = {
            id: card.id,
            name: card.name,
            description: card.description,
            image_url: card.image_url,
            rarity: card.rarity || 'common',
            price: card.price,
            profit_rate: card.profit_rate,
            max_level: card.max_level,
            level_profits: card.level_profits,
            is_owned: true,
          };
          if (!card.user_card) return null;
          return {
            id: card.user_card.id,
            user_id: 0,
            card_id: card.id,
            level: card.user_card.level,
            purchase_date: card.user_card.purchase_date,
            last_profit_claim: card.user_card.last_profit_claim,
            total_claimed_profit: card.user_card.total_claimed_profit,
            accumulated_profit: card.user_card.accumulated_profit,
            current_hourly_profit: card.user_card.current_hourly_profit,
            next_upgrade_cost: card.user_card.next_upgrade_cost,
            card: catalogInfo,
          } as UserCard;
        })
        .filter(Boolean) as UserCard[],
    [unifiedCards]
  );

  const availableCards = useMemo(
    () =>
      (unifiedCards ?? [])
        .filter(card => !card.is_owned)
        .map(
          card =>
            ({
              id: card.id,
              name: card.name,
              description: card.description,
              image_url: card.image_url,
              rarity: card.rarity || 'common',
              price: card.price,
              profit_rate: card.profit_rate,
              max_level: card.max_level,
              level_profits: card.level_profits,
              is_owned: false,
            }) as GameCardCatalog
        ),
    [unifiedCards]
  );

  const allCardsOwned = useMemo(() => {
    return (
      (unifiedCards ?? []).length > 0 &&
      (unifiedCards ?? []).filter(card => !card.is_owned).length === 0
    );
  }, [unifiedCards]);

  const innerTabOptions = useMemo(
    () => [
      { id: 'my-cards', label: `My Deck (${userCards?.length ?? 0})`, icon: FaChessRook },
      { id: 'available-cards', label: `Market (${availableCards?.length ?? 0})`, icon: FaPlus },
    ],
    [userCards?.length, availableCards?.length]
  );

  const handleInnerSectionChange = useCallback((id: string) => {
    setActiveInnerSection(id as 'my-cards' | 'available-cards');
  }, []);

  function parseCatalogArrays(catalog: GameCardCatalog) {
    return {
      ...catalog,
      level_profits: Array.isArray(catalog.level_profits)
        ? catalog.level_profits
        : JSON.parse((catalog as any).level_profits_json || '[]'),
      level_costs: Array.isArray((catalog as any).level_costs)
        ? (catalog as any).level_costs
        : JSON.parse((catalog as any).level_costs_json || '[]'),
    };
  }

  const catalogMap = useMemo(() => {
    const map = new Map();
    (unifiedCards ?? []).forEach(card => {
      if (card) map.set(card.id, parseCatalogArrays(card));
    });
    return map;
  }, [unifiedCards]);

  // Only after all hooks, do your conditional returns
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <LoadingSpinner size="lg" />
        <p className="mt-4 text-white/70 text-sm font-display">Syncing with network...</p>
      </div>
    );
  }

  return (
    <div>
      {/* Remove ClaimAllBanner from here */}
      {/* 
      {activeInnerSection === 'my-cards' && (
         <ClaimAllBanner
            totalAccumulatedProfit={totalAccumulatedProfit}
            totalHourlyProfit={totalHourlyProfit}
            onClaimAll={onClaimAllProfits} // Use the passed-in handler
            isClaiming={isClaimingAll} // Use the passed-in state
          />
      )}
      */}

      {/* Sticky Header Section using pill style - Stick below the EarnTab header */}
      <div className="sticky top-[38px] z-10 mb-4 flex justify-center py-2 bg-gradient-to-b from-[#0a0018] via-[#0a0018]/95 to-[#0a0018]/80 backdrop-blur-sm -mx-4 border-b border-purple-500/10 shadow-sm">
        <div className="flex bg-black/30 rounded-full p-0.5 border border-white/5 w-full max-w-xs sm:max-w-sm">
          {innerTabOptions.map(option => (
            <button
              key={option.id}
              onClick={() => handleInnerSectionChange(option.id)}
              className={`flex-1 py-1.5 px-3 rounded-full text-xs sm:text-sm font-medium transition-all flex items-center justify-center gap-1.5 ${
                activeInnerSection === option.id
                  ? 'bg-white/10 text-white shadow-inner' // Subtle inset active style
                  : 'text-white/60 hover:text-white hover:bg-white/5'
              }`}
            >
              {option.icon && (
                <option.icon
                  className={`w-3.5 h-3.5 ${activeInnerSection === option.id ? 'text-purple-300' : 'text-white/50'}`}
                />
              )}
              <span>{option.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content Area */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeInnerSection}
          initial={{ opacity: 0, x: activeInnerSection === 'my-cards' ? -20 : 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: activeInnerSection === 'my-cards' ? 20 : -20 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
        >
          {/* My Cards Section - Removed ClaimAllBanner */}
          {activeInnerSection === 'my-cards' && (
            <Section title={userCards && userCards.length > 0 ? undefined : undefined}>
              {/* Use optional chaining and nullish coalescing for safety */}
              {(userCards?.length ?? 0) === 0 ? (
                <div className="text-center py-6 bg-black/30 rounded-xl border border-purple-500/10">
                  <div className="text-4xl mb-3 opacity-50">🃏</div>
                  <h3 className="text-lg font-medium text-white/80 mb-2 font-display">
                    Your Deck is Empty
                  </h3>
                  <p className="text-white/60 mb-4 px-4 max-w-md mx-auto text-sm font-display">
                    Acquire cards from the Market to start earning passive income.
                  </p>
                  <button
                    onClick={() => setActiveInnerSection('available-cards')}
                    className="px-4 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors font-display text-sm flex items-center gap-2 mx-auto"
                  >
                    Go to Market <FaArrowRight className="text-xs" />
                  </button>
                </div>
              ) : (
                <motion.div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {/* Map safely */}
                  {userCards?.map((userCard: UserCard) => {
                    const unifiedCard = (unifiedCards ?? []).find(
                      uc => uc.is_owned && uc.user_card && uc.user_card.id === userCard.id
                    );
                    if (!unifiedCard) return null;
                    return (
                      <motion.div
                        key={userCard.id}
                        onClick={() => {
                          console.log('[CardTab] Card clicked:', unifiedCard);
                          onCardSelect(unifiedCard);
                        }}
                        className="cursor-pointer h-full group relative"
                        whileHover={{ y: -3 }}
                        transition={{ type: 'spring', stiffness: 300 }}
                      >
                        <UserCardItem
                          userCard={userCard}
                          processingCardId={processingCardId}
                          nextLevelProfitIncrease={unifiedCard.next_level_profit_increase ?? null}
                        />
                      </motion.div>
                    );
                  })}
                </motion.div>
              )}
            </Section>
          )}

          {/* Available Cards Section */}
          {activeInnerSection === 'available-cards' && (
            <Section title={!allCardsOwned ? undefined : undefined}>
              {allCardsOwned ? (
                <div className="text-center py-6 bg-black/30 rounded-xl border border-green-500/20">
                  <div className="text-3xl mb-2">🏆</div>
                  <h3 className="text-lg font-medium text-white mb-1 font-display">
                    Market Cleared!
                  </h3>
                  <p className="text-white/60 px-4 text-sm max-w-md mx-auto font-display">
                    You own all available cards. Excellent work!
                  </p>
                </div>
              ) : (
                <motion.div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                  {/* Map safely */}
                  {availableCards?.map(
                    (card: GameCardCatalog) =>
                      card && ( // Add check for card existence
                        <motion.div
                          key={card.id}
                          className="h-full group relative"
                          whileHover={{ y: -3 }}
                          transition={{ type: 'spring', stiffness: 300 }}
                        >
                          <CatalogCardItem
                            card={card}
                            onPurchase={onPurchaseCard}
                            processingCardId={processingCardId}
                            owned={card.is_owned ?? false}
                          />
                        </motion.div>
                      )
                  )}
                </motion.div>
              )}
            </Section>
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  );
};

// --- User Card Item Component (Refactored) ---
interface UserCardItemProps {
  userCard: UserCard;
  processingCardId: number | null;
  nextLevelProfitIncrease: number | null;
}

const UserCardItem: React.FC<UserCardItemProps> = React.memo(
  ({ userCard, processingCardId, nextLevelProfitIncrease }) => {
    // Use optional chaining for safety
    const cardInfo = userCard?.card as GameCardCatalog | undefined;
    const rarity: RarityKey = (cardInfo?.rarity as RarityKey) || 'common';
    const hourlyProfit = userCard?.current_hourly_profit ?? 0;
    const canUpgrade = !!(
      userCard &&
      cardInfo &&
      userCard.next_upgrade_cost !== null &&
      userCard.level < cardInfo.max_level
    );

    // Use the prop directly instead of calculating it
    const profitIncrease = nextLevelProfitIncrease ?? 0;

    // Check if cardInfo or userCard is missing
    if (!cardInfo || !userCard) {
      console.warn('Rendering error: Missing cardInfo or userCard in UserCardItem', { userCard });
      return (
        <CornerGlowCard
          borderColorClass="border-red-500/50"
          shadowColorClass="shadow-red-500/30"
          glowColor="#f87171"
        >
          <div className="p-2 text-red-300 text-xs">Data Error</div>
        </CornerGlowCard>
      );
    }

    const borderColor = rarityBorderMap[rarity] || rarityBorderMap['common'];
    const shadowColor = rarityShadowMap[rarity] || rarityShadowMap['common'];
    const glowColor = rarityColorBases[rarity]?.glow || rarityColorBases['common'].glow;

    return (
      <CornerGlowCard
        borderColorClass={borderColor}
        shadowColorClass={shadowColor}
        glowColor={glowColor}
        glowPosition="bottom-left"
        className="group hover:scale-[1.02] transition-transform duration-200 ease-in-out"
      >
        {/* Image Banner - Increased height */}
        <div
          className="h-28 sm:h-32 bg-cover bg-center relative flex-shrink-0 group-hover:brightness-110 transition-all duration-300 ease-in-out z-10 rounded-t-lg -m-3 mb-0" // Height Increased
          style={{ backgroundImage: `url(${cardInfo?.image_url || '/img/card-placeholder.jpg'})` }} // Use optional chaining
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent rounded-t-lg" />
          {/* Level Badge - Removed border */}
          <div className="absolute top-1.5 left-1.5 bg-black/60 backdrop-blur-sm px-1.5 py-0.5 rounded text-[10px] flex items-center gap-0.5 shadow-md">
            <span className="text-yellow-400 font-bold font-display">
              Lv {userCard?.level ?? '?'}
            </span>{' '}
            {/* Use optional chaining */}
          </div>
          {/* Rarity Badge - Removed border */}
          <div
            className={`absolute top-1.5 right-1.5 bg-black/60 backdrop-blur-sm px-1.5 py-0.5 rounded text-[10px] font-bold shadow-md ${rarityTextColors[rarity]}`}
          >
            {rarity.toUpperCase()}
          </div>
        </div>

        {/* Content Area - Adjusted stats layout */}
        <div className="flex-grow flex flex-col gap-1.5 z-10 pt-2">
          <h3 className="text-sm font-semibold text-white truncate font-display leading-tight">
            {cardInfo?.name ?? 'Unknown Card'}
          </h3>{' '}
          {/* Use optional chaining */}
          {/* Stats Layout - Changed to flex column */}
          <div className="flex flex-col gap-1.5 text-[10px] sm:text-[11px]">
            {/* Hourly Profit Stat */}
            <div className="bg-black/30 p-1.5 rounded border border-purple-500/30 flex items-center gap-1.5 overflow-hidden">
              <FaBolt className="text-yellow-400 text-lg flex-shrink-0" />{' '}
              {/* Slightly larger icon? */}
              <div className="flex flex-col min-w-0">
                <span className="text-purple-300/80 font-medium leading-tight mb-0.5">
                  Profit/hr
                </span>
                <span className="text-white font-semibold leading-tight truncate text-xs">
                  {formatCurrency(hourlyProfit)}
                </span>
              </div>
            </div>

            {/* Upgrade Info / Max Level Stat */}
            <div
              className={`p-1.5 rounded border ${canUpgrade ? 'bg-black/30 border-green-500/30' : 'bg-black/30 border-gray-500/30'} flex items-center gap-1.5 overflow-hidden`}
            >
              {canUpgrade && profitIncrease > 0 ? (
                <>
                  <FaArrowUp className="text-green-400 text-lg flex-shrink-0" />
                  <div className="flex flex-col min-w-0">
                    <span className="text-green-300/80 font-medium leading-tight mb-0.5">
                      Next Lvl Profit
                    </span>
                    <span className="text-white font-semibold leading-tight truncate text-xs">
                      +{formatCurrency(profitIncrease)}/hr
                    </span>
                  </div>
                </>
              ) : (
                <>
                  <FaCheckCircle className="text-gray-400 text-lg flex-shrink-0" />
                  <div className="flex flex-col min-w-0">
                    <span className="text-gray-300/80 font-medium leading-tight mb-0.5">Level</span>
                    <span className="text-white/80 font-semibold leading-tight text-xs">
                      Max Reached
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>
          <div className="mt-auto pt-1"></div>
        </div>
      </CornerGlowCard>
    );
  }
);

// --- Catalog Card Item Component (Refactored) ---
interface CatalogCardItemProps {
  card: GameCardCatalog;
  onPurchase: (cardId: number) => void;
  processingCardId: number | null;
  owned: boolean;
}

const CatalogCardItem: React.FC<CatalogCardItemProps> = React.memo(
  ({ card, onPurchase, processingCardId, owned }) => {
    const isProcessing = processingCardId === card.id;
    const rarity: RarityKey = (card.rarity as RarityKey) || 'common';
    const baseHourlyProfit = card.profit_rate;

    const handlePurchaseClick = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      if (!isProcessing && !owned && !card.is_owned) {
        onPurchase(card.id);
      }
    };

    const borderColor = rarityBorderMap[rarity] || rarityBorderMap['common'];
    const shadowColor = rarityShadowMap[rarity] || rarityShadowMap['common'];
    const glowColor = rarityColorBases[rarity]?.glow || rarityColorBases['common'].glow;

    return (
      <CornerGlowCard
        borderColorClass={borderColor}
        shadowColorClass={shadowColor}
        glowColor={glowColor}
        glowPosition="top-right"
        className={`group hover:scale-[1.02] transition-transform duration-200 ease-in-out ${owned ? 'opacity-50 grayscale cursor-not-allowed' : 'cursor-pointer'}`}
      >
        {/* Image Banner - Increased height */}
        <div
          className="h-28 sm:h-32 bg-cover bg-center relative flex-shrink-0 group-hover:brightness-110 transition-all duration-300 ease-in-out z-10 rounded-t-lg -m-3 mb-0" // Height Increased
          style={{ backgroundImage: `url(${card.image_url || '/img/card-placeholder.jpg'})` }}
        >
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent rounded-t-lg" />
          {/* Price Badge - Removed border */}
          <div className="absolute top-1.5 left-1.5 bg-gradient-to-r from-amber-500 to-yellow-500 text-white text-[10px] font-bold px-1.5 py-0.5 rounded flex items-center gap-0.5 shadow-md">
            <FaCoins className="text-yellow-200 text-[10px]" />
            <span className="font-display">{formatCurrency(card.price)}</span>
          </div>
          {/* Rarity Badge - Removed border */}
          <div
            className={`absolute top-1.5 right-1.5 bg-black/60 backdrop-blur-sm px-1.5 py-0.5 rounded text-[10px] font-bold shadow-md ${rarityTextColors[rarity]}`}
          >
            {rarity.toUpperCase()}
          </div>
        </div>

        {/* Content Area - Adjusted stats layout */}
        <div className="flex-grow flex flex-col gap-1.5 z-10 pt-2">
          <h3 className="text-sm font-semibold text-white truncate font-display leading-tight">
            {card.name}
          </h3>

          {/* Stats Layout - Changed to flex column */}
          <div className="flex flex-col gap-1.5 text-[10px] sm:text-[11px]">
            {/* Base Hourly Profit Stat */}
            <div className="bg-black/30 p-1.5 rounded border border-purple-500/30 flex items-center gap-1.5 overflow-hidden">
              <FaBolt className="text-yellow-400 text-lg flex-shrink-0" />
              <div className="flex flex-col min-w-0">
                <span className="text-purple-300/80 font-medium leading-tight mb-0.5">
                  Base Profit/hr
                </span>
                <span className="text-white font-semibold leading-tight truncate text-xs">
                  {formatCurrency(baseHourlyProfit)}
                </span>
              </div>
            </div>
            {/* Max Level */}
            <div className="bg-black/30 p-1.5 rounded border border-gray-500/30 flex items-center gap-1.5 overflow-hidden">
              <FaCheckCircle className="text-gray-400 text-lg flex-shrink-0" />
              <div className="flex flex-col min-w-0">
                <span className="text-gray-300/80 font-medium leading-tight mb-0.5">Max Level</span>
                <span className="text-white/80 font-semibold leading-tight text-xs">
                  {card.max_level}
                </span>
              </div>
            </div>
          </div>

          {/* Purchase Button */}
          <div className="mt-auto pt-1.5">
            <button
              onClick={handlePurchaseClick}
              disabled={isProcessing || owned}
              className={`w-full py-2 rounded-lg text-xs sm:text-sm font-semibold flex items-center justify-center gap-1 transition-all duration-200 font-display shadow-md group-hover:shadow-lg
              ${
                isProcessing
                  ? 'bg-gray-600 text-white/70 cursor-wait'
                  : owned
                    ? 'bg-green-700/50 text-green-300/70 cursor-not-allowed'
                    : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-purple-500/20 group-hover:shadow-purple-500/30 active:scale-[0.98]'
              }`}
            >
              {isProcessing ? (
                <FaSpinner className="animate-spin text-xs sm:text-sm" />
              ) : owned ? (
                'Owned'
              ) : (
                <>
                  {' '}
                  <FaCoins className="-ml-1 text-xs sm:text-sm" /> Purchase{' '}
                </>
              )}
            </button>
          </div>
        </div>
      </CornerGlowCard>
    );
  }
);

UserCardItem.displayName = 'UserCardItem';
CatalogCardItem.displayName = 'CatalogCardItem';

export default CardTab;
