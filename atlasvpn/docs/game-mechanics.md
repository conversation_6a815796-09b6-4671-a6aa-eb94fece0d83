# Game Mechanics and Features Overview

## Overview

This document outlines the game mechanics and interactive features of the VIPVerse Telegram mini app, detailing how they will be migrated and optimized in the Astro framework while maintaining full functionality.

## Current Game Architecture

### Core Game Systems

**1. Card Collection System**
- **User Cards**: Collectible cards that generate passive income
- **Card Catalog**: Available cards for purchase
- **Upgrade System**: Card level progression with increasing costs
- **Profit Generation**: Hourly passive income based on card levels

**2. Task System**
- **Daily Tasks**: Check-in rewards and daily challenges
- **Social Tasks**: Telegram channel joins, YouTube views, social media follows
- **Referral Tasks**: Friend invitation rewards
- **Verification System**: Automated and manual task verification

**3. Referral Program**
- **Referral Codes**: Unique codes for each user
- **Multi-level Rewards**: Earnings from referred users
- **Referral Statistics**: Tracking and analytics
- **Bonus Systems**: Special rewards for active referrers

**4. VPN Service Integration**
- **Subscription Management**: Active VPN subscriptions
- **Package Selection**: Available VPN packages
- **Payment Integration**: Subscription purchasing
- **Service Activation**: VPN service provisioning

**5. 3D Interactive World (Verse)**
- **Land Plots**: Virtual land ownership system
- **3D Visualization**: Three.js-powered interactive globe
- **Real-time Interactions**: Live user interactions
- **Chat System**: Integrated messaging

## Astro Migration Strategy for Game Features

### 1. Card System Migration

**Server-Side Rendering for Initial Data:**
```astro
---
// src/pages/dashboard/earn.astro
import { api } from '../../utils/apiClient';
import EarnTab from '../../components/react/EarnTab';

// Fetch initial card data on server
let initialData = null;
try {
  const [cardsResponse, catalogResponse] = await Promise.all([
    fetch(`${process.env.API_URL}/api/cards/unified`, {
      headers: { 'Cookie': Astro.request.headers.get('cookie') || '' },
      credentials: 'include'
    }),
    fetch(`${process.env.API_URL}/api/cards/catalog`, {
      headers: { 'Cookie': Astro.request.headers.get('cookie') || '' },
      credentials: 'include'
    })
  ]);
  
  if (cardsResponse.ok && catalogResponse.ok) {
    initialData = {
      userCards: await cardsResponse.json(),
      catalog: await catalogResponse.json()
    };
  }
} catch (error) {
  console.error('Failed to fetch card data:', error);
}
---

<EarnTab 
  client:load 
  initialUserCards={initialData?.userCards}
  initialCatalog={initialData?.catalog}
/>
```

**React Island for Interactive Card Management:**
```typescript
// src/components/react/CardSystem.tsx
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';

interface Props {
  initialUserCards?: UserCard[];
  initialCatalog?: CardCatalog[];
}

export default function CardSystem({ initialUserCards, initialCatalog }: Props) {
  const queryClient = useQueryClient();
  
  const { data: userCards } = useQuery({
    queryKey: ['userCards'],
    queryFn: () => api.get('/api/cards/unified').then(res => res.data),
    initialData: initialUserCards,
    refetchInterval: 30000, // Refresh every 30 seconds for profit updates
  });
  
  const { data: catalog } = useQuery({
    queryKey: ['cardCatalog'],
    queryFn: () => api.get('/api/cards/catalog').then(res => res.data),
    initialData: initialCatalog,
    staleTime: 5 * 60 * 1000, // Catalog changes less frequently
  });
  
  const upgradeCardMutation = useMutation({
    mutationFn: (cardId: number) => api.post(`/api/cards/${cardId}/upgrade`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userCards'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
    },
  });
  
  const buyCardMutation = useMutation({
    mutationFn: (cardId: number) => api.post(`/api/cards/${cardId}/buy`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['userCards'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
    },
  });
  
  return (
    <div className="space-y-6">
      {/* User Cards Section */}
      <section>
        <h2 className="text-xl font-bold text-white mb-4">Your Cards</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <AnimatePresence>
            {userCards?.map((card) => (
              <motion.div
                key={card.id}
                layout
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="bg-gradient-to-br from-purple-900/50 to-blue-900/50 rounded-xl p-4 border border-purple-500/20"
              >
                <div className="text-center">
                  <div className="text-2xl mb-2">{card.emoji}</div>
                  <h3 className="font-semibold text-white">{card.name}</h3>
                  <p className="text-sm text-purple-300">Level {card.level}</p>
                  <p className="text-xs text-green-400">
                    +{formatCurrency(card.hourly_profit)}/h
                  </p>
                  
                  <button
                    onClick={() => upgradeCardMutation.mutate(card.id)}
                    disabled={upgradeCardMutation.isPending}
                    className="mt-3 w-full py-2 bg-purple-600 hover:bg-purple-700 text-white text-sm rounded-lg transition-colors disabled:opacity-50"
                  >
                    {upgradeCardMutation.isPending ? 'Upgrading...' : `Upgrade (${formatCurrency(card.upgrade_cost)})`}
                  </button>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </section>
      
      {/* Card Catalog Section */}
      <section>
        <h2 className="text-xl font-bold text-white mb-4">Available Cards</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {catalog?.map((card) => (
            <div
              key={card.id}
              className="bg-gradient-to-br from-gray-900/50 to-gray-800/50 rounded-xl p-4 border border-gray-600/20"
            >
              <div className="text-center">
                <div className="text-2xl mb-2">{card.emoji}</div>
                <h3 className="font-semibold text-white">{card.name}</h3>
                <p className="text-sm text-gray-300">{card.description}</p>
                <p className="text-xs text-green-400">
                  +{formatCurrency(card.base_profit)}/h
                </p>
                
                <button
                  onClick={() => buyCardMutation.mutate(card.id)}
                  disabled={buyCardMutation.isPending}
                  className="mt-3 w-full py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-lg transition-colors disabled:opacity-50"
                >
                  {buyCardMutation.isPending ? 'Buying...' : `Buy (${formatCurrency(card.base_cost)})`}
                </button>
              </div>
            </div>
          ))}
        </div>
      </section>
    </div>
  );
}
```

### 2. Task System Migration

**Task Management Component:**
```typescript
// src/components/react/TaskSystem.tsx
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';

export default function TaskSystem({ initialTasks }: { initialTasks?: Task[] }) {
  const queryClient = useQueryClient();
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  
  const { data: tasks } = useQuery({
    queryKey: ['tasks'],
    queryFn: () => api.get('/api/tasks/available').then(res => res.data),
    initialData: initialTasks,
    refetchInterval: 60000, // Refresh every minute
  });
  
  const startTaskMutation = useMutation({
    mutationFn: (taskId: number) => api.post(`/api/tasks/${taskId}/start`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });
  
  const verifyTaskMutation = useMutation({
    mutationFn: (taskId: number) => api.post(`/api/tasks/${taskId}/verify`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
    },
  });
  
  const claimRewardMutation = useMutation({
    mutationFn: (taskId: number) => api.post(`/api/tasks/${taskId}/claim`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
    },
  });
  
  const getTaskStatusColor = (status: TaskStatus) => {
    switch (status) {
      case 'available': return 'text-blue-400';
      case 'in_progress': return 'text-yellow-400';
      case 'completed': return 'text-green-400';
      case 'claimed': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };
  
  const getTaskAction = (task: Task) => {
    switch (task.status) {
      case 'available':
        return (
          <button
            onClick={() => startTaskMutation.mutate(task.id)}
            disabled={startTaskMutation.isPending}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm"
          >
            Start Task
          </button>
        );
      case 'in_progress':
        return (
          <button
            onClick={() => verifyTaskMutation.mutate(task.id)}
            disabled={verifyTaskMutation.isPending}
            className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg text-sm"
          >
            Verify
          </button>
        );
      case 'completed':
        return (
          <button
            onClick={() => claimRewardMutation.mutate(task.id)}
            disabled={claimRewardMutation.isPending}
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm"
          >
            Claim Reward
          </button>
        );
      case 'claimed':
        return (
          <span className="px-4 py-2 bg-gray-600 text-gray-300 rounded-lg text-sm">
            Completed
          </span>
        );
    }
  };
  
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-bold text-white">Available Tasks</h2>
      
      {tasks?.map((task) => (
        <div
          key={task.id}
          className="bg-gradient-to-r from-gray-900/50 to-gray-800/50 rounded-xl p-4 border border-gray-600/20"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-3">
                <span className="text-2xl">{task.icon}</span>
                <div>
                  <h3 className="font-semibold text-white">{task.title}</h3>
                  <p className="text-sm text-gray-300">{task.description}</p>
                  <div className="flex items-center gap-4 mt-2">
                    <span className="text-sm text-green-400">
                      Reward: {formatCurrency(task.reward)}
                    </span>
                    <span className={`text-sm ${getTaskStatusColor(task.status)}`}>
                      {task.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="ml-4">
              {getTaskAction(task)}
            </div>
          </div>
          
          {task.type === 'social' && task.url && (
            <div className="mt-3 pt-3 border-t border-gray-600/20">
              <a
                href={task.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-blue-400 hover:text-blue-300"
              >
                Open Link →
              </a>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
```

### 3. 3D Verse System Migration

**Verse Layout (Astro):**
```astro
---
// src/pages/verse/index.astro
import BaseLayout from '../../layouts/BaseLayout.astro';
import VerseWorld from '../../components/react/VerseWorld';

// Fetch initial verse data
let initialData = null;
try {
  const response = await fetch(`${process.env.API_URL}/api/verse/lands`, {
    headers: { 'Cookie': Astro.request.headers.get('cookie') || '' },
    credentials: 'include'
  });
  
  if (response.ok) {
    initialData = await response.json();
  }
} catch (error) {
  console.error('Failed to fetch verse data:', error);
}
---

<BaseLayout title="VIPVerse - 3D World" requireAuth={true}>
  <VerseWorld 
    client:load 
    initialLands={initialData?.lands}
    initialUser={Astro.locals.user}
  />
</BaseLayout>
```

**3D World Component (React Island):**
```typescript
// src/components/react/VerseWorld.tsx
import { Suspense, lazy } from 'react';
import { Canvas } from '@react-three/fiber';
import { ErrorBoundary } from 'react-error-boundary';

// Lazy load heavy 3D components
const VerseScene = lazy(() => import('./VerseScene'));
const ChatSystem = lazy(() => import('./ChatSystem'));

interface Props {
  initialLands?: LandPlot[];
  initialUser?: User;
}

export default function VerseWorld({ initialLands, initialUser }: Props) {
  return (
    <div className="h-screen w-full relative">
      <ErrorBoundary
        fallback={<div className="flex items-center justify-center h-full text-white">3D World failed to load</div>}
      >
        <Suspense fallback={<div className="flex items-center justify-center h-full text-white">Loading 3D World...</div>}>
          <Canvas
            camera={{ position: [0, 0, 50], fov: 50 }}
            gl={{ antialias: true, alpha: true }}
            className="w-full h-full"
          >
            <VerseScene 
              lands={initialLands} 
              user={initialUser}
            />
          </Canvas>
          
          <ChatSystem 
            user={initialUser}
            className="absolute bottom-4 left-4 right-4"
          />
        </Suspense>
      </ErrorBoundary>
    </div>
  );
}
```

### 4. Real-Time Features

**WebSocket Integration:**
```typescript
// src/hooks/useWebSocket.ts
import { useEffect, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';

export function useWebSocket() {
  const queryClient = useQueryClient();
  const wsRef = useRef<WebSocket | null>(null);
  
  useEffect(() => {
    const wsUrl = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8000/ws';
    wsRef.current = new WebSocket(wsUrl);
    
    wsRef.current.onmessage = (event) => {
      const data = JSON.parse(event.data);
      
      switch (data.type) {
        case 'profit_update':
          queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
          queryClient.invalidateQueries({ queryKey: ['userCards'] });
          break;
          
        case 'task_completed':
          queryClient.invalidateQueries({ queryKey: ['tasks'] });
          break;
          
        case 'new_referral':
          queryClient.invalidateQueries({ queryKey: ['referralInfo'] });
          break;
          
        case 'chat_message':
          queryClient.setQueryData(['chatMessages'], (old: any) => {
            return [...(old || []), data.message];
          });
          break;
      }
    };
    
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [queryClient]);
  
  const sendMessage = (message: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    }
  };
  
  return { sendMessage };
}
```

### 5. Performance Optimizations

**Lazy Loading Strategy:**
```typescript
// src/components/react/LazyGameComponents.tsx
import { lazy, Suspense } from 'react';

// Lazy load heavy game components
const CardSystem = lazy(() => import('./CardSystem'));
const TaskSystem = lazy(() => import('./TaskSystem'));
const VerseWorld = lazy(() => import('./VerseWorld'));

export const LazyCardSystem = (props: any) => (
  <Suspense fallback={<div className="animate-pulse bg-gray-800 h-64 rounded-xl" />}>
    <CardSystem {...props} />
  </Suspense>
);

export const LazyTaskSystem = (props: any) => (
  <Suspense fallback={<div className="animate-pulse bg-gray-800 h-64 rounded-xl" />}>
    <TaskSystem {...props} />
  </Suspense>
);

export const LazyVerseWorld = (props: any) => (
  <Suspense fallback={<div className="flex items-center justify-center h-screen text-white">Loading 3D World...</div>}>
    <VerseWorld {...props} />
  </Suspense>
);
```

## Game Features Summary

### Migrated Features
1. **Card Collection System** - Full functionality with real-time updates
2. **Task Management** - All task types with verification
3. **Referral Program** - Complete referral tracking
4. **VPN Integration** - Subscription management
5. **3D Verse World** - Interactive 3D environment
6. **Real-time Updates** - WebSocket integration
7. **Chat System** - Integrated messaging
8. **Payment System** - Wallet and transactions

### Performance Improvements
- **Reduced Bundle Size**: 3D components lazy-loaded
- **Faster Initial Load**: Server-side data fetching
- **Better Caching**: Optimized React Query setup
- **Smooth Animations**: Framer Motion integration
- **Mobile Optimization**: Touch-friendly interactions

### Maintained Compatibility
- **Telegram Integration**: Full WebApp SDK support
- **Backend API**: 100% compatibility
- **User Experience**: Identical functionality
- **Real-time Features**: WebSocket integration
- **Security**: Encrypted storage and secure auth

## Testing Strategy

### Game Mechanics Testing
- [ ] Card purchase and upgrade flows
- [ ] Task completion and verification
- [ ] Referral system functionality
- [ ] VPN subscription management
- [ ] 3D world interactions
- [ ] Real-time updates
- [ ] Payment processing
- [ ] Chat system

### Performance Testing
- [ ] 3D rendering performance
- [ ] Bundle size optimization
- [ ] Loading time measurements
- [ ] Memory usage monitoring
- [ ] Mobile device testing

### Integration Testing
- [ ] Backend API integration
- [ ] WebSocket connectivity
- [ ] Telegram WebApp features
- [ ] Authentication flows
- [ ] Error handling

## Conclusion

The game mechanics migration to Astro maintains full functionality while significantly improving performance through strategic use of server-side rendering, lazy loading, and optimized client-side hydration. The hybrid approach ensures smooth gameplay and optimal user experience.
