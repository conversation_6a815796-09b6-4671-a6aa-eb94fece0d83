2025-04-08 23:44:46,619 - INFO - Starting task creation process...
2025-04-08 23:44:46,654 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 23:44:46,656 - INFO - Retrieved CSRF token for admin login
2025-04-08 23:44:47,698 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 23:44:47,705 - INFO - Admin login successful
2025-04-08 23:44:47,706 - INFO - Prepared 8 test tasks
2025-04-08 23:44:47,915 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 23:44:47,917 - INFO - Created task: Watch Tutorial Video (ID: 1)
2025-04-08 23:44:48,571 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 23:44:48,573 - INFO - Created task: View Instagram Post (ID: 2)
2025-04-08 23:44:49,601 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 500 Internal Server Error"
2025-04-08 23:44:49,602 - ERROR - Failed to create task: 500 Internal Server Error
2025-04-08 23:44:50,565 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-08 23:44:50,566 - INFO - Created task: Join Our Telegram Channel (ID: 3)
2025-04-08 23:44:51,693 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 500 Internal Server Error"
2025-04-08 23:44:51,695 - ERROR - Failed to create task: 500 Internal Server Error
2025-04-08 23:44:52,534 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 422 Unprocessable Entity"
2025-04-08 23:44:52,536 - ERROR - Failed to create task: 422 {"detail":[{"type":"extra_forbidden","loc":["body","required_duration"],"msg":"Extra inputs are not permitted","input":30}]}
2025-04-08 23:44:53,492 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 422 Unprocessable Entity"
2025-04-08 23:44:53,494 - ERROR - Failed to create task: 422 {"detail":[{"type":"string_pattern_mismatch","loc":["body","platform_url"],"msg":"String should match pattern '^https?://.+'","input":"","ctx":{"pattern":"^https?://.+"}}]}
2025-04-08 23:44:54,528 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 422 Unprocessable Entity"
2025-04-08 23:44:54,531 - ERROR - Failed to create task: 422 {"detail":[{"type":"list_type","loc":["body","daily_rewards"],"msg":"Input should be a valid list","input":"[2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 15.0]"},{"type":"string_pattern_mismatch","loc":["body","platform_url"],"msg":"String should match pattern '^https?://.+'","input":"","ctx":{"pattern":"^https?://.+"}}]}
2025-04-08 23:44:55,458 - INFO - ==================================================
2025-04-08 23:44:55,461 - INFO - Task Creation Report
2025-04-08 23:44:55,463 - INFO - ==================================================
2025-04-08 23:44:55,465 - INFO - Tasks created: 3
2025-04-08 23:44:55,465 - INFO - Tasks failed: 5
2025-04-08 23:44:55,466 - INFO - ==================================================
2025-04-08 23:59:13,546 - INFO - Starting task creation process...
2025-04-08 23:59:13,698 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-08 23:59:13,717 - INFO - Retrieved CSRF token for admin login
2025-04-08 23:59:14,813 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-08 23:59:14,816 - INFO - Admin login successful
2025-04-08 23:59:14,817 - INFO - Prepared 8 test tasks
2025-04-08 23:59:16,485 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 422 Unprocessable Entity"
2025-04-08 23:59:16,491 - ERROR - Failed to create task: 422 {"detail":[{"type":"extra_forbidden","loc":["body","required_duration"],"msg":"Extra inputs are not permitted","input":60}]}
2025-04-08 23:59:17,593 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 422 Unprocessable Entity"
2025-04-08 23:59:17,629 - ERROR - Failed to create task: 422 {"detail":[{"type":"extra_forbidden","loc":["body","required_duration"],"msg":"Extra inputs are not permitted","input":30}]}
2025-04-08 23:59:18,569 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 500 Internal Server Error"
2025-04-08 23:59:18,587 - ERROR - Failed to create task: 500 Internal Server Error
2025-04-08 23:59:19,521 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 422 Unprocessable Entity"
2025-04-08 23:59:19,545 - ERROR - Failed to create task: 422 {"detail":[{"type":"extra_forbidden","loc":["body","required_duration"],"msg":"Extra inputs are not permitted","input":0}]}
2025-04-08 23:59:20,565 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 500 Internal Server Error"
2025-04-08 23:59:20,577 - ERROR - Failed to create task: 500 Internal Server Error
2025-04-08 23:59:21,518 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 422 Unprocessable Entity"
2025-04-08 23:59:21,520 - ERROR - Failed to create task: 422 {"detail":[{"type":"extra_forbidden","loc":["body","required_duration"],"msg":"Extra inputs are not permitted","input":30}]}
2025-04-08 23:59:22,505 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 422 Unprocessable Entity"
2025-04-08 23:59:22,514 - ERROR - Failed to create task: 422 {"detail":[{"type":"string_pattern_mismatch","loc":["body","platform_url"],"msg":"String should match pattern '^https?://.+'","input":"","ctx":{"pattern":"^https?://.+"}}]}
2025-04-08 23:59:23,525 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 422 Unprocessable Entity"
2025-04-08 23:59:23,534 - ERROR - Failed to create task: 422 {"detail":[{"type":"list_type","loc":["body","daily_rewards"],"msg":"Input should be a valid list","input":"[2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 15.0]"}]}
2025-04-08 23:59:24,457 - INFO - ==================================================
2025-04-08 23:59:24,459 - INFO - Task Creation Report
2025-04-08 23:59:24,460 - INFO - ==================================================
2025-04-08 23:59:24,460 - INFO - Tasks created: 0
2025-04-08 23:59:24,460 - INFO - Tasks failed: 8
2025-04-08 23:59:24,461 - INFO - ==================================================
2025-04-09 00:02:48,576 - INFO - Starting task creation process...
2025-04-09 00:02:48,821 - INFO - HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
2025-04-09 00:02:48,823 - INFO - Retrieved CSRF token for admin login
2025-04-09 00:02:50,905 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
2025-04-09 00:02:50,948 - INFO - Admin login successful
2025-04-09 00:02:50,949 - INFO - Prepared 8 test tasks
2025-04-09 00:02:51,913 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-09 00:02:51,915 - INFO - Created task: Watch Tutorial Video (ID: 1)
2025-04-09 00:02:52,720 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-09 00:02:52,722 - INFO - Created task: View Instagram Post (ID: 2)
2025-04-09 00:02:53,552 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-09 00:02:53,562 - INFO - Created task: Follow Us on Instagram (ID: 3)
2025-04-09 00:02:54,543 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-09 00:02:54,544 - INFO - Created task: Join Our Telegram Channel (ID: 4)
2025-04-09 00:02:55,551 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-09 00:02:55,554 - INFO - Created task: Follow Us on Twitter (ID: 5)
2025-04-09 00:02:56,588 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-09 00:02:56,589 - INFO - Created task: Visit Our Website (ID: 6)
2025-04-09 00:02:57,570 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-09 00:02:57,579 - INFO - Created task: Refer a Friend (ID: 7)
2025-04-09 00:02:58,564 - INFO - HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
2025-04-09 00:02:58,571 - INFO - Created task: Daily Check-in (ID: 8)
2025-04-09 00:02:59,457 - INFO - ==================================================
2025-04-09 00:02:59,457 - INFO - Task Creation Report
2025-04-09 00:02:59,457 - INFO - ==================================================
2025-04-09 00:02:59,457 - INFO - Tasks created: 8
2025-04-09 00:02:59,458 - INFO - Tasks failed: 0
2025-04-09 00:02:59,458 - INFO - ==================================================
