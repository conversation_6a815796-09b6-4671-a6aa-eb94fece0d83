/**
 * Error handling utilities for consistent error management across the application
 */

import { ERROR_MESSAGES } from './constants';

/**
 * Custom error types
 */
export class AppError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly isOperational: boolean;

  constructor(
    message: string,
    code: string = 'UNKNOWN_ERROR',
    statusCode: number = 500,
    isOperational: boolean = true
  ) {
    super(message);
    this.name = 'AppError';
    this.code = code;
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    // Maintains proper stack trace for where our error was thrown
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }
}

export class NetworkError extends AppError {
  constructor(message: string = ERROR_MESSAGES.NETWORK_ERROR) {
    super(message, 'NETWORK_ERROR', 0);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = ERROR_MESSAGES.AUTH_FAILED) {
    super(message, 'AUTH_ERROR', 401);
  }
}

export class ValidationError extends AppError {
  public readonly field?: string;

  constructor(message: string = ERROR_MESSAGES.VALIDATION_ERROR, field?: string) {
    super(message, 'VALIDATION_ERROR', 400);
    this.field = field;
  }
}

export class RateLimitError extends AppError {
  constructor(message: string = ERROR_MESSAGES.RATE_LIMIT_EXCEEDED) {
    super(message, 'RATE_LIMIT_ERROR', 429);
  }
}

export class InsufficientBalanceError extends AppError {
  constructor(message: string = ERROR_MESSAGES.INSUFFICIENT_BALANCE) {
    super(message, 'INSUFFICIENT_BALANCE', 400);
  }
}

/**
 * Error handler class for centralized error management
 */
class ErrorHandler {
  private errorCallbacks: Array<(error: Error) => void> = [];
  private isProduction = process.env.NODE_ENV === 'production';

  /**
   * Add error callback
   * @param callback - Function to call when error occurs
   */
  onError(callback: (error: Error) => void): void {
    this.errorCallbacks.push(callback);
  }

  /**
   * Remove error callback
   * @param callback - Function to remove
   */
  offError(callback: (error: Error) => void): void {
    const index = this.errorCallbacks.indexOf(callback);
    if (index > -1) {
      this.errorCallbacks.splice(index, 1);
    }
  }

  /**
   * Handle error
   * @param error - Error to handle
   * @param context - Additional context
   */
  handle(error: Error, context?: Record<string, any>): void {
    // Log error
    this.logError(error, context);

    // Notify callbacks
    this.errorCallbacks.forEach(callback => {
      try {
        callback(error);
      } catch (callbackError) {
        console.error('Error in error callback:', callbackError);
      }
    });

    // Report to external service in production
    if (this.isProduction) {
      this.reportError(error, context);
    }
  }

  /**
   * Log error to console
   * @param error - Error to log
   * @param context - Additional context
   */
  private logError(error: Error, context?: Record<string, any>): void {
    const errorInfo = {
      name: error.name,
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : 'server',
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'server',
      context,
    };

    if (this.isProduction) {
      console.error('Application Error:', errorInfo);
    } else {
      console.group('🚨 Application Error');
      console.error('Error:', error);
      console.log('Context:', context);
      console.log('Stack:', error.stack);
      console.groupEnd();
    }
  }

  /**
   * Report error to external service
   * @param error - Error to report
   * @param context - Additional context
   */
  private reportError(error: Error, context?: Record<string, any>): void {
    // In production, you would send this to your error reporting service
    // Examples: Sentry, Bugsnag, LogRocket, etc.
    
    // For now, we'll just log it
    console.log('Would report error to external service:', {
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      context,
    });
  }

  /**
   * Create user-friendly error message
   * @param error - Error to process
   * @returns User-friendly message
   */
  getUserMessage(error: Error): string {
    if (error instanceof AppError) {
      return error.message;
    }

    // Map common error types to user-friendly messages
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return ERROR_MESSAGES.NETWORK_ERROR;
    }

    if (error.name === 'SyntaxError' && error.message.includes('JSON')) {
      return ERROR_MESSAGES.SERVER_ERROR;
    }

    // Default message for unknown errors
    return this.isProduction 
      ? 'An unexpected error occurred. Please try again.'
      : error.message;
  }

  /**
   * Check if error should be reported
   * @param error - Error to check
   * @returns True if error should be reported
   */
  shouldReport(error: Error): boolean {
    // Don't report operational errors in production
    if (error instanceof AppError && error.isOperational) {
      return false;
    }

    // Don't report network errors (user's connection issue)
    if (error instanceof NetworkError) {
      return false;
    }

    return true;
  }
}

// Create singleton instance
export const errorHandler = new ErrorHandler();

/**
 * Async error wrapper for functions
 * @param fn - Async function to wrap
 * @returns Wrapped function that handles errors
 */
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args);
    } catch (error) {
      errorHandler.handle(error as Error, {
        function: fn.name,
        arguments: args,
      });
      throw error;
    }
  }) as T;
}

/**
 * Error boundary for Vue components
 * @param error - Error that occurred
 * @param instance - Vue component instance
 * @param info - Error info
 */
export function handleVueError(error: Error, instance: any, info: string): void {
  errorHandler.handle(error, {
    component: instance?.$options.name || 'Unknown',
    info,
    props: instance?.$props,
    route: instance?.$route?.path,
  });
}

/**
 * Handle unhandled promise rejections
 * @param event - Unhandled rejection event
 */
export function handleUnhandledRejection(event: PromiseRejectionEvent): void {
  const error = event.reason instanceof Error 
    ? event.reason 
    : new Error(String(event.reason));

  errorHandler.handle(error, {
    type: 'unhandledRejection',
    reason: event.reason,
  });

  // Prevent the default browser behavior
  event.preventDefault();
}

/**
 * Handle global errors
 * @param event - Error event
 */
export function handleGlobalError(event: ErrorEvent): void {
  const error = event.error || new Error(event.message);
  
  errorHandler.handle(error, {
    type: 'globalError',
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno,
  });
}

/**
 * Retry function with exponential backoff
 * @param fn - Function to retry
 * @param maxRetries - Maximum number of retries
 * @param baseDelay - Base delay in milliseconds
 * @returns Promise that resolves with function result
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxRetries) {
        break;
      }

      // Don't retry certain types of errors
      if (error instanceof ValidationError || 
          error instanceof AuthenticationError ||
          (error instanceof AppError && error.statusCode >= 400 && error.statusCode < 500)) {
        break;
      }

      // Calculate delay with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

/**
 * Safe JSON parse with error handling
 * @param json - JSON string to parse
 * @param fallback - Fallback value if parsing fails
 * @returns Parsed object or fallback
 */
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json);
  } catch (error) {
    errorHandler.handle(new Error(`Failed to parse JSON: ${error}`), {
      json: json.substring(0, 100), // First 100 chars for debugging
    });
    return fallback;
  }
}

/**
 * Safe async operation with timeout
 * @param operation - Async operation to perform
 * @param timeout - Timeout in milliseconds
 * @returns Promise that resolves with operation result or rejects with timeout
 */
export function withTimeout<T>(
  operation: Promise<T>,
  timeout: number = 10000
): Promise<T> {
  return Promise.race([
    operation,
    new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeout}ms`));
      }, timeout);
    }),
  ]);
}

/**
 * Initialize global error handlers
 */
export function initializeErrorHandlers(): void {
  if (typeof window === 'undefined') return;

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', handleUnhandledRejection);

  // Handle global errors
  window.addEventListener('error', handleGlobalError);

  // Handle Vue errors (if Vue is available)
  if (typeof window !== 'undefined' && (window as any).Vue) {
    (window as any).Vue.config.errorHandler = handleVueError;
  }
}

/**
 * Cleanup global error handlers
 */
export function cleanupErrorHandlers(): void {
  if (typeof window === 'undefined') return;

  window.removeEventListener('unhandledrejection', handleUnhandledRejection);
  window.removeEventListener('error', handleGlobalError);
}

// Auto-initialize in browser environment
if (typeof window !== 'undefined') {
  initializeErrorHandlers();
}
