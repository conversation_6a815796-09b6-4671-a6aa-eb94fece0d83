import { useQuery, useMutation, useQueryClient, QueryFunctionContext } from '@tanstack/react-query';
import { fetchData, mutateData, ApiClientQueryKey } from '../utils/apiClient';
import type {
  Task,
  DailyTaskStreak,
  TaskCompletion,
  TaskVerificationResult,
  CreateTaskData,
  UpdateTaskData,
  TaskAnalytics,
  DailyCheckInResponse,
} from '../types/task';

const TASKS_CACHE_TTL = 60 * 1000; // 60 seconds
const STREAK_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// --- useTasksQuery ---
export type TasksQueryKey = ApiClientQueryKey;

/**
 * Hook to fetch available tasks.
 */
export function useTasksQuery(options?: {
  staleTime?: number;
  enabled?: boolean;
  refetchInterval?: number | false;
}) {
  const queryKey: TasksQueryKey = ['tasks', { endpoint: '/api/tasks/available' }];

  const queryFn = async (context: QueryFunctionContext<TasksQueryKey>): Promise<Task[]> => {
    try {
      const rawTasks = await fetchData<Task[]>(context);
      const processedTasks = (rawTasks || [])
        .filter(task => task && typeof task === 'object' && task.type)
        .map(task => ({
          ...task,
          avg_completion_time:
            task.avg_completion_time !== undefined &&
            task.avg_completion_time !== null &&
            task.avg_completion_time < 0
              ? null
              : task.avg_completion_time,
        }));

      processedTasks.sort((a, b) => {
        const statusPriority: Record<string, number> = {
          pending: 0,
          completed: a.is_claimed ? 3 : 1,
          available: 2,
          failed: 4,
          expired: 5,
          revoked: 6,
        };
        const aPriority = statusPriority[a.status.toLowerCase()] ?? 999;
        const bPriority = statusPriority[b.status.toLowerCase()] ?? 999;
        if (aPriority !== bPriority) return aPriority - bPriority;
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });
      return processedTasks;
    } catch (error: any) {
      // Handle specific errors gracefully
      if (error?.response?.status === 404) {
        // Return empty array if no tasks found
        return [];
      }
      throw error;
    }
  };

  return useQuery<Task[], Error, Task[], TasksQueryKey>({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? TASKS_CACHE_TTL,
    enabled: options?.enabled,
    refetchInterval: options?.refetchInterval,
  });
}

// --- useDailyStreakQuery ---
// Query key needs taskId, so it will be [string, { endpoint: string, taskId: number }]
// We need to ensure ApiClientQueryKey can accommodate this or define a new type.
// For now, let's assume ApiClientQueryKey is [string, { endpoint: string; params?: Record<string, any> }]
// and we can pass taskId within params.

export type DailyStreakQueryKey = [string, { endpoint: string; taskId: number }];

/**
 * Hook to fetch daily streak for a specific task.
 */
export function useDailyStreakQuery(
  taskId: number,
  options?: {
    staleTime?: number;
    enabled?: boolean;
    refetchInterval?: number | false;
  }
) {
  // The actual endpoint includes the taskId in the path, not as a query param in this case.
  const queryKey: DailyStreakQueryKey = [
    'dailyStreak',
    { endpoint: `/api/tasks/daily/streak/${taskId}`, taskId },
  ];

  const queryFn = async (
    context: QueryFunctionContext<DailyStreakQueryKey>
  ): Promise<DailyTaskStreak> => {
    const endpoint = context.queryKey[1].endpoint;
    const fetchContextKey: ApiClientQueryKey = [context.queryKey[0], { endpoint }];
    const streakData = await fetchData<DailyTaskStreak>({ ...context, queryKey: fetchContextKey });
    return {
      ...streakData,
      daily_rewards: streakData.daily_rewards || [10, 15, 20, 25, 30, 35, 50],
    };
  };

  return useQuery<DailyTaskStreak, Error, DailyTaskStreak, DailyStreakQueryKey>({
    queryKey,
    queryFn,
    staleTime: options?.staleTime ?? STREAK_CACHE_TTL,
    enabled: options?.enabled && !!taskId,
    refetchInterval: options?.refetchInterval,
  });
}

// Mutations from taskStore

/**
 * Start a task by ID.
 */
export function useStartTaskMutation() {
  const queryClient = useQueryClient();
  return useMutation<TaskCompletion, Error, number, unknown>({
    mutationFn: (taskId: number) =>
      mutateData<TaskCompletion>({ endpoint: `/api/tasks/${taskId}/start` }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });
}

/**
 * Verify a task with optional verification data.
 */
export function useVerifyTaskMutation() {
  const queryClient = useQueryClient();
  return useMutation<TaskVerificationResult, Error, { taskId: number; data?: any }, unknown>({
    mutationFn: (params: { taskId: number; data?: any }) =>
      mutateData<TaskVerificationResult>({
        endpoint: `/api/tasks/${params.taskId}/verify`,
        variables: params.data,
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });
}

/**
 * Claim a completed task reward.
 */
export function useClaimRewardMutation() {
  const queryClient = useQueryClient();
  return useMutation<TaskCompletion, Error, number, unknown>({
    mutationFn: (taskId: number) =>
      mutateData<TaskCompletion>({ endpoint: `/api/tasks/${taskId}/claim` }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
    },
  });
}

/**
 * Perform daily check-in for a task (or general if no ID provided).
 */
export function usePerformDailyCheckInMutation() {
  const queryClient = useQueryClient();
  return useMutation<DailyCheckInResponse, Error, number | undefined, unknown>({
    mutationFn: (taskId?: number) =>
      mutateData<DailyCheckInResponse>({
        endpoint: taskId ? `/api/tasks/daily/check-in/${taskId}` : '/api/tasks/daily/check-in',
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['dailyStreak'] });
    },
  });
}

// Admin mutations from taskStore

export function useCreateTaskMutation() {
  const queryClient = useQueryClient();
  return useMutation<Task, Error, CreateTaskData, unknown>({
    mutationFn: (data: CreateTaskData) =>
      mutateData<Task>({ endpoint: '/api/tasks/admin/tasks/create', variables: data }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });
}

export function useUpdateTaskMutation() {
  const queryClient = useQueryClient();
  return useMutation<Task, Error, { taskId: number; data: UpdateTaskData }, unknown>({
    mutationFn: (params: { taskId: number; data: UpdateTaskData }) =>
      mutateData<Task>({
        endpoint: `/api/tasks/admin/tasks/${params.taskId}`,
        variables: params.data,
        method: 'PATCH',
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });
}

export function useDeleteTaskMutation() {
  const queryClient = useQueryClient();
  return useMutation<void, Error, number, unknown>({
    mutationFn: (taskId: number) =>
      mutateData<void>({ endpoint: `/api/tasks/admin/tasks/${taskId}`, method: 'DELETE' }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });
}

export function useToggleTaskMutation() {
  const queryClient = useQueryClient();
  return useMutation<Task, Error, number, unknown>({
    mutationFn: (taskId: number) =>
      mutateData<Task>({ endpoint: `/api/tasks/admin/tasks/${taskId}/toggle` }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });
}

export function useResetTaskVerificationMutation() {
  const queryClient = useQueryClient();
  return useMutation<Task, Error, number, unknown>({
    mutationFn: (taskId: number) =>
      mutateData<Task>({ endpoint: `/api/tasks/admin/tasks/${taskId}/reset-verification` }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
    },
  });
}

/**
 * Hook to fetch task analytics for admin.
 */
export function useTaskAnalyticsQuery(options?: { enabled?: boolean }) {
  return useQuery<TaskAnalytics[], Error, TaskAnalytics[], ApiClientQueryKey>({
    queryKey: ['taskAnalytics', { endpoint: '/api/tasks/admin/analytics' }],
    queryFn: ctx => fetchData<TaskAnalytics[]>(ctx),
    enabled: options?.enabled,
  });
}

/**
 * Hook to fetch task stats for admin.
 */
export function useTaskStatsQuery(options?: { enabled?: boolean }) {
  return useQuery<any, Error, any, ApiClientQueryKey>({
    queryKey: ['taskStats', { endpoint: '/api/tasks/admin/stats' }],
    queryFn: ctx => fetchData<any>(ctx),
    enabled: options?.enabled,
  });
}
