import type { User, VerifySessionData, TelegramLoginParams, ApiResponse } from '~/types'

/**
 * Modern auth composable using Nuxt 4 data fetching patterns
 * Optimized for Telegram Mini App with proper session management
 */
export const useAuth = () => {
  const user = ref<User | null>(null)
  const isLoading = ref(false)
  const isAuthenticated = computed(() => !!user.value)

  // Initialize Telegram WebApp
  const initTelegram = () => {
    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      window.Telegram.WebApp.ready()
      window.Telegram.WebApp.expand()
      return window.Telegram.WebApp
    }
    return null
  }

  // Attempt automatic Telegram authentication
  const attemptTelegramAuth = async (): Promise<{ success: boolean; isNewUser?: boolean; error?: string }> => {
    try {
      const { hasValidAuthData, getAuthData } = useTelegram()

      if (!hasValidAuthData.value) {
        return {
          success: false,
          error: 'No valid Telegram authentication data available'
        }
      }

      const authData = getAuthData()
      if (!authData?.initData) {
        return {
          success: false,
          error: 'Telegram initData not available'
        }
      }

      console.log('[Auth] Attempting automatic Telegram authentication...')

      const result = await telegramLogin(authData.initData)

      if (result.user) {
        console.log('[Auth] Automatic Telegram authentication successful')
        return {
          success: true,
          isNewUser: result.isNewUser
        }
      } else {
        return {
          success: false,
          error: 'Authentication failed - no user data returned'
        }
      }
    } catch (error: any) {
      console.error('[Auth] Automatic Telegram authentication failed:', error)
      return {
        success: false,
        error: error.message || 'Authentication failed'
      }
    }
  }

  // Verify existing session
  const verifySession = async (): Promise<boolean> => {
    if (isLoading.value) return false
    
    isLoading.value = true
    
    try {
      console.log('[Auth] Starting session verification...')
      
      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        console.log('[Auth] Request timeout - aborting')
        controller.abort()
      }, 15000)
      
      try {
        const response = await $fetch<VerifySessionData>('/api/auth/verify-session', {
          method: 'POST',
          signal: controller.signal,
          timeout: 12000
        })

        clearTimeout(timeoutId)

        if (response?.status === 'authenticated' && response.data) {
          user.value = response.data as User
          console.log('[Auth] Session verification successful')
          return true
        }

        console.log('[Auth] User is unauthenticated')
        return false
      } catch (error: any) {
        clearTimeout(timeoutId)
        
        console.log('[Auth] Session verification error:', {
          message: error.message,
          status: error?.response?.status,
          code: error?.code,
          name: error?.name,
          isAborted: error.name === 'AbortError' || error.code === 'ERR_CANCELED'
        })
        
        // Handle abort/timeout gracefully
        if (error.name === 'AbortError' || error.code === 'ERR_CANCELED' || error.message?.includes('timeout')) {
          console.warn('[Auth] Session verification was canceled/timed out, treating as unauthenticated')
          return false
        }
        
        // Handle 401 responses gracefully - expected for unauthenticated users
        if (error?.response?.status === 401 || error?.status === 401) {
          console.log('[Auth] User is unauthenticated (401)')
          return false
        }
        
        // For network errors or server issues, also return unauthenticated to prevent hanging
        if (error?.code === 'NETWORK_ERROR' || error?.response?.status >= 500) {
          console.warn('[Auth] Network/server error, treating as unauthenticated:', error.message)
          return false
        }
        
        // For other errors, also return unauthenticated to prevent hanging
        console.warn('[Auth] Treating error as unauthenticated:', error.message)
        return false
      }
    } finally {
      isLoading.value = false
    }
  }

  // Login with Telegram
  const telegramLogin = async (initData: string): Promise<{ isNewUser: boolean; user?: User; telegram_data?: any }> => {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 8000)
    
    try {
      console.log('[TelegramLogin] Starting login process')
      
      const response = await $fetch<ApiResponse>('/api/auth/telegram/login', {
        method: 'POST',
        body: {
          init_data: initData,
          auth_method: 'telegram'
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      // If we get a successful response, it means existing user
      if (response?.data) {
        console.log('[TelegramLogin] Existing user login successful')
        user.value = response.data as User
        return {
          isNewUser: false,
          user: response.data as User
        }
      }
      
      throw new Error('Invalid response from server')
    } catch (error: any) {
      clearTimeout(timeoutId)
      console.error('[TelegramLogin] Error:', error)
      
      // Handle timeout
      if (error.name === 'AbortError' || error.message?.includes('timeout')) {
        throw new Error('Login request timed out. Please try again.')
      }
      
      // Handle specific error cases
      if (error?.response?.status === 404) {
        console.log('[TelegramLogin] User not found - new user')
        return {
          isNewUser: true,
          telegram_data: initData
        }
      }
      
      // Simplify error handling for other cases
      if (error?.response?.status === 401) {
        throw new Error('Telegram authentication failed')
      }
      if (error?.response?.status === 400) {
        throw new Error('Invalid Telegram data')
      }
      
      // For other errors, re-throw with a generic message
      throw new Error(error?.response?.data?.detail || error?.response?.data?.message || 'Login failed. Please try again.')
    }
  }

  // Login with credentials
  const login = async (credentials?: { username: string; password: string }) => {
    isLoading.value = true
    
    try {
      if (credentials) {
        // Traditional login
        const response = await $fetch<ApiResponse>('/api/auth/token', {
          method: 'POST',
          body: credentials
        })
        if (response?.data) {
          user.value = response.data as User
          await navigateTo('/dashboard')
        }
      } else {
        // Telegram login
        const webApp = initTelegram()
        if (!webApp?.initData) {
          throw new Error('Telegram WebApp not available')
        }

        const result = await telegramLogin(webApp.initData)
        
        if (result.isNewUser) {
          // Redirect to new user setup
          await navigateTo('/auth/new-user-setup')
        } else if (result.user) {
          // Existing user, redirect to dashboard
          await navigateTo('/dashboard')
        }
      }
    } catch (error: any) {
      console.error('Authentication failed:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // Logout
  const logout = async () => {
    try {
      await $fetch('/api/auth/logout', {
        method: 'POST'
      })
      user.value = null
      await navigateTo('/')
    } catch (error) {
      console.error('Logout failed:', error)
      // Even if logout fails, clear local state
      user.value = null
      await navigateTo('/')
    }
  }

  // Register new user
  const register = async (initData: string, username: string) => {
    try {
      const response = await $fetch<ApiResponse>('/api/auth/register', {
        method: 'POST',
        body: {
          init_data: initData,
          username: username
        }
      })

      if (response?.data) {
        user.value = response.data as User
        await navigateTo('/dashboard')
      }
    } catch (error: any) {
      console.error('Registration failed:', error)
      throw error
    }
  }

  // Get authentication status
  const getAuthStatus = () => {
    return {
      isAuthenticated: isAuthenticated.value,
      isLoading: isLoading.value,
      user: user.value
    }
  }

  return {
    user: readonly(user),
    isLoading: readonly(isLoading),
    isAuthenticated,
    login,
    logout,
    register,
    verifySession,
    telegramLogin,
    attemptTelegramAuth,
    getAuthStatus,
    initTelegram
  }
}
