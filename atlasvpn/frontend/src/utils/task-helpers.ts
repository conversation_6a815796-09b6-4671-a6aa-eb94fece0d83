import { Task, TaskType, VerificationData } from '../types/task';

interface VerificationContext {
  verificationCode?: string;
  telegramUsername?: string;
  userLanguage?: string;
  userPlatform?: string;
  isTestMode?: boolean;
}

/**
 * Creates appropriate verification data based on task type and context
 */
export const createVerificationData = (
  task: Task,
  context: VerificationContext
): VerificationData => {
  const { verificationCode, telegramUsername, userLanguage, userPlatform, isTestMode } = context;

  switch (task.type) {
    case TaskType.YOUTUBE_VIEW:
    case TaskType.INSTAGRAM_VIEW:
      return {
        code: verificationCode || (isTestMode ? 'TEST123' : ''),
        verification_code: verificationCode || (isTestMode ? 'TEST123' : ''), // Include both formats for backwards compatibility
        platform: userPlatform,
        language: userLanguage,
      };

    case TaskType.INSTAGRAM_FOLLOW:
    case TaskType.TWITTER_FOLLOW:
      return {
        username: verificationCode || '',
        platform: userPlatform,
        language: userLanguage,
      };

    case TaskType.TELEGRAM_CHANNEL:
      return {
        telegram_username: telegramUsername || (isTestMode ? 'test_user' : ''),
        platform: userPlatform,
        language: userLanguage,
      };

    case TaskType.WEBSITE_VISIT:
      return {
        visit_duration: calculateDuration(task),
        platform: userPlatform,
        language: userLanguage,
      };

    case TaskType.REFERRAL:
      return { platform: userPlatform };

    default:
      return {};
  }
};

/**
 * Calculates duration in seconds for time-based tasks
 */
const calculateDuration = (task: Task): number => {
  if (!task || !task.started_at) return 0;

  const startTime = new Date(task.started_at).getTime();
  const currentTime = Date.now();
  const durationSeconds = Math.floor((currentTime - startTime) / 1000);

  return durationSeconds;
};

/**
 * Get the required verification time based on task configuration
 */
export const getRequiredVerificationTime = (task: Task): number | null => {
  // Check if the task has a required duration
  if (task?.social_task?.required_duration) {
    return task.social_task.required_duration;
  }

  // Default durations by task type if not explicitly set
  switch (task?.type) {
    case TaskType.YOUTUBE_VIEW:
      return 30; // 30 seconds to watch a video
    case TaskType.INSTAGRAM_VIEW:
      return 15; // 15 seconds to view a post
    case TaskType.WEBSITE_VISIT:
      return 20; // 20 seconds to visit website
    default:
      return null; // No time requirement for other tasks
  }
};

/**
 * Checks if a task is in cooldown period
 */
export const isTaskInCooldown = (task: Task): boolean => {
  if (!task.last_verified_at || !task.social_task?.verification_interval) {
    return false;
  }

  const lastVerified = new Date(task.last_verified_at).getTime();
  const cooldownPeriod = task.social_task.verification_interval * 60 * 1000; // Convert minutes to ms
  const now = new Date().getTime();

  return now - lastVerified < cooldownPeriod;
};

/**
 * Gets the remaining cooldown time in seconds
 */
export const getRemainingCooldown = (task: Task): number => {
  if (!task.last_verified_at || !task.social_task?.verification_interval) {
    return 0;
  }

  const lastVerified = new Date(task.last_verified_at).getTime();
  const cooldownPeriod = task.social_task.verification_interval * 60 * 1000; // Convert minutes to ms
  const now = new Date().getTime();
  const remaining = cooldownPeriod - (now - lastVerified);

  return Math.max(0, Math.floor(remaining / 1000)); // Remaining time in seconds
};

/**
 * Formats verification status messages
 */
export const getVerificationStatusMessage = (task: Task): string | null => {
  // Check if verification_attempts is defined and greater than 0
  if (!task.verification_attempts || task.verification_attempts <= 0) {
    return null;
  }

  const maxAttempts = task.social_task?.max_verification_attempts || 3;

  return task.verification_attempts >= maxAttempts
    ? `Maximum verification attempts reached (${task.verification_attempts}/${maxAttempts})`
    : `Verification attempts: ${task.verification_attempts}/${maxAttempts}`;
};
