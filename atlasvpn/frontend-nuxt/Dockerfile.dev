# Development Dockerfile for fast iteration
FROM node:20-alpine

# Install system dependencies for development
RUN apk add --no-cache \
    git \
    wget \
    curl

# Set working directory
WORKDIR /app

# Install pnpm globally
RUN npm install -g pnpm@latest

# Copy package files first for better caching
COPY package*.json pnpm-lock.yaml* ./

# Install dependencies (including dev dependencies)
RUN pnpm install --frozen-lockfile

# Copy source code (will be overridden by volume mount in development)
COPY . .

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nuxt -u 1001 -G nodejs

# Change ownership of the app directory
RUN chown -R nuxt:nodejs /app

# Switch to non-root user
USER nuxt

# Expose port for development server
EXPOSE 3005

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:3005/ || exit 1

# Start development server with hot reload
CMD ["pnpm", "dev"]
