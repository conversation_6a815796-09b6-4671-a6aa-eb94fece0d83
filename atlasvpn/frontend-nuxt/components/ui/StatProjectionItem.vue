<template>
  <div 
    class="stat-projection-item p-2 rounded-lg border transition-all duration-200 hover:scale-[1.02]"
    :class="[
      highlight 
        ? 'border-purple-400/30 bg-purple-500/10' 
        : 'border-gray-700/50 bg-gray-800/30'
    ]"
  >
    <div class="text-center">
      <div 
        class="text-xs uppercase tracking-wider font-bold mb-1"
        :class="highlight ? 'text-purple-300' : 'text-gray-400'"
      >
        {{ label }}
      </div>
      <div 
        class="text-sm font-bold truncate"
        :class="highlight ? 'text-white' : 'text-gray-200'"
      >
        {{ value }}
      </div>
      <div 
        class="text-xs mt-1"
        :class="highlight ? 'text-purple-400/70' : 'text-gray-500'"
      >
        {{ period }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  label: string
  value: string
  period: string
  highlight?: boolean
}

withDefaults(defineProps<Props>(), {
  highlight: false
})
</script>
