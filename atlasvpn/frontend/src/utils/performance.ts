/**
 * Performance utilities for optimizing card components and scrolling
 */

// Image loading optimization
export const optimizeImageUrl = (
  imageUrl: string | undefined | null,
  size: 'small' | 'medium' | 'large' = 'medium'
): string => {
  // If no image, return placeholder
  if (!imageUrl) return '/img/card-placeholder.jpg';

  // If already using a local image, just return it
  if (imageUrl.startsWith('/')) return imageUrl;

  // For remote images, we can add size parameters if using a CDN
  // This is a placeholder for actual CDN implementation
  // Example: return `${imageUrl}?width=300&quality=80`;
  return imageUrl;
};

// Debounce function to limit performance-intensive operations
export const debounce = <F extends (...args: any[]) => any>(func: F, waitFor: number) => {
  let timeout: ReturnType<typeof setTimeout> | null = null;

  return (...args: Parameters<F>): Promise<ReturnType<F>> => {
    if (timeout !== null) {
      clearTimeout(timeout);
    }

    return new Promise(resolve => {
      timeout = setTimeout(() => {
        resolve(func(...args));
      }, waitFor);
    });
  };
};

// Throttle function for scroll events
export const throttle = <F extends (...args: any[]) => any>(func: F, waitFor: number) => {
  let timeoutId: ReturnType<typeof setTimeout> | null = null;
  let lastExecuted = 0;

  return (...args: Parameters<F>): void => {
    const now = Date.now();
    const timeSinceLastExecution = now - lastExecuted;

    const execute = () => {
      lastExecuted = now;
      func(...args);
    };

    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    if (timeSinceLastExecution >= waitFor) {
      execute();
    } else {
      timeoutId = setTimeout(execute, waitFor - timeSinceLastExecution);
    }
  };
};

// Windowed rendering helper for large card lists (virtual list)
export const getVisibleItems = <T>(
  items: T[],
  scrollTop: number,
  viewportHeight: number,
  itemHeight: number,
  buffer: number = 5
): { startIndex: number; endIndex: number; visibleItems: T[] } => {
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - buffer);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + viewportHeight) / itemHeight) + buffer
  );

  // Return items in the visible range plus buffer
  return {
    startIndex,
    endIndex,
    visibleItems: items.slice(startIndex, endIndex + 1),
  };
};

// Check if browser supports intersection observer for lazy loading
export const supportsIntersectionObserver = (): boolean => {
  return (
    'IntersectionObserver' in window &&
    'IntersectionObserverEntry' in window &&
    'intersectionRatio' in window.IntersectionObserverEntry.prototype
  );
};

// Detect low-end devices to apply performance optimizations
export const isLowEndDevice = (): boolean => {
  // Check for memory (Chrome-only feature)
  const memory = (navigator as any)?.deviceMemory || 4; // Default to mid-range if not available

  // Check for hardware concurrency (CPU cores)
  const cores = navigator.hardwareConcurrency || 4;

  // Check if the device is a mobile device
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  );

  // Consider it a low-end device if it has limited memory or CPU cores
  return (memory <= 2 || cores <= 2) && isMobile;
};

// Optimized card rendering settings based on device capability
export const getCardRenderSettings = () => {
  const lowEnd = isLowEndDevice();

  return {
    // Reduce animation complexity on low-end devices
    useSimpleAnimations: lowEnd,
    // Reduce shadow complexity
    useLightShadows: lowEnd,
    // Use smaller images and less blur effects
    imageQuality: lowEnd ? 'low' : 'high',
    // Use windowed rendering for large lists
    useWindowedRendering: lowEnd || true, // Always use for large lists
    // Limit the number of cards shown at once
    maxVisibleCards: lowEnd ? 8 : 20,
    // Disable certain effects
    disableBlurEffects: lowEnd,
    // Fast scrolling threshold
    fastScrollThreshold: 30, // pixels per frame
    // Debounce time for search/filter operations
    filterDebounceTime: lowEnd ? 300 : 150,
  };
};

/**
 * Performance monitoring utilities for Telegram Mini Apps
 * Tracks key metrics and optimizes for mobile performance
 */

import { onCLS, onINP, onFCP, onLCP, onTTFB, type Metric } from 'web-vitals';

interface TelegramPerformanceMetrics {
  // Core Web Vitals
  cls?: number;
  inp?: number;
  fcp?: number;
  lcp?: number;
  ttfb?: number;
  
  // Telegram-specific metrics
  telegramInitTime?: number;
  webSocketConnectTime?: number;
  authTime?: number;
  firstInteractionTime?: number;
  
  // App-specific metrics
  dashboardLoadTime?: number;
  chatLoadTime?: number;
  navigationTime?: number;
}

class TelegramPerformanceMonitor {
  private metrics: TelegramPerformanceMetrics = {};
  private startTimes: Map<string, number> = new Map();
  private isEnabled: boolean;

  constructor() {
    this.isEnabled = import.meta.env.PROD || import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true';
    
    if (this.isEnabled) {
      this.initWebVitals();
      this.trackTelegramSpecificMetrics();
    }
  }

  private initWebVitals() {
    // Track Core Web Vitals
    onCLS((metric: Metric) => {
      this.metrics.cls = metric.value;
      this.reportMetric('CLS', metric.value);
    });

    onINP((metric: Metric) => {
      this.metrics.inp = metric.value;
      this.reportMetric('INP', metric.value);
    });

    onFCP((metric: Metric) => {
      this.metrics.fcp = metric.value;
      this.reportMetric('FCP', metric.value);
    });

    onLCP((metric: Metric) => {
      this.metrics.lcp = metric.value;
      this.reportMetric('LCP', metric.value);
    });

    onTTFB((metric: Metric) => {
      this.metrics.ttfb = metric.value;
      this.reportMetric('TTFB', metric.value);
    });
  }

  private trackTelegramSpecificMetrics() {
    // Track Telegram WebApp initialization
    if (typeof window !== 'undefined' && window.Telegram?.WebApp) {
      const telegramStartTime = performance.now();
      
      // Monitor when Telegram WebApp is ready
      const checkTelegramReady = () => {
        if (window.Telegram?.WebApp?.initData) {
          this.metrics.telegramInitTime = performance.now() - telegramStartTime;
          this.reportMetric('TelegramInit', this.metrics.telegramInitTime);
        } else {
          setTimeout(checkTelegramReady, 10);
        }
      };
      
      checkTelegramReady();
    }

    // Track first user interaction
    const trackFirstInteraction = () => {
      this.metrics.firstInteractionTime = performance.now();
      this.reportMetric('FirstInteraction', this.metrics.firstInteractionTime);
      
      // Remove listeners after first interaction
      document.removeEventListener('click', trackFirstInteraction);
      document.removeEventListener('touchstart', trackFirstInteraction);
      document.removeEventListener('keydown', trackFirstInteraction);
    };

    // Add event listeners with proper types
    document.addEventListener('click', trackFirstInteraction, { once: true });
    document.addEventListener('touchstart', trackFirstInteraction, { once: true });
    document.addEventListener('keydown', trackFirstInteraction, { once: true });
  }

  // Start timing a custom metric
  startTiming(name: string) {
    if (!this.isEnabled) return;
    this.startTimes.set(name, performance.now());
  }

  // End timing and record the metric
  endTiming(name: string): number | undefined {
    if (!this.isEnabled) return undefined;
    
    const startTime = this.startTimes.get(name);
    if (startTime) {
      const duration = performance.now() - startTime;
      this.startTimes.delete(name);
      
      // Store in metrics object
      switch (name) {
        case 'webSocketConnect':
          this.metrics.webSocketConnectTime = duration;
          break;
        case 'auth':
          this.metrics.authTime = duration;
          break;
        case 'dashboardLoad':
          this.metrics.dashboardLoadTime = duration;
          break;
        case 'chatLoad':
          this.metrics.chatLoadTime = duration;
          break;
        case 'navigation':
          this.metrics.navigationTime = duration;
          break;
      }
      
      this.reportMetric(name, duration);
      return duration;
    }
    
    return undefined;
  }

  // Report metric to console and potentially to analytics
  private reportMetric(name: string, value: number) {
    if (import.meta.env.DEV) {
      console.log(`📊 Performance [${name}]:`, `${value.toFixed(2)}ms`);
    }

    // In production, you could send to analytics service
    if (import.meta.env.PROD) {
      // Example: Send to your analytics endpoint
      // this.sendToAnalytics(name, value);
    }
  }

  // Get current performance summary
  getMetrics(): TelegramPerformanceMetrics {
    return { ...this.metrics };
  }

  // Get performance score (0-100)
  getPerformanceScore(): number {
    const { lcp, inp, cls } = this.metrics;
    
    if (!lcp || !inp || cls === undefined) return 0;
    
    // Scoring based on Core Web Vitals thresholds
    const lcpScore = lcp <= 2500 ? 100 : lcp <= 4000 ? 50 : 0;
    const inpScore = inp <= 100 ? 100 : inp <= 300 ? 50 : 0;
    const clsScore = cls <= 0.1 ? 100 : cls <= 0.25 ? 50 : 0;
    
    return Math.round((lcpScore + inpScore + clsScore) / 3);
  }

  // Memory usage monitoring
  getMemoryUsage() {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1048576), // MB
        total: Math.round(memory.totalJSHeapSize / 1048576), // MB
        limit: Math.round(memory.jsHeapSizeLimit / 1048576), // MB
      };
    }
    return null;
  }

  // Network information (if available)
  getNetworkInfo() {
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData,
      };
    }
    return null;
  }

  // Generate performance report
  generateReport() {
    const metrics = this.getMetrics();
    const score = this.getPerformanceScore();
    const memory = this.getMemoryUsage();
    const network = this.getNetworkInfo();

    return {
      timestamp: new Date().toISOString(),
      score,
      metrics,
      memory,
      network,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
      viewport: typeof window !== 'undefined' ? {
        width: window.innerWidth,
        height: window.innerHeight,
      } : { width: 0, height: 0 },
    };
  }
}

// Create singleton instance
export const performanceMonitor = new TelegramPerformanceMonitor();

// Utility functions for easy use in components
export const startTiming = (name: string) => performanceMonitor.startTiming(name);
export const endTiming = (name: string) => performanceMonitor.endTiming(name);
export const getPerformanceMetrics = () => performanceMonitor.getMetrics();
export const getPerformanceScore = () => performanceMonitor.getPerformanceScore();
export const generatePerformanceReport = () => performanceMonitor.generateReport();

// React hook for performance monitoring
export function usePerformanceMonitoring() {
  return {
    startTiming,
    endTiming,
    getMetrics: getPerformanceMetrics,
    getScore: getPerformanceScore,
    generateReport: generatePerformanceReport,
  };
}
