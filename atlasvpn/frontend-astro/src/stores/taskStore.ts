/**
 * Task Store - Lightweight Zustand store for task management
 * Migrated from original frontend
 */

import { create } from 'zustand';

interface Task {
  id: string;
  title: string;
  description?: string;
  reward?: number;
  completed?: boolean;
  type?: string;
  [key: string]: any;
}

interface TaskCompletion {
  taskId: string;
  completedAt: Date;
  reward?: number;
}

interface TaskState {
  // Task data
  tasks: Task[];
  setTasks: (tasks: Task[]) => void;
  addTask: (task: Task) => void;
  updateTask: (taskId: string, updates: Partial<Task>) => void;
  removeTask: (taskId: string) => void;
  
  // Task completions
  taskCompletions: TaskCompletion[];
  setTaskCompletions: (completions: TaskCompletion[]) => void;
  addTaskCompletion: (completion: TaskCompletion) => void;
  
  // Loading and error states
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
  
  error: string | null;
  setError: (error: string | null) => void;
  
  // Utility functions
  getTaskById: (taskId: string) => Task | undefined;
  isTaskCompleted: (taskId: string) => boolean;
  getCompletedTasks: () => Task[];
  getPendingTasks: () => Task[];
  
  // Reset function
  resetTasks: () => void;
}

const initialState = {
  tasks: [],
  taskCompletions: [],
  isLoading: false,
  error: null,
};

export const useTaskStore = create<TaskState>()((set, get) => ({
  ...initialState,
  
  setTasks: (tasks: Task[]) => set({ tasks }),
  
  addTask: (task: Task) => set((state) => ({
    tasks: [...state.tasks, task]
  })),
  
  updateTask: (taskId: string, updates: Partial<Task>) => set((state) => ({
    tasks: state.tasks.map(task => 
      task.id === taskId ? { ...task, ...updates } : task
    )
  })),
  
  removeTask: (taskId: string) => set((state) => ({
    tasks: state.tasks.filter(task => task.id !== taskId)
  })),
  
  setTaskCompletions: (completions: TaskCompletion[]) => 
    set({ taskCompletions: completions }),
  
  addTaskCompletion: (completion: TaskCompletion) => set((state) => ({
    taskCompletions: [...state.taskCompletions, completion]
  })),
  
  setLoading: (loading: boolean) => set({ isLoading: loading }),
  
  setError: (error: string | null) => set({ error }),
  
  getTaskById: (taskId: string) => {
    const { tasks } = get();
    return tasks.find(task => task.id === taskId);
  },
  
  isTaskCompleted: (taskId: string) => {
    const { taskCompletions } = get();
    return taskCompletions.some(completion => completion.taskId === taskId);
  },
  
  getCompletedTasks: () => {
    const { tasks, taskCompletions } = get();
    const completedTaskIds = new Set(taskCompletions.map(c => c.taskId));
    return tasks.filter(task => completedTaskIds.has(task.id));
  },
  
  getPendingTasks: () => {
    const { tasks, taskCompletions } = get();
    const completedTaskIds = new Set(taskCompletions.map(c => c.taskId));
    return tasks.filter(task => !completedTaskIds.has(task.id));
  },
  
  resetTasks: () => set(initialState),
}));

// Export store instance for direct access if needed
export default useTaskStore;