#!/usr/bin/env python
"""
Rate Limiting Test
----------------
Test rate limiting implementation across endpoints.
"""
import logging
import time
from typing import Dict, Any, List, Tuple
from test_utils import (
    print_header,
    print_success,
    print_error,
    print_warning,
    print_info,
    log_request_details,
    log_error_with_traceback,
    make_request,
    get_critical_endpoints,
    log_test_result,
    log_environment_info
)
from test_config import API_BASE_URL, RATE_LIMIT_CONFIG

logger = logging.getLogger(__name__)

def simulate_distributed_attack(url: str, num_requests: int = 20, concurrent: int = 5) -> Tuple[bool, Dict[str, Any]]:
    """
    Simulate a distributed attack by making multiple requests in quick succession.
    
    Args:
        url: The URL to test
        num_requests: Total number of requests to make
        concurrent: Number of "concurrent" requests in each batch
        
    Returns:
        Tuple[bool, Dict]: (was_blocked, results_dict)
    """
    logger.info(f"Simulating distributed attack: {num_requests} requests to {url}")
    
    results = {
        "total": num_requests,
        "success": 0,
        "blocked": 0,
        "errors": 0,
        "response_codes": {},
        "rate_limit_headers_found": False,
        "headers_found": []
    }
    
    for batch in range(0, num_requests, concurrent):
        # Make a batch of concurrent requests
        batch_size = min(concurrent, num_requests - batch)
        logger.info(f"Batch {batch//concurrent + 1}: making {batch_size} requests")
        
        for i in range(batch_size):
            # Add a custom header to indicate this is a security test
            headers = {"X-Security-Test": "true", "User-Agent": "SecurityTestAgent/1.0"}
            success, response = make_request(url, headers=headers)
            
            if success:
                status = response.get("status_code", 0)
                results["response_codes"][status] = results["response_codes"].get(status, 0) + 1
                
                # Check for rate limit headers
                response_headers = response.get("headers", {})
                for header_name in response_headers:
                    if "ratelimit" in header_name.lower() or "rate-limit" in header_name.lower():
                        if header_name not in results["headers_found"]:
                            results["headers_found"].append(header_name)
                        results["rate_limit_headers_found"] = True
                
                # Check if this was blocked by rate limiting (429 Too Many Requests)
                if status == 429:
                    results["blocked"] += 1
                    logger.info(f"Request {batch + i + 1}/{num_requests} was rate limited (429)")
                else:
                    results["success"] += 1
            else:
                results["errors"] += 1
                error_msg = response.get("error", "Unknown error")
                logger.warning(f"Request {batch + i + 1}/{num_requests} failed: {error_msg}")
        
        # Small delay between batches to simulate more realistic traffic pattern
        if batch + concurrent < num_requests:
            time.sleep(0.2)
    
    # Calculate block rate
    results["block_rate"] = results["blocked"] / num_requests if num_requests > 0 else 0
    
    # Was the attack successfully blocked or rate limit headers found?
    was_blocked = results["block_rate"] > 0 or results["rate_limit_headers_found"]
    
    logger.info(f"Attack simulation results: {results}")
    return was_blocked, results

def test_rate_limits() -> Dict[str, Any]:
    """Test rate limiting across critical endpoints"""
    print_header("Testing Rate Limits")
    
    # Log environment info for debugging
    log_environment_info()
    
    # Setup results dictionary
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "endpoints_tested": [],
        "errors": []
    }
    
    # Get all critical endpoints
    endpoints = get_critical_endpoints()
    print_info(f"Testing {len(endpoints)} endpoints for rate limiting")
    results["total_tests"] = len(endpoints)
    
    # Test each endpoint
    for endpoint in endpoints:
        # Create URL for endpoint (replace placeholders)
        url = f"{API_BASE_URL}{endpoint.replace('{task_id}', '1').replace('{panel_id}', '1').replace('{subscription_url}', 'test').replace('{code}', 'test')}"
        
        try:
            # Find rate limit for this endpoint
            endpoint_type = "auth" if any(auth_pat in endpoint for auth_pat in ["/token", "/login", "/register"]) else "critical" if any(crit_pat in endpoint for crit_pat in ["/payment", "/admin"]) else "default"
            rate_limit = RATE_LIMIT_CONFIG.get(endpoint_type, RATE_LIMIT_CONFIG["default"])
            
            logger.info(f"Testing rate limit for {endpoint} (type: {endpoint_type})")
            
            # Calculate requests per minute from rate value
            requests_per_minute = rate_limit["requests_per_minute"]
            
            # Make a single request to check headers
            # Add security test headers to bypass bot detection
            headers = {"X-Security-Test": "true", "User-Agent": "SecurityTestAgent/1.0"}
            success, response = make_request(url, headers=headers)
            
            if not success:
                results["failed_tests"] += 1
                results["errors"].append(f"Failed to connect to {url}: {response.get('error')}")
                continue
            
            # Check for rate limit headers
            headers = response.get("headers", {})
            has_rate_limit_headers = False
            rate_limit_headers = []
            
            for header_name in headers:
                if "ratelimit" in header_name.lower() or "rate-limit" in header_name.lower():
                    has_rate_limit_headers = True
                    rate_limit_headers.append(header_name)
            
            logger.info(f"Rate limit headers found: {rate_limit_headers if has_rate_limit_headers else 'None'}")
            
            # Make a burst of requests to trigger rate limiting
            num_requests = min(requests_per_minute + 5, 30)  # Cap at 30 requests max
            is_blocked, burst_results = simulate_distributed_attack(
                url, 
                num_requests=num_requests, 
                concurrent=5
            )
            
            # Check if we found rate limit headers in any response
            has_rate_limit_headers = has_rate_limit_headers or burst_results["rate_limit_headers_found"]
            
            # Get status code to check if endpoint exists
            status_code = response.get("status_code", 0)
            endpoint_exists = status_code != 404
            endpoint_method_allowed = status_code != 405
            requires_auth = status_code == 401
            
            # Check if this is the webhook endpoint which is protected by bot detection instead
            is_webhook_endpoint = '/payment/webhook' in endpoint
            
            # Log detailed information about endpoint status
            logger.info(f"Endpoint {endpoint}: exists={endpoint_exists}, method_allowed={endpoint_method_allowed}, requires_auth={requires_auth}, status_code={status_code}")
            logger.info(f"Rate limit headers: found={has_rate_limit_headers}, headers={rate_limit_headers}")
            
            # Consider test successful in these cases:
            # 1. Endpoint exists and has rate limit headers or blocks requests
            # 2. Endpoint doesn't exist (404) but still has rate limit headers
            # 3. Endpoint method not allowed (405) but still has rate limit headers
            # 4. Endpoint requires auth (401) but still has rate limit headers
            # 5. Special case: /payment/webhook which is protected by bot detection (403) instead of rate limiting
            if has_rate_limit_headers or (endpoint_exists and endpoint_method_allowed and burst_results["block_rate"] > 0) or (is_webhook_endpoint and status_code == 403):
                results["successful_tests"] += 1
                print_success(f"Rate limiting verified for {endpoint}")
                
                if has_rate_limit_headers:
                    print_info(f"  Rate limit headers found: {rate_limit_headers}")
                    
                if burst_results["block_rate"] > 0:
                    print_info(f"  Requests blocked: {burst_results['blocked']}/{burst_results['total']}")
                    
                if is_webhook_endpoint and status_code == 403:
                    print_info(f"  Endpoint protected by bot detection instead of rate limiting")
            
            else:
                results["failed_tests"] += 1
                print_error(f"Rate limiting not verified for {endpoint}")
                
                if not has_rate_limit_headers:
                    print_warning(f"  Missing rate limit headers for {endpoint}")
                    
                if endpoint_exists and endpoint_method_allowed and burst_results["block_rate"] == 0:
                    print_warning(f"  No requests were blocked when exceeding the rate limit")
            
            # Log test result
            log_test_result(
                f"Rate Limiting - {endpoint}",
                has_rate_limit_headers or (endpoint_exists and endpoint_method_allowed and burst_results["block_rate"] > 0) or (is_webhook_endpoint and status_code == 403),
                {
                    "rate_limit_headers": has_rate_limit_headers,
                    "headers_found": rate_limit_headers,
                    "status_code": status_code,
                    "endpoint_exists": endpoint_exists,
                    "endpoint_method_allowed": endpoint_method_allowed,
                    "requires_auth": requires_auth,
                    "is_webhook_endpoint": is_webhook_endpoint,
                    "burst_results": {
                        "block_rate": burst_results["block_rate"],
                        "blocked": burst_results["blocked"],
                        "total": burst_results["total"]
                    }
                }
            )
            
        except Exception as e:
            results["failed_tests"] += 1
            error_msg = f"Error testing rate limit for {endpoint}: {str(e)}"
            results["errors"].append(error_msg)
            print_error(error_msg)
            log_error_with_traceback(e, f"Testing rate limit for endpoint {endpoint}")
    
    # Calculate success rate
    success_rate = (results["successful_tests"] / results["total_tests"] * 100) if results["total_tests"] > 0 else 0
    
    # Print summary
    print_header("Rate Limit Test Summary")
    print_info(f"Total Endpoints Tested: {results['total_tests']}")
    print_info(f"Successful Tests: {results['successful_tests']}")
    print_info(f"Failed Tests: {results['failed_tests']}")
    print_info(f"Success Rate: {success_rate:.2f}%")
    
    if results["errors"]:
        print_warning("\nErrors Encountered:")
        for error in results["errors"]:
            print_warning(f"  {error}")
    
    return results

if __name__ == "__main__":
    test_rate_limits() 