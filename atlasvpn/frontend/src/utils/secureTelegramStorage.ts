/**
 * Secure Telegram Storage Utility
 *
 * Provides secure, encrypted storage specifically for Telegram WebApp data
 * replacing insecure sessionStorage usage in telegram.ts
 *
 * <AUTHOR> Enhancement - Frontend Vulnerability Fix
 */

import { encryptedSessionStorage } from './encryptedStorage';

// Storage keys for Telegram data
const STORAGE_KEYS = {
  REFERRAL_CODE: 'referral_code',
  APP_DEACTIVATED_FLAG: 'telegramMiniAppDeactivated',
  SESSION_STATE: 'telegramMiniAppState',
  TELEGRAM_INIT_DATA: 'telegram_init_data',
  USER_STATE: 'telegram_user_state',
} as const;

// Types for Telegram storage
interface TelegramSessionState {
  lastHref: string;
  activeDashboardTab?: string;
  timestamp: number;
  // Add other relevant UI state
}

interface TelegramUserState {
  userId?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  photoUrl?: string;
  timestamp: number;
}

/**
 * Secure Telegram storage manager
 */
export class SecureTelegramStorage {
  /**
   * Store referral code securely
   */
  static async setReferralCode(code: string): Promise<void> {
    await encryptedSessionStorage.setItem(STORAGE_KEYS.REFERRAL_CODE, code, 24);
  }

  /**
   * Get referral code securely
   */
  static async getReferralCode(): Promise<string | null> {
    return await encryptedSessionStorage.getItem<string>(STORAGE_KEYS.REFERRAL_CODE);
  }

  /**
   * Set app deactivated flag securely
   */
  static async setAppDeactivatedFlag(deactivated: boolean): Promise<void> {
    if (deactivated) {
      await encryptedSessionStorage.setItem(STORAGE_KEYS.APP_DEACTIVATED_FLAG, 'true', 8);
    } else {
      encryptedSessionStorage.removeItem(STORAGE_KEYS.APP_DEACTIVATED_FLAG);
    }
  }

  /**
   * Check if app was deactivated
   */
  static async wasAppDeactivated(): Promise<boolean> {
    const flag = await encryptedSessionStorage.getItem<string>(STORAGE_KEYS.APP_DEACTIVATED_FLAG);
    return flag === 'true';
  }

  /**
   * Store telegram session state securely
   */
  static async persistSessionState(state: Partial<TelegramSessionState>): Promise<void> {
    const sessionState: TelegramSessionState = {
      lastHref: window.location.href,
      timestamp: Date.now(),
      ...state,
    };

    await encryptedSessionStorage.setItem(STORAGE_KEYS.SESSION_STATE, sessionState, 8);
    console.log('[SecureTelegramStorage] Session state persisted securely');
  }

  /**
   * Restore telegram session state securely
   */
  static async restoreSessionState(): Promise<TelegramSessionState | null> {
    const state = await encryptedSessionStorage.getItem<TelegramSessionState>(
      STORAGE_KEYS.SESSION_STATE
    );
    if (state) {
      console.log('[SecureTelegramStorage] Session state restored:', state.lastHref);
    }
    return state;
  }

  /**
   * Store telegram init data securely
   */
  static async setInitData(initData: string): Promise<void> {
    await encryptedSessionStorage.setItem(STORAGE_KEYS.TELEGRAM_INIT_DATA, initData, 24);
  }

  /**
   * Get telegram init data securely
   */
  static async getInitData(): Promise<string | null> {
    return await encryptedSessionStorage.getItem<string>(STORAGE_KEYS.TELEGRAM_INIT_DATA);
  }

  /**
   * Store telegram user state securely
   */
  static async setUserState(userState: Partial<TelegramUserState>): Promise<void> {
    const fullUserState: TelegramUserState = {
      timestamp: Date.now(),
      ...userState,
    };

    await encryptedSessionStorage.setItem(STORAGE_KEYS.USER_STATE, fullUserState, 24);
  }

  /**
   * Get telegram user state securely
   */
  static async getUserState(): Promise<TelegramUserState | null> {
    return await encryptedSessionStorage.getItem<TelegramUserState>(STORAGE_KEYS.USER_STATE);
  }

  /**
   * Clear all telegram session data
   */
  static clearAll(): void {
    Object.values(STORAGE_KEYS).forEach(key => {
      encryptedSessionStorage.removeItem(key);
    });
    console.log('[SecureTelegramStorage] All telegram data cleared');
  }

  /**
   * Clear session state specifically (for restoration cleanup)
   */
  static clearSessionState(): void {
    encryptedSessionStorage.removeItem(STORAGE_KEYS.SESSION_STATE);
    encryptedSessionStorage.removeItem(STORAGE_KEYS.APP_DEACTIVATED_FLAG);
  }

  /**
   * Check if any telegram data exists
   */
  static async hasAnyData(): Promise<boolean> {
    for (const key of Object.values(STORAGE_KEYS)) {
      if (await encryptedSessionStorage.hasItem(key)) {
        return true;
      }
    }
    return false;
  }

  /**
   * Get storage statistics for debugging
   */
  static async getStorageStats(): Promise<{ [key: string]: any }> {
    const stats: { [key: string]: any } = {};

    for (const [name, key] of Object.entries(STORAGE_KEYS)) {
      const metadata = await encryptedSessionStorage.getItemMetadata(key);
      stats[name] = metadata
        ? {
            exists: true,
            timestamp: new Date(metadata.timestamp).toISOString(),
            expiresAt: metadata.expiresAt ? new Date(metadata.expiresAt).toISOString() : 'never',
          }
        : { exists: false };
    }

    return stats;
  }
}

// Export helper functions that match the original patterns
export const secureTelegramStorage = {
  // Referral code functions
  setReferralCode: SecureTelegramStorage.setReferralCode,
  getReferralCode: SecureTelegramStorage.getReferralCode,

  // App state functions
  setAppDeactivatedFlag: SecureTelegramStorage.setAppDeactivatedFlag,
  wasAppDeactivated: SecureTelegramStorage.wasAppDeactivated,

  // Session state functions
  persistSessionState: SecureTelegramStorage.persistSessionState,
  restoreSessionState: SecureTelegramStorage.restoreSessionState,
  clearSessionState: SecureTelegramStorage.clearSessionState,

  // User and init data functions
  setInitData: SecureTelegramStorage.setInitData,
  getInitData: SecureTelegramStorage.getInitData,
  setUserState: SecureTelegramStorage.setUserState,
  getUserState: SecureTelegramStorage.getUserState,

  // Utility functions
  clearAll: SecureTelegramStorage.clearAll,
  hasAnyData: SecureTelegramStorage.hasAnyData,
  getStorageStats: SecureTelegramStorage.getStorageStats,
};

export default SecureTelegramStorage;
