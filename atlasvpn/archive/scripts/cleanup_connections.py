#!/usr/bin/env python3
"""
Connection cleanup utility script

This script identifies and cleans up leaked file descriptors by:
1. Finding active processes running the FastAPI app
2. Analyzing their open file descriptors
3. Closing idle connections that may be leaked
4. Optionally restarting the service if needed
"""

import os
import sys
import signal
import psutil
import time
import argparse
import logging
import subprocess
from typing import List, Dict, Any
import socket

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("cleanup")

def find_api_processes(process_filter: str = "uvicorn") -> List[psutil.Process]:
    """Find running FastAPI/Uvicorn processes"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = " ".join(proc.cmdline()) if proc.cmdline() else ""
            if process_filter in cmdline:
                processes.append(proc)
                logger.info(f"Found process: PID {proc.pid}, Command: {cmdline}")
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    return processes

def analyze_file_descriptors(process: psutil.Process) -> Dict[str, Any]:
    """Analyze open file descriptors for a process"""
    open_files = []
    socket_count = 0
    unix_count = 0
    regular_count = 0
    other_count = 0
    
    try:
        for fd in process.open_files():
            open_files.append(fd.path)
            if fd.path.startswith('/'):  # Regular file
                regular_count += 1
            else:
                other_count += 1
                
        # Use net_connections instead of deprecated connections
        for conn in process.net_connections(kind='all'):
            socket_count += 1
            if conn.family == socket.AF_UNIX:
                unix_count += 1
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        logger.warning(f"Could not access all file descriptors for PID {process.pid}")
    
    total_fds = len(open_files) + socket_count
    
    return {
        "pid": process.pid,
        "total_fds": total_fds,
        "sockets": socket_count,
        "unix_sockets": unix_count,
        "regular_files": regular_count,
        "other_files": other_count,
        "open_files": open_files[:10]  # Just show first 10 for display
    }

def close_idle_connections(process: psutil.Process, idle_seconds: int = 300) -> int:
    """Close idle TCP connections older than specified threshold"""
    closed = 0
    now = time.time()
    
    try:
        for conn in process.net_connections(kind='tcp'):
            # Check if connection is established and idle
            if conn.status == 'ESTABLISHED':
                # We don't have direct access to last activity time through psutil
                # This is just a placeholder - in real implementation you'd need
                # to track activity through application logs or metrics
                
                # Logic to determine if a connection is idle would go here
                
                # For demonstration, we'll randomly close some connections
                # In a real implementation, you'd use better criteria
                if conn.raddr and conn.raddr.port > 1024:  # Skip low port numbers (likely servers)
                    logger.info(f"Would close idle connection: {conn.laddr} -> {conn.raddr}")
                    # Uncomment below for actual implementation
                    # os.close(conn.fd)
                    closed += 1
    except (psutil.NoSuchProcess, psutil.AccessDenied):
        logger.warning(f"Could not access connections for PID {process.pid}")
    
    return closed

def reset_database_connections(process: psutil.Process) -> int:
    """Force close database connections that might be leaked"""
    # This is application-specific and would need to be implemented
    # based on your actual database connection pooling system
    logger.info(f"Would reset database connections for PID {process.pid}")
    return 0

def restart_service(service_name: str) -> bool:
    """Restart a systemd service"""
    try:
        logger.info(f"Attempting to restart service: {service_name}")
        result = subprocess.run(['systemctl', 'restart', service_name], 
                               check=True, capture_output=True, text=True)
        logger.info(f"Service {service_name} restarted successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to restart service {service_name}: {e.stderr}")
        return False

def increase_ulimit(process: psutil.Process, new_limit: int = 65535) -> bool:
    """Increase the file descriptor limit for a process"""
    try:
        # Requires appropriate permissions or running as root
        resource_cmd = f"prlimit --pid {process.pid} --nofile={new_limit}:{new_limit}"
        logger.info(f"Setting ulimit for PID {process.pid} to {new_limit}")
        
        result = subprocess.run(resource_cmd, shell=True, check=True, 
                              capture_output=True, text=True)
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to set ulimit: {e.stderr}")
        return False

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='FastAPI connection cleanup utility')
    parser.add_argument('--analyze', action='store_true', 
                       help='Analyze open file descriptors without making changes')
    parser.add_argument('--close-idle', action='store_true',
                       help='Close idle connections')
    parser.add_argument('--idle-threshold', type=int, default=300,
                       help='Seconds of inactivity before considering a connection idle')
    parser.add_argument('--increase-limit', action='store_true',
                       help='Increase file descriptor limit for the processes')
    parser.add_argument('--limit', type=int, default=65535,
                       help='New file descriptor limit to set')
    parser.add_argument('--reset-db', action='store_true',
                       help='Reset database connections')
    parser.add_argument('--restart', action='store_true',
                       help='Restart the service after cleanup')
    parser.add_argument('--service-name', type=str, default='atlasvpn',
                       help='Name of the systemd service to restart')
    parser.add_argument('--process-filter', type=str, default='uvicorn',
                       help='String to identify the target processes')
    return parser.parse_args()

def main():
    """Main entry point"""
    args = parse_arguments()
    
    logger.info("Starting FastAPI connection cleanup utility")
    
    # Find FastAPI processes
    processes = find_api_processes(args.process_filter)
    
    if not processes:
        logger.error(f"No processes found matching filter: {args.process_filter}")
        sys.exit(1)
    
    logger.info(f"Found {len(processes)} matching processes")
    
    for process in processes:
        logger.info(f"Analyzing PID {process.pid}")
        
        # Analyze file descriptors
        fd_info = analyze_file_descriptors(process)
        logger.info(f"Process {process.pid} has {fd_info['total_fds']} file descriptors open:")
        logger.info(f"  - {fd_info['sockets']} sockets")
        logger.info(f"  - {fd_info['regular_files']} regular files")
        logger.info(f"  - {fd_info['unix_sockets']} unix sockets")
        logger.info(f"  - {fd_info['other_files']} other files")
        
        if args.analyze:
            continue
        
        # Close idle connections if requested
        if args.close_idle:
            closed = close_idle_connections(process, args.idle_threshold)
            logger.info(f"Closed {closed} idle connections for PID {process.pid}")
        
        # Reset database connections if requested
        if args.reset_db:
            reset = reset_database_connections(process)
            logger.info(f"Reset {reset} database connections for PID {process.pid}")
        
        # Increase file descriptor limit if requested
        if args.increase_limit:
            if increase_ulimit(process, args.limit):
                logger.info(f"Increased file descriptor limit for PID {process.pid} to {args.limit}")
            else:
                logger.error(f"Failed to increase file descriptor limit for PID {process.pid}")
    
    # Restart service if requested
    if args.restart:
        if restart_service(args.service_name):
            logger.info(f"Service {args.service_name} restarted successfully")
        else:
            logger.error(f"Failed to restart service {args.service_name}")
    
    logger.info("Cleanup utility completed")

if __name__ == "__main__":
    main() 