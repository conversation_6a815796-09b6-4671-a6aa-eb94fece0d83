from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, and_, or_
from sqlalchemy.orm import selectinload
from typing import Dict, Optional, List, Any
from datetime import datetime, timedelta
import logging

from database import get_async_db
from models import (
    User, ChatMessage, ChatConversation, OnlineUser
)
from auth import get_current_admin

router = APIRouter(
    prefix="/chat",
    tags=["Admin - Chat"]
)

logger = logging.getLogger(__name__)

# Message Monitoring
@router.get("/messages")
async def list_chat_messages(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 100,
    user_id: Optional[int] = None,
    is_public: Optional[bool] = None,
    search: Optional[str] = None,
    hours: int = 24
):
    """Get chat messages with filtering"""
    try:
        # Build base query
        stmt = select(ChatMessage).options(
            selectinload(ChatMessage.sender),
            selectinload(ChatMessage.receiver)
        )
        
        # Time filter
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        stmt = stmt.filter(ChatMessage.created_at >= cutoff_time)
        
        # Apply filters
        if user_id:
            stmt = stmt.filter(
                or_(
                    ChatMessage.sender_id == user_id,
                    ChatMessage.receiver_id == user_id
                )
            )
        
        if is_public is not None:
            stmt = stmt.filter(ChatMessage.is_public == is_public)
        
        if search:
            stmt = stmt.filter(ChatMessage.content.ilike(f"%{search}%"))
        
        # Order and paginate
        stmt = stmt.order_by(ChatMessage.created_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(stmt)
        messages = result.scalars().all()
        
        # Format response
        formatted_messages = []
        for msg in messages:
            formatted_messages.append({
                "id": msg.id,
                "content": msg.content,
                "created_at": msg.created_at.isoformat(),
                "is_public": msg.is_public,
                "sender": {
                    "id": msg.sender.id,
                    "username": msg.sender.username
                } if msg.sender else None,
                "receiver": {
                    "id": msg.receiver.id,
                    "username": msg.receiver.username
                } if msg.receiver else None,
                "conversation_id": msg.conversation_id
            })
        
        return {
            "messages": formatted_messages,
            "total_count": len(formatted_messages),
            "filters_applied": {
                "user_id": user_id,
                "is_public": is_public,
                "search": search,
                "hours": hours
            }
        }
        
    except Exception as e:
        logger.error(f"Error listing chat messages: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/conversations")
async def list_conversations(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 50
):
    """Get all chat conversations"""
    try:
        # Get conversations with participants
        stmt = select(ChatConversation).options(
            selectinload(ChatConversation.participant1),
            selectinload(ChatConversation.participant2),
            selectinload(ChatConversation.messages)
        ).order_by(ChatConversation.updated_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(stmt)
        conversations = result.scalars().all()
        
        formatted_conversations = []
        for conv in conversations:
            # Get latest message
            latest_message = None
            if conv.messages:
                latest_msg = max(conv.messages, key=lambda x: x.created_at)
                latest_message = {
                    "content": latest_msg.content[:100] + "..." if len(latest_msg.content) > 100 else latest_msg.content,
                    "created_at": latest_msg.created_at.isoformat(),
                    "sender_username": latest_msg.sender.username if latest_msg.sender else "Unknown"
                }
            
            formatted_conversations.append({
                "id": conv.id,
                "participant1": {
                    "id": conv.participant1.id,
                    "username": conv.participant1.username
                } if conv.participant1 else None,
                "participant2": {
                    "id": conv.participant2.id,
                    "username": conv.participant2.username
                } if conv.participant2 else None,
                "message_count": len(conv.messages),
                "created_at": conv.created_at.isoformat(),
                "updated_at": conv.updated_at.isoformat(),
                "latest_message": latest_message
            })
        
        return formatted_conversations
        
    except Exception as e:
        logger.error(f"Error listing conversations: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/conversations/{conversation_id}/messages")
async def get_conversation_messages(
    conversation_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    skip: int = 0,
    limit: int = 100
):
    """Get messages from a specific conversation"""
    try:
        # Verify conversation exists
        conv_stmt = select(ChatConversation).filter(ChatConversation.id == conversation_id)
        conv_result = await db.execute(conv_stmt)
        conversation = conv_result.scalar_one_or_none()
        
        if not conversation:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        # Get messages
        stmt = select(ChatMessage).options(
            selectinload(ChatMessage.sender),
            selectinload(ChatMessage.receiver)
        ).filter(
            ChatMessage.conversation_id == conversation_id
        ).order_by(ChatMessage.created_at.desc()).offset(skip).limit(limit)
        
        result = await db.execute(stmt)
        messages = result.scalars().all()
        
        # Format messages
        formatted_messages = []
        for msg in messages:
            formatted_messages.append({
                "id": msg.id,
                "content": msg.content,
                "created_at": msg.created_at.isoformat(),
                "sender": {
                    "id": msg.sender.id,
                    "username": msg.sender.username
                } if msg.sender else None
            })
        
        return {
            "conversation_id": conversation_id,
            "messages": formatted_messages,
            "total_messages": len(formatted_messages)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation messages: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/messages/{message_id}")
async def delete_message(
    message_id: int,
    reason: str = Query(..., description="Reason for deletion"),
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Delete a chat message"""
    try:
        # Get message
        stmt = select(ChatMessage).filter(ChatMessage.id == message_id)
        result = await db.execute(stmt)
        message = result.scalar_one_or_none()
        
        if not message:
            raise HTTPException(status_code=404, detail="Message not found")
        
        # Store message info for logging
        message_info = {
            "id": message.id,
            "content": message.content,
            "sender_id": message.sender_id,
            "created_at": message.created_at.isoformat()
        }
        
        # Delete message
        await db.delete(message)
        await db.commit()
        
        logger.info(f"Admin {current_user.username} deleted message {message_id}: {reason}")
        
        return {
            "message": "Message deleted successfully",
            "deleted_message": message_info,
            "reason": reason
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error deleting message: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Online Users Management
@router.get("/online-users")
async def get_online_users(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    minutes: int = 5
):
    """Get currently online users"""
    try:
        # Get online users from the last X minutes
        cutoff_time = datetime.utcnow() - timedelta(minutes=minutes)
        
        stmt = select(OnlineUser).options(
            selectinload(OnlineUser.user)
        ).filter(
            OnlineUser.last_active_at >= cutoff_time
        ).order_by(OnlineUser.last_active_at.desc())
        
        result = await db.execute(stmt)
        online_users = result.scalars().all()
        
        # Group by user (in case of multiple connections)
        user_activity = {}
        for ou in online_users:
            if ou.user_id not in user_activity:
                user_activity[ou.user_id] = {
                    "user": {
                        "id": ou.user.id,
                        "username": ou.user.username,
                        "first_name": ou.user.first_name,
                        "last_name": ou.user.last_name
                    },
                    "connections": [],
                    "latest_activity": ou.last_active_at
                }
            
            user_activity[ou.user_id]["connections"].append({
                "connection_id": ou.connection_id,
                "last_active_at": ou.last_active_at.isoformat()
            })
            
            # Update latest activity
            if ou.last_active_at > user_activity[ou.user_id]["latest_activity"]:
                user_activity[ou.user_id]["latest_activity"] = ou.last_active_at
        
        # Convert to list and sort by latest activity
        online_users_list = list(user_activity.values())
        online_users_list.sort(key=lambda x: x["latest_activity"], reverse=True)
        
        # Format datetime for JSON
        for user_data in online_users_list:
            user_data["latest_activity"] = user_data["latest_activity"].isoformat()
        
        return {
            "online_users": online_users_list,
            "total_online": len(online_users_list),
            "total_connections": len(online_users),
            "cutoff_minutes": minutes
        }
        
    except Exception as e:
        logger.error(f"Error getting online users: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/broadcast")
async def broadcast_message(
    message_data: dict,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin)
):
    """Broadcast a message to all users or specific users"""
    try:
        content = message_data.get("content")
        target_user_ids = message_data.get("target_user_ids", [])  # Empty list means all users
        
        if not content:
            raise HTTPException(status_code=400, detail="Message content is required")
        
        # Get target users
        if target_user_ids:
            # Specific users
            users_stmt = select(User).filter(User.id.in_(target_user_ids))
        else:
            # All active users
            users_stmt = select(User).filter(User.is_active == True)
        
        users_result = await db.execute(users_stmt)
        target_users = users_result.scalars().all()
        
        if not target_users:
            raise HTTPException(status_code=404, detail="No target users found")
        
        # Create broadcast messages
        messages_created = []
        for user in target_users:
            # Create a public message from admin
            broadcast_message = ChatMessage(
                sender_id=current_user.id,
                receiver_id=user.id,
                content=f"[ADMIN BROADCAST] {content}",
                is_public=False,  # Private message to each user
                created_at=datetime.utcnow()
            )
            db.add(broadcast_message)
            messages_created.append({
                "user_id": user.id,
                "username": user.username
            })
        
        await db.commit()
        
        logger.info(f"Admin {current_user.username} broadcasted message to {len(target_users)} users")
        
        return {
            "message": "Broadcast sent successfully",
            "content": content,
            "recipients_count": len(target_users),
            "recipients": messages_created[:10]  # Show first 10 recipients
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"Error broadcasting message: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Chat Statistics
@router.get("/stats")
async def get_chat_stats(
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 7
):
    """Get chat activity statistics"""
    try:
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Message statistics
        total_messages_stmt = select(func.count(ChatMessage.id)).filter(
            ChatMessage.created_at >= start_date
        )
        total_messages_result = await db.execute(total_messages_stmt)
        total_messages = total_messages_result.scalar()
        
        public_messages_stmt = select(func.count(ChatMessage.id)).filter(
            ChatMessage.created_at >= start_date,
            ChatMessage.is_public == True
        )
        public_messages_result = await db.execute(public_messages_stmt)
        public_messages = public_messages_result.scalar()
        
        private_messages = total_messages - public_messages
        
        # Active users (users who sent messages)
        active_users_stmt = select(func.count(func.distinct(ChatMessage.sender_id))).filter(
            ChatMessage.created_at >= start_date
        )
        active_users_result = await db.execute(active_users_stmt)
        active_users = active_users_result.scalar()
        
        # Daily message counts
        daily_messages_stmt = select(
            func.date(ChatMessage.created_at).label('date'),
            func.count(ChatMessage.id).label('message_count')
        ).filter(
            ChatMessage.created_at >= start_date
        ).group_by(
            func.date(ChatMessage.created_at)
        ).order_by('date')
        
        daily_messages_result = await db.execute(daily_messages_stmt)
        daily_messages = daily_messages_result.all()
        
        # Top active users
        top_users_stmt = select(
            ChatMessage.sender_id,
            func.count(ChatMessage.id).label('message_count')
        ).join(
            User, ChatMessage.sender_id == User.id
        ).filter(
            ChatMessage.created_at >= start_date
        ).group_by(
            ChatMessage.sender_id
        ).order_by(
            func.count(ChatMessage.id).desc()
        ).limit(10)
        
        top_users_result = await db.execute(top_users_stmt)
        top_users_data = top_users_result.all()
        
        # Get user details for top users
        top_users = []
        if top_users_data:
            user_ids = [row.sender_id for row in top_users_data]
            users_stmt = select(User).filter(User.id.in_(user_ids))
            users_result = await db.execute(users_stmt)
            users = {user.id: user for user in users_result.scalars().all()}
            
            for row in top_users_data:
                user = users.get(row.sender_id)
                if user:
                    top_users.append({
                        "user_id": user.id,
                        "username": user.username,
                        "message_count": row.message_count
                    })
        
        # Current online users count
        current_online_stmt = select(func.count(func.distinct(OnlineUser.user_id))).filter(
            OnlineUser.last_active_at >= datetime.utcnow() - timedelta(minutes=5)
        )
        current_online_result = await db.execute(current_online_stmt)
        current_online = current_online_result.scalar()
        
        return {
            "period_days": days,
            "message_stats": {
                "total_messages": total_messages,
                "public_messages": public_messages,
                "private_messages": private_messages,
                "active_users": active_users
            },
            "daily_activity": [
                {
                    "date": row.date.isoformat(),
                    "message_count": row.message_count
                }
                for row in daily_messages
            ],
            "top_active_users": top_users,
            "current_online": current_online
        }
        
    except Exception as e:
        logger.error(f"Error getting chat stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/user-activity/{user_id}")
async def get_user_chat_activity(
    user_id: int,
    db: AsyncSession = Depends(get_async_db),
    current_user: User = Depends(get_current_admin),
    days: int = 30
):
    """Get detailed chat activity for a specific user"""
    try:
        # Verify user exists
        user_stmt = select(User).filter(User.id == user_id)
        user_result = await db.execute(user_stmt)
        user = user_result.scalar_one_or_none()
        
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=days)
        
        # Message statistics
        sent_messages_stmt = select(func.count(ChatMessage.id)).filter(
            ChatMessage.sender_id == user_id,
            ChatMessage.created_at >= start_date
        )
        sent_messages_result = await db.execute(sent_messages_stmt)
        sent_messages = sent_messages_result.scalar()
        
        received_messages_stmt = select(func.count(ChatMessage.id)).filter(
            ChatMessage.receiver_id == user_id,
            ChatMessage.created_at >= start_date
        )
        received_messages_result = await db.execute(received_messages_stmt)
        received_messages = received_messages_result.scalar()
        
        # Recent messages
        recent_messages_stmt = select(ChatMessage).options(
            selectinload(ChatMessage.receiver)
        ).filter(
            ChatMessage.sender_id == user_id,
            ChatMessage.created_at >= start_date
        ).order_by(ChatMessage.created_at.desc()).limit(20)
        
        recent_messages_result = await db.execute(recent_messages_stmt)
        recent_messages = recent_messages_result.scalars().all()
        
        # Format recent messages
        formatted_messages = []
        for msg in recent_messages:
            formatted_messages.append({
                "id": msg.id,
                "content": msg.content[:100] + "..." if len(msg.content) > 100 else msg.content,
                "created_at": msg.created_at.isoformat(),
                "is_public": msg.is_public,
                "receiver": {
                    "id": msg.receiver.id,
                    "username": msg.receiver.username
                } if msg.receiver else None
            })
        
        return {
            "user": {
                "id": user.id,
                "username": user.username
            },
            "period_days": days,
            "message_stats": {
                "sent_messages": sent_messages,
                "received_messages": received_messages,
                "total_messages": sent_messages + received_messages
            },
            "recent_messages": formatted_messages
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user chat activity: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e)) 