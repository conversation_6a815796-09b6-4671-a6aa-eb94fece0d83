#!/usr/bin/env python
# Task System Performance Test
# This script tests task system performance with high load

import os
import sys
import json
import random
import asyncio
import logging
import time
import statistics
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from contextlib import contextmanager

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import get_db, init_db
from models import (
    User, Task, TaskCompletion, TaskStatus,
    TaskType, RewardType, TaskVerification
)
from services.task_service import TaskService
from services.verification_service import VerificationService
from database import SessionLocal

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('task_performance.log')
    ]
)
logger = logging.getLogger("TaskPerformance")

# Mock Request class
class MockRequest:
    class Client:
        def __init__(self, host: str):
            self.host = host
    
    def __init__(self, host: str = "127.0.0.1", user_agent: str = "TaskPerformance/1.0"):
        self.client = self.Client(host)
        self.headers = {"user-agent": user_agent}


class TaskPerformanceTester:
    """Test task system performance under load"""
    
    def __init__(self, db):
        self.db = db
        self.task_service = TaskService(db)
        self.verification_service = VerificationService(db)
        self.tasks = {}  # Cache for test tasks
        self.users = []  # Cache for test users
        self.metrics = {
            "start_task": {
                "times": [],
                "success_count": 0,
                "error_count": 0
            },
            "verify_task": {
                "times": [],
                "success_count": 0,
                "error_count": 0
            },
            "claim_reward": {
                "times": [],
                "success_count": 0,
                "error_count": 0
            },
            "full_flow": {
                "times": [],
                "success_count": 0,
                "error_count": 0
            }
        }
        
    async def setup_test_users(self, count: int) -> None:
        """Create multiple test users"""
        logger.info(f"Setting up {count} test users...")
        
        for i in range(count):
            username = f"perf_user_{random.randint(10000, 99999)}"
            
            # Create new test user
            user = User(
                username=username,
                hashed_password="test_password",
                wallet_balance=0.0,
                referral_code=f"REF{random.randint(1000, 9999)}"
            )
            self.db.add(user)
            
            if i % 100 == 0:
                self.db.commit()
                
        self.db.commit()
        
        # Load users
        self.users = self.db.query(User).filter(
            User.username.like("perf_user_%")
        ).limit(count).all()
        
        logger.info(f"Created {len(self.users)} test users")

    async def setup_test_task(self, task_type: TaskType) -> Task:
        """Create or get a test task for performance testing"""
        if task_type in self.tasks:
            return self.tasks[task_type]
            
        # Check if test task already exists
        task_name = f"Perf Test {task_type.value}"
        existing_task = self.db.query(Task).filter(
            Task.name == task_name,
            Task.type == task_type
        ).first()
        
        if existing_task:
            self.tasks[task_type] = existing_task
            return existing_task
            
        # Create new test task
        task = Task(
            name=task_name,
            description=f"Performance test task for {task_type.value}",
            type=task_type,
            reward_type=RewardType.WALLET_BONUS,
            reward_value=1.0,  # Small reward for performance testing
            target_value=1,
            is_active=True
        )
        
        # Set task-specific attributes
        if task_type == TaskType.DAILY_CHECKIN:
            task.cycle_length = 7
            task.daily_rewards = json.dumps([5, 7, 10, 12, 15, 20, 25])
            task.cycle_bonus_reward = 50
            task.reset_streak_after_hours = 48
            
        elif task_type == TaskType.TELEGRAM_CHANNEL:
            task.platform_id = "performance_channel"
            task.platform_url = "https://t.me/performance_channel"
            
        elif task_type == TaskType.YOUTUBE_VIEW:
            task.platform_url = "https://youtube.com/watch?v=performance_test"
            task.verify_key = "TEST123"
            
        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)
        
        self.tasks[task_type] = task
        logger.info(f"Created performance test task: {task_name}")
        return task

    async def time_operation(self, operation_name: str, coro) -> Tuple[bool, float]:
        """Time an async operation and record metrics"""
        start_time = time.time()
        
        try:
            result = await coro
            success = True
        except Exception as e:
            logger.error(f"Error in {operation_name}: {str(e)}")
            success = False
            
        elapsed = time.time() - start_time
        
        # Record metrics
        self.metrics[operation_name]["times"].append(elapsed)
        if success:
            self.metrics[operation_name]["success_count"] += 1
        else:
            self.metrics[operation_name]["error_count"] += 1
            
        return success, elapsed

    async def run_task_flow(self, user: User, task: Task) -> Tuple[bool, float]:
        """Run complete task flow and measure performance"""
        mock_request = MockRequest()
        start_time = time.time()
        success = False
        
        try:
            # Step 1: Start task
            start_success, _ = await self.time_operation(
                "start_task", 
                self.task_service.start_task(task.id, user.id)
            )
            
            if not start_success:
                return False, time.time() - start_time
                
            # Step 2: Verify task
            verification_data = self._get_verification_data_for_task(task)
            verify_success, _ = await self.time_operation(
                "verify_task",
                self.verification_service.verify_task(
                    task_id=task.id,
                    user_id=user.id,
                    verification_data=verification_data,
                    request=mock_request
                )
            )
            
            if not verify_success:
                return False, time.time() - start_time
                
            # Step 3: Force claim availability
            completion = self.db.query(TaskCompletion).filter(
                TaskCompletion.task_id == task.id,
                TaskCompletion.user_id == user.id
            ).first()
            
            if completion:
                completion.claim_available_at = datetime.utcnow()
                self.db.commit()
                
            # Step 4: Claim reward
            claim_success, _ = await self.time_operation(
                "claim_reward",
                self.task_service.claim_reward(task.id, user.id)
            )
            
            success = claim_success
            
        except Exception as e:
            logger.error(f"Error in task flow: {str(e)}")
            success = False
            
        elapsed = time.time() - start_time
        
        # Record full flow metrics
        self.metrics["full_flow"]["times"].append(elapsed)
        if success:
            self.metrics["full_flow"]["success_count"] += 1
        else:
            self.metrics["full_flow"]["error_count"] += 1
            
        return success, elapsed

    def _get_verification_data_for_task(self, task: Task) -> Dict:
        """Generate appropriate verification data based on task type"""
        if task.type == TaskType.YOUTUBE_VIEW:
            return {"verification_code": "TEST123"}
        elif task.type == TaskType.TELEGRAM_CHANNEL:
            return {"channel_id": task.platform_id}
        elif task.type == TaskType.INSTAGRAM_FOLLOW:
            return {"username": "test_user"}
        elif task.type == TaskType.DAILY_CHECKIN:
            return {"check_in": True}
        else:
            return {"test": "data"}
            
    async def run_concurrent_flows(self, task_type: TaskType, concurrency: int) -> Dict:
        """Run multiple task flows concurrently"""
        if len(self.users) < concurrency:
            logger.warning(f"Not enough users ({len(self.users)}) for requested concurrency ({concurrency})")
            concurrency = len(self.users)
            
        task = await self.setup_test_task(task_type)
        
        logger.info(f"Running {concurrency} concurrent task flows for {task.name}")
        
        # Create a list of users to use for this test (random sample)
        test_users = random.sample(self.users, concurrency)
        
        # Reset metrics for this test
        self.metrics = {
            "start_task": {"times": [], "success_count": 0, "error_count": 0},
            "verify_task": {"times": [], "success_count": 0, "error_count": 0},
            "claim_reward": {"times": [], "success_count": 0, "error_count": 0},
            "full_flow": {"times": [], "success_count": 0, "error_count": 0}
        }
        
        # Run flows concurrently
        start_time = time.time()
        tasks = [self.run_task_flow(user, task) for user in test_users]
        results = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Calculate success rate
        success_count = sum(1 for success, _ in results if success)
        success_rate = (success_count / concurrency) * 100 if concurrency > 0 else 0
        
        # Calculate statistics
        flow_times = [elapsed for _, elapsed in results]
        
        stats = {
            "task_type": task_type.value,
            "concurrency": concurrency,
            "total_time": total_time,
            "throughput": concurrency / total_time,
            "success_rate": success_rate,
            "flow_time_avg": statistics.mean(flow_times) if flow_times else 0,
            "flow_time_min": min(flow_times) if flow_times else 0,
            "flow_time_max": max(flow_times) if flow_times else 0,
            "flow_time_median": statistics.median(flow_times) if flow_times else 0,
            "flow_time_stdev": statistics.stdev(flow_times) if len(flow_times) > 1 else 0,
            "step_metrics": {
                "start_task": self._calculate_step_metrics("start_task"),
                "verify_task": self._calculate_step_metrics("verify_task"),
                "claim_reward": self._calculate_step_metrics("claim_reward")
            }
        }
        
        logger.info(f"Completed {concurrency} flows in {total_time:.2f}s - " +
                   f"Success rate: {success_rate:.2f}% - " +
                   f"Avg flow time: {stats['flow_time_avg']:.3f}s")
                   
        return stats
        
    def _calculate_step_metrics(self, step_name: str) -> Dict:
        """Calculate metrics for a single step"""
        times = self.metrics[step_name]["times"]
        
        if not times:
            return {
                "count": 0,
                "success_count": 0,
                "error_count": 0,
                "success_rate": 0,
                "avg_time": 0,
                "min_time": 0,
                "max_time": 0,
                "median_time": 0,
                "stdev_time": 0
            }
            
        success_count = self.metrics[step_name]["success_count"]
        error_count = self.metrics[step_name]["error_count"]
        total_count = success_count + error_count
        success_rate = (success_count / total_count) * 100 if total_count > 0 else 0
        
        return {
            "count": total_count,
            "success_count": success_count,
            "error_count": error_count,
            "success_rate": success_rate,
            "avg_time": statistics.mean(times),
            "min_time": min(times),
            "max_time": max(times),
            "median_time": statistics.median(times),
            "stdev_time": statistics.stdev(times) if len(times) > 1 else 0
        }
        
    async def run_scalability_test(self, task_type: TaskType, max_concurrency: int) -> Dict:
        """Run scalability test with increasing concurrency"""
        results = []
        
        # Test with different concurrency levels
        concurrency_levels = [
            1, 5, 10, 
            25, 50, 100,
            250, 500, 1000
        ]
        
        # Limit to max_concurrency
        concurrency_levels = [c for c in concurrency_levels if c <= max_concurrency]
        
        logger.info(f"Running scalability test for {task_type.value} with concurrency levels: {concurrency_levels}")
        
        for concurrency in concurrency_levels:
            logger.info(f"Testing with concurrency = {concurrency}")
            result = await self.run_concurrent_flows(task_type, concurrency)
            results.append(result)
            
            # Short pause between tests
            await asyncio.sleep(1)
            
        return {
            "task_type": task_type.value,
            "test_time": datetime.utcnow().isoformat(),
            "results": results
        }
        
    def generate_report(self, results: List[Dict], report_path: Optional[str] = None) -> None:
        """Generate performance test report"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = report_path or f"test_reports/task_performance_{timestamp}.json"
        
        # Create report directory if it doesn't exist
        os.makedirs(os.path.dirname(report_path), exist_ok=True)
        
        # Create report data
        report = {
            "timestamp": timestamp,
            "test_results": results,
            "summary": self._generate_summary(results)
        }
        
        # Write report to file
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
            
        logger.info(f"Performance test report generated: {report_path}")
        
    def _generate_summary(self, results: List[Dict]) -> Dict:
        """Generate summary of performance test results"""
        task_types = set(result["task_type"] for result in results)
        summary = {}
        
        for task_type in task_types:
            task_results = [r for r in results if r["task_type"] == task_type]
            
            # Extract concurrency levels and throughputs
            concurrency_levels = []
            throughputs = []
            success_rates = []
            avg_times = []
            
            for result in task_results:
                for r in result["results"]:
                    concurrency_levels.append(r["concurrency"])
                    throughputs.append(r["throughput"])
                    success_rates.append(r["success_rate"])
                    avg_times.append(r["flow_time_avg"])
            
            if not concurrency_levels:
                continue
                
            # Find optimal concurrency (highest throughput)
            max_throughput_idx = throughputs.index(max(throughputs))
            optimal_concurrency = concurrency_levels[max_throughput_idx]
            
            summary[task_type] = {
                "optimal_concurrency": optimal_concurrency,
                "max_throughput": max(throughputs),
                "min_success_rate": min(success_rates),
                "max_success_rate": max(success_rates),
                "min_avg_time": min(avg_times),
                "max_avg_time": max(avg_times),
                "throughput_by_concurrency": {
                    c: t for c, t in zip(concurrency_levels, throughputs)
                }
            }
            
        return summary


async def main():
    # Initialize database
    logger.info("Initializing database...")
    init_db()
    
    # Use a with statement to get a database session
    @contextmanager
    def get_db_session():
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()
            
    with get_db_session() as db:
        # Initialize performance tester
        tester = TaskPerformanceTester(db)
        
        # Number of test users to create
        num_users = 100  # Reduced for easier testing
        
        # Setup test users
        await tester.setup_test_users(num_users)
        
        # Task types to test
        task_types = [
            TaskType.DAILY_CHECKIN,
            TaskType.YOUTUBE_VIEW,
            TaskType.TELEGRAM_CHANNEL
        ]
        
        # Maximum concurrency to test
        max_concurrency = 500
        
        # Run scalability tests
        test_results = []
        for task_type in task_types:
            logger.info(f"Running scalability test for {task_type.value}")
            result = await tester.run_scalability_test(task_type, max_concurrency)
            test_results.append(result)
        
        # Generate report
        tester.generate_report(test_results)
        
        logger.info("Performance testing completed")


if __name__ == "__main__":
    # Set test mode environment variable
    os.environ["TEST_MODE"] = "true"
    
    # Run the async main function
    asyncio.run(main())