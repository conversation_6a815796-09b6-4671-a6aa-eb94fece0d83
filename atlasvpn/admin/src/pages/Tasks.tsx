import { useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { api } from '@/lib/api'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Switch } from '@/components/ui/switch'
import { 
  CheckSquare, 
  Plus, 
  Edit, 
  Search, 
  DollarSign, 
  Clock, 
  Package,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  RefreshCw
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Task {
  id: number
  title: string
  description: string
  reward: number
  task_type: string
  verification_method: string
  verification_data?: any
  is_active: boolean
  created_at: string
  completion_count: number
  success_rate: number
}

interface TaskPack {
  id: number
  name: string
  description: string
  vpn_package_id: number
  vpn_package_name: string
  is_lifetime: boolean
  order_index: number
  is_active: boolean
  created_at: string
  task_count: number
  completion_count: number
  completion_rate: number
}

interface TaskVerification {
  id: number
  user_id: number
  username: string
  task_id: number
  task_title: string
  verification_data: any
  status: 'pending' | 'approved' | 'rejected'
  submitted_at: string
  reviewed_at?: string
  reviewed_by?: string
  notes?: string
}

interface DailyTaskStreak {
  id: number
  user_id: number
  username: string
  task_id: number
  task_title: string
  current_streak: number
  max_streak: number
  last_completion: string
  total_completions: number
}

interface TaskStats {
  total_tasks: number
  active_tasks: number
  total_packs: number
  active_packs: number
  pending_verifications: number
  total_completions: number
  total_rewards_paid: number
  avg_completion_rate: number
}

interface CreateTaskData {
  title: string
  description: string
  reward: number
  task_type: string
  verification_method: string
  verification_data?: any
}

interface CreateTaskPackData {
  name: string
  description: string
  vpn_package_id: number
  is_lifetime: boolean
}

export function Tasks() {
  const [searchTerm, setSearchTerm] = useState('')
  const [taskDialogOpen, setTaskDialogOpen] = useState(false)
  const [packDialogOpen, setPackDialogOpen] = useState(false)
  const [verificationDialogOpen, setVerificationDialogOpen] = useState(false)
  const [selectedVerification, setSelectedVerification] = useState<TaskVerification | null>(null)
  const [taskTypeFilter, setTaskTypeFilter] = useState<string>('all')
  const [verificationStatusFilter, setVerificationStatusFilter] = useState<string>('all')
  
  const { toast } = useToast()
  const queryClient = useQueryClient()

  const { data: taskStats, isLoading: isStatsLoading } = useQuery<TaskStats>({
    queryKey: ['task-stats'],
    queryFn: () => api.get('/admin/tasks/stats').then(res => res.data)
  })

  const { data: tasks, isLoading: isTasksLoading } = useQuery<Task[]>({
    queryKey: ['admin-tasks'],
    queryFn: () => api.get('/admin/tasks').then(res => res.data)
  })

  const { data: taskPacks, isLoading: isPacksLoading } = useQuery<TaskPack[]>({
    queryKey: ['task-packs'],
    queryFn: () => api.get('/admin/task-packs').then(res => res.data)
  })

  const { data: taskVerifications, isLoading: isVerificationsLoading } = useQuery<TaskVerification[]>({
    queryKey: ['task-verifications'],
    queryFn: () => api.get('/admin/task-verifications').then(res => res.data)
  })

  const { data: dailyTaskStreaks, isLoading: isStreaksLoading } = useQuery<DailyTaskStreak[]>({
    queryKey: ['daily-task-streaks'],
    queryFn: () => api.get('/admin/daily-tasks/streaks').then(res => res.data)
  })

  const { data: vpnPackages } = useQuery({
    queryKey: ['vpn-packages-list'],
    queryFn: () => api.get('/admin/vpn/packages').then(res => res.data)
  })

  const createTaskMutation = useMutation({
    mutationFn: (taskData: CreateTaskData) => api.post('/admin/tasks', taskData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-tasks'] })
      queryClient.invalidateQueries({ queryKey: ['task-stats'] })
      setTaskDialogOpen(false)
      toast({
        title: "Success",
        description: "Task created successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to create task",
        variant: "destructive",
      })
    }
  })

  const createTaskPackMutation = useMutation({
    mutationFn: (packData: CreateTaskPackData) => api.post('/admin/task-packs', packData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task-packs'] })
      queryClient.invalidateQueries({ queryKey: ['task-stats'] })
      setPackDialogOpen(false)
      toast({
        title: "Success",
        description: "Task pack created successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to create task pack",
        variant: "destructive",
      })
    }
  })

  const toggleTaskMutation = useMutation({
    mutationFn: (taskId: number) => api.post(`/admin/tasks/${taskId}/toggle`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-tasks'] })
      toast({
        title: "Success",
        description: "Task status updated successfully",
      })
    }
  })

  const toggleTaskPackMutation = useMutation({
    mutationFn: (packId: number) => api.post(`/admin/task-packs/${packId}/toggle`),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task-packs'] })
      toast({
        title: "Success",
        description: "Task pack status updated successfully",
      })
    }
  })

  const reviewVerificationMutation = useMutation({
    mutationFn: ({ id, status, notes }: { id: number; status: 'approved' | 'rejected'; notes?: string }) => 
      api.post(`/admin/task-verifications/${id}/review`, { status, notes }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['task-verifications'] })
      setVerificationDialogOpen(false)
      toast({
        title: "Success",
        description: "Verification reviewed successfully",
      })
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.response?.data?.detail || "Failed to review verification",
        variant: "destructive",
      })
    }
  })

  const resetStreakMutation = useMutation({
    mutationFn: ({ userId, taskId }: { userId: number; taskId: number }) => 
      api.post(`/admin/daily-tasks/reset-streak`, { user_id: userId, task_id: taskId }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['daily-task-streaks'] })
      toast({
        title: "Success",
        description: "Streak reset successfully",
      })
    }
  })

  const getTaskTypeColor = (type: string) => {
    switch (type) {
      case 'telegram_join': return 'bg-blue-100 text-blue-800'
      case 'social_follow': return 'bg-green-100 text-green-800'
      case 'website_visit': return 'bg-purple-100 text-purple-800'
      case 'survey': return 'bg-orange-100 text-orange-800'
      case 'daily': return 'bg-indigo-100 text-indigo-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getVerificationStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800'
      case 'rejected': return 'bg-red-100 text-red-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getVerificationIcon = (status: string) => {
    switch (status) {
      case 'approved': return CheckCircle
      case 'rejected': return XCircle
      case 'pending': return Clock
      default: return AlertTriangle
    }
  }

  const StatCard = ({ 
    title, 
    value, 
    description, 
    icon: Icon, 
    isLoading,
    variant = 'default'
  }: { 
    title: string
    value: string | number
    description: string
    icon: any
    isLoading: boolean
    variant?: 'default' | 'success' | 'warning'
  }) => (
    <Card className={variant === 'success' ? 'border-green-200' : variant === 'warning' ? 'border-yellow-200' : ''}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className={`h-4 w-4 ${variant === 'success' ? 'text-green-600' : variant === 'warning' ? 'text-yellow-600' : 'text-muted-foreground'}`} />
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-4 w-32" />
          </div>
        ) : (
          <>
            <div className={`text-2xl font-bold ${variant === 'success' ? 'text-green-700' : variant === 'warning' ? 'text-yellow-700' : ''}`}>
              {value}
            </div>
            <p className="text-xs text-muted-foreground">{description}</p>
          </>
        )}
      </CardContent>
    </Card>
  )

  const CreateTaskDialog = () => {
    const [formData, setFormData] = useState<CreateTaskData>({
      title: '',
      description: '',
      reward: 0,
      task_type: 'telegram_join',
      verification_method: 'manual',
      verification_data: {}
    })

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      createTaskMutation.mutate(formData)
    }

    return (
      <Dialog open={taskDialogOpen} onOpenChange={setTaskDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Task</DialogTitle>
            <DialogDescription>
              Add a new task to the system
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="title">Task Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                required
              />
            </div>
            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
                required
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="reward">Reward ($)</Label>
                <Input
                  id="reward"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.reward}
                  onChange={(e) => setFormData({...formData, reward: parseFloat(e.target.value) || 0})}
                  required
                />
              </div>
              <div>
                <Label htmlFor="task_type">Task Type</Label>
                <Select value={formData.task_type} onValueChange={(value) => setFormData({...formData, task_type: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="telegram_join">Telegram Join</SelectItem>
                    <SelectItem value="social_follow">Social Follow</SelectItem>
                    <SelectItem value="website_visit">Website Visit</SelectItem>
                    <SelectItem value="survey">Survey</SelectItem>
                    <SelectItem value="daily">Daily Task</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div>
              <Label htmlFor="verification_method">Verification Method</Label>
              <Select value={formData.verification_method} onValueChange={(value) => setFormData({...formData, verification_method: value})}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="manual">Manual Review</SelectItem>
                  <SelectItem value="automatic">Automatic</SelectItem>
                  <SelectItem value="screenshot">Screenshot</SelectItem>
                  <SelectItem value="api">API Verification</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setTaskDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={createTaskMutation.isPending}>
                {createTaskMutation.isPending ? 'Creating...' : 'Create Task'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    )
  }

  const CreateTaskPackDialog = () => {
    const [formData, setFormData] = useState<CreateTaskPackData>({
      name: '',
      description: '',
      vpn_package_id: 0,
      is_lifetime: false
    })

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      createTaskPackMutation.mutate(formData)
    }

    return (
      <Dialog open={packDialogOpen} onOpenChange={setPackDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create Task Pack</DialogTitle>
            <DialogDescription>
              Create a bundle of tasks with VPN package reward
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="pack_name">Pack Name</Label>
              <Input
                id="pack_name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                required
              />
            </div>
            <div>
              <Label htmlFor="pack_description">Description</Label>
              <Textarea
                id="pack_description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
                required
              />
            </div>
            <div>
              <Label htmlFor="vpn_package">VPN Package Reward</Label>
              <Select value={formData.vpn_package_id.toString()} onValueChange={(value) => setFormData({...formData, vpn_package_id: parseInt(value)})}>
                <SelectTrigger>
                  <SelectValue placeholder="Select VPN package" />
                </SelectTrigger>
                <SelectContent>
                  {vpnPackages?.map((pkg: any) => (
                    <SelectItem key={pkg.id} value={pkg.id.toString()}>
                      {pkg.name} - ${pkg.price}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="is_lifetime"
                checked={formData.is_lifetime}
                onCheckedChange={(checked: boolean) => setFormData({...formData, is_lifetime: checked})}
              />
              <Label htmlFor="is_lifetime">Lifetime Package</Label>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setPackDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={createTaskPackMutation.isPending}>
                {createTaskPackMutation.isPending ? 'Creating...' : 'Create Pack'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    )
  }

  const VerificationDialog = () => {
    const [notes, setNotes] = useState('')

    const handleReview = (status: 'approved' | 'rejected') => {
      if (selectedVerification) {
        reviewVerificationMutation.mutate({
          id: selectedVerification.id,
          status,
          notes
        })
      }
    }

    return (
      <Dialog open={verificationDialogOpen} onOpenChange={setVerificationDialogOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Review Task Verification</DialogTitle>
            <DialogDescription>
              Review and approve/reject user task submission
            </DialogDescription>
          </DialogHeader>
          {selectedVerification && (
            <div className="space-y-4">
              <div>
                <Label>User</Label>
                <div className="font-medium">{selectedVerification.username}</div>
              </div>
              <div>
                <Label>Task</Label>
                <div className="font-medium">{selectedVerification.task_title}</div>
              </div>
              <div>
                <Label>Submitted</Label>
                <div className="text-sm text-gray-600">
                  {new Date(selectedVerification.submitted_at).toLocaleString()}
                </div>
              </div>
              <div>
                <Label>Verification Data</Label>
                <div className="bg-gray-50 p-3 rounded text-sm">
                  <pre>{JSON.stringify(selectedVerification.verification_data, null, 2)}</pre>
                </div>
              </div>
              <div>
                <Label htmlFor="review_notes">Review Notes</Label>
                <Textarea
                  id="review_notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Add notes about your review decision..."
                  rows={3}
                />
              </div>
              <DialogFooter>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setVerificationDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button 
                  type="button" 
                  variant="destructive" 
                  onClick={() => handleReview('rejected')}
                  disabled={reviewVerificationMutation.isPending}
                >
                  Reject
                </Button>
                <Button 
                  type="button" 
                  onClick={() => handleReview('approved')}
                  disabled={reviewVerificationMutation.isPending}
                >
                  Approve
                </Button>
              </DialogFooter>
            </div>
          )}
        </DialogContent>
      </Dialog>
    )
  }

  if (isStatsLoading || isTasksLoading || isPacksLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Task Management</h1>
          <p className="text-gray-600">Manage tasks, task packs, and verifications</p>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <Skeleton className="h-4 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const filteredTasks = tasks?.filter(task => 
    task.title.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (taskTypeFilter === 'all' || task.task_type === taskTypeFilter)
  )

  const filteredVerifications = taskVerifications?.filter(verification => 
    verification.username.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (verificationStatusFilter === 'all' || verification.status === verificationStatusFilter)
  )

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Task Management</h1>
          <p className="text-gray-600">Manage tasks, task packs, and verifications</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setTaskDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Task
          </Button>
          <Button variant="outline" onClick={() => setPackDialogOpen(true)}>
            <Package className="h-4 w-4 mr-2" />
            Create Pack
          </Button>
        </div>
      </div>

      {/* Task Statistics */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Task System Overview</h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <StatCard
            title="Total Tasks"
            value={taskStats?.total_tasks || 0}
            description={`${taskStats?.active_tasks || 0} active`}
            icon={CheckSquare}
            isLoading={isStatsLoading}
          />
          <StatCard
            title="Task Packs"
            value={taskStats?.total_packs || 0}
            description={`${taskStats?.active_packs || 0} active`}
            icon={Package}
            isLoading={isStatsLoading}
          />
          <StatCard
            title="Pending Reviews"
            value={taskStats?.pending_verifications || 0}
            description="Awaiting verification"
            icon={Clock}
            isLoading={isStatsLoading}
            variant={taskStats?.pending_verifications && taskStats.pending_verifications > 10 ? 'warning' : 'default'}
          />
          <StatCard
            title="Total Rewards"
            value={`$${taskStats?.total_rewards_paid?.toFixed(2) || '0.00'}`}
            description={`${taskStats?.total_completions || 0} completions`}
            icon={DollarSign}
            isLoading={isStatsLoading}
          />
        </div>
      </div>

      {/* Tabs for different views */}
      <Tabs defaultValue="tasks" className="space-y-4">
        <TabsList>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="packs">Task Packs</TabsTrigger>
          <TabsTrigger value="verifications">Verifications</TabsTrigger>
          <TabsTrigger value="streaks">Daily Streaks</TabsTrigger>
        </TabsList>

        <TabsContent value="tasks" className="space-y-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search tasks..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={taskTypeFilter} onValueChange={setTaskTypeFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="telegram_join">Telegram Join</SelectItem>
                    <SelectItem value="social_follow">Social Follow</SelectItem>
                    <SelectItem value="website_visit">Website Visit</SelectItem>
                    <SelectItem value="survey">Survey</SelectItem>
                    <SelectItem value="daily">Daily Task</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Tasks ({filteredTasks?.length || 0})</CardTitle>
              <CardDescription>Manage individual tasks and their settings</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Task</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Reward</TableHead>
                    <TableHead>Completions</TableHead>
                    <TableHead>Success Rate</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredTasks?.map((task) => (
                    <TableRow key={task.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{task.title}</div>
                          <div className="text-sm text-gray-600 max-w-xs truncate">
                            {task.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getTaskTypeColor(task.task_type)}>
                          {task.task_type.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>${task.reward.toFixed(2)}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{task.completion_count}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Progress value={task.success_rate} className="w-16" />
                          <span className="text-sm">{task.success_rate.toFixed(1)}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={task.is_active ? 'default' : 'secondary'}>
                          {task.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleTaskMutation.mutate(task.id)}
                          >
                            {task.is_active ? 'Disable' : 'Enable'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedVerification(task)
                              setVerificationDialogOpen(true)
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="packs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Task Packs ({taskPacks?.length || 0})</CardTitle>
              <CardDescription>Manage task bundles with VPN package rewards</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Pack Name</TableHead>
                    <TableHead>VPN Package</TableHead>
                    <TableHead>Tasks</TableHead>
                    <TableHead>Completions</TableHead>
                    <TableHead>Completion Rate</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {taskPacks?.map((pack) => (
                    <TableRow key={pack.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{pack.name}</div>
                          <div className="text-sm text-gray-600 max-w-xs truncate">
                            {pack.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{pack.vpn_package_name}</div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{pack.task_count}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{pack.completion_count}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Progress value={pack.completion_rate} className="w-16" />
                          <span className="text-sm">{pack.completion_rate.toFixed(1)}%</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={pack.is_lifetime ? 'default' : 'secondary'}>
                          {pack.is_lifetime ? 'Lifetime' : 'Regular'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={pack.is_active ? 'default' : 'secondary'}>
                          {pack.is_active ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => toggleTaskPackMutation.mutate(pack.id)}
                          >
                            {pack.is_active ? 'Disable' : 'Enable'}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedVerification(pack)
                              // Open pack edit dialog
                            }}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="verifications" className="space-y-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search verifications..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={verificationStatusFilter} onValueChange={setVerificationStatusFilter}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="approved">Approved</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Task Verifications ({filteredVerifications?.length || 0})</CardTitle>
              <CardDescription>Review and approve user task submissions</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Task</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Submitted</TableHead>
                    <TableHead>Reviewed</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredVerifications?.map((verification) => {
                    const StatusIcon = getVerificationIcon(verification.status)
                    return (
                      <TableRow key={verification.id}>
                        <TableCell>
                          <div className="font-medium">{verification.username}</div>
                        </TableCell>
                        <TableCell>
                          <div className="font-medium">{verification.task_title}</div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <StatusIcon className="h-4 w-4" />
                            <Badge className={getVerificationStatusColor(verification.status)}>
                              {verification.status}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          {new Date(verification.submitted_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          {verification.reviewed_at ? (
                            <div>
                              <div className="text-sm">{new Date(verification.reviewed_at).toLocaleDateString()}</div>
                              <div className="text-xs text-gray-600">by {verification.reviewed_by}</div>
                            </div>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setSelectedVerification(verification)
                              setVerificationDialogOpen(true)
                            }}
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            Review
                          </Button>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="streaks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Task Streaks ({dailyTaskStreaks?.length || 0})</CardTitle>
              <CardDescription>Monitor and manage user daily task streaks</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Task</TableHead>
                    <TableHead>Current Streak</TableHead>
                    <TableHead>Max Streak</TableHead>
                    <TableHead>Total Completions</TableHead>
                    <TableHead>Last Completion</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {dailyTaskStreaks?.map((streak) => (
                    <TableRow key={streak.id}>
                      <TableCell>
                        <div className="font-medium">{streak.username}</div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{streak.task_title}</div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={streak.current_streak > 7 ? 'default' : 'secondary'}>
                          {streak.current_streak} days
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{streak.max_streak} days</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{streak.total_completions}</Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(streak.last_completion).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <RefreshCw className="h-4 w-4 mr-2" />
                              Reset
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Reset Streak</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to reset {streak.username}'s streak for {streak.task_title}? 
                                This will set their current streak to 0.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => resetStreakMutation.mutate({
                                  userId: streak.user_id,
                                  taskId: streak.task_id
                                })}
                              >
                                Reset Streak
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <CreateTaskDialog />
      <CreateTaskPackDialog />
      <VerificationDialog />
    </div>
  )
} 