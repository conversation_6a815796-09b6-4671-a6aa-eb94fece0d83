"""Fix task_completions table to make completed_at nullable."""
from sqlalchemy import text
import logging

logger = logging.getLogger(__name__)

def upgrade(engine):
    """Make completed_at column nullable in task_completions table."""
    try:
        with engine.connect() as connection:
            # SQLite doesn't support ALTER COLUMN, so we need to use a transaction
            logger.info("Making completed_at column nullable...")
            
            # Begin transaction
            connection.execute(text("BEGIN TRANSACTION"))
            
            # Create temporary table
            connection.execute(text("""
                CREATE TEMPORARY TABLE task_completions_backup (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    task_id INTEGER NOT NULL,
                    started_at TIMESTAMP NOT NULL,
                    completed_at TIMESTAMP,
                    current_progress INTEGER NOT NULL DEFAULT 0,
                    is_claimed BOOLEAN NOT NULL DEFAULT FALSE,
                    claimed_at TIMESTAMP,
                    status TEXT NOT NULL,
                    last_verified_at TIMESTAMP,
                    auto_verify_at TIMESTAMP,
                    streak_day INTEGER,
                    verification_attempts INTEGER NOT NULL DEFAULT 0,
                    FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY(task_id) REFERENCES tasks(id) ON DELETE CASCADE
                )
            """))
            
            # Copy data
            connection.execute(text("""
                INSERT INTO task_completions_backup 
                SELECT * FROM task_completions
            """))
            
            # Drop original table
            connection.execute(text("DROP TABLE task_completions"))
            
            # Create table with new schema
            connection.execute(text("""
                CREATE TABLE task_completions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    task_id INTEGER NOT NULL,
                    started_at TIMESTAMP NOT NULL,
                    completed_at TIMESTAMP,
                    current_progress INTEGER NOT NULL DEFAULT 0,
                    is_claimed BOOLEAN NOT NULL DEFAULT FALSE,
                    claimed_at TIMESTAMP,
                    status TEXT NOT NULL,
                    last_verified_at TIMESTAMP,
                    auto_verify_at TIMESTAMP,
                    streak_day INTEGER,
                    verification_attempts INTEGER NOT NULL DEFAULT 0,
                    FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE,
                    FOREIGN KEY(task_id) REFERENCES tasks(id) ON DELETE CASCADE
                )
            """))
            
            # Copy data back
            connection.execute(text("""
                INSERT INTO task_completions 
                SELECT * FROM task_completions_backup
            """))
            
            # Drop temporary table
            connection.execute(text("DROP TABLE task_completions_backup"))
            
            # Recreate indexes
            connection.execute(text("CREATE INDEX idx_user_task_status ON task_completions (user_id, task_id, status)"))
            connection.execute(text("CREATE INDEX idx_user_completion ON task_completions (user_id, is_claimed, status)"))
            
            # Commit transaction
            connection.execute(text("COMMIT"))
            connection.commit()
            
        logger.info("Migration completed successfully")

    except Exception as e:
        logger.error(f"Error in migration: {str(e)}")
        raise

def downgrade(connection):
    """Revert completed_at to NOT NULL."""
    try:
        # Create new table with NOT NULL constraint
        connection.execute(text("""
            CREATE TABLE task_completions_new (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                task_id INTEGER NOT NULL,
                started_at TIMESTAMP NOT NULL,
                completed_at TIMESTAMP NOT NULL,
                current_progress INTEGER NOT NULL DEFAULT 0,
                is_claimed BOOLEAN NOT NULL DEFAULT FALSE,
                claimed_at TIMESTAMP,
                status TEXT NOT NULL,
                last_verified_at TIMESTAMP,
                auto_verify_at TIMESTAMP,
                streak_day INTEGER,
                verification_attempts INTEGER NOT NULL DEFAULT 0,
                FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY(task_id) REFERENCES tasks(id) ON DELETE CASCADE
            )
        """))

        # Copy data, setting completed_at to started_at if NULL
        connection.execute(text("""
            INSERT INTO task_completions_new 
            SELECT id, user_id, task_id, started_at, 
                   COALESCE(completed_at, started_at) as completed_at,
                   current_progress, is_claimed, claimed_at, status,
                   last_verified_at, auto_verify_at, streak_day,
                   verification_attempts
            FROM task_completions
        """))

        # Drop old table and rename new one
        connection.execute(text("DROP TABLE task_completions"))
        connection.execute(text("ALTER TABLE task_completions_new RENAME TO task_completions"))

        # Recreate indexes
        connection.execute(text("CREATE INDEX idx_user_task_status ON task_completions (user_id, task_id, status)"))
        connection.execute(text("CREATE INDEX idx_user_completion ON task_completions (user_id, is_claimed, status)"))

    except Exception as e:
        logger.error(f"Error in downgrade: {str(e)}")
        raise 