/**
 * @file connection.test.ts
 * @description Simple connection tests for auth endpoints
 * Tests real HTTP connections to verify backend is accessible
 */

import { describe, it, expect } from 'vitest';

const API_BASE_URL = 'https://app.atlasvip.cloud/api';

describe('Backend Connection Tests', () => {
  it('should connect to backend health endpoint', async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      expect(response.ok).toBe(true);
      
      const data = await response.json();
      expect(data).toHaveProperty('status');
      expect(data.status).toBe('ok');
    } catch (error) {
      console.error('Health check failed:', error);
      throw error;
    }
  });

  it('should handle auth endpoint (expect authentication error)', async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/verify-session`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({}),
      });
      
      // We expect 401 for unauthenticated requests
      expect([401, 422, 200]).toContain(response.status);
    } catch (error) {
      console.error('Auth endpoint test failed:', error);
      throw error;
    }
  });

  it('should handle telegram login endpoint', async () => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/telegram/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          init_data: 'test_data',
          auth_method: 'telegram',
        }),
      });
      
      // We expect validation error or CSRF error for invalid data
      expect([400, 422, 403]).toContain(response.status);
    } catch (error) {
      console.error('Telegram login endpoint test failed:', error);
      throw error;
    }
  });
}); 