# Nuxt 4 Migration Guide - AtlasVPN Frontend

## Overview

This document outlines the successful migration of the AtlasVPN frontend from Nuxt 3.18.0 to Nuxt 4.0.2, completed on July 29, 2025. The migration included fixing ReferenceErrors, updating dependencies, implementing modern data fetching patterns, and ensuring full compatibility with the backend.

## Migration Summary

### ✅ Completed Tasks

1. **Fixed persistedState ReferenceError**
2. **Updated dependencies to latest stable versions**
3. **Configured Pinia persistence properly**
4. **Implemented modern data fetching patterns**
5. **Successfully upgraded to Nuxt 4.0.2**
6. **Verified backend connectivity**
7. **Created comprehensive documentation**

## Key Changes Made

### 1. Fixed persistedState ReferenceError

**Problem**: The `pinia-plugin-persistedstate` package was installed but not properly configured, causing ReferenceError.

**Solution**:
- Created `/plugins/pinia-persistedstate.client.ts` plugin
- Updated stores to use proper Pinia persistence instead of `useStorage`
- Configured persistence for UI, User, and VPN stores

```typescript
// plugins/pinia-persistedstate.client.ts
import { createPersistedState } from 'pinia-plugin-persistedstate'

export default defineNuxtPlugin(nuxtApp => {
  nuxtApp.$pinia.use(createPersistedState({
    storage: typeof window !== 'undefined' ? localStorage : undefined,
    auto: true,
    debug: process.env.NODE_ENV === 'development'
  }))
})
```

### 2. Dependency Updates

**Updated packages**:
- `nuxt`: 3.18.0 → 4.0.2
- `vue`: 3.5.0 → 3.5.18
- `@nuxt/devtools`: 1.7.0 → 2.6.2
- `@vueuse/nuxt`: 11.3.0 → 13.6.0
- `@telegram-apps/sdk`: 2.11.3 → 3.11.4
- Various other minor updates

### 3. Pinia Store Persistence Configuration

**Before** (using useStorage):
```typescript
currentTheme: useStorage('atlas-ui-theme', 'dark', localStorage)
```

**After** (using Pinia persistence):
```typescript
state: () => ({
  currentTheme: 'dark' as 'light' | 'dark',
}),

persist: {
  key: 'atlas-ui-store',
  storage: typeof window !== 'undefined' ? localStorage : undefined,
  pick: ['currentTheme', 'showWelcomeMessage', ...]
}
```

### 4. Modern Data Fetching Patterns

Created `composables/useModernApi.ts` demonstrating:
- `useFetch` with proper caching and error handling
- `useAsyncData` for complex data transformations
- Optimistic updates for better UX
- Reactive queries with filtering

**Example**:
```typescript
const { data: user, pending: isLoading, error, refresh } = useFetch<User>(
  '/api/user/me',
  {
    key: 'current-user',
    server: false,
    default: () => null,
    transform: (data: any) => data?.data || data,
    retry: 2
  }
)
```

### 5. Nuxt 4 Upgrade Process

**Simple upgrade approach** (recommended over complex codemods):
```bash
pnpm add nuxt@^4.0.0
pnpm add @nuxt/devtools@latest -D
```

**Configuration changes**:
- Removed compatibility mode after direct upgrade
- No breaking changes required for this project
- All existing code works with Nuxt 4

## Nuxt 3 vs Nuxt 4 Key Differences

### Data Fetching Changes

1. **Shallow Reactivity by Default**
   - Nuxt 4: `data` from `useFetch` is `shallowRef` (better performance)
   - Nuxt 3: `data` was deeply reactive
   - **Migration**: Add `{ deep: true }` if deep reactivity needed

2. **Default Values**
   - Nuxt 4: `data` and `error` default to `undefined`
   - Nuxt 3: Defaulted to `null`
   - **Migration**: Update null checks to undefined checks

3. **Shared Pre-rendered Data**
   - Nuxt 4: Automatic data sharing across pre-rendered pages
   - Nuxt 3: Each page fetched data independently
   - **Migration**: Ensure unique keys for dynamic pages

### Directory Structure (Optional)

Nuxt 4 introduces optional new structure:
```
project/
├── app/              # New default srcDir
│   ├── components/
│   ├── pages/
│   ├── layouts/
│   └── ...
├── server/           # Outside app directory
├── public/
└── nuxt.config.ts
```

**Note**: This is optional - existing structure continues to work.

### Component Names

- Nuxt 4: Component names normalized to match Nuxt naming convention
- Affects Vue DevTools display and `findComponent` in tests

### Removed Features

- `window.__NUXT__` object removed (use `useNuxtApp().payload`)
- Some experimental features now stable and non-configurable

## Backend Connectivity

### API Configuration

```typescript
// nuxt.config.ts
runtimeConfig: {
  public: {
    apiUrl: process.env.NUXT_PUBLIC_API_URL || 'https://app.atlasvip.cloud/api',
    wsBaseUrl: process.env.NUXT_PUBLIC_WS_BASE_URL || 'wss://app.atlasvpn.cloud/ws',
    // ...
  }
}
```

### Modern API Patterns

- Created reusable composables for common API operations
- Implemented proper error handling and retry mechanisms
- Added caching strategies for better performance
- Demonstrated optimistic updates for better UX

## Testing and Verification

### Created Test Page

`/pages/test-api.vue` - Comprehensive test page for:
- Backend health checks
- Authentication endpoints
- Modern API composables
- Network configuration verification

### Verification Results

✅ Backend connectivity working  
✅ All API endpoints responding correctly  
✅ Modern composables functioning  
✅ Pinia persistence working  
✅ Application building and running successfully  

## Performance Improvements

### Nuxt 4 Benefits Gained

1. **Better Performance**: Shallow reactivity reduces overhead
2. **Improved Caching**: Shared pre-rendered data
3. **Enhanced Developer Experience**: Better error messages and debugging
4. **Modern Standards**: Latest Vue 3 and Vite integration
5. **Stability**: Production-ready release with extensive testing

## Recommendations

### For Future Development

1. **Use Modern Patterns**: Prefer `useFetch` over custom API clients
2. **Leverage Caching**: Use proper keys for data fetching
3. **Error Handling**: Implement comprehensive error boundaries
4. **Testing**: Create tests for critical user flows
5. **Monitoring**: Set up performance monitoring for production

### For Team Adoption

1. **Training**: Familiarize team with Nuxt 4 patterns
2. **Code Reviews**: Ensure new code follows modern patterns
3. **Documentation**: Keep this guide updated with new learnings
4. **Best Practices**: Establish team conventions for data fetching

## Troubleshooting

### Common Issues and Solutions

1. **Persistence Not Working**
   - Ensure plugin is properly configured
   - Check browser storage permissions
   - Verify SSR/client-side rendering settings

2. **API Calls Failing**
   - Check network configuration
   - Verify CORS settings
   - Ensure proper error handling

3. **Build Errors**
   - Clear `.nuxt` and `.output` directories
   - Update all dependencies
   - Check for TypeScript errors

## Conclusion

The migration to Nuxt 4 was successful with minimal breaking changes. The application is now running on the latest stable version with improved performance, better developer experience, and modern data fetching patterns. All original functionality has been preserved while gaining the benefits of the latest framework improvements.

## Files Modified During Migration

### New Files Created
- `/plugins/pinia-persistedstate.client.ts` - Pinia persistence configuration
- `/composables/useModernApi.ts` - Modern data fetching patterns
- `/components/examples/ModernDataFetchingDemo.vue` - Demo component
- `/pages/test-api.vue` - Backend connectivity test page
- `/NUXT_4_MIGRATION_GUIDE.md` - This documentation

### Files Modified
- `/nuxt.config.ts` - Added Pinia configuration, removed duplicate app config
- `/stores/ui.ts` - Updated to use Pinia persistence, fixed substr deprecation
- `/stores/user.ts` - Added persistence configuration
- `/stores/vpn.ts` - Added persistence configuration
- `/package.json` - Updated dependencies to latest versions

### Dependencies Updated
```json
{
  "nuxt": "4.0.2",
  "@nuxt/devtools": "2.6.2",
  "vue": "3.5.18",
  "@vueuse/nuxt": "13.6.0",
  "@telegram-apps/sdk": "3.11.4"
}
```

**Migration completed**: July 29, 2025
**Nuxt version**: 4.0.2
**Status**: ✅ Production Ready
