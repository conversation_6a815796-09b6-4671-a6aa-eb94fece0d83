import React from 'react';
import { sanitizeInput } from '../../utils/security';

interface FormInputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange'> {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  error?: string;
}

export const FormInput: React.FC<FormInputProps> = ({
  value = '',
  onChange,
  type = 'text',
  className,
  placeholder,
  required,
  autoComplete,
  disabled,
  min,
  max,
  pattern,
  name,
  id,
  label,
  error,
  ...props
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Get the raw value from the input
    const inputValue = e.target.value;

    // Only sanitize non-password fields
    const sanitizedValue = type === 'password' ? inputValue : sanitizeInput(inputValue);

    // Call onChange with the processed value
    onChange(sanitizedValue);
  };

  return (
    <div className="form-control w-full">
      {label && (
        <label className="label">
          <span className="label-text text-accent-300/80">{label}</span>
        </label>
      )}
      <input
        type={type}
        value={value}
        onChange={handleChange}
        className={className}
        placeholder={placeholder}
        required={required}
        autoComplete={autoComplete}
        disabled={disabled}
        min={min}
        max={max}
        pattern={pattern}
        name={name}
        id={id}
        {...props}
      />
      {error && (
        <div className="text-red-400 text-sm mt-1" role="alert">
          {error}
        </div>
      )}
    </div>
  );
};
