---
export interface Props {
  title: string;
  description?: string;
  activeTab?: 'home' | 'earn' | 'friends' | 'wallet' | 'premium';
}

const { title, description = "VIPVerse Dashboard", activeTab = 'home' } = Astro.props;

import BaseLayout from './BaseLayout.astro';
---

<BaseLayout title={title} description={description} requireAuth={true}>
  <div class="dashboard-container">
    <main>
      <slot />
    </main>
  </div>
</BaseLayout>