#!/usr/bin/env python3
import requests
import json

# Test the Telegram authentication endpoint
url = "http://atlasvpn-backend:8000/api/auth/telegram/login"
headers = {
    "Content-Type": "application/json"
}
data = {
    "init_data": "dummy_init_data_for_development"
}

try:
    response = requests.post(url, headers=headers, json=data, timeout=10)
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
except Exception as e:
    print(f"Error: {e}")