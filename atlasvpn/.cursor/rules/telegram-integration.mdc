---
description: 
globs: 
alwaysApply: false
---
# Telegram Mini App Integration

## Telegram WebApp SDK

This app is specifically designed as a **Telegram Mini App** with deep integration into Telegram's ecosystem.

### Core Telegram Files

- **Telegram Utils**: [frontend/src/utils/telegram.ts](mdc:frontend/src/utils/telegram.ts) - WebApp SDK integration
- **HTML Integration**: [frontend/index.html](mdc:frontend/index.html) - Telegram script loading
- **Main Layout**: [frontend/src/components/DashboardLayout.tsx](mdc:frontend/src/components/DashboardLayout.tsx) - Telegram viewport handling

### Telegram WebApp Features

#### Authentication
- **Telegram Login**: Uses `window.Telegram.WebApp.initData` for secure auth
- **User Data**: Automatic access to Telegram user profile
- **Session Management**: Persistent sessions via Telegram

#### UI Integration
- **Viewport Management**: Automatic expansion and safe area handling
- **Theme Integration**: Respects Telegram's dark/light theme
- **Haptic Feedback**: Native Telegram haptics for interactions
- **Main Button**: Telegram's native action button

#### Navigation
- **Back Button**: Telegram's native back button handling
- **Deep Linking**: Support for Telegram deep links
- **Share Integration**: Native Telegram sharing

### Key Telegram Hooks

```typescript
// Telegram viewport management
const { viewportHeight, safeAreaInsets } = useTelegramViewport();

// App visibility (when user switches apps)
const [isVisible, isMinimized] = useAppVisibility();

// Safe area for notched devices
const safeAreaInsets = useTelegramSafeArea();
```

### Telegram-Specific Components

#### Layout Components
- **DashboardLayout** - Handles Telegram viewport and safe areas
- **Header** - Respects Telegram's status bar
- **BottomNavigation** - Accounts for Telegram's bottom safe area

#### Interactive Elements
- **Haptic Feedback** - All buttons use Telegram haptics
- **Toast Notifications** - Fallback to Telegram alerts when possible
- **Loading States** - Optimized for Telegram's WebView

### Performance Considerations

#### WebView Limitations
- **Memory Constraints** - Telegram WebView has limited memory
- **JavaScript Performance** - Slower than native browsers
- **Network Handling** - Different caching behavior

#### Optimization Strategies
- **Lazy Loading** - Critical for Telegram's memory limits
- **Bundle Splitting** - Keep initial load under 300KB
- **Image Optimization** - WebP format preferred
- **Animation Throttling** - Reduce animations on low-end devices

### Development Guidelines

#### Testing
- **Telegram Desktop** - Primary development environment
- **Telegram Mobile** - Test on actual mobile devices
- **Browser Fallback** - Graceful degradation for non-Telegram environments

#### Deployment
- **HTTPS Required** - Telegram only loads HTTPS mini apps
- **Domain Verification** - Must be registered with @BotFather
- **CSP Headers** - Strict Content Security Policy required

### Common Patterns

#### Initialization
```typescript
useEffect(() => {
  if (window.Telegram?.WebApp) {
    const tg = window.Telegram.WebApp;
    tg.ready();
    tg.expand();
    tg.disableVerticalSwipes();
  }
}, []);
```

#### User Authentication
```typescript
const telegramUser = window.Telegram?.WebApp?.initDataUnsafe?.user;
const initData = window.Telegram?.WebApp?.initData;
```

#### Haptic Feedback
```typescript
import { haptics } from '../utils/haptics';

// On button click
haptics.impact('medium');
haptics.notification('success');
```

### Security Considerations

- **Init Data Validation** - Always validate Telegram init data on backend
- **CSRF Protection** - Use Telegram's hash validation
- **User Verification** - Verify user ID matches Telegram data
- **Secure Storage** - Use Telegram's secure storage when available

### Debugging Tips

1. **Use Telegram Desktop** - Better dev tools than mobile
2. **Check initData** - Verify Telegram data is available
3. **Test offline** - Handle network failures gracefully
4. **Monitor memory** - Watch for memory leaks in WebView
5. **Validate CSP** - Ensure Content Security Policy compliance
