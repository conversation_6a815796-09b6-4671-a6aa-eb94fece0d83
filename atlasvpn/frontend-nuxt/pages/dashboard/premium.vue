<template>
  <div class="premium-tab">
    <!-- Header -->
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-white mb-2">Premium VPN</h1>
      <p class="text-gray-400">Secure your connection with our VPN services</p>
    </div>

    <!-- Current Subscriptions -->
    <div v-if="activeSubscriptions.length > 0" class="mb-6">
      <h2 class="text-lg font-semibold text-white mb-4">Active Subscriptions</h2>
      <div class="space-y-4">
        <div
          v-for="subscription in activeSubscriptions"
          :key="subscription.id"
          class="card border-green-500/20 bg-green-500/5"
        >
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-3">
              <div class="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center">
                <Icon name="i-heroicons-shield-check" class="w-6 h-6 text-green-400" />
              </div>
              <div>
                <h3 class="text-white font-medium">{{ subscription.package.name }}</h3>
                <p class="text-green-400 text-sm">Active</p>
              </div>
            </div>
            <UButton
              @click="viewSubscription(subscription)"
              variant="outline"
              size="sm"
            >
              Manage
            </UButton>
          </div>

          <!-- Data Usage -->
          <div class="mb-4">
            <div class="flex justify-between text-sm mb-2">
              <span class="text-gray-400">Data Usage</span>
              <span class="text-white">
                {{ formatDataUsage(subscription.data_used) }} / {{ formatDataUsage(subscription.data_limit) }}
              </span>
            </div>
            <div class="w-full bg-gray-700 rounded-full h-2">
              <div 
                class="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${getUsagePercentage(subscription)}%` }"
              ></div>
            </div>
          </div>

          <!-- Expiry Info -->
          <div class="flex justify-between text-sm">
            <span class="text-gray-400">Expires</span>
            <span class="text-white">
              {{ formatDate(subscription.expires_at) }}
              <span class="text-gray-400">
                ({{ getDaysUntilExpiry(subscription.expires_at) }} days)
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Packages -->
    <div class="mb-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-lg font-semibold text-white">Available Packages</h2>
        <UButton
          @click="refreshPackages"
          :loading="isLoadingPackages"
          variant="ghost"
          size="sm"
          icon="i-heroicons-arrow-path"
        />
      </div>

      <!-- Loading State -->
      <div v-if="isLoadingPackages" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div v-for="i in 4" :key="i" class="card animate-pulse">
          <div class="h-6 bg-gray-700 rounded mb-4"></div>
          <div class="h-4 bg-gray-700 rounded mb-2"></div>
          <div class="h-4 bg-gray-700 rounded w-2/3 mb-4"></div>
          <div class="h-10 bg-gray-700 rounded"></div>
        </div>
      </div>

      <!-- Packages Grid -->
      <div v-else-if="activePackages.length > 0" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <VPNPackageCard
          v-for="pkg in activePackages"
          :key="pkg.id"
          :vpn-package="pkg"
          :is-selected="selectedPackage?.id === pkg.id"
          :is-popular="pkg.is_popular"
          :is-loading="isPurchasing && selectedPackage?.id === pkg.id"
          :wallet-balance="walletBalance"
          @select="selectPackage"
          @purchase="startPurchaseFlow"
        />
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <Icon name="i-heroicons-shield-exclamation" class="w-16 h-16 mx-auto mb-4 text-gray-600" />
        <h3 class="text-lg font-medium text-white mb-2">No packages available</h3>
        <p class="text-gray-400">Check back later for new VPN packages</p>
      </div>
    </div>

    <!-- Wallet Balance Info -->
    <div class="card bg-blue-500/5 border-blue-500/20">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <Icon name="i-heroicons-wallet" class="w-6 h-6 text-blue-400" />
          <div>
            <h3 class="text-white font-medium">Wallet Balance</h3>
            <p class="text-blue-400 text-sm">{{ formattedBalance }}</p>
          </div>
        </div>
        <UButton
          @click="navigateTo('/dashboard/wallet')"
          variant="outline"
          size="sm"
        >
          Top Up
        </UButton>
      </div>
    </div>

    <!-- Error States -->
    <div v-if="packagesError" class="card border-red-500/20 bg-red-500/5 mt-4">
      <div class="flex items-center space-x-3">
        <Icon name="i-heroicons-exclamation-triangle" class="w-6 h-6 text-red-400" />
        <div>
          <h4 class="text-red-400 font-medium">Failed to load packages</h4>
          <p class="text-red-300 text-sm">{{ packagesError }}</p>
        </div>
      </div>
    </div>

    <!-- Purchase Flow Modal -->
    <UModal v-model="showPurchaseModal" :ui="{ width: 'sm:max-w-2xl' }">
      <div class="p-6">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <UButton
              v-if="purchaseStep !== 'package'"
              @click="goBackStep"
              variant="ghost"
              size="sm"
              icon="i-heroicons-arrow-left"
            />
            <h3 class="text-xl font-bold text-white">
              {{ getPurchaseStepTitle() }}
            </h3>
          </div>
          <UButton
            @click="closePurchaseModal"
            variant="ghost"
            size="sm"
            icon="i-heroicons-x-mark"
          />
        </div>

        <!-- Package Selection Step -->
        <div v-if="purchaseStep === 'package'" class="space-y-4">
          <div v-if="selectedPackageForPurchase" class="bg-purple-500/10 border border-purple-500/20 rounded-lg p-4">
            <h4 class="text-lg font-semibold text-white mb-2">{{ selectedPackageForPurchase.name }}</h4>
            <p class="text-gray-400 text-sm mb-3">{{ selectedPackageForPurchase.description }}</p>
            <div class="text-2xl font-bold text-purple-400">${{ selectedPackageForPurchase.price.toFixed(2) }}</div>
          </div>

          <UButton
            @click="purchaseStep = 'server'"
            color="primary"
            class="w-full"
            size="lg"
          >
            Continue to Server Selection
          </UButton>
        </div>

        <!-- Server Selection Step -->
        <div v-else-if="purchaseStep === 'server'" class="space-y-4">
          <div class="text-gray-400 text-sm mb-4">
            Choose a server location for your VPN connection:
          </div>

          <div class="grid grid-cols-1 gap-3 max-h-64 overflow-y-auto">
            <div
              v-for="panel in availableServers"
              :key="panel.id"
              class="p-3 rounded-lg border cursor-pointer transition-all"
              :class="selectedServer?.id === panel.id
                ? 'border-purple-500 bg-purple-500/20'
                : 'border-gray-700 bg-gray-800/50 hover:bg-gray-800'"
              @click="selectedServer = panel"
            >
              <div class="flex items-center justify-between">
                <div>
                  <div class="text-white font-medium">{{ panel.name }}</div>
                  <div class="text-gray-400 text-sm">{{ panel.location || 'Server Location' }}</div>
                </div>
                <div class="flex items-center space-x-2">
                  <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span class="text-green-400 text-xs">Online</span>
                </div>
              </div>
            </div>
          </div>

          <UButton
            @click="purchaseStep = 'confirm'"
            :disabled="!selectedServer"
            color="primary"
            class="w-full"
            size="lg"
          >
            Continue to Confirmation
          </UButton>
        </div>

        <!-- Confirmation Step -->
        <div v-else-if="purchaseStep === 'confirm'" class="space-y-6">
          <div class="bg-gray-800/50 rounded-lg p-4">
            <h4 class="text-white font-semibold mb-3">Purchase Summary</h4>

            <div class="space-y-2 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-400">Package</span>
                <span class="text-white">{{ selectedPackageForPurchase?.name }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-400">Server</span>
                <span class="text-white">{{ selectedServer?.name }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-400">Duration</span>
                <span class="text-white">{{ selectedPackageForPurchase?.expire_days }} days</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-400">Data Limit</span>
                <span class="text-white">{{ formatDataUsage(selectedPackageForPurchase?.data_limit || 0) }}</span>
              </div>
              <div class="border-t border-gray-700 pt-2 mt-2">
                <div class="flex justify-between font-semibold">
                  <span class="text-white">Total</span>
                  <span class="text-purple-400">${{ selectedPackageForPurchase?.price.toFixed(2) }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <div class="flex items-center space-x-2 mb-2">
              <Icon name="i-heroicons-wallet" class="w-5 h-5 text-blue-400" />
              <span class="text-blue-400 font-medium">Wallet Balance</span>
            </div>
            <div class="text-2xl font-bold text-white">{{ formattedBalance }}</div>
            <div v-if="selectedPackageForPurchase && walletBalance >= selectedPackageForPurchase.price" class="text-green-400 text-sm mt-1">
              ✓ Sufficient balance
            </div>
            <div v-else class="text-red-400 text-sm mt-1">
              ⚠ Insufficient balance
            </div>
          </div>

          <UButton
            @click="confirmPurchase"
            :loading="isPurchasing"
            :disabled="!selectedPackageForPurchase || !selectedServer || walletBalance < (selectedPackageForPurchase?.price || 0)"
            color="primary"
            class="w-full"
            size="lg"
          >
            <template #leading>
              <Icon name="i-heroicons-credit-card" class="w-5 h-5" />
            </template>
            Confirm Purchase
          </UButton>
        </div>
      </div>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { format } from 'date-fns'

definePageMeta({
  middleware: 'auth',
  layout: 'dashboard'
})

// SEO
useHead({
  title: 'Premium VPN - AtlasVPN'
})

const vpnStore = useVPNStore()
const userStore = useUserStore()
const { 
  fetchPackages, 
  fetchSubscriptions, 
  purchasePackage: purchasePackageAction 
} = useVPN()

// Computed properties from stores
const activePackages = computed(() => vpnStore.activePackages)
const activeSubscriptions = computed(() => vpnStore.activeSubscriptions)
const isLoadingPackages = computed(() => vpnStore.isLoadingPackages)
const packagesError = computed(() => vpnStore.packagesError)
const isPurchasing = computed(() => vpnStore.isPurchasing)
const selectedPackage = computed(() => vpnStore.selectedPackage)
const formattedBalance = computed(() => userStore.formattedBalance)

// Local state
const showSubscriptionModal = ref(false)
const selectedSubscription = ref(null)
const showPurchaseModal = ref(false)
const selectedPackageForPurchase = ref(null)
const selectedServer = ref(null)
const purchaseStep = ref('package') // 'package' | 'server' | 'confirm'

// Actions
const selectPackage = (pkg: any) => {
  vpnStore.selectPackage(pkg)
}

const startPurchaseFlow = (pkg: any) => {
  selectedPackageForPurchase.value = pkg
  selectedServer.value = null
  purchaseStep.value = 'package'
  showPurchaseModal.value = true
}

const closePurchaseModal = () => {
  showPurchaseModal.value = false
  selectedPackageForPurchase.value = null
  selectedServer.value = null
  purchaseStep.value = 'package'
}

const goBackStep = () => {
  if (purchaseStep.value === 'server') {
    purchaseStep.value = 'package'
  } else if (purchaseStep.value === 'confirm') {
    purchaseStep.value = 'server'
  }
}

const confirmPurchase = async () => {
  if (!selectedPackageForPurchase.value || !selectedServer.value) return

  try {
    await purchasePackageAction(selectedPackageForPurchase.value.id, selectedServer.value.id)
    // Show success message
    const uiStore = useUiStore()
    uiStore.showSuccessToast('Package purchased successfully!')
    closePurchaseModal()
  } catch (error: any) {
    console.error('Failed to purchase package:', error)
    const uiStore = useUiStore()
    uiStore.showErrorToast('Purchase failed', error.message)
  }
}

const refreshPackages = async () => {
  await Promise.all([
    fetchPackages(),
    fetchSubscriptions()
  ])
}

const viewSubscription = (subscription: any) => {
  selectedSubscription.value = subscription
  showSubscriptionModal.value = true
}

// Computed properties
const availableServers = computed(() => {
  if (!selectedPackageForPurchase.value) return []
  return selectedPackageForPurchase.value.allowed_panels || []
})

const walletBalance = computed(() => userStore.walletBalance)

const getPurchaseStepTitle = () => {
  const titles = {
    package: 'Package Details',
    server: 'Select Server',
    confirm: 'Confirm Purchase'
  }
  return titles[purchaseStep.value] || 'Purchase VPN'
}

// Helper functions
const canAfford = (price: number) => userStore.canAfford(price)

const formatDataUsage = (bytes: number) => vpnStore.formatDataUsage(bytes)

const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'MMM d, yyyy')
}

const getDaysUntilExpiry = (expiresAt: string) => vpnStore.getDaysUntilExpiry(expiresAt)

const getUsagePercentage = (subscription: any) => {
  if (subscription.data_limit === 0) return 0
  return Math.min((subscription.data_used / subscription.data_limit) * 100, 100)
}

// Initialize
onMounted(async () => {
  await refreshPackages()
})
</script>
