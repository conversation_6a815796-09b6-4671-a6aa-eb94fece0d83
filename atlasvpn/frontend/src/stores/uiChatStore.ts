import { create } from 'zustand';
import { ChatViewMode } from '../types/chat';

interface UIChatState {
  selectedChat: number | 'public';
  viewMode: ChatViewMode;
  setSelectedChat: (chatId: number | 'public') => void;
  setViewMode: (mode: ChatViewMode) => void;
}

export const useUIChatStore = create<UIChatState>(set => ({
  selectedChat: 'public',
  viewMode: ChatViewMode.HIDDEN,
  setSelectedChat: chatId => set({ selectedChat: chatId }),
  setViewMode: viewMode => set({ viewMode }),
}));
