/**
 * @file api/utils.ts
 * @description API utilities, constants, and helper functions
 */

import { APIError } from './client';
import type { ApiError } from './types';

// API Constants
export const API_CONSTANTS = {
  // Timeouts
  DEFAULT_TIMEOUT: 15000,
  AUTH_TIMEOUT: 12000,
  UPLOAD_TIMEOUT: 30000,
  
  // Retry settings - DISABLED FOR DEVELOPMENT
  MAX_RETRIES: 0, // Disable retries to avoid rate limiting
  RETRY_DELAY: 0, // No delay for development
  
  // Cache times (in milliseconds)
  CACHE_TIME: {
    SHORT: 2 * 60 * 1000,    // 2 minutes
    MEDIUM: 5 * 60 * 1000,   // 5 minutes
    LONG: 10 * 60 * 1000,    // 10 minutes
    VERY_LONG: 30 * 60 * 1000 // 30 minutes
  },
  
  // Stale times (in milliseconds)
  STALE_TIME: {
    SHORT: 1 * 60 * 1000,    // 1 minute
    MEDIUM: 3 * 60 * 1000,   // 3 minutes
    LONG: 5 * 60 * 1000,     // 5 minutes
    VERY_LONG: 15 * 60 * 1000 // 15 minutes
  },
  
  // HTTP Status codes
  STATUS_CODES: {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    TOO_MANY_REQUESTS: 429,
    INTERNAL_SERVER_ERROR: 500,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503
  }
} as const;

// Query Keys Factory
export const createQueryKey = {
  // Auth keys
  auth: {
    all: ['auth'] as const,
    currentUser: () => [...createQueryKey.auth.all, 'currentUser'] as const,
    verifySession: () => [...createQueryKey.auth.all, 'verifySession'] as const
  },
  
  // User keys
  user: {
    all: ['user'] as const,
    stats: () => [...createQueryKey.user.all, 'stats'] as const,
    transactions: () => [...createQueryKey.user.all, 'transactions'] as const,
    profile: () => [...createQueryKey.user.all, 'profile'] as const
  },
  
  // Card keys
  cards: {
    all: ['cards'] as const,
    list: () => [...createQueryKey.cards.all, 'list'] as const,
    owned: () => [...createQueryKey.cards.all, 'owned'] as const
  },
  
  // Task keys
  tasks: {
    all: ['tasks'] as const,
    available: () => [...createQueryKey.tasks.all, 'available'] as const,
    completed: () => [...createQueryKey.tasks.all, 'completed'] as const,
    dailyStreak: () => [...createQueryKey.tasks.all, 'dailyStreak'] as const,
    analytics: () => [...createQueryKey.tasks.all, 'analytics'] as const
  },
  
  // Referral keys
  referrals: {
    all: ['referrals'] as const,
    info: () => [...createQueryKey.referrals.all, 'info'] as const,
    users: () => [...createQueryKey.referrals.all, 'users'] as const
  },
  
  // VPN keys
  vpn: {
    all: ['vpn'] as const,
    packages: () => [...createQueryKey.vpn.all, 'packages'] as const,
    subscriptions: () => [...createQueryKey.vpn.all, 'subscriptions'] as const,
    panels: () => [...createQueryKey.vpn.all, 'panels'] as const
  },
  
  // Chat keys
  chat: {
    all: ['chat'] as const,
    conversations: () => [...createQueryKey.chat.all, 'conversations'] as const,
    messages: (conversationId: number) => [...createQueryKey.chat.all, 'messages', conversationId] as const
  },
  
  // System keys
  system: {
    all: ['system'] as const,
    health: () => [...createQueryKey.system.all, 'health'] as const,
    status: () => [...createQueryKey.system.all, 'status'] as const
  }
};

// Error handling utilities
export const handleApiError = (error: unknown): string => {
  if (error instanceof APIError) {
    switch (error.status) {
      case API_CONSTANTS.STATUS_CODES.UNAUTHORIZED:
        return 'Authentication required. Please log in again.';
      case API_CONSTANTS.STATUS_CODES.FORBIDDEN:
        return 'Access denied. You do not have permission for this action.';
      case API_CONSTANTS.STATUS_CODES.NOT_FOUND:
        return 'Resource not found.';
      case API_CONSTANTS.STATUS_CODES.CONFLICT:
        return 'Conflict: The resource already exists or is in use.';
      case API_CONSTANTS.STATUS_CODES.UNPROCESSABLE_ENTITY:
        return 'Invalid data provided. Please check your input.';
      case API_CONSTANTS.STATUS_CODES.TOO_MANY_REQUESTS:
        return 'Too many requests. Please try again later.';
      case API_CONSTANTS.STATUS_CODES.INTERNAL_SERVER_ERROR:
        return 'Server error. Please try again later.';
      case API_CONSTANTS.STATUS_CODES.BAD_GATEWAY:
        return 'Service temporarily unavailable. Please try again.';
      case API_CONSTANTS.STATUS_CODES.SERVICE_UNAVAILABLE:
        return 'Service is currently down for maintenance.';
      default:
        return error.message || 'An unexpected error occurred.';
    }
  }

  if (error instanceof Error) {
    // Handle network errors
    if (error.message.includes('Network Error') || error.message.includes('fetch')) {
      return 'Network error. Please check your connection and try again.';
    }
    
    // Handle timeout errors
    if (error.message.includes('timeout') || error.message.includes('aborted')) {
      return 'Request timed out. Please try again.';
    }
    
    return error.message;
  }

  return 'An unexpected error occurred.';
};

// API response validators
export const validateApiResponse = {
  // Check if response has required structure
  hasValidStructure: (response: any): boolean => {
    return response && typeof response === 'object';
  },
  
  // Check if response indicates success
  isSuccess: (response: any): boolean => {
    return response?.success === true || response?.status === 'success';
  },
  
  // Check if response has data
  hasData: (response: any): boolean => {
    return response?.data !== undefined && response?.data !== null;
  },
  
  // Check if response is paginated
  isPaginated: (response: any): boolean => {
    return response?.total !== undefined && response?.page !== undefined;
  }
};

// URL and parameter utilities
export const urlUtils = {
  // Build query string from object
  buildQueryString: (params: Record<string, any>): string => {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        searchParams.append(key, String(value));
      }
    });
    
    const queryString = searchParams.toString();
    return queryString ? `?${queryString}` : '';
  },
  
  // Combine base URL with endpoint
  combineUrl: (baseUrl: string, endpoint: string): string => {
    const cleanBase = baseUrl.replace(/\/$/, '');
    const cleanEndpoint = endpoint.replace(/^\//, '');
    return `${cleanBase}/${cleanEndpoint}`;
  },
  
  // Validate URL format
  isValidUrl: (url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
};

// Data transformation utilities
export const dataUtils = {
  // Convert snake_case to camelCase
  toCamelCase: (obj: any): any => {
    if (Array.isArray(obj)) {
      return obj.map(dataUtils.toCamelCase);
    }
    
    if (obj !== null && typeof obj === 'object') {
      return Object.keys(obj).reduce((result, key) => {
        const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        result[camelKey] = dataUtils.toCamelCase(obj[key]);
        return result;
      }, {} as any);
    }
    
    return obj;
  },
  
  // Convert camelCase to snake_case
  toSnakeCase: (obj: any): any => {
    if (Array.isArray(obj)) {
      return obj.map(dataUtils.toSnakeCase);
    }
    
    if (obj !== null && typeof obj === 'object') {
      return Object.keys(obj).reduce((result, key) => {
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
        result[snakeKey] = dataUtils.toSnakeCase(obj[key]);
        return result;
      }, {} as any);
    }
    
    return obj;
  },
  
  // Deep clone object
  deepClone: <T>(obj: T): T => {
    return JSON.parse(JSON.stringify(obj));
  },
  
  // Remove undefined/null values
  removeEmpty: (obj: Record<string, any>): Record<string, any> => {
    return Object.entries(obj).reduce((acc, [key, value]) => {
      if (value !== undefined && value !== null) {
        acc[key] = value;
      }
      return acc;
    }, {} as Record<string, any>);
  }
};

// Environment utilities
export const envUtils = {
  // Check if running in development
  isDevelopment: (): boolean => {
    return import.meta.env.DEV || import.meta.env.VITE_ENVIRONMENT === 'development';
  },
  
  // Check if running in production
  isProduction: (): boolean => {
    return import.meta.env.PROD || import.meta.env.VITE_ENVIRONMENT === 'production';
  },
  
  // Check if running on client side
  isClient: (): boolean => {
    return typeof window !== 'undefined';
  },
  
  // Check if running on server side
  isServer: (): boolean => {
    return typeof window === 'undefined';
  },
  
  // Get environment variable with fallback
  getEnvVar: (key: string, fallback: string = ''): string => {
    return import.meta.env[key] || process.env[key] || fallback;
  }
};

// Retry utilities
export const retryUtils = {
  // Simple retry with exponential backoff
  withRetry: async <T>(
    fn: () => Promise<T>,
    maxRetries: number = API_CONSTANTS.MAX_RETRIES,
    baseDelay: number = API_CONSTANTS.RETRY_DELAY
  ): Promise<T> => {
    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === maxRetries) {
          throw lastError;
        }
        
        // Exponential backoff
        const delay = baseDelay * Math.pow(2, attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError!;
  },
  
  // Check if error is retryable
  isRetryableError: (error: any): boolean => {
    if (error instanceof APIError) {
      // Don't retry client errors (4xx) except for specific cases
      if (error.status >= 400 && error.status < 500) {
        return error.status === API_CONSTANTS.STATUS_CODES.TOO_MANY_REQUESTS;
      }
      // Retry server errors (5xx)
      return error.status >= 500;
    }
    
    // Retry network errors
    return error.message?.includes('Network Error') || 
           error.message?.includes('fetch') ||
           error.message?.includes('timeout');
  }
};

// Export all utilities
export default {
  API_CONSTANTS,
  createQueryKey,
  handleApiError,
  validateApiResponse,
  urlUtils,
  dataUtils,
  envUtils,
  retryUtils
};