/**
 * @file api/hooks/cards.ts
 * @description Card-related React Query hooks
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type {
  Card,
  QueryOptions,
  MutationOptions
} from '../types';
import { cardService } from '../services';

// Query Keys
export const CARD_QUERY_KEYS = {
  ALL_CARDS: ['cards', 'all']
} as const;

// Default query options
const DEFAULT_QUERY_OPTIONS: QueryOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  retry: false
};

// Card Hooks
export function useAllCardsQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: CARD_QUERY_KEYS.ALL_CARDS,
    queryFn: () => cardService.getAllCards(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}

export function useBuyCardMutation(options?: MutationOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (cardId: number) => cardService.buyCard(cardId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CARD_QUERY_KEYS.ALL_CARDS });
      queryClient.invalidateQueries({ queryKey: ['user', 'stats'] });
    },
    ...options
  });
}

export function useUpgradeCardMutation(options?: MutationOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (cardId: number) => cardService.upgradeCard(cardId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CARD_QUERY_KEYS.ALL_CARDS });
      queryClient.invalidateQueries({ queryKey: ['user', 'stats'] });
    },
    ...options
  });
}

export function useClaimAllProfitsMutation(options?: MutationOptions) {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: () => cardService.claimAllProfits(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user', 'stats'] });
      queryClient.invalidateQueries({ queryKey: CARD_QUERY_KEYS.ALL_CARDS });
    },
    ...options
  });
}