<template>
  <div class="profit-card relative overflow-hidden rounded-xl border border-yellow-500/20 bg-gradient-to-br from-gray-900/80 to-gray-800/80 backdrop-blur-sm shadow-lg shadow-yellow-500/15">
    <!-- Glow Effect -->
    <div class="absolute top-0 right-0 w-32 h-32 bg-yellow-500/10 rounded-full blur-3xl -translate-y-16 translate-x-16"></div>
    
    <!-- Corner Accents -->
    <div class="absolute top-0 left-0 w-8 h-8 opacity-70">
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
        <path d="M 0 12 V 0 H 12" stroke="#eab308" stroke-width="1" />
        <path d="M 0 8 V 0 H 8" stroke="#eab308" stroke-width="2" />
      </svg>
    </div>

    <div class="relative p-4">
      <!-- Header -->
      <div class="mb-3">
        <div class="flex items-center gap-2">
          <Icon name="i-heroicons-bolt" class="text-yellow-400 text-sm" />
          <div class="text-sm uppercase tracking-wider text-yellow-300 font-bold">
            Card Profits
          </div>
        </div>
        <div class="mt-1 h-px bg-gradient-to-r from-yellow-500/50 via-yellow-400/20 to-transparent" />
      </div>

      <!-- Profit Display -->
      <div class="text-center mb-4">
        <div class="text-2xl sm:text-3xl font-bold text-white mb-1">
          {{ formattedProfit }}
        </div>
        <div class="text-xs text-yellow-300/70">
          Available to claim
        </div>
      </div>

      <!-- Live Update Indicator -->
      <div class="flex items-center justify-center mb-4">
        <div class="flex items-center space-x-2 text-xs text-yellow-300/60">
          <div class="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
          <span>Live updating</span>
        </div>
      </div>

      <!-- Claim Button -->
      <UButton
        @click="handleClaim"
        :disabled="!hasProfit || isClaiming"
        :loading="isClaiming"
        color="yellow"
        variant="solid"
        class="w-full"
        size="lg"
      >
        <template #leading>
          <Icon name="i-heroicons-gift" class="w-5 h-5" />
        </template>
        {{ isClaiming ? 'Claiming...' : hasProfit ? 'Claim Profits' : 'No Profits Yet' }}
      </UButton>

      <!-- Next Update Timer -->
      <div v-if="nextUpdateIn > 0" class="mt-3 text-center">
        <div class="text-xs text-yellow-300/50">
          Next update in {{ nextUpdateIn }}s
        </div>
        <div class="w-full bg-gray-700 rounded-full h-1 mt-1">
          <div 
            class="bg-yellow-400 h-1 rounded-full transition-all duration-1000"
            :style="{ width: `${updateProgress}%` }"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  profit: number
  isClaiming?: boolean
  updateInterval?: number // seconds
}

const props = withDefaults(defineProps<Props>(), {
  isClaiming: false,
  updateInterval: 60
})

const emit = defineEmits<{
  claim: []
}>()

const nextUpdateIn = ref(props.updateInterval)
const updateProgress = ref(100)

const hasProfit = computed(() => props.profit > 0.0001)

const formattedProfit = computed(() => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 4,
    maximumFractionDigits: 4
  }).format(props.profit)
})

const handleClaim = () => {
  if (hasProfit.value && !props.isClaiming) {
    emit('claim')
  }
}

// Update timer
let timerInterval: NodeJS.Timeout | null = null

const startTimer = () => {
  if (timerInterval) clearInterval(timerInterval)
  
  nextUpdateIn.value = props.updateInterval
  updateProgress.value = 100
  
  timerInterval = setInterval(() => {
    nextUpdateIn.value--
    updateProgress.value = (nextUpdateIn.value / props.updateInterval) * 100
    
    if (nextUpdateIn.value <= 0) {
      nextUpdateIn.value = props.updateInterval
      updateProgress.value = 100
    }
  }, 1000)
}

onMounted(() => {
  startTimer()
})

onUnmounted(() => {
  if (timerInterval) {
    clearInterval(timerInterval)
  }
})

// Restart timer when interval changes
watch(() => props.updateInterval, () => {
  startTimer()
})
</script>

<style scoped>
.profit-card {
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(234, 179, 8, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(251, 191, 36, 0.05) 0%, transparent 50%);
}
</style>
