{"version": 1, "disable_existing_loggers": false, "formatters": {"default": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "datefmt": "%Y-%m-%d %H:%M:%S"}, "access": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(client_addr)s - %(request_line)s - %(status_code)s", "datefmt": "%Y-%m-%d %H:%M:%S"}}, "handlers": {"console": {"class": "logging.StreamHandler", "level": "INFO", "formatter": "default", "stream": "ext://sys.stdout"}, "file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "default", "filename": "logs/app.log", "maxBytes": 10485760, "backupCount": 5}, "access_file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "access", "filename": "logs/access.log", "maxBytes": 10485760, "backupCount": 5}, "error_file": {"class": "logging.handlers.RotatingFileHandler", "level": "ERROR", "formatter": "default", "filename": "logs/error.log", "maxBytes": 10485760, "backupCount": 5}}, "loggers": {"uvicorn": {"level": "INFO", "handlers": ["console", "file"], "propagate": false}, "uvicorn.error": {"level": "INFO", "handlers": ["error_file"], "propagate": false}, "uvicorn.access": {"level": "INFO", "handlers": ["access_file"], "propagate": false}, "database": {"level": "INFO", "handlers": ["file", "error_file"], "propagate": false}, "sqlalchemy.engine": {"level": "WARNING", "handlers": ["file", "error_file"], "propagate": false}, "sqlalchemy.pool": {"level": "WARNING", "handlers": ["file", "error_file"], "propagate": false}}, "root": {"level": "INFO", "handlers": ["console", "file", "error_file"], "propagate": true}}