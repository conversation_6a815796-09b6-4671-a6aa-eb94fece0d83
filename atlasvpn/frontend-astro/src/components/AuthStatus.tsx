/**
 * @file components/AuthStatus.tsx
 * @description Component to display authentication status and provide manual retry
 */

import React from 'react';
import { apiClient } from '../api';
import './AuthStatus.css';

interface AuthStatusProps {
  className?: string;
}

export function AuthStatus({ className = '' }: AuthStatusProps) {
  const [authStatus, setAuthStatus] = React.useState(() => apiClient.getAuthStatus());
  const [isRetrying, setIsRetrying] = React.useState(false);

  const handleRetry = async () => {
    setIsRetrying(true);
    try {
      apiClient.resetAuthState();
      // Trigger a page reload to restart the authentication process
      window.location.reload();
    } catch (error) {
      console.error('Failed to retry authentication:', error);
    } finally {
      setIsRetrying(false);
    }
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  // Update status periodically
  React.useEffect(() => {
    const interval = setInterval(() => {
      setAuthStatus(apiClient.getAuthStatus());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Don't show anything if user is authenticated
  if (authStatus.isAuthenticated) {
    return null;
  }

  // Show retry options if authentication failed
  if (!authStatus.canRetry && authStatus.attemptsRemaining === 0) {
    return (
      <div className={`auth-status auth-status--failed ${className}`}>
        <div className="auth-status__content">
          <h3 className="auth-status__title">Authentication Failed</h3>
          <p className="auth-status__message">
            Unable to authenticate with Telegram after multiple attempts.
            Please try refreshing the page or restarting the app.
          </p>
          <div className="auth-status__actions">
            <button 
              onClick={handleRefresh}
              className="auth-status__button auth-status__button--primary"
              disabled={isRetrying}
            >
              {isRetrying ? 'Refreshing...' : 'Refresh Page'}
            </button>
            <button 
              onClick={handleRetry}
              className="auth-status__button auth-status__button--secondary"
              disabled={isRetrying}
            >
              {isRetrying ? 'Retrying...' : 'Reset & Retry'}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Show waiting state if authentication is in progress
  return (
    <div className={`auth-status auth-status--loading ${className}`}>
      <div className="auth-status__content">
        <h3 className="auth-status__title">Authenticating...</h3>
        <p className="auth-status__message">
          Connecting to Telegram...
          {authStatus.attemptsRemaining > 0 && (
            <span className="auth-status__attempts">
              {' '}({authStatus.attemptsRemaining} attempts remaining)
            </span>
          )}
        </p>
      </div>
    </div>
  );
}

export default AuthStatus;