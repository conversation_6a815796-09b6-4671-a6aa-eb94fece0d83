<template>
  <div 
    class="transaction-card group relative overflow-hidden rounded-lg border transition-all duration-200 hover:bg-gray-800/50"
    :class="getBorderClass()"
  >
    <!-- Background Glow -->
    <div 
      class="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      :class="getGlowClass()"
    />

    <div class="relative p-4">
      <div class="flex items-center justify-between">
        <!-- Transaction Info -->
        <div class="flex items-center space-x-4 flex-1 min-w-0">
          <!-- Icon -->
          <div 
            class="w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0"
            :class="getIconBgClass()"
          >
            <Icon 
              :name="getTransactionIcon()" 
              class="w-5 h-5"
              :class="getIconColorClass()"
            />
          </div>

          <!-- Details -->
          <div class="flex-1 min-w-0">
            <div class="flex items-center space-x-2 mb-1">
              <h4 class="text-white font-medium text-sm truncate">
                {{ getTransactionTitle() }}
              </h4>
              <div 
                class="px-2 py-0.5 rounded-full text-xs font-medium"
                :class="getStatusBadgeClass()"
              >
                {{ getStatusText() }}
              </div>
            </div>
            
            <div class="text-gray-400 text-xs">
              {{ formatDate(transaction.created_at) }}
            </div>
            
            <div v-if="transaction.description" class="text-gray-500 text-xs mt-1 truncate">
              {{ transaction.description }}
            </div>
          </div>
        </div>

        <!-- Amount and Balance -->
        <div class="text-right flex-shrink-0 ml-4">
          <div 
            class="font-semibold text-sm mb-1"
            :class="getAmountColorClass()"
          >
            {{ formatAmount() }}
          </div>
          <div class="text-gray-400 text-xs">
            Balance: {{ formatCurrency(transaction.balance_after) }}
          </div>
        </div>
      </div>

      <!-- Additional Details (expandable) -->
      <div v-if="showDetails" class="mt-3 pt-3 border-t border-gray-700">
        <div class="grid grid-cols-2 gap-3 text-xs">
          <div v-if="transaction.transaction_id">
            <span class="text-gray-400">Transaction ID:</span>
            <div class="text-white font-mono">{{ transaction.transaction_id.slice(-8) }}</div>
          </div>
          
          <div v-if="transaction.reference_id">
            <span class="text-gray-400">Reference:</span>
            <div class="text-white">{{ transaction.reference_id }}</div>
          </div>
          
          <div>
            <span class="text-gray-400">Type:</span>
            <div class="text-white capitalize">{{ transaction.type.replace(/_/g, ' ') }}</div>
          </div>
          
          <div>
            <span class="text-gray-400">Status:</span>
            <div class="text-white capitalize">{{ transaction.status || 'completed' }}</div>
          </div>
        </div>
      </div>

      <!-- Toggle Details Button -->
      <div class="mt-2 text-center">
        <UButton
          @click="showDetails = !showDetails"
          variant="ghost"
          size="xs"
          class="text-gray-400 hover:text-white"
        >
          {{ showDetails ? 'Less Details' : 'More Details' }}
          <Icon 
            :name="showDetails ? 'i-heroicons-chevron-up' : 'i-heroicons-chevron-down'" 
            class="w-3 h-3 ml-1" 
          />
        </UButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { format } from 'date-fns'
import type { Transaction } from '~/types'

interface Props {
  transaction: Transaction
}

const props = defineProps<Props>()

const showDetails = ref(false)

// Helper functions
const getTransactionIcon = () => {
  const icons: Record<string, string> = {
    wallet_topup: 'i-heroicons-arrow-up-circle',
    subscription_purchase: 'i-heroicons-shield-check',
    subscription_renewal: 'i-heroicons-arrow-path',
    referral_commission: 'i-heroicons-user-group',
    card_profit_claim: 'i-heroicons-gift',
    card_level_up: 'i-heroicons-arrow-up',
    card_purchase: 'i-heroicons-shopping-cart',
    admin_adjustment: 'i-heroicons-cog-6-tooth'
  }
  return icons[props.transaction.type] || 'i-heroicons-document-text'
}

const getTransactionTitle = () => {
  const titles: Record<string, string> = {
    wallet_topup: 'Wallet Top-up',
    subscription_purchase: 'VPN Purchase',
    subscription_renewal: 'VPN Renewal',
    referral_commission: 'Referral Bonus',
    card_profit_claim: 'Card Profit Claim',
    card_level_up: 'Card Level Up',
    card_purchase: 'Card Purchase',
    admin_adjustment: 'Admin Adjustment'
  }
  return titles[props.transaction.type] || 'Transaction'
}

const getIconBgClass = () => {
  if (props.transaction.amount >= 0) {
    return 'bg-green-500/20'
  } else {
    return 'bg-red-500/20'
  }
}

const getIconColorClass = () => {
  if (props.transaction.amount >= 0) {
    return 'text-green-400'
  } else {
    return 'text-red-400'
  }
}

const getBorderClass = () => {
  if (props.transaction.amount >= 0) {
    return 'border-green-500/20'
  } else {
    return 'border-red-500/20'
  }
}

const getGlowClass = () => {
  if (props.transaction.amount >= 0) {
    return 'bg-green-500/5'
  } else {
    return 'bg-red-500/5'
  }
}

const getAmountColorClass = () => {
  if (props.transaction.amount >= 0) {
    return 'text-green-400'
  } else {
    return 'text-red-400'
  }
}

const getStatusBadgeClass = () => {
  const status = props.transaction.status || 'completed'
  const classes: Record<string, string> = {
    completed: 'bg-green-500/20 text-green-400',
    pending: 'bg-yellow-500/20 text-yellow-400',
    failed: 'bg-red-500/20 text-red-400',
    cancelled: 'bg-gray-500/20 text-gray-400'
  }
  return classes[status] || 'bg-gray-500/20 text-gray-400'
}

const getStatusText = () => {
  const status = props.transaction.status || 'completed'
  return status.charAt(0).toUpperCase() + status.slice(1)
}

const formatAmount = () => {
  const amount = Math.abs(props.transaction.amount)
  const sign = props.transaction.amount >= 0 ? '+' : '-'
  return `${sign}${formatCurrency(amount)}`
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'MMM d, yyyy HH:mm')
}
</script>

<style scoped>
.transaction-card {
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.8) 0%, rgba(31, 41, 55, 0.6) 100%);
}
</style>
