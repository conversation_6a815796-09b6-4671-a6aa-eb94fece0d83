Daily Task Test Configuration
API Base URL: http://localhost:8000
Admin Username: admin_test
User Username: user
Test Mode: true


Starting Daily Task Tests...
INFO:httpx:HTTP Request: GET http://localhost:8000/auth/token "HTTP/1.1 405 Method Not Allowed"
INFO:__main__:Retrieved CSRF token for admin login
INFO:httpx:HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 200 OK"
INFO:__main__:Admin login successful
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/admin/tasks/create "HTTP/1.1 200 OK"
INFO:__main__:TaskOut schema validation passed
INFO:__main__:Created daily task with ID: 414
INFO:__main__:Test environment setup complete

Test environment setup complete

Testing User Login...
INFO:__main__:User login successful (using admin token)
User login test passed

Testing Schema Validation...
INFO:__main__:Testing schema validation for daily task endpoints
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily "HTTP/1.1 404 Not Found"
ERROR:__main__:Failed to get daily tasks: {"detail":"Not Found"}
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 7, longest: 7
INFO:__main__:Schema validation test passed
Schema validation test failed

Testing Streak Progression...
Debug: Checking task router implementation...
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 200 OK"
Daily task router info: {'next_check_in': 'Status: 200, Response: {"can_check_in":true,"base_time":"00:00 
UTC","next_check_in":"2025-04-09T00:00:00","time_left":0,"cu...', 'task_info': 'Status: ACTIVE, Completion ID: None', 'start_task': 'Status: 200, Response: 
{"task_id":414,"user_id":1,"status":"PENDING","started_at":"2025-04-08T21:39:42.500832","current_pro...'}
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/admin/daily/reset-streak/414 "HTTP/1.1 200 OK"
INFO:__main__:Streak reset successful: Streak reset successfully
INFO:__main__:Starting simulation at base time: 2024-12-29T12:00:00
INFO:__main__:Simulating day 1 check-in at 2024-12-29T12:00:00
INFO:__main__:Initializing task 414 with proper state transitions
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 400 Bad Request"
INFO:__main__:Task is already in progress, continuing with verification
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/verify "HTTP/1.1 200 OK"
INFO:__main__:Task verified successfully
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/claim "HTTP/1.1 400 Bad Request"
INFO:__main__:Task already claimed, continuing
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:__main__:Found task with status: PENDING, completion_id: 80
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:Next check-in status: 200, Response: {"can_check_in":false,"base_time":"00:00 UTC","next_check_in":"2024-12-30T00:00:00","time_left":720,"current_streak":0,"current_cycle_day":0,"last_check_in":"2025-03-17T00:00:00"}
INFO:__main__:Task is already in PENDING state with completion_id: 80
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/verify "HTTP/1.1 200 OK"
INFO:__main__:Task verified successfully
INFO:__main__:Verification response: {'success': False, 'status': 'PENDING', 'message': '', 'error_code': None, 'remaining_attempts': None, 'verification_time_left': None, 'next_verification_time': None, 'verification_data': None, 'claim_available_at': None}
ERROR:__main__:Verification was not successful: {'success': False, 'status': 'PENDING', 'message': '', 'error_code': None, 'remaining_attempts': None, 'verification_time_left': None, 'next_verification_time': None, 'verification_data': None, 'claim_available_at': None}
INFO:__main__:Attempting daily check-in with task status: PENDING
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/daily/check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyCheckInResponse schema validation passed
INFO:__main__:Daily check-in successful, streak: 1
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 1, longest: 7
INFO:__main__:Day 1 check-in successful with streak day 1
INFO:__main__:Advanced time to 2024-12-30T18:00:00
INFO:__main__:Simulating day 2 check-in at 2024-12-30T18:00:00
INFO:__main__:Initializing task 414 with proper state transitions
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 400 Bad Request"
ERROR:__main__:Failed to start task: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
ERROR:__main__:Failed to initialize task on day 2
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:__main__:Found task with status: COMPLETED, completion_id: 80
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:Next check-in status: 200, Response: {"can_check_in":true,"base_time":"00:00 UTC","next_check_in":"2024-12-31T00:00:00","time_left":0,"current_streak":1,"current_cycle_day":1,"last_check_in":"2024-12-29T12:00:00"}
INFO:__main__:Attempting daily check-in with task status: COMPLETED
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/daily/check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyCheckInResponse schema validation passed
INFO:__main__:Daily check-in successful, streak: 2
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 2, longest: 7
INFO:__main__:Day 2 check-in successful with streak day 2
INFO:__main__:Advanced time to 2025-01-01T00:00:00
INFO:__main__:Simulating day 3 check-in at 2025-01-01T00:00:00
INFO:__main__:Initializing task 414 with proper state transitions
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 400 Bad Request"
ERROR:__main__:Failed to start task: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
ERROR:__main__:Failed to initialize task on day 3
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:__main__:Found task with status: COMPLETED, completion_id: 80
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:Next check-in status: 200, Response: {"can_check_in":true,"base_time":"00:00 UTC","next_check_in":"2025-01-02T00:00:00","time_left":0,"current_streak":2,"current_cycle_day":2,"last_check_in":"2024-12-30T18:00:00"}
INFO:__main__:Attempting daily check-in with task status: COMPLETED
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/daily/check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyCheckInResponse schema validation passed
INFO:__main__:Daily check-in successful, streak: 3
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 3, longest: 7
INFO:__main__:Day 3 check-in successful with streak day 3
INFO:__main__:Advanced time to 2025-01-02T06:00:00
INFO:__main__:Streak progression simulation completed successfully
Streak progression test passed

Testing Streak Reset...
INFO:__main__:Starting streak reset test at base time: 2025-03-19T12:00:00
INFO:__main__:Initializing task 414 with proper state transitions
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 400 Bad Request"
ERROR:__main__:Failed to start task: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
ERROR:__main__:Failed to initialize task
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:__main__:Found task with status: COMPLETED, completion_id: 80
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:Next check-in status: 200, Response: {"can_check_in":true,"base_time":"00:00 UTC","next_check_in":"2025-03-20T00:00:00","time_left":0,"current_streak":3,"current_cycle_day":3,"last_check_in":"2025-01-01T00:00:00"}
INFO:__main__:Attempting daily check-in with task status: COMPLETED
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/daily/check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyCheckInResponse schema validation passed
INFO:__main__:Daily check-in successful, streak: 1
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 1, longest: 7
INFO:__main__:Advanced time to 2025-03-20T18:00:00
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/admin/daily/reset-streak/414 "HTTP/1.1 200 OK"
INFO:__main__:Streak reset successful: Streak reset successfully
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 0, longest: 7
INFO:__main__:Streak reset test passed
Streak reset test passed

Testing Cycle Completion...
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/admin/daily/reset-streak/414 "HTTP/1.1 200 OK"
INFO:__main__:Streak reset successful: Streak reset successfully
INFO:__main__:Starting cycle test at base time: 2025-03-09T12:00:00
INFO:__main__:Simulating day 1 of cycle at time 2025-03-09T12:00:00
INFO:__main__:Initializing task 414 with proper state transitions
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 400 Bad Request"
ERROR:__main__:Failed to start task: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
ERROR:__main__:Failed to initialize task on day 1
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:__main__:Found task with status: COMPLETED, completion_id: 80
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:Next check-in status: 200, Response: {"can_check_in":false,"base_time":"00:00 UTC","next_check_in":"2025-03-10T00:00:00","time_left":720,"current_streak":0,"current_cycle_day":0,"last_check_in":"2025-03-19T12:00:00"}
INFO:__main__:Attempting daily check-in with task status: COMPLETED
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/daily/check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyCheckInResponse schema validation passed
INFO:__main__:Daily check-in successful, streak: 1
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 1, longest: 7
INFO:__main__:Advanced time to 2025-03-10T18:00:00
INFO:__main__:Simulating day 2 of cycle at time 2025-03-10T18:00:00
INFO:__main__:Initializing task 414 with proper state transitions
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 400 Bad Request"
ERROR:__main__:Failed to start task: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
ERROR:__main__:Failed to initialize task on day 2
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:__main__:Found task with status: COMPLETED, completion_id: 80
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:Next check-in status: 200, Response: {"can_check_in":true,"base_time":"00:00 UTC","next_check_in":"2025-03-11T00:00:00","time_left":0,"current_streak":1,"current_cycle_day":1,"last_check_in":"2025-03-09T12:00:00"}
INFO:__main__:Attempting daily check-in with task status: COMPLETED
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/daily/check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyCheckInResponse schema validation passed
INFO:__main__:Daily check-in successful, streak: 2
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 2, longest: 7
INFO:__main__:Advanced time to 2025-03-12T00:00:00
INFO:__main__:Simulating day 3 of cycle at time 2025-03-12T00:00:00
INFO:__main__:Initializing task 414 with proper state transitions
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 400 Bad Request"
ERROR:__main__:Failed to start task: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
ERROR:__main__:Failed to initialize task on day 3
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:__main__:Found task with status: COMPLETED, completion_id: 80
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:Next check-in status: 200, Response: {"can_check_in":true,"base_time":"00:00 UTC","next_check_in":"2025-03-13T00:00:00","time_left":0,"current_streak":2,"current_cycle_day":2,"last_check_in":"2025-03-10T18:00:00"}
INFO:__main__:Attempting daily check-in with task status: COMPLETED
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/daily/check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyCheckInResponse schema validation passed
INFO:__main__:Daily check-in successful, streak: 3
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 3, longest: 7
INFO:__main__:Advanced time to 2025-03-13T06:00:00
INFO:__main__:Simulating day 4 of cycle at time 2025-03-13T06:00:00
INFO:__main__:Initializing task 414 with proper state transitions
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 400 Bad Request"
ERROR:__main__:Failed to start task: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
ERROR:__main__:Failed to initialize task on day 4
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:__main__:Found task with status: COMPLETED, completion_id: 80
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:Next check-in status: 200, Response: {"can_check_in":true,"base_time":"00:00 UTC","next_check_in":"2025-03-14T00:00:00","time_left":0,"current_streak":3,"current_cycle_day":3,"last_check_in":"2025-03-12T00:00:00"}
INFO:__main__:Attempting daily check-in with task status: COMPLETED
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/daily/check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyCheckInResponse schema validation passed
INFO:__main__:Daily check-in successful, streak: 4
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 4, longest: 7
INFO:__main__:Advanced time to 2025-03-14T12:00:00
INFO:__main__:Simulating day 5 of cycle at time 2025-03-14T12:00:00
INFO:__main__:Initializing task 414 with proper state transitions
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 400 Bad Request"
ERROR:__main__:Failed to start task: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
ERROR:__main__:Failed to initialize task on day 5
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:__main__:Found task with status: COMPLETED, completion_id: 80
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:Next check-in status: 200, Response: {"can_check_in":true,"base_time":"00:00 UTC","next_check_in":"2025-03-15T00:00:00","time_left":0,"current_streak":4,"current_cycle_day":4,"last_check_in":"2025-03-13T06:00:00"}
INFO:__main__:Attempting daily check-in with task status: COMPLETED
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/daily/check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyCheckInResponse schema validation passed
INFO:__main__:Daily check-in successful, streak: 5
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 5, longest: 7
INFO:__main__:Advanced time to 2025-03-15T18:00:00
INFO:__main__:Simulating day 6 of cycle at time 2025-03-15T18:00:00
INFO:__main__:Initializing task 414 with proper state transitions
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 400 Bad Request"
ERROR:__main__:Failed to start task: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
ERROR:__main__:Failed to initialize task on day 6
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:__main__:Found task with status: COMPLETED, completion_id: 80
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:Next check-in status: 200, Response: {"can_check_in":true,"base_time":"00:00 UTC","next_check_in":"2025-03-16T00:00:00","time_left":0,"current_streak":5,"current_cycle_day":5,"last_check_in":"2025-03-14T12:00:00"}
INFO:__main__:Attempting daily check-in with task status: COMPLETED
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/daily/check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyCheckInResponse schema validation passed
INFO:__main__:Daily check-in successful, streak: 6
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 6, longest: 7
INFO:__main__:Advanced time to 2025-03-17T00:00:00
INFO:__main__:Simulating day 7 of cycle at time 2025-03-17T00:00:00
INFO:__main__:Initializing task 414 with proper state transitions
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/414/start "HTTP/1.1 400 Bad Request"
ERROR:__main__:Failed to start task: {"detail":{"message":"You've already started this task. Please verify or claim your reward.","code":"TASK_ALREADY_EXISTS","original_message":"Task already has a completion record with status: COMPLETED"}}
ERROR:__main__:Failed to initialize task on day 7
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/available "HTTP/1.1 200 OK"
INFO:__main__:Found task with status: COMPLETED, completion_id: 80
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/next-check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:Next check-in status: 200, Response: {"can_check_in":true,"base_time":"00:00 UTC","next_check_in":"2025-03-18T00:00:00","time_left":0,"current_streak":6,"current_cycle_day":6,"last_check_in":"2025-03-15T18:00:00"}
INFO:__main__:Attempting daily check-in with task status: COMPLETED
INFO:httpx:HTTP Request: POST http://localhost:8000/api/tasks/daily/check-in/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyCheckInResponse schema validation passed
INFO:__main__:Daily check-in successful, streak: 7
INFO:httpx:HTTP Request: GET http://localhost:8000/api/tasks/daily/streak/414 "HTTP/1.1 200 OK"
INFO:__main__:DailyTaskStreakOut schema validation passed
INFO:__main__:Current streak: 7, longest: 7
INFO:__main__:Cycle completion bonus received: 100.0
INFO:__main__:Advanced time to 2025-03-18T06:00:00
INFO:__main__:Cycle completion test passed
Cycle completion test passed

Testing Wallet Balance Update...
INFO:httpx:HTTP Request: GET http://localhost:8000/users/wallet/ "HTTP/1.1 200 OK"
INFO:__main__:Current wallet balance: 3885.0
Wallet balance: 3885.0

Daily Task Test Report
             Test Results             
┏━━━━━━━━━━━━━━━━━━━━━━━━━━┳━━━━━━━━━┓
┃ Metric                   ┃ Value   ┃
┡━━━━━━━━━━━━━━━━━━━━━━━━━━╇━━━━━━━━━┩
│ Tests Run                │ 6       │
│ Tests Passed             │ 6       │
│ Tests Failed             │ 0       │
│ Success Rate             │ 100.00% │
│ Schema Validations       │ 25      │
│ Schema Validation Errors │ 0       │
└──────────────────────────┴─────────┘
All tests passed!
