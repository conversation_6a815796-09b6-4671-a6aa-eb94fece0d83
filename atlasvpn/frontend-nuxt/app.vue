<template>
  <div id="app" class="min-h-screen bg-[#030303] text-white">
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>

<script setup lang="ts">
// Global head configuration
useHead({
  title: 'AtlasVPN - Secure VPN Service',
  meta: [
    { name: 'description', content: 'Secure VPN service for your privacy and freedom' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1, user-scalable=no' },
    { name: 'theme-color', content: '#030303' },
    { name: 'apple-mobile-web-app-capable', content: 'yes' },
    { name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' }
  ],
  script: [
    {
      src: 'https://telegram.org/js/telegram-web-app.js',
      defer: true
    }
  ],
  htmlAttrs: {
    class: 'dark'
  }
})

// Initialize authentication on app start
const { verifySession } = useAuth()

onMounted(async () => {
  // Initialize UI theme
  const uiStore = useUiStore()
  uiStore.initializeTheme()
  
  // Try to verify existing session
  try {
    await verifySession()
  } catch (error) {
    console.error('Session verification failed:', error)
  }
})
</script>

<style>
/* Global styles */
html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  -webkit-tap-highlight-color: transparent;
}

/* Telegram WebApp specific styles */
.tg-viewport {
  height: 100vh;
  height: var(--tg-viewport-height, 100vh);
}

/* Disable text selection on UI elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(139, 92, 246, 0.3);
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(139, 92, 246, 0.5);
}
</style>
