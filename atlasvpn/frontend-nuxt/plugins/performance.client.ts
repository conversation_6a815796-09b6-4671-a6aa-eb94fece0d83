/**
 * Performance monitoring plugin for client-side performance tracking
 */

import { performanceMonitor, getPerformanceMetrics, logPerformanceMetrics } from '~/utils/performance';

export default defineNuxtPlugin(() => {
  // Only run on client side
  if (process.server) return;

  // Initialize performance monitoring
  const startTime = performance.now();

  // Log initial performance metrics after app is ready
  nuxtApp.hook('app:mounted', () => {
    setTimeout(() => {
      const metrics = getPerformanceMetrics();

      // Log performance metrics in development
      if (process.env.NODE_ENV === 'development') {
        logPerformanceMetrics();
      }

      // Report performance metrics to analytics service
      if (process.env.NODE_ENV === 'production') {
        // In production, you would send these metrics to your analytics service
        console.log('Performance metrics:', metrics);
      }
    }, 2000); // Wait 2 seconds for all metrics to be available
  });

  // Monitor route changes for performance
  const router = useRouter();
  router.beforeEach((to, from) => {
    // Mark route change start
    if (typeof window !== 'undefined' && window.performance?.mark) {
      window.performance.mark(`route-start-${to.path}`);
    }
  });

  router.afterEach((to, from) => {
    // Mark route change end and measure
    if (typeof window !== 'undefined' && window.performance?.mark) {
      window.performance.mark(`route-end-${to.path}`);
      
      try {
        window.performance.measure(
          `route-${to.path}`,
          `route-start-${to.path}`,
          `route-end-${to.path}`
        );
      } catch (error) {
        // Ignore measurement errors
      }
    }
  });

  // Monitor memory usage (if available)
  if (typeof window !== 'undefined' && (window.performance as any).memory) {
    setInterval(() => {
      const memory = (window.performance as any).memory;
      const usedMB = Math.round(memory.usedJSHeapSize / 1048576);
      const totalMB = Math.round(memory.totalJSHeapSize / 1048576);
      
      // Warn if memory usage is high
      if (usedMB > 100) {
        console.warn(`High memory usage: ${usedMB}MB / ${totalMB}MB`);
      }
    }, 30000); // Check every 30 seconds
  }

  // Preload critical resources
  const criticalResources = [
    '/avatars/Antiddos Gaurdian.webp',
    '/avatars/Datacenter Gauridan.webp',
    '/img/logo.png'
  ];

  criticalResources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource;
    link.as = resource.endsWith('.webp') || resource.endsWith('.png') ? 'image' : 'fetch';
    document.head.appendChild(link);
  });

  // Optimize images with lazy loading
  const observeImages = () => {
    const images = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window && images.length > 0) {
      const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            img.src = img.dataset.src || '';
            img.classList.remove('lazy');
            imageObserver.unobserve(img);
          }
        });
      });

      images.forEach(img => imageObserver.observe(img));
    }
  };

  // Run image optimization after DOM updates
  nextTick(() => {
    observeImages();
  });

  // Re-run image optimization on route changes
  router.afterEach(() => {
    nextTick(() => {
      observeImages();
    });
  });
});
