#!/bin/bash

# Redis Optimization Script for Security and Performance
# This script configures Redis for optimal security and performance in production

# Set default values
REDIS_HOST=${REDIS_HOST:-"localhost"}
REDIS_PORT=${REDIS_PORT:-6379}
REDIS_PASSWORD=${REDIS_PASSWORD:-""}
REDIS_CONFIG_FILE=${REDIS_CONFIG_FILE:-"/etc/redis/redis.conf"}
BACKUP_DIR="./redis_backups"

# Display help message
usage() {
    echo "Usage: $0 [options]"
    echo
    echo "Options:"
    echo "  --host <host>         Redis host (default: localhost)"
    echo "  --port <port>         Redis port (default: 6379)"
    echo "  --password <password> Redis password"
    echo "  --config <file>       Redis config file (default: /etc/redis/redis.conf)"
    echo "  --help                Display this help message"
    echo
    echo "Examples:"
    echo "  $0 --host redis.example.com --port 6379 --password mypassword"
    echo "  $0 --config /path/to/redis.conf"
    echo
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --host)
                REDIS_HOST="$2"
                shift 2
                ;;
            --port)
                REDIS_PORT="$2"
                shift 2
                ;;
            --password)
                REDIS_PASSWORD="$2"
                shift 2
                ;;
            --config)
                REDIS_CONFIG_FILE="$2"
                shift 2
                ;;
            --help)
                usage
                exit 0
                ;;
            *)
                echo "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
}

# Check if Redis CLI is installed
check_redis_cli() {
    if ! command -v redis-cli &> /dev/null; then
        echo "Error: redis-cli is not installed"
        echo "Please install Redis CLI before running this script"
        exit 1
    fi
}

# Create backup directory
create_backup_dir() {
    mkdir -p "$BACKUP_DIR"
    echo "Backup directory created: $BACKUP_DIR"
}

# Backup Redis configuration
backup_redis_config() {
    if [ -f "$REDIS_CONFIG_FILE" ]; then
        timestamp=$(date +"%Y%m%d_%H%M%S")
        backup_file="$BACKUP_DIR/redis_config_backup_$timestamp.conf"
        cp "$REDIS_CONFIG_FILE" "$backup_file"
        echo "Redis configuration backed up to: $backup_file"
    else
        echo "Warning: Redis configuration file not found at $REDIS_CONFIG_FILE"
    fi
}

# Apply security optimizations
apply_security_optimizations() {
    echo "Applying security optimizations..."
    
    # Set Redis password if provided
    if [ -n "$REDIS_PASSWORD" ]; then
        redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" CONFIG SET requirepass "$REDIS_PASSWORD"
        echo "Redis password set"
    fi
    
    # Disable dangerous commands
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET rename-command FLUSHALL ""
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET rename-command FLUSHDB ""
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET rename-command DEBUG ""
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET rename-command CONFIG ""
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET rename-command SHUTDOWN ""
    echo "Dangerous commands disabled"
    
    # Set protected mode
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET protected-mode yes
    echo "Protected mode enabled"
    
    # Disable commands that might be dangerous in production
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET rename-command KEYS ""
    echo "KEYS command disabled (can be expensive in production)"
}

# Apply performance optimizations
apply_performance_optimizations() {
    echo "Applying performance optimizations..."
    
    # Set memory limit (adjust based on your server's available memory)
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET maxmemory "256mb"
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET maxmemory-policy "allkeys-lru"
    echo "Memory limit set to 256MB with LRU eviction policy"
    
    # Optimize persistence
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET appendonly yes
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET appendfsync everysec
    echo "Persistence optimized with AOF and 1-second fsync"
    
    # Optimize client connections
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET timeout 300
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET tcp-keepalive 60
    echo "Client connection settings optimized"
    
    # Optimize for rate limiting use case
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG SET hz 20
    echo "Background task frequency optimized for rate limiting"
}

# Save configuration
save_configuration() {
    echo "Saving configuration..."
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" CONFIG REWRITE
    redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" -a "$REDIS_PASSWORD" SAVE
    echo "Configuration saved"
}

# Main function
main() {
    # Parse arguments
    parse_args "$@"
    
    # Check Redis CLI
    check_redis_cli
    
    # Create backup directory
    create_backup_dir
    
    # Backup Redis configuration
    backup_redis_config
    
    # Apply security optimizations
    apply_security_optimizations
    
    # Apply performance optimizations
    apply_performance_optimizations
    
    # Save configuration
    save_configuration
    
    echo "Redis optimization completed successfully"
}

# Run main function
main "$@" 