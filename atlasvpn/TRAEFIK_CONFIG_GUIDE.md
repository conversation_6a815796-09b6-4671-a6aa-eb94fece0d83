# Traefik Configuration Guide for AtlasVPN

## Overview
This guide documents the Traefik setup in `docker-compose-traefik.yml` for reliability, scalability, and future migration to Kubernetes or multi-server environments.

## Key Features
- **Security**: Removed insecure API access, enabled HTTPS with Let's Encrypt, HTTP/3 support.
- **Routing**: Dynamic labels for frontend (dev mode on port 3005 at app.atlasvip.cloud), backend API at /api, WebSocket at /ws.
- **Monitoring**: Added Prometheus metrics on port 8082 for Kubernetes integration.
- **Rate Limiting & CORS**: Configured for API protection.
- **Networks**: Isolated networks for traefik and backend.

## Future-Proofing
- Use Docker provider now; switch to Kubernetes provider later.
- Metrics enable cluster monitoring.
- Avoid hard-coded ports; use labels for flexibility.

## Maintenance
- Update via labels in services.
- For Kubernetes: Deploy Traefik as Helm chart, map similar configs.

Refer to Traefik docs for advanced features.