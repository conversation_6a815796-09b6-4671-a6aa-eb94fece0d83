/**
 * @file api/client.ts
 * @description Centralized API client with axios and fetch implementations
 * Centralized API client (consolidated from previous duplicate implementations)
 */

import axios from 'axios';
import type { AxiosInstance } from 'axios';
import { telegramWebApp } from '../utils/telegramWebApp';

// API Error class
export class APIError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = 'APIError';
  }
}

// Smart API URL detection
const getApiBaseUrl = (): string => {
  console.log('[ApiClient] 🔧 Smart API URL detection...');

  if (typeof window !== 'undefined') {
    // Client-side: Smart domain detection
    const hostname = window.location.hostname;
    const protocol = window.location.protocol;

    console.log('[ApiClient] 🌐 Current domain:', `${protocol}//${hostname}`);

    // If accessing from app.atlasvip.cloud (our test domain), use same domain's API
    if (hostname === 'app.atlasvip.cloud') {
      console.log('[ApiClient] 🚀 TEST DOMAIN: Using same domain API');
      return `${protocol}//${hostname}/api`;
    }

    // If accessing from localhost, use local API
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
      console.log('[ApiClient] 🔧 LOCALHOST: Using local API');
      return 'http://localhost:8000/api';
    }

    // For any other domain, use same domain's API (production will be different domain)
    console.log('[ApiClient] 🌐 OTHER DOMAIN: Using same domain API');
    return `${protocol}//${hostname}/api`;
  }

  // Server-side: Use environment variables or default to test domain
  const serverUrl = process.env.API_URL || process.env.PUBLIC_API_URL;
  console.log('[ApiClient] 🖥️ SERVER-SIDE URL:', serverUrl);

  return serverUrl || 'https://app.atlasvip.cloud/api';
};

// Create axios instance
const createApiInstance = (): AxiosInstance => {
  const baseURL = getApiBaseUrl();
  console.log('[ApiClient] 🚀 Creating NEW axios instance with baseURL:', baseURL);

  return axios.create({
    baseURL: baseURL,
    withCredentials: true,
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
    },
  });
};

// Export the API instance
export const api: AxiosInstance = createApiInstance();

// Request interceptor for authentication
api.interceptors.request.use((config) => {
  const fullUrl = (config.baseURL || '') + (config.url || '');
  console.log('🚀 [ApiClient] Request:', config.method?.toUpperCase(), fullUrl);

  // Add CSRF token if available (only for production)
  if (typeof document !== 'undefined' && !config.baseURL?.includes('localhost')) {
    const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken;
    }
  }

  return config;
});

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle authentication errors
      if (typeof window !== 'undefined') {
        window.location.href = '/';
      }
    }
    return Promise.reject(error);
  }
);

// Helper function for server-side requests
export const createServerApiClient = (cookies?: string): AxiosInstance => {
  return axios.create({
    baseURL: process.env.API_URL || 'https://app.atlasvip.cloud/api',
    timeout: 15000,
    headers: {
      'Content-Type': 'application/json',
      'Cookie': cookies || '',
    },
    withCredentials: true,
  });
};

// Fetch-based API Client class with automatic Telegram authentication
class APIClient {
  private baseURL: string;
  private token: string | null = null;
  private isAuthenticating = false;
  private authPromise: Promise<void> | null = null;
  private authAttempts = 0;
  private maxAuthAttempts = 3;
  private lastAuthAttempt = 0;
  private authCooldown = 30000; // 30 seconds cooldown between failed attempts

  constructor() {
    this.baseURL = getApiBaseUrl();
    console.log('[APIClient] Initialized with baseURL:', this.baseURL);
    console.log('[APIClient] Window location:', typeof window !== 'undefined' ? window.location.href : 'server-side');
    console.log('[APIClient] Environment:', import.meta.env.DEV ? 'development' : 'production');
    this.initializeAuth();
  }

  private async initializeAuth(): Promise<void> {
    // Try to get token from localStorage first
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }

    // Only attempt Telegram auth if we haven't exceeded max attempts and cooldown has passed
    if (!this.token && telegramWebApp.isAvailable() && this.canAttemptAuth()) {
      try {
        await this.authenticateWithTelegram();
      } catch (error) {
        console.warn('Failed to authenticate with Telegram:', error);
        this.authAttempts++;
        this.lastAuthAttempt = Date.now();
      }
    }
  }

  private canAttemptAuth(): boolean {
    const now = Date.now();
    const timeSinceLastAttempt = now - this.lastAuthAttempt;
    
    // Reset attempts if enough time has passed
    if (timeSinceLastAttempt > this.authCooldown * 2) {
      this.authAttempts = 0;
    }
    
    return this.authAttempts < this.maxAuthAttempts && timeSinceLastAttempt > this.authCooldown;
  }

  private async authenticateWithTelegram(): Promise<void> {
    if (this.isAuthenticating) {
      return this.authPromise || Promise.resolve();
    }

    this.isAuthenticating = true;
    this.authPromise = this._performTelegramAuth();

    try {
      await this.authPromise;
    } finally {
      this.isAuthenticating = false;
      this.authPromise = null;
    }
  }

  private async _performTelegramAuth(): Promise<void> {
    const webApp = telegramWebApp.getWebApp();
    if (!webApp?.initData) {
      throw new Error('No Telegram init data available');
    }

    const response = await fetch(`${this.baseURL}/auth/telegram/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        init_data: webApp.initData,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      
      // If we've exceeded max attempts, provide a more helpful error
      if (this.authAttempts >= this.maxAuthAttempts - 1) {
        throw new APIError(`Authentication failed after ${this.maxAuthAttempts} attempts. Please refresh the page or restart the app.`, response.status);
      }
      
      throw new APIError(
        errorData.detail || 'Authentication failed',
        response.status,
        errorData.code
      );
    }

    const data = await response.json();
    this.token = data.access_token;
    
    // Reset auth attempts on successful authentication
    this.authAttempts = 0;
    this.lastAuthAttempt = 0;

    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', this.token!);
    }
  }

  // Public method to reset authentication state (useful for manual retry)
  public resetAuthState(): void {
    this.authAttempts = 0;
    this.lastAuthAttempt = 0;
    this.token = null;
    this.isAuthenticating = false;
    this.authPromise = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  // Public method to check if authentication is available
  public canAuthenticate(): boolean {
    return telegramWebApp.isAvailable() && this.canAttemptAuth();
  }

  // Public method to get authentication status
  public getAuthStatus(): { isAuthenticated: boolean; canRetry: boolean; attemptsRemaining: number } {
    return {
      isAuthenticated: !!this.token,
      canRetry: this.canAttemptAuth(),
      attemptsRemaining: Math.max(0, this.maxAuthAttempts - this.authAttempts)
    };
  }

  private async ensureAuthenticated(): Promise<void> {
    if (this.token) return;

    if (telegramWebApp.isAvailable()) {
      await this.authenticateWithTelegram();
    } else {
      throw new APIError('No authentication method available', 401);
    }
  }

  async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    await this.ensureAuthenticated();

    const url = `${this.baseURL}${endpoint}`;
    console.log('[APIClient] Making request to:', url);
    console.log('[APIClient] Current baseURL:', this.baseURL);

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string> || {}),
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      if (response.status === 401) {
        // Only try to re-authenticate if we can attempt auth and haven't exceeded limits
        if (telegramWebApp.isAvailable() && this.canAttemptAuth()) {
          try {
            // Token expired, clear it and try to re-authenticate
            this.token = null;
            if (typeof window !== 'undefined') {
              localStorage.removeItem('auth_token');
            }

            await this.authenticateWithTelegram();
            // Retry the original request
            return this.request<T>(endpoint, options);
          } catch (authError) {
            console.warn('Re-authentication failed:', authError);
            this.authAttempts++;
            this.lastAuthAttempt = Date.now();
          }
        }
      }

      const errorData = await response.json().catch(() => ({}));
      throw new APIError(
        errorData.detail || errorData.message || `HTTP ${response.status}`,
        response.status,
        errorData.code
      );
    }

    return response.json();
  }
}

// Export singleton instance
export const apiClient = new APIClient();

// Error handling utilities
export const handleAPIError = (error: unknown): string => {
  if (error instanceof APIError) {
    switch (error.status) {
      case 401:
        return 'Authentication required. Please log in again.';
      case 403:
        return 'Access denied. You do not have permission for this action.';
      case 404:
        return 'Resource not found.';
      case 429:
        return 'Too many requests. Please try again later.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return error.message || 'An unexpected error occurred.';
    }
  }

  if (error instanceof Error) {
    return error.message;
  }

  return 'An unexpected error occurred.';
};

// Development mode helpers
export const isDevelopment = (): boolean => {
  return import.meta.env.DEV || import.meta.env.VITE_ENVIRONMENT === 'development';
};

export default apiClient;