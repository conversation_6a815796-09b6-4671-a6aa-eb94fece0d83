import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { useCurrentUserQuery, useTelegramLoginMutation, useLogoutMutation } from '../api/hooks';
import { createSessionManager, SessionManager } from '../services/SessionManager';
import type { User } from '../types/api';

interface AuthContextType {
  // User state
  user: User | null; // This expects User | null
  isAuthenticated: boolean;
  isLoading: boolean;
  isError: boolean;
  
  // Authentication actions
  login: (initData: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
  
  // Session management
  sessionStatus: 'authenticated' | 'unauthenticated' | 'loading' | 'error';
  lastActivity: Date | null;
  sessionExpiry: Date | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const queryClient = useQueryClient();
  const sessionManagerRef = useRef<SessionManager | null>(null);
  const [lastActivity, setLastActivity] = useState<Date | null>(null);
  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);
  
  // React Query hooks
  const {
    data: sessionData,
    isLoading,
    isError,
    refetch: refetchSession,
    error
  } = useCurrentUserQuery();
  
  const telegramLoginMutation = useTelegramLoginMutation();
  const logoutMutation = useLogoutMutation();
  
  // Fix: Explicitly type the user to ensure it's User | null
  const user: User | null = sessionData ?? null;
  const isAuthenticated = !!user;
  
  // Session status
  const getSessionStatus = (): 'authenticated' | 'unauthenticated' | 'loading' | 'error' => {
    if (isLoading) return 'loading';
    if (isError) return 'error';
    if (isAuthenticated) return 'authenticated';
    return 'unauthenticated';
  };
  
  const sessionStatus = getSessionStatus();
  
  // Activity tracking
  const updateActivity = useCallback(() => {
    const now = new Date();
    setLastActivity(now);
    
    // Update session expiry (15 minutes from last activity)
    const expiry = new Date(now.getTime() + 15 * 60 * 1000);
    setSessionExpiry(expiry);
    
    // Store in localStorage for persistence across tabs
    if (typeof window !== 'undefined') {
      localStorage.setItem('lastActivity', now.toISOString());
      localStorage.setItem('sessionExpiry', expiry.toISOString());
    }
  }, []);
  
  // Login function
  const login = useCallback(async (initData: string) => {
    try {
      console.log('[AuthContext] Starting login process');
      
      const result = await telegramLoginMutation.mutateAsync({
        init_data: initData,
        auth_method: 'telegram'
      });
      
      if (result?.user) {
        console.log('[AuthContext] Login successful');
        updateActivity();
      } else if (result?.isNewUser) {
        console.log('[AuthContext] New user detected, setup required');
        // New user flow is handled by the mutation's onSuccess
      }
    } catch (error) {
      console.error('[AuthContext] Login failed:', error);
      throw error;
    }
  }, [telegramLoginMutation, updateActivity]);
  
  // Logout function
  const logout = useCallback(async () => {
    try {
      console.log('[AuthContext] Starting logout process');
      await logoutMutation.mutateAsync();
      
      // Clear activity tracking
      setLastActivity(null);
      setSessionExpiry(null);
      
      if (typeof window !== 'undefined') {
        localStorage.removeItem('lastActivity');
        localStorage.removeItem('sessionExpiry');
        localStorage.removeItem('isFreshLogin');
      }
      
      console.log('[AuthContext] Logout completed');
    } catch (error) {
      console.error('[AuthContext] Logout failed:', error);
      // Even if logout fails on server, clear local state
    }
  }, [logoutMutation]);
  
  // Refresh session
  const refreshSession = useCallback(async () => {
    try {
      console.log('[AuthContext] Refreshing session');
      await refetchSession();
      if (isAuthenticated) {
        updateActivity();
      }
    } catch (error) {
      console.error('[AuthContext] Session refresh failed:', error);
    }
  }, [refetchSession, isAuthenticated, updateActivity]);
  
  // Initialize activity tracking from localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedActivity = localStorage.getItem('lastActivity');
      const storedExpiry = localStorage.getItem('sessionExpiry');
      
      if (storedActivity) {
        setLastActivity(new Date(storedActivity));
      }
      
      if (storedExpiry) {
        setSessionExpiry(new Date(storedExpiry));
      }
    }
  }, []);
  
  // Update activity when user becomes authenticated
  useEffect(() => {
    if (isAuthenticated && !lastActivity) {
      updateActivity();
    }
  }, [isAuthenticated, lastActivity, updateActivity]);
  
  // Activity tracking for user interactions
  useEffect(() => {
    if (!isAuthenticated) return;
    
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleActivity = () => {
      updateActivity();
    };
    
    // Throttle activity updates to once per minute
    let lastUpdate = 0;
    const throttledHandler = () => {
      const now = Date.now();
      if (now - lastUpdate > 60000) { // 1 minute
        lastUpdate = now;
        handleActivity();
      }
    };
    
    events.forEach(event => {
      document.addEventListener(event, throttledHandler, true);
    });
    
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, throttledHandler, true);
      });
    };
  }, [isAuthenticated, updateActivity]);
  
  // Session expiry check
  useEffect(() => {
    if (!isAuthenticated || !sessionExpiry) return;
    
    const checkExpiry = () => {
      const now = new Date();
      if (now > sessionExpiry) {
        console.log('[AuthContext] Session expired, logging out');
        logout();
      }
    };
    
    // Check every minute
    const interval = setInterval(checkExpiry, 60000);
    
    return () => clearInterval(interval);
  }, [isAuthenticated, sessionExpiry, logout]);
  
  // Cross-tab session synchronization
  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'lastActivity' && e.newValue) {
        setLastActivity(new Date(e.newValue));
      }
      if (e.key === 'sessionExpiry' && e.newValue) {
        setSessionExpiry(new Date(e.newValue));
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);
  
  const value: AuthContextType = {
    user, // Now properly typed as User | null
    isAuthenticated,
    isLoading,
    isError,
    login,
    logout,
    refreshSession,
    sessionStatus,
    lastActivity,
    sessionExpiry
  };
  
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;