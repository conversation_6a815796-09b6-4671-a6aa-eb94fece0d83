=== Task API Test Suite - Wed Apr  2 19:26:15 UTC 2025 ===

=== Running task_api_test.py ===


Starting Core Task API Tests...
2025-04-02 19:26:28,737 - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 403 Forbidden"
2025-04-02 19:26:28,739 - ERROR - Admin login failed: {"detail":"CSRF token missing or invalid"}
2025-04-02 19:26:28,753 - ERROR - Admin login failed, cannot continue tests
  Setup failed

❌ task_api_test.py failed with exit code 1

-------------------------------------------

=== Running api_task_flow_test.py ===

2025-04-02 19:26:45,943 - httpx - INFO - HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 403 Forbidden"
2025-04-02 19:26:45,968 - APITaskFlowTest - ERROR - Failed to login as admin: 403 - {"detail":"CSRF token missing or invalid"}
2025-04-02 19:26:46,504 - APITaskFlowTest - ERROR - Admin login failed, cannot continue

✅ api_task_flow_test.py completed successfully

-------------------------------------------

=== Running task_performance_test.py ===

INFO:TaskPerformance:Initializing database...
INFO:database:Database initialized successfully
INFO:TaskPerformance:Setting up 100 test users...
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
WARNING:database:Passive checkpoint failed: Not an executable object: 'PRAGMA wal_checkpoint(PASSIVE)'
Traceback (most recent call last):
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2115, in _exec_insertmany_context
    dialect.do_execute(
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
sqlite3.IntegrityError: UNIQUE constraint failed: users.referral_code

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/root/project/backend/scripts/task_performance_test.py", line 499, in <module>
    asyncio.run(main())
  File "/usr/lib/python3.10/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/root/project/backend/scripts/task_performance_test.py", line 469, in main
    await tester.setup_test_users(num_users)
  File "/root/project/backend/scripts/task_performance_test.py", line 103, in setup_test_users
    self.db.commit()
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/state_changes.py", line 139, in _go
    ret_value = fn(self, *arg, **kw)
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 4353, in flush
    self._flush(objects)
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 4488, in _flush
    with util.safe_reraise():
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/util/langhelpers.py", line 146, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/session.py", line 4449, in _flush
    flush_context.execute()
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/unitofwork.py", line 466, in execute
    rec.execute(self)
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/persistence.py", line 93, in save_obj
    _emit_insert_statements(
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/orm/persistence.py", line 1143, in _emit_insert_statements
    result = connection.execute(
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1416, in execute
    return meth(
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/sql/elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1638, in _execute_clauseelement
    ret = self._execute_context(
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 1841, in _execute_context
    return self._exec_insertmany_context(dialect, context)
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2123, in _exec_insertmany_context
    self._handle_dbapi_exception(
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2352, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/engine/base.py", line 2115, in _exec_insertmany_context
    dialect.do_execute(
  File "/root/project/backend/venv/lib/python3.10/site-packages/sqlalchemy/engine/default.py", line 942, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.IntegrityError: (sqlite3.IntegrityError) UNIQUE constraint failed: users.referral_code
[SQL: INSERT INTO users (telegram_id, username, hashed_password, role, wallet_balance, created_at, first_name, last_name, email, is_active, telegram_photo_url, discount_percent, marzban_username, marzban_subscription_url, referred_by, referral_code, device_platform, device_id, last_login) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) RETURNING id]
[parameters: (None, 'perf_user_99205', 'test_password', 'user', 0.0, '2025-04-02 19:28:01.765308', None, None, None, 1, None, 0.0, None, None, None, 'REF4795', None, None, None)]
(Background on this error at: https://sqlalche.me/e/20/gkpj)

❌ task_performance_test.py failed with exit code 1

-------------------------------------------

=== Test Suite Summary ===
Total scripts: 3
Failed scripts: 2
Log file: /root/project/backend/scripts/logs/all_tests_20250402_192615.log

[0;31m2 test scripts failed![0m
