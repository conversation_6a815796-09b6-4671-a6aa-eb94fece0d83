// Currency formatting
export function formatCurrency(amount: number, options?: {
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  compact?: boolean;
}): string {
  const {
    minimumFractionDigits = 0,
    maximumFractionDigits = 2,
    compact = false,
  } = options || {};

  if (compact && amount >= 1000000) {
    return `${(amount / 1000000).toFixed(1)}M`;
  }
  
  if (compact && amount >= 1000) {
    return `${(amount / 1000).toFixed(1)}K`;
  }

  return new Intl.NumberFormat('en-US', {
    minimumFractionDigits,
    maximumFractionDigits,
  }).format(amount);
}

// Number formatting with abbreviations
export function formatNumber(num: number, options?: {
  compact?: boolean;
  precision?: number;
}): string {
  const { compact = false, precision = 1 } = options || {};

  if (!compact) {
    return new Intl.NumberFormat('en-US').format(num);
  }

  const units = ['', 'K', 'M', 'B', 'T'];
  let unitIndex = 0;
  let value = num;

  while (value >= 1000 && unitIndex < units.length - 1) {
    value /= 1000;
    unitIndex++;
  }

  return `${value.toFixed(precision)}${units[unitIndex]}`;
}

// Time formatting
export function formatTime(date: string | Date): string {
  const now = new Date();
  const target = new Date(date);
  const diffInSeconds = Math.floor((now.getTime() - target.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'just now';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return `${diffInMinutes}m ago`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return `${diffInHours}h ago`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return `${diffInDays}d ago`;
  }

  return target.toLocaleDateString();
}

// Duration formatting
export function formatDuration(seconds: number): string {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const remainingSeconds = seconds % 60;

  if (hours > 0) {
    return `${hours}h ${minutes}m`;
  }
  
  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`;
  }
  
  return `${remainingSeconds}s`;
}

// Date formatting
export function formatDate(date: string | Date, options?: {
  includeTime?: boolean;
  format?: 'short' | 'medium' | 'long';
}): string {
  const { includeTime = false, format = 'medium' } = options || {};
  const target = new Date(date);

  const dateOptions: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: format === 'short' ? 'short' : format === 'long' ? 'long' : 'short',
    day: 'numeric',
  };

  if (includeTime) {
    dateOptions.hour = '2-digit';
    dateOptions.minute = '2-digit';
  }

  return target.toLocaleDateString('en-US', dateOptions);
}

// Percentage formatting
export function formatPercentage(value: number, options?: {
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
}): string {
  const {
    minimumFractionDigits = 0,
    maximumFractionDigits = 1,
  } = options || {};

  return new Intl.NumberFormat('en-US', {
    style: 'percent',
    minimumFractionDigits,
    maximumFractionDigits,
  }).format(value / 100);
}

// Text truncation
export function truncateText(text: string, maxLength: number, suffix = '...'): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.slice(0, maxLength - suffix.length) + suffix;
}

// Username formatting
export function formatUsername(user: {
  username?: string;
  first_name?: string;
  last_name?: string;
}): string {
  if (user.username) {
    return `@${user.username}`;
  }
  
  if (user.first_name) {
    return user.last_name ? `${user.first_name} ${user.last_name}` : user.first_name;
  }
  
  return 'Anonymous';
}

// File size formatting
export function formatFileSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(1)} ${units[unitIndex]}`;
}

// Progress formatting
export function formatProgress(current: number, total: number): {
  percentage: number;
  text: string;
} {
  const percentage = total > 0 ? Math.min((current / total) * 100, 100) : 0;
  const text = `${formatNumber(current)} / ${formatNumber(total)}`;
  
  return { percentage, text };
}

// Card level formatting
export function formatCardLevel(level: number, maxLevel?: number): string {
  if (maxLevel) {
    return `Level ${level}/${maxLevel}`;
  }
  return `Level ${level}`;
}

// Profit rate formatting
export function formatProfitRate(amount: number, period: 'hour' | 'day' | 'week' = 'hour'): string {
  const periodSuffix = {
    hour: '/h',
    day: '/day',
    week: '/week',
  };
  
  return `+${formatCurrency(amount)}${periodSuffix[period]}`;
}

// Task status formatting
export function formatTaskStatus(status: string): string {
  const statusMap: Record<string, string> = {
    available: 'Available',
    in_progress: 'In Progress',
    completed: 'Completed',
    claimed: 'Claimed',
  };
  
  return statusMap[status] || status;
}

// Transaction type formatting
export function formatTransactionType(type: string): string {
  const typeMap: Record<string, string> = {
    earn: 'Earned',
    spend: 'Spent',
    referral: 'Referral Bonus',
    task: 'Task Reward',
    card_upgrade: 'Card Upgrade',
    vpn_purchase: 'VPN Purchase',
  };
  
  return typeMap[type] || type;
}

// URL formatting
export function formatShareUrl(baseUrl: string, params: Record<string, string>): string {
  const url = new URL(baseUrl);
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.set(key, value);
  });
  return url.toString();
}

// Phone number formatting
export function formatPhoneNumber(phoneNumber: string): string {
  const cleaned = phoneNumber.replace(/\D/g, '');
  
  if (cleaned.length === 11 && cleaned.startsWith('1')) {
    return `+1 (${cleaned.slice(1, 4)}) ${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }
  
  return phoneNumber;
}

// Color formatting for status
export function getStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    active: 'text-green-400',
    inactive: 'text-gray-400',
    pending: 'text-yellow-400',
    completed: 'text-green-400',
    failed: 'text-red-400',
    cancelled: 'text-gray-400',
    expired: 'text-red-400',
  };
  
  return colorMap[status] || 'text-gray-400';
}

// Validation helpers
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidUsername(username: string): boolean {
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  return usernameRegex.test(username);
}

// Utility for safe JSON parsing
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json);
  } catch {
    return fallback;
  }
}
