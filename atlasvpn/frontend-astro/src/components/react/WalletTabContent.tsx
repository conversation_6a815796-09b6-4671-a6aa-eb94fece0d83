/**
 * @file WalletTabContent.tsx
 * @description Wallet tab content component - balance and transactions
 * Placeholder for the full wallet tab implementation
 */

import React from 'react';

const WalletTabContent: React.FC = () => {
  return (
    <div className="flex-1 overflow-y-auto p-4">
      <div className="max-w-md mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-2">Wallet</h1>
          <p className="text-white/70">Manage your balance and transactions</p>
        </div>

        {/* Balance Card */}
        <div className="bg-gradient-to-br from-accent-500/20 to-purple-600/20 backdrop-blur-sm rounded-xl p-6 border border-accent-500/30">
          <div className="text-center">
            <div className="text-white/70 text-sm font-medium mb-2">Total Balance</div>
            <div className="text-white text-3xl font-bold mb-4">$0.00</div>
            <div className="flex space-x-3">
              <button className="flex-1 bg-accent-500 hover:bg-accent-600 text-white py-2 rounded-lg text-sm font-medium transition-colors">
                Withdraw
              </button>
              <button className="flex-1 bg-white/10 hover:bg-white/20 text-white py-2 rounded-lg text-sm font-medium transition-colors">
                Deposit
              </button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-3">
          <div className="bg-white/5 backdrop-blur-sm rounded-xl p-3 border border-white/10 text-center">
            <div className="text-green-400 text-sm font-medium">Earned</div>
            <div className="text-white text-lg font-bold">$0.00</div>
          </div>
          <div className="bg-white/5 backdrop-blur-sm rounded-xl p-3 border border-white/10 text-center">
            <div className="text-blue-400 text-sm font-medium">Spent</div>
            <div className="text-white text-lg font-bold">$0.00</div>
          </div>
          <div className="bg-white/5 backdrop-blur-sm rounded-xl p-3 border border-white/10 text-center">
            <div className="text-purple-400 text-sm font-medium">Pending</div>
            <div className="text-white text-lg font-bold">$0.00</div>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-white">Payment Methods</h2>
          <button className="w-full bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl p-4 text-left transition-colors">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-accent-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-accent-400 text-lg">💳</span>
                </div>
                <div>
                  <div className="text-white font-medium">Add Payment Method</div>
                  <div className="text-white/60 text-sm">Connect your bank or card</div>
                </div>
              </div>
              <div className="text-white/40">→</div>
            </div>
          </button>
        </div>

        {/* Recent Transactions */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-white">Recent Transactions</h2>
            <button className="text-accent-400 text-sm font-medium">View All</button>
          </div>
          <div className="bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
            <div className="text-center text-white/60 py-8">
              <div className="text-4xl mb-2">💰</div>
              <div>No transactions yet</div>
              <div className="text-sm">Your transaction history will appear here</div>
            </div>
          </div>
        </div>

        {/* Security Section */}
        <div className="space-y-3">
          <h2 className="text-lg font-semibold text-white">Security</h2>
          <div className="space-y-2">
            <div className="flex items-center justify-between bg-white/5 backdrop-blur-sm rounded-xl p-4 border border-white/10">
              <div className="flex items-center space-x-3">
                <div className="text-green-400 text-lg">🔒</div>
                <div>
                  <div className="text-white font-medium">Two-Factor Auth</div>
                  <div className="text-white/60 text-sm">Enabled</div>
                </div>
              </div>
              <div className="w-5 h-3 bg-green-500 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WalletTabContent;
