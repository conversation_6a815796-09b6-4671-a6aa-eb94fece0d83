{"timestamp": "2025-03-25T22:37:51.501541", "total_tests": 2, "passed": 2, "failed": 0, "success_rate": 100.0, "results": [{"script": "test_security_headers.py", "start_time": "2025-03-25T22:37:39.285305", "success": true, "output": "\n================================================================================\n                    SECURITY HEADERS AND BOT DETECTION TEST                     \n================================================================================\n\n\nTesting security headers on main page...\n2025-03-25 22:37:39,531 - INFO - Testing security headers for: http://localhost:8000\n2025-03-25 22:37:39,543 - WARNING - Header X-Frame-Options has incorrect value: DENY\n⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN\n✓ All required security headers are present\n\nTesting Content-Security-Policy header...\n✓ Content-Security-Policy is properly configured\n\nTesting security headers on API endpoints...\n2025-03-25 22:37:39,543 - INFO - Testing security headers for: http://localhost:8000/api/tasks/available\n2025-03-25 22:37:39,565 - WARNING - Header X-Frame-Options has incorrect value: DENY\n⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN\n✓ Endpoint /api/tasks/available has all required security headers\n2025-03-25 22:37:39,566 - INFO - Testing security headers for: http://localhost:8000/api/security/verify-captcha\n2025-03-25 22:37:39,578 - WARNING - Header X-Frame-Options has incorrect value: DENY\n⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN\n✓ Endpoint /api/security/verify-captcha has all required security headers\n2025-03-25 22:37:39,579 - INFO - Testing security headers for: http://localhost:8000/auth/token\n2025-03-25 22:37:39,595 - WARNING - Header X-Frame-Options has incorrect value: DENY\n⚠ Header X-Frame-Options has value DENY, expected SAMEORIGIN\n✓ Endpoint /auth/token has all required security headers\n\nTesting bot detection...\n2025-03-25 22:37:39,607 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)': 404\n2025-03-25 22:37:39,628 - INFO - Bot test with User-Agent 'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)': 404\n2025-03-25 22:37:39,647 - INFO - Bot test with User-Agent 'python-requests/2.25.1': 404\n2025-03-25 22:37:39,664 - INFO - Bot test with User-Agent 'Wget/1.20.3 (linux-gnu)': 404\n2025-03-25 22:37:39,682 - INFO - Bot test with User-Agent 'curl/7.68.0': 404\n⚠ Legitimate search engine bots blocked: Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)\n⚠ Automation tools allowed without CAPTCHA: python-requests/2.25.1, Wget/1.20.3 (linux-gnu), curl/7.68.0\n✗ Bot detection has issues\n\nDetailed Bot Detection Results:\n  Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html): ✓ OK\n  Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm): ✓ OK\n  python-requests/2.25.1: ✓ OK\n  Wget/1.20.3 (linux-gnu): ✓ OK\n  curl/7.68.0: ✓ OK\n", "error": "", "duration": 0.4346494674682617}, {"script": "test_rate_limit_captcha.py", "start_time": "2025-03-25T22:37:39.720466", "success": true, "output": "\n================================================================================\n                         RATE LIMITING AND CAPTCHA TEST                         \n================================================================================\n\n\nTesting login rate limiting...\n2025-03-25 22:37:40,476 - INFO - Testing rate limiting for: http://localhost:8000/auth/token with 12 attempts\n2025-03-25 22:37:40,487 - INFO - Attempt 1: Status 500\n2025-03-25 22:37:41,177 - INFO - Attempt 2: Status 500\n2025-03-25 22:37:41,385 - INFO - Attempt 3: Status 500\n2025-03-25 22:37:41,596 - INFO - Attempt 4: Status 500\n2025-03-25 22:37:41,807 - INFO - Attempt 5: Status 500\n2025-03-25 22:37:42,164 - INFO - Attempt 6: Status 500\n2025-03-25 22:37:42,378 - INFO - Attempt 7: Status 500\n2025-03-25 22:37:42,589 - INFO - Attempt 8: Status 500\n2025-03-25 22:37:42,800 - INFO - Attempt 9: Status 500\n2025-03-25 22:37:43,015 - INFO - Attempt 10: Status 500\n2025-03-25 22:37:43,226 - INFO - Attempt 11: Status 500\n2025-03-25 22:37:43,438 - INFO - Attempt 12: Status 500\n✗ Rate limiting was not triggered after 12 attempts\n✗ Login rate limiting is not working properly\n\nRate limit headers for login attempts:\n  Attempt 1: OK - Headers: {}\n  Attempt 2: OK - Headers: {}\n  Attempt 3: OK - Headers: {}\n  Attempt 4: OK - Headers: {}\n  Attempt 5: OK - Headers: {}\n  Attempt 6: OK - Headers: {}\n  Attempt 7: OK - Headers: {}\n  Attempt 8: OK - Headers: {}\n  Attempt 9: OK - Headers: {}\n  Attempt 10: OK - Headers: {}\n  Attempt 11: OK - Headers: {}\n  Attempt 12: OK - Headers: {}\n\nTesting rate limiting for API endpoints...\n\nTesting rate limiting for /api/tasks/available...\n2025-03-25 22:37:43,641 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/available with 10 attempts\n2025-03-25 22:37:43,668 - INFO - Attempt 1: Status 401\n2025-03-25 22:37:43,896 - INFO - Attempt 2: Status 401\n2025-03-25 22:37:44,141 - INFO - Attempt 3: Status 401\n2025-03-25 22:37:44,366 - INFO - Attempt 4: Status 401\n2025-03-25 22:37:44,595 - INFO - Attempt 5: Status 401\n2025-03-25 22:37:44,819 - INFO - Attempt 6: Status 401\n2025-03-25 22:37:45,044 - INFO - Attempt 7: Status 401\n2025-03-25 22:37:45,276 - INFO - Attempt 8: Status 401\n2025-03-25 22:37:45,510 - INFO - Attempt 9: Status 401\n2025-03-25 22:37:45,730 - INFO - Attempt 10: Status 401\n✗ Rate limiting was not triggered after 10 attempts\n✗ Rate limiting is not working properly for /api/tasks/available\n\nTesting rate limiting for /api/tasks/analytics...\n2025-03-25 22:37:45,931 - INFO - Testing rate limiting for: http://localhost:8000/api/tasks/analytics with 10 attempts\n2025-03-25 22:37:45,953 - INFO - Attempt 1: Status 401\n2025-03-25 22:37:46,188 - INFO - Attempt 2: Status 401\n2025-03-25 22:37:46,412 - INFO - Attempt 3: Status 401\n2025-03-25 22:37:46,640 - INFO - Attempt 4: Status 401\n2025-03-25 22:37:46,867 - INFO - Attempt 5: Status 401\n2025-03-25 22:37:47,093 - INFO - Attempt 6: Status 401\n2025-03-25 22:37:47,314 - INFO - Attempt 7: Status 401\n2025-03-25 22:37:47,544 - INFO - Attempt 8: Status 401\n2025-03-25 22:37:47,766 - INFO - Attempt 9: Status 401\n2025-03-25 22:37:47,991 - INFO - Attempt 10: Status 401\n✗ Rate limiting was not triggered after 10 attempts\n✗ Rate limiting is not working properly for /api/tasks/analytics\n\nTesting rate limiting for /auth/register...\n2025-03-25 22:37:48,193 - INFO - Testing rate limiting for: http://localhost:8000/auth/register with 10 attempts\n2025-03-25 22:37:48,206 - INFO - Attempt 1: Status 405\n2025-03-25 22:37:48,419 - INFO - Attempt 2: Status 405\n2025-03-25 22:37:48,633 - INFO - Attempt 3: Status 405\n2025-03-25 22:37:48,846 - INFO - Attempt 4: Status 405\n2025-03-25 22:37:49,058 - INFO - Attempt 5: Status 405\n2025-03-25 22:37:49,271 - INFO - Attempt 6: Status 405\n2025-03-25 22:37:49,485 - INFO - Attempt 7: Status 405\n2025-03-25 22:37:49,697 - INFO - Attempt 8: Status 405\n2025-03-25 22:37:49,909 - INFO - Attempt 9: Status 405\n2025-03-25 22:37:50,122 - INFO - Attempt 10: Status 405\n✗ Rate limiting was not triggered after 10 attempts\n✗ Rate limiting is not working properly for /auth/register\n\nTesting Cloudflare CAPTCHA integration...\n2025-03-25 22:37:50,345 - INFO - Testing CAPTCHA integration for login endpoint: /auth/token\n2025-03-25 22:37:50,365 - INFO - No CAPTCHA indicators found for login endpoint, checking rejection behavior\n2025-03-25 22:37:50,376 - INFO - Testing CAPTCHA integration for register endpoint: /auth/register\n2025-03-25 22:37:50,396 - INFO - No CAPTCHA indicators found for register endpoint, checking rejection behavior\n2025-03-25 22:37:50,397 - INFO - Testing CAPTCHA integration for admin_login endpoint: /auth/admin/login\n2025-03-25 22:37:50,407 - INFO - No CAPTCHA indicators found for admin_login endpoint, checking rejection behavior\n⚠ No CAPTCHA detected for login endpoint\n⚠ No CAPTCHA detected for register endpoint\n⚠ No CAPTCHA detected for admin_login endpoint\n\nSimulating distributed attack...\n2025-03-25 22:37:50,408 - INFO - Simulating distributed attack on http://localhost:8000/api/tasks/available with 30 requests, 5 concurrent\n2025-03-25 22:37:51,433 - INFO - Distributed attack results: 0 blocked, 30 succeeded, 0 errored\n✗ Distributed attack was not effectively blocked (block rate: 0.00%)\n\nSample distributed attack results:\n  Request from IP *************: ALLOWED\n  Request from IP **************: ALLOWED\n  Request from IP ***************: ALLOWED\n  Request from IP ***************: ALLOWED\n  Request from IP ***************: ALLOWED\n", "error": "", "duration": 11.7783682346344}]}