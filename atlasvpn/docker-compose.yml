# AtlasVPN Docker Compose with Traefik v3.3 - Modern Setup 2024
# No version field (obsolete in modern Docker Compose)

services:
  # Traefik v3.3 - Modern Cloud-Native Reverse Proxy
  traefik:
    image: traefik:v3.3
    container_name: atlasvpn-traefik
    command:
      # Dashboard Configuration
      - "--api.dashboard=true"
      # Removed --api.insecure=true for security
      
      # Ping endpoint for health checks
      - "--ping=true"
      
      # Log Level
      - "--log.level=DEBUG"
      - "--accesslog=true"
      
      # Docker Provider for Service Discovery
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--providers.docker.network=atlasvpn-traefik"
      
      # Enable real IP forwarding for proper client IP detection
      - "--entrypoints.websecure.forwardedHeaders.trustedIPs=0.0.0.0/0"
      - "--entrypoints.web.forwardedHeaders.trustedIPs=0.0.0.0/0"
      
      # Entry Points
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      
      # HTTP/3 Support (Production Ready in Traefik v3)
      - "--entrypoints.websecure.http3={}"
      
      # HTTP to HTTPS Redirect
      - "--entrypoints.web.http.redirections.entrypoint.to=websecure"
      - "--entrypoints.web.http.redirections.entrypoint.scheme=https"
      
      # Let's Encrypt SSL/TLS
      - "--certificatesresolvers.letsencrypt.acme.httpchallenge=true"
      - "--certificatesresolvers.letsencrypt.acme.httpchallenge.entrypoint=web"
      - "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>"
      
      - "--certificatesresolvers.letsencrypt.acme.storage=/letsencrypt/acme.json"
      
      # Rate Limiting - Built-in and Flexible
      - "--api.dashboard=true"
      
      # Metrics for Prometheus (for future Kubernetes monitoring)
      - "--metrics.prometheus=true"
      - "--metrics.prometheus.entryPoint=metrics"
      - "--entrypoints.metrics.address=:8082"
    ports:
      - "80:80"
      - "443:443"     # HTTPS (TCP)
      - "443:443/udp" # HTTP/3 (UDP)
      - "8080:8080"   # Dashboard (will be secured)
      - "8082:8082"   # Metrics
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - "traefik_letsencrypt:/letsencrypt"
    labels:
      # Traefik Dashboard - Secured
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.app.atlasvip.cloud`)"
      - "traefik.http.routers.dashboard.entrypoints=websecure"
      - "traefik.http.routers.dashboard.tls=true"
      - "traefik.http.routers.dashboard.tls.certresolver=letsencrypt"
      - "traefik.http.routers.dashboard.service=api@internal"
      
      # Dashboard Authentication (Basic Auth)
      - "traefik.http.routers.dashboard.middlewares=dashboard-auth"
      - "traefik.http.middlewares.dashboard-auth.basicauth.users=admin:$$apr1$$89Uw4W5T$$ofEp/3HrCeeUYb1w0yxgh0"  # admin:password123
    networks:
      - atlasvpn-traefik
      - atlasvpn-backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL Database Service
  postgres:
    image: postgres:15-alpine
    container_name: atlasvpn-postgres
    environment:
      POSTGRES_DB: atlasvpn
      POSTGRES_USER: atlasvpn_user
      POSTGRES_PASSWORD: atlasvpn_secure_pass_2025
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
    networks:
      - atlasvpn-backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U atlasvpn_user -d atlasvpn"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.3'
        reservations:
          memory: 128M
          cpus: '0.1'

  # Redis Cache Service
  redis:
    image: redis:7-alpine
    container_name: atlasvpn-redis
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - atlasvpn-backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.2'
        reservations:
          memory: 64M
          cpus: '0.1'

  # FastAPI Backend Service with Traefik Labels
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: atlasvpn-backend
    volumes:
      - ./backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - DATABASE_URL=******************************************************************/atlasvpn
      - REDIS_URL=redis://redis:6379/0
      - BOT_TOKEN=${BOT_TOKEN}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_BOT_USERNAME=${TELEGRAM_BOT_USERNAME}
      - ENVIRONMENT=production
      - WORKERS=1
      - RELOAD=true
      - DISABLE_RATE_LIMIT=true
      - DEVELOPMENT_MODE=true
      - DEBUG=true
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"
      
      # API Routes
      - "traefik.http.routers.api.rule=Host(`app.atlasvip.cloud`) && PathPrefix(`/api/`)"
      - "traefik.http.routers.api.entrypoints=websecure"
      - "traefik.http.routers.api.tls=true"
      - "traefik.http.routers.api.tls.certresolver=letsencrypt"
      - "traefik.http.services.api.loadbalancer.server.port=8000"
      - "traefik.http.routers.api.priority=20"  # Highest priority for API routes
      
      # WebSocket Support for /ws/
      - "traefik.http.routers.ws.rule=Host(`app.atlasvip.cloud`) && PathPrefix(`/ws/`)"
      - "traefik.http.routers.ws.entrypoints=websecure"
      - "traefik.http.routers.ws.tls=true"
      - "traefik.http.routers.ws.tls.certresolver=letsencrypt"
      - "traefik.http.routers.ws.priority=20"  # High priority for WebSocket routes
      
      # API Documentation
      - "traefik.http.routers.docs.rule=Host(`app.atlasvip.cloud`) && (PathPrefix(`/docs`) || PathPrefix(`/redoc`) || PathPrefix(`/openapi.json`))"
      - "traefik.http.routers.docs.entrypoints=websecure"
      - "traefik.http.routers.docs.tls=true"
      - "traefik.http.routers.docs.tls.certresolver=letsencrypt"
      - "traefik.http.routers.docs.priority=20"  # High priority for docs routes
      
      # Rate Limiting Middleware for API - DISABLED FOR DEVELOPMENT
      # - "traefik.http.middlewares.api-ratelimit.ratelimit.burst=200"
      # - "traefik.http.middlewares.api-ratelimit.ratelimit.average=300"
      # - "traefik.http.routers.api.middlewares=api-ratelimit"
      
      # CORS Middleware for API
      - "traefik.http.middlewares.api-cors.headers.accesscontrolallowmethods=GET,POST,PUT,DELETE,OPTIONS"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolalloworiginlist=https://app.atlasvip.cloud"
      - "traefik.http.middlewares.api-cors.headers.accesscontrolmaxage=100"
      - "traefik.http.middlewares.api-cors.headers.addvaryheader=true"
      
    networks:
      - atlasvpn-traefik
      - atlasvpn-backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.2'

  # Nuxt Frontend Service with Traefik Labels
  frontend:
    build:
      context: ./frontend-nuxt
      dockerfile: Dockerfile
    container_name: atlasvpn-frontend
    environment:
      - NODE_ENV=production
      - PORT=3005
      - NITRO_PORT=3005
      - NUXT_PUBLIC_API_URL=https://app.atlasvip.cloud/api
      - NUXT_PUBLIC_WS_BASE_URL=wss://app.atlasvip.cloud/ws
      - NUXT_PUBLIC_TELEGRAM_WEBAPP_URL=https://app.atlasvip.cloud
      - NUXT_PUBLIC_TELEGRAM_BOT_USERNAME=${TELEGRAM_BOT_USERNAME}
    labels:
      # Enable Traefik for this service
      - "traefik.enable=true"
      
      # Main Frontend Route - Exclude API and WS paths
      - "traefik.http.routers.frontend.rule=Host(`app.atlasvip.cloud`) && !PathPrefix(`/api/`) && !PathPrefix(`/ws/`) && !PathPrefix(`/docs`) && !PathPrefix(`/redoc`) && !PathPrefix(`/openapi.json`)"
      - "traefik.http.routers.frontend.entrypoints=websecure"
      - "traefik.http.routers.frontend.tls=true"
      - "traefik.http.routers.frontend.tls.certresolver=letsencrypt"
      - "traefik.http.services.frontend.loadbalancer.server.port=3005"
      - "traefik.http.routers.frontend.priority=1"  # Lower priority for frontend catch-all
      
      # Development Files - NUXT AUTOMATIC PROXYING!
      - "traefik.http.routers.dev-files.rule=Host(`app.atlasvip.cloud`) && (PathPrefix(`/_nuxt/`) || PathPrefix(`/node_modules/`) || PathPrefix(`/__nuxt_devtools__/`))"
      - "traefik.http.routers.dev-files.entrypoints=websecure"
      - "traefik.http.routers.dev-files.tls=true"
      - "traefik.http.routers.dev-files.tls.certresolver=letsencrypt"
      - "traefik.http.routers.dev-files.priority=10"  # Higher priority

      # HMR WebSocket - NUXT AUTOMATIC!
      - "traefik.http.routers.hmr.rule=Host(`app.atlasvip.cloud`) && PathPrefix(`/_nuxt/hmr`)"
      - "traefik.http.routers.hmr.entrypoints=websecure"
      - "traefik.http.routers.hmr.tls=true"
      - "traefik.http.routers.hmr.tls.certresolver=letsencrypt"
      - "traefik.http.routers.hmr.priority=15"  # Highest priority
      
      # Rate Limiting for Development Files - DISABLED
      # - "traefik.http.middlewares.dev-ratelimit.ratelimit.burst=500"
      # - "traefik.http.middlewares.dev-ratelimit.ratelimit.average=1000"
      # - "traefik.http.routers.dev-files.middlewares=dev-ratelimit,telegram-headers"
      
      # Update dev-files middleware to only use telegram-headers
      - "traefik.http.routers.dev-files.middlewares=telegram-headers"
      
      # Telegram Mini App Headers - CRITICAL!
      - "traefik.http.middlewares.telegram-headers.headers.customrequestheaders.Access-Control-Allow-Origin=*"
      - "traefik.http.middlewares.telegram-headers.headers.frameDeny=false"
      - "traefik.http.middlewares.telegram-headers.headers.customresponseheaders.X-Frame-Options="
      - "traefik.http.middlewares.telegram-headers.headers.contentSecurityPolicy=default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://telegram.org https://*.telegram.org; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' data: https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' wss: https:; frame-src https://telegram.org https://*.telegram.org; frame-ancestors https://web.telegram.org https://*.telegram.org https://telegram.org;"
      - "traefik.http.routers.frontend.middlewares=telegram-headers"
      
    networks:
      - atlasvpn-traefik
    # Removed volume mounts to prevent node_modules conflicts
    # volumes:
    #   - ./frontend-nuxt:/app
    #   - /app/node_modules
    restart: unless-stopped
    # healthcheck:
    #   test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://127.0.0.1:3005"]
    #   interval: 15s
    #   timeout: 5s
    #   retries: 5
    #   start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.3'
        reservations:
          memory: 128M
          cpus: '0.1'

# Named Volumes for Data Persistence
volumes:
  postgres_data:
    name: atlasvpn-postgres-data
  redis_data:
    name: atlasvpn-redis-data
  traefik_letsencrypt:
    name: atlasvpn-traefik-certs

# Networks for Service Isolation
networks:
  atlasvpn-traefik:
    name: atlasvpn-traefik
    driver: bridge
  atlasvpn-backend:
    name: atlasvpn-backend
    driver: bridge