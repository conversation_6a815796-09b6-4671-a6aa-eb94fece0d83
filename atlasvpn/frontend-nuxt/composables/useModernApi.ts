/**
 * Modern Nuxt 3/4 data fetching composable using useFetch and useAsyncData
 * This demonstrates the latest patterns for backend connectivity
 */

import type { UseFetchOptions } from 'nuxt/app'
import type { User, DashboardStats, Transaction, Task, VPNPackage } from '~/types'

// Base API configuration
const API_BASE_URL = '/api'

// Default fetch options with proper error handling
const defaultFetchOptions: UseFetchOptions<any> = {
  server: false, // Client-side only for Telegram Mini App
  retry: 3,
  retryDelay: 1000,
  timeout: 15000,
  onRequestError({ error }) {
    console.error('Request error:', error)
  },
  onResponseError({ response }) {
    console.error('Response error:', response.status, response.statusText)
  }
}

/**
 * Modern authentication composable using useFetch
 */
export const useModernAuth = () => {
  // Get current user with caching
  const { data: user, pending: isLoading, error, refresh } = useFetch<User>(
    `${API_BASE_URL}/user/me`,
    {
      ...defaultFetchOptions,
      key: 'current-user',
      default: () => null,
      transform: (data: any) => data?.data || data
    }
  )

  // Login with Telegram
  const loginWithTelegram = async (initData: string) => {
    const { data, error } = await $fetch<{ data: User }>(`${API_BASE_URL}/auth/telegram`, {
      method: 'POST',
      body: { init_data: initData }
    }).catch(err => ({ data: null, error: err }))

    if (error) throw error
    if (data?.data) {
      await refresh() // Refresh user data
      return data.data
    }
    throw new Error('Login failed')
  }

  // Logout
  const logout = async () => {
    await $fetch(`${API_BASE_URL}/auth/logout`, { method: 'POST' })
    await refresh() // This will clear the user data
  }

  return {
    user: readonly(user),
    isLoading: readonly(isLoading),
    error: readonly(error),
    isAuthenticated: computed(() => !!user.value),
    loginWithTelegram,
    logout,
    refresh
  }
}

/**
 * Modern dashboard data composable with proper caching
 */
export const useModernDashboard = () => {
  // Dashboard stats with 5-minute cache
  const { data: stats, pending: isLoadingStats, error: statsError, refresh: refreshStats } = 
    useFetch<DashboardStats>(`${API_BASE_URL}/user/dashboard/stats`, {
      ...defaultFetchOptions,
      key: 'dashboard-stats',
      default: () => ({
        active_subscriptions: 0,
        total_data_used: 0,
        total_data_limit: 0,
        wallet_balance: 0,
        total_accumulated_card_profit: 0,
        total_passive_hourly_income: 0
      }),
      transform: (data: any) => data?.data || data,
      getCachedData: (key) => nuxtApp.ssrContext?.cache?.[key] ?? nuxtApp.payload.data[key]
    })

  // User transactions with pagination
  const { data: transactions, pending: isLoadingTransactions, error: transactionsError, refresh: refreshTransactions } = 
    useFetch<Transaction[]>(`${API_BASE_URL}/user/transactions`, {
      ...defaultFetchOptions,
      key: 'user-transactions',
      default: () => [],
      query: { limit: 50, offset: 0 },
      transform: (data: any) => data?.data || data || []
    })

  // Refresh all dashboard data
  const refreshAll = async () => {
    await Promise.all([refreshStats(), refreshTransactions()])
  }

  // Computed values
  const formattedBalance = computed(() => {
    if (!stats.value) return '$0.00'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(stats.value.wallet_balance)
  })

  const dataUsagePercentage = computed(() => {
    if (!stats.value || stats.value.total_data_limit === 0) return 0
    return Math.min(
      (stats.value.total_data_used / stats.value.total_data_limit) * 100,
      100
    )
  })

  return {
    // Data
    stats: readonly(stats),
    transactions: readonly(transactions),
    
    // Loading states
    isLoadingStats: readonly(isLoadingStats),
    isLoadingTransactions: readonly(isLoadingTransactions),
    
    // Errors
    statsError: readonly(statsError),
    transactionsError: readonly(transactionsError),
    
    // Actions
    refreshStats,
    refreshTransactions,
    refreshAll,
    
    // Computed
    formattedBalance,
    dataUsagePercentage
  }
}

/**
 * Modern tasks composable with optimistic updates
 */
export const useModernTasks = () => {
  // Available tasks
  const { data: tasks, pending: isLoading, error, refresh } = useFetch<Task[]>(
    `${API_BASE_URL}/tasks/available`,
    {
      ...defaultFetchOptions,
      key: 'available-tasks',
      default: () => [],
      transform: (data: any) => data?.data || data || []
    }
  )

  // Complete a task with optimistic update
  const completeTask = async (taskId: number) => {
    // Optimistic update
    if (tasks.value) {
      const taskIndex = tasks.value.findIndex(t => t.id === taskId)
      if (taskIndex !== -1) {
        tasks.value[taskIndex] = { ...tasks.value[taskIndex], status: 'completed' }
      }
    }

    try {
      await $fetch(`${API_BASE_URL}/tasks/${taskId}/complete`, { method: 'POST' })
      await refresh() // Refresh to get the actual state
    } catch (error) {
      // Revert optimistic update on error
      await refresh()
      throw error
    }
  }

  return {
    tasks: readonly(tasks),
    isLoading: readonly(isLoading),
    error: readonly(error),
    completeTask,
    refresh
  }
}

/**
 * Modern VPN packages composable with reactive queries
 */
export const useModernVPN = () => {
  // VPN packages with filtering
  const packagesQuery = reactive({
    category: '',
    minPrice: 0,
    maxPrice: 1000
  })

  const { data: packages, pending: isLoading, error, refresh } = useFetch<VPNPackage[]>(
    `${API_BASE_URL}/vpn/packages`,
    {
      ...defaultFetchOptions,
      key: 'vpn-packages',
      query: packagesQuery,
      default: () => [],
      transform: (data: any) => data?.data || data || []
    }
  )

  // Purchase package
  const purchasePackage = async (packageId: number, panelId: number) => {
    const { data } = await $fetch(`${API_BASE_URL}/vpn/purchase`, {
      method: 'POST',
      body: { package_id: packageId, panel_id: panelId }
    })
    
    // Refresh packages to update availability
    await refresh()
    return data
  }

  return {
    packages: readonly(packages),
    isLoading: readonly(isLoading),
    error: readonly(error),
    packagesQuery,
    purchasePackage,
    refresh
  }
}

/**
 * Generic API composable for custom endpoints
 */
export const useModernFetch = <T>(
  url: string,
  options: UseFetchOptions<T> = {}
) => {
  return useFetch<T>(url, {
    ...defaultFetchOptions,
    ...options,
    baseURL: API_BASE_URL
  })
}

/**
 * Async data composable for complex data transformations
 */
export const useModernAsyncData = <T>(
  key: string,
  handler: () => Promise<T>,
  options: any = {}
) => {
  return useAsyncData<T>(key, handler, {
    server: false,
    ...options
  })
}
