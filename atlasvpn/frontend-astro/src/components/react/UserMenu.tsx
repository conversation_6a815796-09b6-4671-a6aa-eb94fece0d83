/**
 * @file UserMenu.tsx
 * @description Interactive user menu component for the header
 * React island for handling user interactions and menu state
 */

import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import {
  UserCircleIcon,
  WalletIcon,
  ArrowLeftStartOnRectangleIcon,
} from '@heroicons/react/24/outline';
import Avatar3DViewer from './Avatar3DViewer';
import type { User } from '../../types/api';

interface UserMenuProps {
  user?: User | null;
}

interface UserMenuDropdownProps {
  user: User;
  onNavigate: (path: string) => void;
  onClose: () => void;
}

const UserMenuDropdown: React.FC<UserMenuDropdownProps> = ({ user, onNavigate, onClose }) => {
  return (
    <div className="absolute top-full right-0 mt-2 w-64 bg-gray-900/95 backdrop-blur-md rounded-xl border border-white/10 shadow-xl z-50">
      <div className="p-4">
        <div className="flex items-center space-x-3">
          <Avatar3DViewer
            filename={`${user?.telegram_photo_url || 'Lovely Angel'}.webp`}
            size={{ width: 57, height: 57 }}
            className="transform transition-all"
          />
          <div>
            <div className="font-display font-medium text-white">{user?.username}</div>
            <div className="text-sm text-white/60">{user?.email || 'No email set'}</div>
            <div className="text-xs text-accent-500 mt-1">
              Balance: ${user?.wallet_balance?.toFixed(2) || '0.00'}
            </div>
          </div>
        </div>
      </div>
      <div className="p-2 space-y-1">
        <button
          onClick={() => {
            onNavigate('/profile');
            onClose();
          }}
          className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-white/5 transition-colors text-left"
        >
          <UserCircleIcon className="w-5 h-5 text-white/70" />
          <span className="text-white">Profile</span>
        </button>
        <button
          onClick={() => {
            onNavigate('/dashboard/wallet');
            onClose();
          }}
          className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-white/5 transition-colors text-left"
        >
          <WalletIcon className="w-5 h-5 text-white/70" />
          <span className="text-white">Wallet</span>
        </button>
        <button
          onClick={() => {
            // Handle logout
            onClose();
          }}
          className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg hover:bg-red-500/10 transition-colors text-left text-red-400"
        >
          <ArrowLeftStartOnRectangleIcon className="w-5 h-5" />
          <span>Logout</span>
        </button>
      </div>
    </div>
  );
};

const UserMenu: React.FC<UserMenuProps> = ({ user }) => {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // Handle navigation
  const handleNavigate = (path: string) => {
    window.location.href = path;
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    if (showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserMenu]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setShowUserMenu(false);
      }
    };

    if (showUserMenu) {
      document.addEventListener('keydown', handleEscape);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [showUserMenu]);

  if (!user) {
    return (
      <div className="w-10 h-10 flex items-center justify-center bg-accent-500/10 text-white rounded-full">
        ?
      </div>
    );
  }

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={() => setShowUserMenu(prev => !prev)}
        className="relative overflow-hidden rounded-full border-2 border-accent-500/30 hover:border-accent-500/50 shadow-md hover:shadow-lg hover:shadow-accent-500/10 transition-all duration-300"
        aria-label="User menu"
      >
        <Avatar3DViewer
          filename={`${user?.telegram_photo_url || 'Lovely Angel'}.webp`}
          size={{ width: 40, height: 40 }}
          className="transform hover:scale-105 transition-all"
        />
      </button>

      {/* User menu dropdown */}
      {showUserMenu && (
        <UserMenuDropdown
          user={user}
          onNavigate={handleNavigate}
          onClose={() => setShowUserMenu(false)}
        />
      )}
    </div>
  );
};

export default UserMenu;
