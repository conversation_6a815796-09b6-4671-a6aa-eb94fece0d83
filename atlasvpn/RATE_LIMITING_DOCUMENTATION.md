# AtlasVPN Rate Limiting Configuration Documentation

## Overview
This document provides a comprehensive overview of all rate limiting configurations in the AtlasVPN system before disabling them for development mode.

## Backend Rate Limiting Configurations

### 1. RateLimitMiddleware (`/opt/atlasvpn/backend/middleware/rate_limit_middleware.py`)
- **Purpose**: Main rate limiting middleware using centralized configuration
- **Redis-backed**: Uses RedisClient for distributed rate limiting
- **Environment Variables**:
  - `DISABLE_RATE_LIMIT`: Can disable rate limiting (currently set to "true" in docker-compose)
  - `TEST_MODE`: Allows bypassing rate limiting for tests

#### Endpoint-Specific Limits:
- **Default**: 60 requests per minute, 300s block time
- **Login**: 5 requests per minute, 300s block time
- **Register**: 3 requests per 5 minutes, 600s block time
- **API**: 30 requests per minute, 300s block time

#### Path Patterns:
- Login: `/auth/token`, `/auth/login`, `/auth/admin/login`
- Register: `/auth/register`
- Telegram Login: `/auth/telegram/login`
- API: `/api/*`

#### Distributed Attack Detection:
- **Enabled**: True
- **Window**: 60 seconds
- **Threshold**: 30 requests from different IPs to same endpoint
- **Block Time**: 1800 seconds (30 minutes)

### 2. AuthRateLimitMiddleware (`/opt/atlasvpn/backend/auth.py`)
- **Purpose**: Additional rate limiting specifically for auth endpoints
- **Scope**: All `/auth/` paths
- **Limits**: 20 attempts per hour per IP
- **Action Type**: "auth_request"

### 3. SecurityMiddleware (`/opt/atlasvpn/backend/middleware/security_middleware.py`)
- **Purpose**: IP-based rate limiting and security protection
- **Implementation**: In-memory counters (not Redis-backed)
- **Features**:
  - Real client IP detection from X-Forwarded-For headers
  - IP whitelisting for localhost
  - Path-specific rate limiting
  - Automatic cleanup of old entries

#### Rate Limiting Logic:
- **Window**: 60 seconds
- **Threshold**: 30 requests per path per IP
- **Block Time**: 120 seconds (2 minutes)
- **Headers**: Adds X-RateLimit-* headers to responses

### 4. RateLimitService (`/opt/atlasvpn/backend/services/rate_limit_service.py`)
- **Purpose**: Enhanced rate limiting service with security features
- **Redis Integration**: Uses Redis for distributed rate limiting
- **Configuration**: Imports from `config/rate_limit_config.py`
- **Environment Control**: `DISABLE_RATE_LIMIT` environment variable

### 5. Rate Limit Configuration (`/opt/atlasvpn/backend/config/rate_limit_config.py`)
- **Purpose**: Centralized rate limit configuration
- **Endpoint Types**:
  - `login`: Token and admin login endpoints
  - `admin_login`: Admin-specific login
  - `register`: Registration endpoints
  - `telegram_login`: Telegram-specific login
  - `api_tasks`: Task-related API endpoints
  - `api_analytics`: Analytics endpoints
  - `task_verification`: Task verification
  - `task_claim`: Task claiming
  - `daily_checkin`: Daily check-in
  - `api_sensitive`: Admin and sensitive operations
  - `api_general`: General API endpoints

### 6. Main Application (`/opt/atlasvpn/backend/main.py`)
- **Current Status**: RateLimitMiddleware is commented out (DISABLED for development)
- **Security Middleware**: IP protection middleware is active
- **CSRF Middleware**: Commented out (DISABLED for development testing)

## Frontend Rate Limiting

### React Query Configuration (`/opt/atlasvpn/frontend-astro/src/components/react/QueryClientWrapper.tsx`)
- **Retry Logic**: Limited retry attempts (retry: 1)
- **Retry Delay**: 1000ms for mutations
- **Stale Time**: Various configurations in different hooks
- **Cache Time**: Configured per query type

### API Client (`/opt/atlasvpn/frontend-astro/src/api/client.ts`)
- **Timeout**: 15000ms default timeout
- **Retry Logic**: Handles 429 Too Many Requests responses
- **Error Handling**: Redirects on 401 errors

### API Utils (`/opt/atlasvpn/frontend-astro/src/api/utils.ts`)
- **Retry Configuration**:
  - `MAX_RETRIES`: 3
  - `RETRY_DELAY`: 1000ms
  - Exponential backoff for retries
  - Special handling for 429 status codes

## Docker Compose Rate Limiting

### Traefik Configuration (`/opt/atlasvpn/docker-compose-traefik.yml`)
- **API Rate Limiting Middleware**:
  - `traefik.http.middlewares.api-ratelimit.ratelimit.burst=200`
  - `traefik.http.middlewares.api-ratelimit.ratelimit.average=300`
  - Applied to: `traefik.http.routers.api.middlewares=api-ratelimit`

- **Development Files Rate Limiting**: Currently commented out
  - Would have: `burst=500`, `average=1000`

### Backend Environment Variables
- `DISABLE_RATE_LIMIT=true`: Already set to disable backend rate limiting

## Current Status
- **Backend**: Rate limiting partially disabled via environment variable
- **Security Middleware**: Still active with IP-based rate limiting
- **Traefik**: Rate limiting middleware still active
- **Frontend**: Minimal rate limiting through React Query retry logic

## Files to Modify for Complete Disabling
1. `/opt/atlasvpn/backend/middleware/security_middleware.py` - Comment out IP rate limiting
2. `/opt/atlasvpn/backend/auth.py` - Comment out AuthRateLimitMiddleware
3. `/opt/atlasvpn/docker-compose-traefik.yml` - Comment out Traefik rate limiting middleware
4. `/opt/atlasvpn/frontend-astro/src/api/utils.ts` - Increase retry limits for development

## Security Considerations
- Rate limiting provides protection against:
  - Brute force attacks
  - DDoS attacks
  - API abuse
  - Resource exhaustion
- Disabling for development should be temporary
- Production deployment should re-enable all rate limiting

---
*Generated on: $(date)*
*Purpose: Development mode optimization*
*Status: Pre-modification documentation*