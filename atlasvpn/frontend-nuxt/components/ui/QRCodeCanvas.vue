<template>
  <canvas
    ref="canvasRef"
    :width="size"
    :height="size"
    class="qr-code-canvas"
  />
</template>

<script setup lang="ts">
import QRCode from 'qrcode'

interface Props {
  value: string
  size?: number
  margin?: number
  level?: 'L' | 'M' | 'Q' | 'H'
  color?: {
    dark?: string
    light?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  size: 200,
  margin: 4,
  level: 'M',
  color: () => ({
    dark: '#000000',
    light: '#FFFFFF'
  })
})

const canvasRef = ref<HTMLCanvasElement>()

const generateQRCode = async () => {
  if (!canvasRef.value || !props.value) return

  try {
    await QRCode.toCanvas(canvasRef.value, props.value, {
      width: props.size,
      margin: props.margin,
      errorCorrectionLevel: props.level,
      color: props.color
    })
  } catch (error) {
    console.error('Failed to generate QR code:', error)
  }
}

// Generate QR code when component mounts or props change
watch(() => [props.value, props.size, props.margin, props.level, props.color], generateQRCode, {
  immediate: true,
  deep: true
})

onMounted(() => {
  generateQRCode()
})
</script>

<style scoped>
.qr-code-canvas {
  display: block;
  border-radius: 8px;
}
</style>
