#!/bin/bash

# AtlasVPN Development Helper Script
# This script provides convenient commands for development workflow

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[DEV]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Help function
show_help() {
    echo "AtlasVPN Development Helper"
    echo ""
    echo "Usage: ./dev.sh [command]"
    echo ""
    echo "Commands:"
    echo "  start       Start development environment (frontend + backend)"
    echo "  frontend    Start only frontend in development mode"
    echo "  backend     Start only backend services"
    echo "  build       Build frontend for production"
    echo "  rebuild     Rebuild frontend development container"
    echo "  logs        Show frontend logs"
    echo "  stop        Stop all services"
    echo "  clean       Clean up containers and volumes"
    echo "  status      Show status of all services"
    echo "  shell       Open shell in frontend container"
    echo "  test        Run tests in frontend"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  ./dev.sh start          # Start full development environment"
    echo "  ./dev.sh frontend       # Start only frontend with hot reload"
    echo "  ./dev.sh logs           # Follow frontend logs"
    echo "  ./dev.sh rebuild        # Rebuild frontend after dependency changes"
}

# Check if docker-compose is available
check_docker() {
    if ! command -v docker-compose &> /dev/null; then
        print_error "docker-compose is not installed or not in PATH"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running"
        exit 1
    fi
}

# Start development environment
start_dev() {
    print_status "Starting development environment..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
    print_success "Development environment started!"
    print_status "Frontend: https://app.atlasvip.cloud"
    print_status "Use './dev.sh logs' to follow logs"
}

# Start only frontend
start_frontend() {
    print_status "Starting frontend in development mode..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d frontend
    print_success "Frontend started in development mode!"
    print_status "URL: https://app.atlasvip.cloud"
    print_status "Hot reload enabled - changes will be reflected automatically"
}

# Start backend services
start_backend() {
    print_status "Starting backend services..."
    docker-compose up -d backend redis postgres traefik
    print_success "Backend services started!"
}

# Build production
build_prod() {
    print_status "Building frontend for production..."
    docker-compose build frontend
    print_success "Production build completed!"
}

# Rebuild development container
rebuild_dev() {
    print_status "Rebuilding development container..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml build --no-cache frontend
    print_success "Development container rebuilt!"
}

# Show logs
show_logs() {
    print_status "Following frontend logs (Ctrl+C to exit)..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f frontend
}

# Stop services
stop_services() {
    print_status "Stopping all services..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml down
    print_success "All services stopped!"
}

# Clean up
clean_up() {
    print_warning "This will remove all containers, networks, and volumes!"
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up..."
        docker-compose -f docker-compose.yml -f docker-compose.dev.yml down -v --remove-orphans
        docker system prune -f
        print_success "Cleanup completed!"
    else
        print_status "Cleanup cancelled"
    fi
}

# Show status
show_status() {
    print_status "Service status:"
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml ps
}

# Open shell
open_shell() {
    print_status "Opening shell in frontend container..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec frontend sh
}

# Run tests
run_tests() {
    print_status "Running tests..."
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec frontend pnpm test
}

# Main script logic
main() {
    check_docker
    
    case "${1:-help}" in
        "start")
            start_dev
            ;;
        "frontend")
            start_frontend
            ;;
        "backend")
            start_backend
            ;;
        "build")
            build_prod
            ;;
        "rebuild")
            rebuild_dev
            ;;
        "logs")
            show_logs
            ;;
        "stop")
            stop_services
            ;;
        "clean")
            clean_up
            ;;
        "status")
            show_status
            ;;
        "shell")
            open_shell
            ;;
        "test")
            run_tests
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

main "$@"
