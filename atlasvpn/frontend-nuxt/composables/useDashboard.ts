import type { DashboardStats, Transaction } from '~/types'

/**
 * Modern dashboard composable using Nuxt 4 data fetching patterns
 * Optimized for Telegram Mini App with proper caching and error handling
 */
export const useDashboard = () => {
  // Default fetch options optimized for Telegram Mini App
  const defaultOptions = {
    server: false, // Client-side only for Telegram Mini App
    retry: 3,
    retryDelay: 1000,
    timeout: 15000,
    onRequestError({ error }: { error: any }) {
      console.error('[Dashboard] Request error:', error)
    },
    onResponseError({ response }: { response: any }) {
      console.error('[Dashboard] Response error:', response.status, response.statusText)
    }
  }

  // Dashboard stats with modern useFetch
  const {
    data: dashboardStats,
    pending: isLoadingStats,
    error: statsError,
    refresh: refreshStats
  } = useFetch<DashboardStats>('/api/user/dashboard/stats', {
    ...defaultOptions,
    key: 'dashboard-stats',
    default: () => ({
      active_subscriptions: 0,
      total_data_used: 0,
      total_data_limit: 0,
      wallet_balance: 0,
      total_accumulated_card_profit: 0,
      total_passive_hourly_income: 0,
    }),
    transform: (data: any) => data?.data || data,
    getCachedData: (key) => nuxtApp.ssrContext?.cache?.[key] ?? nuxtApp.payload.data[key]
  })

  // User transactions with pagination support
  const {
    data: transactions,
    pending: isLoadingTransactions,
    error: transactionsError,
    refresh: refreshTransactions
  } = useFetch<Transaction[]>('/api/user/transactions', {
    ...defaultOptions,
    key: 'user-transactions',
    default: () => [],
    query: { limit: 50, offset: 0 },
    transform: (data: any) => data?.data || data || []
  })

  // Fetch transactions with pagination (for infinite scroll)
  const fetchMoreTransactions = async (limit = 50, offset = 0) => {
    try {
      const { data: newTransactions } = await $fetch<{ data: Transaction[] }>('/api/user/transactions', {
        query: { limit, offset }
      })

      if (offset === 0) {
        // Replace transactions for refresh
        transactions.value = newTransactions?.data || []
      } else {
        // Append for pagination
        transactions.value = [...transactions.value, ...(newTransactions?.data || [])]
      }
    } catch (error: any) {
      console.error('[Dashboard] Failed to fetch more transactions:', error)
      throw error
    }
  }

  // Refresh all dashboard data
  const refreshDashboard = async () => {
    await Promise.all([
      refreshStats(),
      refreshTransactions()
    ])
  }

  // Get formatted wallet balance
  const formattedBalance = computed(() => {
    if (!dashboardStats.value) return '$0.00'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(dashboardStats.value.wallet_balance)
  })

  // Get data usage percentage
  const dataUsagePercentage = computed(() => {
    if (!dashboardStats.value || dashboardStats.value.total_data_limit === 0) return 0
    return Math.min(
      (dashboardStats.value.total_data_used / dashboardStats.value.total_data_limit) * 100,
      100
    )
  })

  // Get formatted data usage
  const formattedDataUsage = computed(() => {
    if (!dashboardStats.value) return { used: '0 GB', total: '0 GB' }
    
    const formatBytes = (bytes: number) => {
      if (bytes === 0) return '0 GB'
      const gb = bytes / (1024 * 1024 * 1024)
      return `${gb.toFixed(1)} GB`
    }

    return {
      used: formatBytes(dashboardStats.value.total_data_used),
      total: formatBytes(dashboardStats.value.total_data_limit)
    }
  })

  // Get recent transactions
  const recentTransactions = computed(() => {
    return transactions.value.slice(0, 5)
  })

  // Get total earnings from cards
  const totalCardEarnings = computed(() => {
    if (!dashboardStats.value) return 0
    return dashboardStats.value.total_accumulated_card_profit || 0
  })

  // Get hourly passive income
  const hourlyPassiveIncome = computed(() => {
    if (!dashboardStats.value) return 0
    return dashboardStats.value.total_passive_hourly_income || 0
  })

  return {
    // State (reactive from useFetch)
    dashboardStats: readonly(dashboardStats),
    transactions: readonly(transactions),
    isLoadingStats: readonly(isLoadingStats),
    isLoadingTransactions: readonly(isLoadingTransactions),
    statsError: readonly(statsError),
    transactionsError: readonly(transactionsError),

    // Actions
    refreshStats,
    refreshTransactions,
    refreshDashboard,
    fetchMoreTransactions,

    // Computed
    formattedBalance,
    dataUsagePercentage,
    formattedDataUsage,
    recentTransactions,
    totalCardEarnings,
    hourlyPassiveIncome
  }
}
