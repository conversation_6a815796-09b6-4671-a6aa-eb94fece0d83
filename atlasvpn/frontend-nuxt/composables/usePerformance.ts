/**
 * Performance monitoring composable for Telegram Mini App
 * Tracks Core Web Vitals, API performance, and user experience metrics
 */

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  url?: string
  type: 'navigation' | 'api' | 'user_interaction' | 'resource'
}

interface APIPerformance {
  endpoint: string
  method: string
  duration: number
  status: number
  timestamp: number
  success: boolean
}

export const usePerformance = () => {
  // Reactive state
  const metrics = ref<PerformanceMetric[]>([])
  const apiMetrics = ref<APIPerformance[]>([])
  const isMonitoring = ref(false)
  const performanceScore = ref(0)

  // Core Web Vitals
  const coreWebVitals = ref({
    LCP: 0, // Largest Contentful Paint
    FID: 0, // First Input Delay
    CLS: 0, // Cumulative Layout Shift
    FCP: 0, // First Contentful Paint
    TTFB: 0 // Time to First Byte
  })

  // Performance thresholds
  const thresholds = {
    LCP: { good: 2500, poor: 4000 },
    FID: { good: 100, poor: 300 },
    CLS: { good: 0.1, poor: 0.25 },
    FCP: { good: 1800, poor: 3000 },
    TTFB: { good: 800, poor: 1800 },
    API_RESPONSE: { good: 1000, poor: 3000 }
  }

  // Initialize performance monitoring
  const init = () => {
    if (process.server) return

    isMonitoring.value = true

    // Monitor Core Web Vitals
    monitorCoreWebVitals()

    // Monitor API performance
    monitorAPIPerformance()

    // Monitor navigation performance
    monitorNavigationPerformance()

    // Monitor resource loading
    monitorResourcePerformance()

    // Calculate performance score periodically
    setInterval(calculatePerformanceScore, 10000) // Every 10 seconds

    console.log('[Performance] Monitoring initialized')
  }

  // Monitor Core Web Vitals
  const monitorCoreWebVitals = () => {
    if (!('PerformanceObserver' in window)) return

    try {
      // Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        const lastEntry = entries[entries.length - 1] as any
        if (lastEntry) {
          coreWebVitals.value.LCP = lastEntry.startTime
          addMetric('LCP', lastEntry.startTime, 'navigation')
        }
      })
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          coreWebVitals.value.FID = entry.processingStart - entry.startTime
          addMetric('FID', entry.processingStart - entry.startTime, 'user_interaction')
        })
      })
      fidObserver.observe({ entryTypes: ['first-input'] })

      // Cumulative Layout Shift (CLS)
      let clsValue = 0
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value
            coreWebVitals.value.CLS = clsValue
            addMetric('CLS', clsValue, 'navigation')
          }
        })
      })
      clsObserver.observe({ entryTypes: ['layout-shift'] })

      // First Contentful Paint (FCP)
      const fcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          if (entry.name === 'first-contentful-paint') {
            coreWebVitals.value.FCP = entry.startTime
            addMetric('FCP', entry.startTime, 'navigation')
          }
        })
      })
      fcpObserver.observe({ entryTypes: ['paint'] })

    } catch (error) {
      console.warn('[Performance] Core Web Vitals monitoring failed:', error)
    }
  }

  // Monitor API performance
  const monitorAPIPerformance = () => {
    // Intercept fetch requests
    const originalFetch = window.fetch
    window.fetch = async (...args) => {
      const startTime = performance.now()
      const url = args[0] as string
      const options = args[1] || {}

      try {
        const response = await originalFetch(...args)
        const endTime = performance.now()
        const duration = endTime - startTime

        // Only track API calls
        if (url.includes('/api/')) {
          const apiMetric: APIPerformance = {
            endpoint: url,
            method: options.method || 'GET',
            duration,
            status: response.status,
            timestamp: Date.now(),
            success: response.ok
          }

          apiMetrics.value.push(apiMetric)
          addMetric(`API_${options.method || 'GET'}`, duration, 'api', url)

          // Keep only last 100 API metrics
          if (apiMetrics.value.length > 100) {
            apiMetrics.value = apiMetrics.value.slice(-100)
          }
        }

        return response
      } catch (error) {
        const endTime = performance.now()
        const duration = endTime - startTime

        if (url.includes('/api/')) {
          const apiMetric: APIPerformance = {
            endpoint: url,
            method: options.method || 'GET',
            duration,
            status: 0,
            timestamp: Date.now(),
            success: false
          }

          apiMetrics.value.push(apiMetric)
          addMetric(`API_${options.method || 'GET'}_ERROR`, duration, 'api', url)
        }

        throw error
      }
    }
  }

  // Monitor navigation performance
  const monitorNavigationPerformance = () => {
    if (!('PerformanceObserver' in window)) return

    try {
      const navObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          // Time to First Byte
          const ttfb = entry.responseStart - entry.requestStart
          coreWebVitals.value.TTFB = ttfb
          addMetric('TTFB', ttfb, 'navigation')

          // DOM Content Loaded
          const dcl = entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart
          addMetric('DOMContentLoaded', dcl, 'navigation')

          // Load Complete
          const loadComplete = entry.loadEventEnd - entry.loadEventStart
          addMetric('LoadComplete', loadComplete, 'navigation')
        })
      })
      navObserver.observe({ entryTypes: ['navigation'] })
    } catch (error) {
      console.warn('[Performance] Navigation monitoring failed:', error)
    }
  }

  // Monitor resource loading performance
  const monitorResourcePerformance = () => {
    if (!('PerformanceObserver' in window)) return

    try {
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry: any) => {
          const duration = entry.responseEnd - entry.startTime
          
          // Track slow resources
          if (duration > 1000) {
            addMetric('SlowResource', duration, 'resource', entry.name)
          }
        })
      })
      resourceObserver.observe({ entryTypes: ['resource'] })
    } catch (error) {
      console.warn('[Performance] Resource monitoring failed:', error)
    }
  }

  // Add performance metric
  const addMetric = (name: string, value: number, type: PerformanceMetric['type'], url?: string) => {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      type,
      url
    }

    metrics.value.push(metric)

    // Keep only last 500 metrics
    if (metrics.value.length > 500) {
      metrics.value = metrics.value.slice(-500)
    }

    // Log significant performance issues
    if (shouldLogMetric(name, value)) {
      console.warn(`[Performance] ${name}: ${value.toFixed(2)}ms`, url ? `(${url})` : '')
    }
  }

  // Determine if metric should be logged
  const shouldLogMetric = (name: string, value: number): boolean => {
    const thresholdKey = name.replace('_ERROR', '') as keyof typeof thresholds
    const threshold = thresholds[thresholdKey]
    
    if (!threshold) return false
    
    return value > threshold.poor
  }

  // Calculate overall performance score (0-100)
  const calculatePerformanceScore = () => {
    let score = 100
    const weights = {
      LCP: 25,
      FID: 25,
      CLS: 25,
      FCP: 15,
      TTFB: 10
    }

    Object.entries(coreWebVitals.value).forEach(([metric, value]) => {
      if (value === 0) return // Skip unset metrics

      const thresholdKey = metric as keyof typeof thresholds
      const threshold = thresholds[thresholdKey]
      const weight = weights[thresholdKey as keyof typeof weights] || 0

      if (!threshold || !weight) return

      let metricScore = 100
      if (value > threshold.poor) {
        metricScore = 0
      } else if (value > threshold.good) {
        metricScore = 50
      }

      score -= (100 - metricScore) * (weight / 100)
    })

    performanceScore.value = Math.max(0, Math.round(score))
  }

  // Get performance summary
  const getPerformanceSummary = () => {
    const now = Date.now()
    const last5Minutes = now - 5 * 60 * 1000

    const recentMetrics = metrics.value.filter(m => m.timestamp > last5Minutes)
    const recentAPIMetrics = apiMetrics.value.filter(m => m.timestamp > last5Minutes)

    return {
      coreWebVitals: coreWebVitals.value,
      performanceScore: performanceScore.value,
      totalMetrics: metrics.value.length,
      recentMetrics: recentMetrics.length,
      apiCalls: {
        total: recentAPIMetrics.length,
        successful: recentAPIMetrics.filter(m => m.success).length,
        failed: recentAPIMetrics.filter(m => !m.success).length,
        averageResponseTime: recentAPIMetrics.length > 0 
          ? recentAPIMetrics.reduce((sum, m) => sum + m.duration, 0) / recentAPIMetrics.length 
          : 0
      },
      slowOperations: recentMetrics.filter(m => 
        (m.name.includes('API') && m.value > thresholds.API_RESPONSE.poor) ||
        (m.name === 'LCP' && m.value > thresholds.LCP.poor) ||
        (m.name === 'FCP' && m.value > thresholds.FCP.poor)
      ).length
    }
  }

  // Get performance grade
  const getPerformanceGrade = computed(() => {
    const score = performanceScore.value
    if (score >= 90) return 'A'
    if (score >= 80) return 'B'
    if (score >= 70) return 'C'
    if (score >= 60) return 'D'
    return 'F'
  })

  // Check if performance is good
  const isPerformanceGood = computed(() => performanceScore.value >= 80)

  // Get slow API endpoints
  const slowAPIEndpoints = computed(() => {
    const endpointStats = new Map<string, { total: number; totalTime: number; failures: number }>()

    apiMetrics.value.forEach(metric => {
      const key = `${metric.method} ${metric.endpoint}`
      const existing = endpointStats.get(key) || { total: 0, totalTime: 0, failures: 0 }
      
      existing.total++
      existing.totalTime += metric.duration
      if (!metric.success) existing.failures++
      
      endpointStats.set(key, existing)
    })

    return Array.from(endpointStats.entries())
      .map(([endpoint, stats]) => ({
        endpoint,
        averageTime: stats.totalTime / stats.total,
        totalCalls: stats.total,
        failureRate: (stats.failures / stats.total) * 100
      }))
      .filter(stat => stat.averageTime > thresholds.API_RESPONSE.good)
      .sort((a, b) => b.averageTime - a.averageTime)
  })

  // Initialize on mount
  onMounted(() => {
    init()
  })

  return {
    // State
    isMonitoring: readonly(isMonitoring),
    performanceScore: readonly(performanceScore),
    coreWebVitals: readonly(coreWebVitals),
    metrics: readonly(metrics),
    apiMetrics: readonly(apiMetrics),

    // Computed
    getPerformanceGrade,
    isPerformanceGood,
    slowAPIEndpoints,

    // Methods
    init,
    addMetric,
    getPerformanceSummary,
    calculatePerformanceScore,

    // Utilities
    thresholds
  }
}
