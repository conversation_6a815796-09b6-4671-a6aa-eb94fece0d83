---
globs: frontend-astro/src/**/*.{ts,tsx,js,jsx,astro}
description: Frontend development rules for Astro, React, and Telegram integration
---

# Frontend Development Rules

## 🌟 Astro + React Architecture

1. **React Components**: Place in [frontend-astro/src/components/react/](mdc:frontend-astro/src/components/react/)
2. **Astro Pages**: Use for routing and SSR, integrate React as islands
3. **No HMR**: Hot Module Replacement disabled for Telegram compatibility
4. **Vite Config**: Use [frontend-astro/astro.config.mjs](mdc:frontend-astro/astro.config.mjs) for build settings

## 🔗 API Communication Rules

**ALWAYS** use [frontend-astro/src/utils/apiClient.ts](mdc:frontend-astro/src/utils/apiClient.ts):

```typescript
import { apiClient } from '@/utils/apiClient';

// ✅ Correct way
const response = await apiClient.get('/auth/me');

// ❌ Never use fetch directly for API calls
```

## 📱 Telegram WebApp Integration

1. Use [frontend-astro/src/utils/telegram.ts](mdc:frontend-astro/src/utils/telegram.ts) for WebApp SDK
2. Access `window.Telegram.WebApp` only after validation
3. Handle `initData` for authentication
4. Test iframe embedding compatibility

## 🎯 React Query Rules

1. Use React Query for all API state management
2. Authentication hooks in [frontend-astro/src/hooks/authHooks.ts](mdc:frontend-astro/src/hooks/authHooks.ts)
3. Error handling with proper user feedback
4. Caching strategies for offline functionality

## 🎨 Styling Rules

1. Use Tailwind CSS for consistent styling
2. Mobile-first responsive design for Telegram
3. Dark mode support for Telegram themes
4. Touch-friendly UI elements

## 🧪 Testing Rules

1. Unit tests in [frontend-astro/src/tests/](mdc:frontend-astro/src/tests/)
2. Mock Telegram WebApp API in tests
3. Test authentication flows end-to-end
4. Mock API responses with MSW
description:
globs:
alwaysApply: false
---
