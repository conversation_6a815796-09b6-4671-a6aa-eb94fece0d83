#!/usr/bin/env python
"""
CAPTCHA Test
-----------
Test CAPTCHA implementation across endpoints.
"""
import logging
from typing import Dict, Any, List, Tuple
from test_utils import (
    print_header,
    print_success,
    print_error,
    print_warning,
    print_info,
    log_request_details,
    log_error_with_traceback,
    make_request,
    get_critical_endpoints,
    log_test_result,
    log_environment_info
)
from test_config import API_BASE_URL, CAPTCHA_CONFIG

logger = logging.getLogger(__name__)

def test_captcha() -> Dict[str, Any]:
    """Test CAPTCHA integration across critical endpoints"""
    print_header("Testing CAPTCHA")
    
    # Log environment info for debugging
    log_environment_info()
    
    # Setup results dictionary
    results = {
        "total_tests": 0,
        "successful_tests": 0,
        "failed_tests": 0,
        "endpoints_tested": [],
        "errors": []
    }
    
    # Get all critical endpoints
    endpoints = get_critical_endpoints()
    print_info(f"Testing {len(endpoints)} endpoints for CAPTCHA")
    results["total_tests"] = len(endpoints)
    
    # Test each endpoint
    for endpoint in endpoints:
        # Create URL for endpoint (replace placeholders)
        url = f"{API_BASE_URL}{endpoint.replace('{task_id}', '1').replace('{panel_id}', '1').replace('{subscription_url}', 'test').replace('{code}', 'test')}"
        
        try:
            # Check if this endpoint requires CAPTCHA
            logger.info(f"Testing CAPTCHA for {endpoint}")
            requires_captcha = CAPTCHA_CONFIG.get(endpoint, CAPTCHA_CONFIG["default"])
            
            # Make request without CAPTCHA token
            # Add SecurityTestAgent user-agent to help with test detection
            headers = {"User-Agent": "SecurityTestAgent/1.0"}
            success, response = make_request(url, method="POST", data={}, headers=headers)
            
            if not success:
                results["failed_tests"] += 1
                results["errors"].append(f"Failed to connect to {url}: {response.get('error')}")
                continue
            
            # Get response status code
            status_code = response.get("status_code", 0)
            endpoint_exists = status_code not in [404, 405]  # Endpoint exists if not 404 or 405
            requires_auth = status_code == 401  # Requires authentication if 401
            
            # Make request with invalid CAPTCHA token
            success2, response2 = make_request(url, method="POST", data={"captcha_token": "invalid_token"}, headers=headers)
            
            if not success2:
                results["failed_tests"] += 1
                results["errors"].append(f"Failed to connect to {url} with invalid token: {response2.get('error')}")
                continue
                
            status_code2 = response2.get("status_code", 0)
            
            # Make request with valid test CAPTCHA token
            success3, response3 = make_request(url, method="POST", data={"captcha_token": CAPTCHA_CONFIG["bypass_token"]}, headers=headers)
            
            if not success3:
                results["failed_tests"] += 1
                results["errors"].append(f"Failed to connect to {url} with valid token: {response3.get('error')}")
                continue
                
            status_code3 = response3.get("status_code", 0)
            
            # Verify CAPTCHA behavior
            expected_behavior = False
            
            # If endpoint doesn't exist (404) or method not allowed (405), consider test successful
            if not endpoint_exists:
                logger.info(f"Endpoint {endpoint} returned {status_code} - skipping CAPTCHA verification")
                expected_behavior = True
            # If endpoint requires authentication (401), check if response is consistent
            elif requires_auth:
                # All responses should be 401 regardless of CAPTCHA token
                expected_behavior = (status_code == 401 and status_code2 == 401 and status_code3 == 401)
                logger.info(f"Endpoint {endpoint} requires authentication (401) - checking consistent responses")
            # If endpoint requires CAPTCHA, check appropriate behavior
            elif requires_captcha["required"]:
                # Should reject no token, reject invalid token, accept valid token (or at least different response)
                expected_behavior = (
                    (status_code in [400, 403, 422]) and 
                    (status_code2 in [400, 403, 422]) and 
                    (status_code3 != status_code or status_code3 != status_code2)
                )
                logger.info(f"Endpoint {endpoint} requires CAPTCHA - checking proper token enforcement")
            else:
                # Should accept all requests regardless of token
                expected_behavior = True
                logger.info(f"Endpoint {endpoint} doesn't require CAPTCHA - all requests should be allowed")
            
            if expected_behavior:
                results["successful_tests"] += 1
                print_success(f"CAPTCHA verification working as expected for {endpoint}")
            else:
                results["failed_tests"] += 1
                if requires_captcha["required"] and endpoint_exists and not requires_auth:
                    print_error(f"CAPTCHA should be required for {endpoint} but wasn't enforced correctly")
                else:
                    print_error(f"CAPTCHA behavior not as expected for {endpoint}")
                
                print_warning(f"  Status codes: no token ({status_code}), invalid ({status_code2}), valid ({status_code3})")
            
            # Log test result
            log_test_result(
                f"CAPTCHA - {endpoint}",
                expected_behavior,
                {
                    "requires_captcha": requires_captcha["required"],
                    "endpoint_exists": endpoint_exists,
                    "requires_auth": requires_auth,
                    "no_token_status": status_code,
                    "invalid_token_status": status_code2,
                    "valid_token_status": status_code3
                }
            )
        except Exception as e:
            results["failed_tests"] += 1
            error_msg = f"Error testing CAPTCHA for {endpoint}: {str(e)}"
            results["errors"].append(error_msg)
            print_error(error_msg)
            log_error_with_traceback(e, f"Testing CAPTCHA for endpoint {endpoint}")
    
    # Calculate success rate
    success_rate = (results["successful_tests"] / results["total_tests"] * 100) if results["total_tests"] > 0 else 0
    
    # Print summary
    print_header("CAPTCHA Test Summary")
    print_info(f"Total Endpoints Tested: {results['total_tests']}")
    print_info(f"Successful Tests: {results['successful_tests']}")
    print_info(f"Failed Tests: {results['failed_tests']}")
    print_info(f"Success Rate: {success_rate:.2f}%")
    
    if results["errors"]:
        print_warning("\nErrors Encountered:")
        for error in results["errors"]:
            print_warning(f"  {error}")
    
    return results

if __name__ == "__main__":
    test_captcha() 