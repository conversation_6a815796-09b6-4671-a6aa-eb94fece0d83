#!/bin/bash

# AtlasVPN Frontend Startup Script - 502 Bad Gateway Fix
# This script starts the frontend container with proper Traefik labels

echo "🚀 Starting AtlasVPN Frontend Container..."

# Stop and remove existing container if it exists
docker stop atlasvpn-frontend 2>/dev/null || true
docker rm atlasvpn-frontend 2>/dev/null || true

# Start the frontend container with all necessary labels
docker run -d \
  --name atlasvpn-frontend \
  --network atlasvpn-traefik \
  -e NODE_ENV=development \
  -e NUXT_PUBLIC_API_URL=https://app.atlasvip.cloud/api \
  -e NUXT_PUBLIC_WS_BASE_URL=wss://app.atlasvip.cloud/ws \
  -e NUXT_PUBLIC_TELEGRAM_WEBAPP_URL=https://app.atlasvip.cloud \
  --label "traefik.enable=true" \
  --label "traefik.http.routers.frontend.rule=Host(\`app.atlasvip.cloud\`) && !PathPrefix(\`/api/\`) && !PathPrefix(\`/ws/\`) && !PathPrefix(\`/docs\`) && !PathPrefix(\`/redoc\`) && !PathPrefix(\`/openapi.json\`)" \
  --label "traefik.http.routers.frontend.entrypoints=websecure" \
  --label "traefik.http.routers.frontend.tls=true" \
  --label "traefik.http.routers.frontend.tls.certresolver=letsencrypt" \
  --label "traefik.http.services.frontend.loadbalancer.server.port=3005" \
  --label "traefik.http.routers.frontend.priority=1" \
  --label "traefik.http.middlewares.telegram-headers.headers.customrequestheaders.Access-Control-Allow-Origin=*" \
  --label "traefik.http.middlewares.telegram-headers.headers.frameDeny=false" \
  --label "traefik.http.middlewares.telegram-headers.headers.customresponseheaders.X-Frame-Options=" \
  --label "traefik.http.routers.frontend.middlewares=telegram-headers" \
  --restart unless-stopped \
  atlasvpn_frontend

if [ $? -eq 0 ]; then
    echo "✅ Frontend container started successfully!"
    echo "🌐 Application should be available at: https://app.atlasvip.cloud/"
    echo "📊 Container status:"
    docker ps | grep atlasvpn-frontend

    echo ""
    echo "⏳ Waiting for Nuxt to start up..."
    sleep 15

    echo "📋 Recent container logs:"
    docker logs atlasvpn-frontend --tail=5

    echo ""
    echo "🧪 Testing application..."
    if curl -s -I -m 10 https://app.atlasvip.cloud/ | grep -q "HTTP/2 200"; then
        echo "✅ Application is responding correctly!"
    else
        echo "❌ Application test failed. Check logs above."
    fi
else
    echo "❌ Failed to start frontend container!"
    exit 1
fi
