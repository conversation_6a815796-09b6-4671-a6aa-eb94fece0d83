<template>
  <nav class="bottom-navigation fixed bottom-0 left-0 right-0 z-50 bg-atlas-black/95 backdrop-blur-md border-t border-atlas-purple-800/30 safe-area-bottom">
    <div class="container mx-auto px-2 h-16">
      <div class="flex justify-around items-center h-full">
        <UButton
          v-for="item in navItems"
          :key="item.id"
          :to="item.path"
          :variant="isActive(item.id) ? 'solid' : 'ghost'"
          :color="isActive(item.id) ? 'primary' : 'gray'"
          size="sm"
          class="nav-item flex flex-col items-center justify-center h-full w-full relative group transition-all duration-200 interactive-element"
          :class="{
            'text-atlas-purple-400 bg-atlas-purple-500/10': isActive(item.id),
            'text-gray-400 hover:text-white': !isActive(item.id)
          }"
          @click="handleNavClick(item)"
        >
          <!-- Active indicator background -->
          <div 
            v-if="isActive(item.id)" 
            class="absolute inset-0 bg-gradient-to-t from-atlas-purple-500/15 via-transparent to-transparent rounded-lg opacity-100"
          />
          
          <!-- Icon -->
          <div class="relative z-10 mb-1">
            <Icon 
              :name="item.icon" 
              class="w-5 h-5 transition-all duration-200"
              :class="{
                'text-atlas-purple-400 glow-purple': isActive(item.id),
                'text-gray-400 group-hover:text-white': !isActive(item.id)
              }"
            />
            
            <!-- Badge for notifications -->
            <div 
              v-if="item.badge && item.badge > 0"
              class="absolute -top-2 -right-2 w-4 h-4 bg-atlas-orange rounded-full flex items-center justify-center"
            >
              <span class="text-xs font-bold text-white">{{ item.badge > 9 ? '9+' : item.badge }}</span>
            </div>
          </div>
          
          <!-- Label -->
          <span 
            class="text-xs font-medium transition-all duration-200 relative z-10"
            :class="{
              'text-atlas-purple-300': isActive(item.id),
              'text-gray-500 group-hover:text-gray-300': !isActive(item.id)
            }"
          >
            {{ item.label }}
          </span>
          
          <!-- Active indicator dot -->
          <div 
            v-if="isActive(item.id)"
            class="absolute bottom-1 w-1 h-1 bg-atlas-purple-400 rounded-full glow-purple"
          />
        </UButton>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
interface NavItem {
  id: string
  icon: string
  label: string
  path: string
  badge?: number
}

const route = useRoute()
const { $telegramOptimization } = useNuxtApp()

// Navigation items with game-like styling
const navItems: NavItem[] = [
  {
    id: 'home',
    icon: 'i-heroicons-home',
    label: 'Home',
    path: '/dashboard/home'
  },
  {
    id: 'earn',
    icon: 'i-heroicons-currency-dollar',
    label: 'Earn',
    path: '/dashboard/earn'
  },
  {
    id: 'friends',
    icon: 'i-heroicons-user-group',
    label: 'Friends',
    path: '/dashboard/friends'
  },
  {
    id: 'wallet',
    icon: 'i-heroicons-wallet',
    label: 'Wallet',
    path: '/dashboard/wallet'
  },
  {
    id: 'premium',
    icon: 'i-heroicons-bolt',
    label: 'Premium',
    path: '/dashboard/premium'
  }
]

// Check if a nav item is active
const isActive = (itemId: string) => {
  const currentPath = route.path
  const item = navItems.find(nav => nav.id === itemId)
  return item && currentPath === item.path
}

// Handle navigation with haptic feedback
const handleNavClick = (item: NavItem) => {
  // Trigger haptic feedback for Telegram Mini App
  if ($telegramOptimization?.triggerHaptic) {
    $telegramOptimization.triggerHaptic('selection')
  }
  
  // Navigation is handled by the :to prop on UButton
}

// Add badges for notifications (can be connected to stores later)
const updateBadges = () => {
  // Example: Update friends badge with unread messages
  // const friendsItem = navItems.find(item => item.id === 'friends')
  // if (friendsItem) {
  //   friendsItem.badge = unreadMessagesCount
  // }
}

onMounted(() => {
  updateBadges()
})
</script>

<style scoped>
.bottom-navigation {
  /* Ensure it's above other content */
  z-index: 50;
}

.nav-item {
  /* Remove default button styles */
  background: none !important;
  border: none !important;
  box-shadow: none !important;
}

.nav-item:hover {
  background: rgba(124, 58, 237, 0.05) !important;
}

.nav-item.active {
  background: rgba(124, 58, 237, 0.1) !important;
}

/* Safe area handling for devices with home indicators */
.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom, 0);
}

/* Responsive adjustments */
@media (max-width: 480px) {
  .nav-item {
    padding: 0.25rem;
  }
  
  .nav-item span {
    font-size: 0.625rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .nav-item,
  .nav-item * {
    transition: none !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .bottom-navigation {
    border-top: 2px solid var(--color-atlas-purple-400);
  }
  
  .nav-item {
    border: 1px solid transparent;
  }
  
  .nav-item:focus-visible {
    border-color: var(--color-atlas-purple-400);
  }
}
</style>
