# TanStack Query Migration Plan

This document outlines the plan to migrate state management from Zustand to TanStack Query (React Query) for server state, focusing on improving data fetching, caching, and real-time updates for the application.

**Project Goals:**

*   Replace Zustand for server state management with TanStack Query.
*   Improve data fetching efficiency and reduce boilerplate.
*   Enhance UI responsiveness with better caching and background updates.
*   Lay the groundwork for more robust real-time features where appropriate.
*   Maintain or improve developer experience with TanStack Query Devtools.

**Key Decisions & Assumptions (Pending User Feedback):**

*   **Client-Side State:** A small, focused Zustand store or React Context will likely remain for purely client-side UI state (e.g., theme, general UI toggles not tied to server data). This plan will highlight areas needing such a store.
*   **Chat Real-Time:** WebSockets will remain the primary mechanism for real-time chat messages. TanStack Query will handle initial loads and mutations, with WebSocket messages updating the TanStack Query cache.
*   **Card Game Live Updates:** A hybrid approach will be used. HTTP for actions (buy, upgrade, claim). WebSockets will be considered for pushing live profit updates for cards.
*   **Admin Panel:** Migration of admin-specific data fetching (`/admin/*` routes, `AdminDashboard.tsx`) is out of scope for this plan, as it's intended for a separate project.

---

## Phase 0: Preparation & Foundation

### Task 0.1: Install TanStack Query [✅ Done]
*   **Description:** Add `@tanstack/react-query` and its devtools to the project.
*   **Details:**
    *   Run `pnpm add @tanstack/react-query` in the `frontend` directory.
    *   Run `pnpm add @tanstack/react-query-devtools` (optional but highly recommended).
*   **AI Prompt:** "Execute `pnpm add @tanstack/react-query @tanstack/react-query-devtools` in the `frontend` directory."

### Task 0.2: Setup QueryClientProvider [✅ Done]
*   **Description:** Wrap the application's root component with `QueryClientProvider` and configure a global `QueryClient` instance.
*   **Details:**
    *   Modify `frontend/src/main.tsx`.
    *   Instantiate `const queryClient = new QueryClient()`.
    *   Provide this client to `<QueryClientProvider client={queryClient}>`.
    *   Optionally, add `<ReactQueryDevtools initialIsOpen={false} />` within the provider for development.
*   **AI Prompt:** "In `frontend/src/main.tsx`, import `QueryClient`, `QueryClientProvider` from `@tanstack/react-query` and `ReactQueryDevtools` from `@tanstack/react-query-devtools`. Create a `queryClient` instance. Wrap the main `App` component with `QueryClientProvider` and include `ReactQueryDevtools`."

### Task 0.3: Define Core Fetch Function (Optional but Recommended) [✅ Done]
*   **Description:** Create a reusable async fetch function that can be used by TanStack Query's `queryFn`.
*   **Details:**
    *   Create a utility, e.g., `frontend/src/utils/apiClient.ts`.
    *   This function might take an endpoint and options, and use the existing `api.ts` (from `frontend/src/utils/api.ts`) or `fetch` directly.
    *   Example:
        ```typescript
        // frontend/src/utils/apiClient.ts
        import api from './api'; // Assuming your existing api setup
        import { QueryFunctionContext } from '@tanstack/react-query';

        interface QueryKeyParams {
          endpoint: string;
          params?: Record<string, any>;
        }

        export const fetchData = async <TData = any>(
          context: QueryFunctionContext<[string, QueryKeyParams] | [string]>
        ): Promise<TData> => {
          const queryKey = context.queryKey;
          let endpoint: string;
          let apiParams: Record<string, any> | undefined;

          if (typeof queryKey[1] === 'object' && queryKey[1] !== null && 'endpoint' in queryKey[1]) {
            // New format: ['queryName', { endpoint: '/path', params: { id: 1 } }]
            const paramsObj = queryKey[1] as QueryKeyParams;
            endpoint = paramsObj.endpoint;
            apiParams = paramsObj.params;
          } else if (typeof queryKey[1] === 'string') {
            // Old format: ['queryName', '/path', { params: { id: 1 } }] - adapt as needed
            // This branch might need adjustment based on how you structure queryKeys with parameters.
            // For simplicity, let's assume the endpoint is the second element if not an object.
            endpoint = queryKey[1] as string;
            // And params might be the third, if present
            apiParams = queryKey[2] as Record<string, any> | undefined;
          } else {
            throw new Error('Invalid queryKey structure for fetchData');
          }

          const response = await api.get<TData>(endpoint, { params: apiParams });
          return response.data;
        };
        ```
*   **AI Prompt:** "Create a utility file `frontend/src/utils/apiClient.ts`. Define an asynchronous function `fetchData` that accepts a TanStack Query `QueryFunctionContext`. It should parse the `queryKey` to extract an API `endpoint` and optional `params`. The function will then use the existing `api.get` method from `frontend/src/utils/api.ts` to make the request and return the response data. Ensure proper typing."

---

## Phase 1: Authentication (`authStore.ts`)

### Task 1.1: Fetch Current User Data [✅ Done]
*   **Description:** Migrate fetching the current user's session/profile data to `useQuery`.
*   **Details:**
    *   Query Key: `['currentUser']` (or `['currentUser', { endpoint: '/auth/verify-session' }]` if using the `fetchData` utility with object params)
    *   `queryFn`: Fetch from `/api/auth/verify-session`.
    *   Configuration: `staleTime: 5 * 60 * 1000` (5 minutes), `cacheTime: 15 * 60 * 1000` (15 minutes). `refetchOnWindowFocus: true`.
*   **AI Prompt:** "Create a custom hook `useCurrentUserQuery` in `frontend/src/hooks/authHooks.ts`. This hook will use TanStack Query's `useQuery` with key `['currentUser']` (or `['currentUser', { endpoint: '/auth/verify-session' }]`) and the `fetchData` utility (if created) to fetch user data from `/api/auth/verify-session`. Configure `staleTime` to 5 minutes and `refetchOnWindowFocus` to true."

### Task 1.2: Authentication Mutations [✅ Done]
*   **Description:** Convert `login`, `telegramLogin`, `logout`, and `refreshAuth` actions into TanStack Query `useMutation` hooks.
*   **Details:**
    *   `useLoginMutation`: `mutationFn` calls `api.auth.login`. On success, invalidate `['currentUser']`.
    *   `useTelegramLoginMutation`: `mutationFn` calls `api.auth.login` (with Telegram params). On success, invalidate `['currentUser']`.
    *   `useLogoutMutation`: `mutationFn` calls `api.auth.logout`. On success, use `queryClient.setQueryData(['currentUser'], null)` and `queryClient.removeQueries(['currentUser'])` for a clean logout.
    *   `useRefreshAuthMutation`: `mutationFn` calls `api.auth.refreshToken`. On success, invalidate `['currentUser']`.
*   **AI Prompt:** "In `frontend/src/hooks/authHooks.ts`, create:
    *   `useLoginMutation`: Uses `useMutation` for `api.auth.login`. On success, invalidates `['currentUser']`.
    *   `useTelegramLoginMutation`: Uses `useMutation` for `api.auth.login` (Telegram variant). On success, invalidates `['currentUser']`.
    *   `useLogoutMutation`: Uses `useMutation` for `api.auth.logout`. On success, sets `['currentUser']` query data to `null` and removes the query.
    *   `useRefreshAuthMutation`: Uses `useMutation` for `api.auth.refreshToken`. On success, invalidates `['currentUser']`.
    Import `useQueryClient` where needed."

### Task 1.3: Refactor Auth-Reliant Components [✅ Done]
*   **Description:** Update components that use `authStore` (e.g., `ProtectedRoutes.tsx`, `App.tsx`, `Header.tsx`) to use the new TanStack Query hooks.
*   **Details:**
    *   Replace `useAuthStore(state => state.user)` with `useCurrentUserQuery().data`.
    *   Replace `useAuthStore(state => state.isLoading)` with `useCurrentUserQuery().isLoading` or specific mutation loading states (`mutation.isPending`).
    *   Call mutation hooks (`mutate` or `mutateAsync`) instead of store actions.
    *   `ProtectedRoutes.tsx`, `App.tsx`, and `Header.tsx` have been updated.
*   **AI Prompt:** "Refactor `frontend/src/routes/ProtectedRoutes.tsx`, `frontend/src/App.tsx`, `frontend/src/components/Header.tsx` and any other components currently using `authStore` for user data or auth actions. Replace store access with `useCurrentUserQuery` and the new authentication mutation hooks. Use `isPending` for mutation loading states."

### Task 1.4: Client-Side Auth State (if any) [✅ Done]
*   **Description:** Determine if any purely client-side state from `authStore.ts` (e.g., `isInitialized`, `isLoginComplete` if not fully derivable from query status, UI-specific flags like `rateLimitWaitUntil`) still needs global management.
*   **Details:** 
    *   `rateLimitWaitUntil` moved to `uiAuthStore.ts`.
    *   `initializeAuth()` from `authStore.ts` removed. `initCsrf()` moved to `main.tsx`.
    *   `initializeChat()` (previously in `initializeAuth`) moved to `App.tsx`, triggered after `useCurrentUserQuery` settles.
    *   `authStore.ts` significantly reduced, server state and actions removed. Cookie utils remain for now.
*   **AI Prompt:** "Review the remaining state variables in `frontend/src/stores/authStore.ts`. If client-side states like `rateLimitWaitUntil` or specific UI flags are still needed globally and not covered by TanStack Query's status, create a minimal Zustand store `uiAuthStore.ts` or a React Context for them. Update components accordingly. `isInitialized` and `isLoginComplete` should ideally be derived from `useCurrentUserQuery` status."

---

## Phase 2: Dashboard & Core User Data (`dashboardStore.ts`)

### Task 2.1: Dashboard Stats Query
*   **Description:** Migrate fetching dashboard statistics.
*   **Details:**
    *   Query Key: `['dashboardStats']` (or `['dashboardStats', { endpoint: '/api/user/dashboard/stats' }]`)
    *   `queryFn`: Fetch from `/api/user/dashboard/stats`. `staleTime` can be moderate (e.g., 1-5 minutes).
    *   Update `HomeTabPage.tsx`, `DashboardLayout.tsx` and any other components displaying these stats.
*   **AI Prompt:** "Create `useDashboardStatsQuery` in `frontend/src/hooks/dashboardHooks.ts` using `useQuery` (key: `['dashboardStats']`) to fetch data from `/api/user/dashboard/stats`. Configure a `staleTime` of 2 minutes. Refactor `HomeTabPage.tsx` and `DashboardLayout.tsx` to use this hook."

### Task 2.2: User Transactions Query
*   **Description:** Migrate fetching user transactions.
*   **Details:**
    *   Query Key: `['userTransactions']` (or `['userTransactions', { endpoint: '/api/user/transactions' }]`)
    *   `queryFn`: Fetch from `/api/user/transactions`. `staleTime` similar to stats.
    *   Update `WalletTabPage.tsx` or other transaction history components.
*   **AI Prompt:** "Create `useUserTransactionsQuery` in `frontend/src/hooks/dashboardHooks.ts` (key: `['userTransactions']`) for `/api/user/transactions`. `staleTime` of 2 minutes. Update `WalletTabPage.tsx`."

### Task 2.3: Referral Data Queries
*   **Description:** Migrate fetching referred users and referral info.
*   **Details:**
    *   `useReferredUsersQuery`: Key `['referredUsers']`, fetch from `/api/referrals/users`.
    *   `useReferralInfoQuery`: Key `['referralInfo']`, fetch from `/api/referrals/info`.
    *   Update `FriendsTab.tsx`.
*   **AI Prompt:** "Create `useReferredUsersQuery` (key: `['referredUsers']`) for `/api/referrals/users` and `useReferralInfoQuery` (key: `['referralInfo']`) for `/api/referrals/info` in a new file `frontend/src/hooks/referralHooks.ts`. Update `FriendsTab.tsx`."

### Task 2.4: Client-Side Dashboard State
*   **Description:** Manage remaining client-side state from `dashboardStore.ts` (e.g., `showWelcomeMessage`, `currentTheme`, tab scroll positions, `isBackgrounded`, `refreshInterval` settings).
*   **Details:** Create a minimal `uiStore.ts` or `uiDashboardStore.ts` (Zustand) or use React Context.
*   **AI Prompt:** "Create `uiStore.ts` (Zustand) to manage global client-side UI states previously in `dashboardStore.ts` such as `currentTheme`, `showWelcomeMessage`, `isBackgrounded`, `refreshInterval` settings, and active tab scroll positions. Update components consuming this state, including `DashboardLayout.tsx`."

---

## Phase 3: Game Mechanics - Cards (`cardStore.ts`) & Earn Tab

### Task 3.1: Fetch All Cards (Unified View)
*   **Description:** Migrate fetching of all cards using the `/api/cards/all` endpoint.
*   **Details:**
    *   Query Key: `['allCards']`
    *   `queryFn`: Fetch from `/api/cards/all`. `staleTime` can be 1-2 minutes, or shorter if live updates are critical and not yet on WebSocket.
    *   This will be used by `EarnTab.tsx` and `CardTab.tsx`.
*   **AI Prompt:** "Create `useAllCardsQuery` in `frontend/src/hooks/cardHooks.ts` (key: `['allCards']`) to fetch data from `/api/cards/all`. Set `staleTime` to 1 minute. This will be the primary source of card data for `EarnTab.tsx` and `CardTab.tsx`."

### Task 3.2: Card Action Mutations
*   **Description:** Convert `claimAllProfits`, `upgradeCard`, `buyCard` into `useMutation` hooks.
*   **Details:**
    *   `useClaimAllProfitsMutation`: `mutationFn` calls `api.post('/api/cards/claim-all')`.
        *   On Success: Invalidate `['allCards']` and `['dashboardStats']`.
    *   `useUpgradeCardMutation`: `mutationFn` takes `userCardId`, calls `api.post(\`/api/cards/\${userCardId}/upgrade\`)`.
        *   On Success: Invalidate `['allCards']` and `['dashboardStats']`.
    *   `useBuyCardMutation`: `mutationFn` takes `cardCatalogId`, calls `api.post(\`/api/cards/buy/\${cardCatalogId}\`)`.
        *   On Success: Invalidate `['allCards']` and `['dashboardStats']`.
*   **AI Prompt:** "In `frontend/src/hooks/cardHooks.ts`, create:
    *   `useClaimAllProfitsMutation`: For POST to `/api/cards/claim-all`. On success, invalidates `['allCards']` and `['dashboardStats']`.
    *   `useUpgradeCardMutation`: For POST to `/api/cards/{userCardId}/upgrade`. On success, invalidates `['allCards']` and `['dashboardStats']`.
    *   `useBuyCardMutation`: For POST to `/api/cards/buy/{cardCatalogId}`. On success, invalidates `['allCards']` and `['dashboardStats']`.
    Implement optimistic updates for balance and card levels where appropriate."

### Task 3.3: Refactor `EarnTab.tsx` and `CardTab.tsx`
*   **Description:** Update these components to use the new card query and mutations.
*   **Details:**
    *   Use `useAllCardsQuery().data` and TanStack Query's status flags (`isLoading`, `isError`).
    *   Local UI state like `selectedUnifiedCard` remains component state.
*   **AI Prompt:** "Refactor `frontend/src/components/EarnTab.tsx` and `frontend/src/components/CardTab.tsx`. Replace `useCardStore` with `useAllCardsQuery` for data fetching and the new card mutation hooks for actions. Manage loading/error states using TanStack Query's flags. `selectedUnifiedCard` can remain local component state."

### Task 3.4: (Future Consideration) WebSocket for Live Card Profit Updates
*   **Description:** (Post-initial migration) Design and implement WebSocket communication for real-time updates of `accumulated_profit`.
*   **Details:** Client uses `queryClient.setQueryData(['allCards'], ...)` to update cache.

---

## Phase 4: Game Mechanics - Tasks (`taskStore.ts`) & Earn Tab

### Task 4.1: Fetch Tasks Query
*   **Description:** Migrate fetching of available tasks.
*   **Details:**
    *   Query Key: `['tasks']`
    *   `queryFn`: Fetch from `/api/tasks/available`. `staleTime` of 1-2 minutes.
    *   Update `EarnTab.tsx` (task section) and `TaskTab.tsx`.
*   **AI Prompt:** "Create `useTasksQuery` in `frontend/src/hooks/taskHooks.ts` (key: `['tasks']`) for `/api/tasks/available`. Set `staleTime` to 1 minute. Refactor `TaskTab.tsx` and the task section of `EarnTab.tsx`."

### Task 4.2: Fetch Daily Streak Query
*   **Description:** Migrate fetching of daily task streak.
*   **Details:**
    *   Query Key: `['dailyStreak', taskId]`
    *   `queryFn`: Fetch from `/api/tasks/daily/streak/{taskId}`.
    *   Update `DailyStreakCard.tsx` (within `TaskTab.tsx`).
*   **AI Prompt:** "Create `useDailyStreakQuery` in `frontend/src/hooks/taskHooks.ts` (key: `['dailyStreak', taskId]`, parameterized) for `/api/tasks/daily/streak/{taskId}`. Update `DailyStreakCard.tsx`."

### Task 4.3: Task Action Mutations
*   **Description:** Convert user task actions and daily check-in into `useMutation` hooks.
*   **Details:**
    *   `useStartTaskMutation`, `useVerifyTaskMutation`, `useClaimRewardMutation`, `usePerformDailyCheckInMutation`.
    *   On Success: Invalidate `['tasks']`, `['dailyStreak', taskId]` (for check-in), and `['dashboardStats']` (for rewards).
*   **AI Prompt:** "In `frontend/src/hooks/taskHooks.ts`, create mutations:
    *   `useStartTaskMutation`: POST to `/api/tasks/{taskId}/start`.
    *   `useVerifyTaskMutation`: POST to `/api/tasks/{taskId}/verify`.
    *   `useClaimRewardMutation`: POST to `/api/tasks/{taskId}/claim`. Invalidate `['tasks']`, `['dashboardStats']`.
    *   `usePerformDailyCheckInMutation`: POST to `/api/tasks/daily/check-in/{taskId}`. Invalidate `['tasks']`, `['dailyStreak', taskId]`.
    On success, mutations should invalidate relevant queries."

### Task 4.4: Refactor `TaskTab.tsx`
*   **Description:** Update `TaskTab.tsx` and `DailyStreakCard.tsx` to use the new task queries and mutations.
*   **AI Prompt:** "Refactor `frontend/src/components/TaskTab.tsx` and `frontend/src/components/DailyStreakCard.tsx` to use the new TanStack Query hooks for task data, streak data, and task-related actions."

---

## Phase 5: Chat System (`chatStore.ts` & `VerseLayout.tsx`)

### Task 5.1: Initial Chat Data Queries
*   **Description:** Fetch initial conversations and messages using `useQuery`.
*   **Details:**
    *   `useChatConversationsQuery`: Key `['chatConversations']`.
    *   `useChatMessagesQuery`: Key `['chatMessages', chatId]`. `enabled` option based on `chatId` being active.
*   **AI Prompt:** "In a new file `frontend/src/hooks/chatHooks.ts`:
    *   Create `useChatConversationsQuery` (key: `['chatConversations']`) for `/api/chat/conversations`.
    *   Create `useChatMessagesQuery` (key: `['chatMessages', chatId]`, parameterized) for `/api/chat/public/messages` or `/api/chat/private/{chatId}/messages`. This query should use the `enabled` option based on whether `chatId` is active.
    Refactor `ChatDialog.tsx` (or relevant components in `VerseLayout.tsx`) to use these hooks."

### Task 5.2: WebSocket Integration with TanStack Query Cache
*   **Description:** Update WebSocket message handling in `VerseLayout.tsx` to modify TanStack Query's cache.
*   **Details:**
    *   On new message: `queryClient.setQueryData(['chatMessages', message.chat_id], (oldData) => { /* append */ })`.
    *   On conversation update: `queryClient.setQueryData(['chatConversations'], (oldData) => { /* update */ })`.
*   **AI Prompt:** "In `frontend/src/components/verse/VerseLayout.tsx` (or where WebSocket messages are handled):
    *   On receiving a new chat message via WebSocket, use `queryClient.setQueryData` to add it to the `['chatMessages', chatId]` cache.
    *   On receiving updates to conversation metadata, use `queryClient.setQueryData` to update the `['chatConversations']` cache."

### Task 5.3: Send Message Mutation
*   **Description:** Convert HTTP-based message sending to `useSendMessageMutation`.
*   **Details:** `mutationFn` calls `/api/chat/messages`. Optimistic update to show message immediately.
*   **AI Prompt:** "Create `useSendMessageMutation` in `frontend/src/hooks/chatHooks.ts` for POST to `/api/chat/messages`. Implement an optimistic update to add the message to the `['chatMessages', chatId]` cache immediately."

### Task 5.4: Client-Side Chat State
*   **Description:** Manage `selectedChat`, `viewMode`, WebSocket `isConnected` status, etc., using a minimal `uiChatStore.ts` or React Context.
*   **AI Prompt:** "Create `uiChatStore.ts` (Zustand) or a React Context for client-side states from `chatStore.ts` like `selectedChat`, `viewMode`, and WebSocket connection UI indicators. Update `ChatDialog.tsx` and `VerseLayout.tsx`."

---

## Phase 6: VPN Service Data (`vpnStore.ts`)

### Task 6.1: VPN Data Queries
*   **Description:** Migrate fetching for subscriptions, packages, and available panels.
*   **Details:**
    *   `useUserSubscriptionsQuery`: Key `['vpnSubscriptions']`, endpoint `/api/user/subscriptions`.
    *   `useVpnPackagesQuery`: Key `['vpnPackages']`, endpoint `/api/user/available-packages`.
    *   `useAvailablePanelsQuery`: Key `['vpnPanels']`, endpoint `/api/user/available-panels`.
    *   Update `PremiumTab.tsx` and `DashboardLayout.tsx` (for conditional fetching).
*   **AI Prompt:** "In a new file `frontend/src/hooks/vpnHooks.ts`:
    *   Create `useUserSubscriptionsQuery` (key: `['vpnSubscriptions']`) for `/api/user/subscriptions`.
    *   Create `useVpnPackagesQuery` (key: `['vpnPackages']`) for `/api/user/available-packages`.
    *   Create `useAvailablePanelsQuery` (key: `['vpnPanels']`) for `/api/user/available-panels`.
    Refactor `PremiumTab.tsx` and `DashboardLayout.tsx` to use these hooks, potentially with `enabled` options based on active tab."

### Task 6.2: Purchase Package Mutation
*   **Description:** Convert `purchasePackage` action to `usePurchaseVpnPackageMutation`.
*   **Details:** `mutationFn` calls `/api/user/purchase`. On success, invalidate `['vpnSubscriptions']`, `['currentUser']` (if balance shown there), and `['dashboardStats']`.
*   **AI Prompt:** "In `frontend/src/hooks/vpnHooks.ts`, create `usePurchaseVpnPackageMutation` for POST to `/api/user/purchase`. On success, invalidate `['vpnSubscriptions']`, `['currentUser']`, and `['dashboardStats']`."

### Task 6.3: Client-Side VPN UI State
*   **Description:** Manage `selectedPanel`, `selectedPackage` from `vpnStore.ts`.
*   **Details:** This is likely local component state within `PremiumTab.tsx` or a very small UI context/store if shared more broadly for a purchase flow.
*   **AI Prompt:** "For `selectedPanel` and `selectedPackage` previously in `vpnStore.ts`, manage them as local component state within `PremiumTab.tsx` or the relevant purchase flow components. If shared across a complex flow, consider a temporary local context."

---

## Phase 7: Component-Level Integration & Layouts

### Task 7.1: Refactor `DashboardLayout.tsx`
*   **Description:** Overhaul `DashboardLayout.tsx` to use TanStack Query for its data fetching logic.
*   **Details:**
    *   Replace manual `fetch` calls in `useEffect` with `useQuery` hooks (e.g., `useDashboardStatsQuery`, `useAllCardsQuery`).
    *   Leverage TanStack Query's `isLoading`, `isError`, `isSuccess` states to manage `initialDataLoaded` and `appLoadingState`.
    *   Use `enabled` option for `useQuery` for tab-specific data, removing manual `activeTab` checks for fetching.
    *   Rely on `refetchOnWindowFocus` for visibility-based refreshes.
*   **AI Prompt:** "Refactor `frontend/src/components/DashboardLayout.tsx`:
    *   Remove manual fetch calls in `useEffect` hooks.
    *   Integrate `useDashboardStatsQuery`, `useAllCardsQuery`, `useTasksQuery`, `useUserSubscriptionsQuery` etc. for initial data loads.
    *   Use query status flags (`isLoading`, `isSuccess`, `isError`) to manage `initialDataLoaded` and interact with `appLoadingState`.
    *   For tab-specific data (like full VPN data on 'premium' tab), use the `enabled` option on the respective `useQuery` hook, controlled by `activeTab`.
    *   Simplify or remove the `visibilitychange` listener if `refetchOnWindowFocus` sufficiently covers its needs."

### Task 7.2: Review `VersePage.tsx` & `SceneSetup.tsx`
*   **Description:** Ensure any server state they might consume is fetched via TanStack Query.
*   **Details:** If these components rely on data from `authStore`, `dashboardStore`, etc., they should now use the corresponding `useQuery` hooks. Client-side performance/visibility toggles remain as local state.
*   **AI Prompt:** "Review `frontend/src/pages/VersePage.tsx` and `frontend/src/components/verse/SceneSetup.tsx`. If they depend on server state from stores like `authStore` or `dashboardStore`, refactor to use the newly created TanStack Query hooks. Purely client-side state (e.g., `isPerformanceMode`) should remain managed locally."

---

## Phase 8: General Cleanup & Refinement

### Task 8.1: Gradual Zustand Store Reduction & Elimination
*   **Description:** Remove migrated functionalities from original Zustand stores. Evaluate if stores can be fully removed or simplified to UI-only stores.
*   **AI Prompt:** "Systematically review `authStore.ts`, `chatStore.ts`, `dashboardStore.ts`, `cardStore.ts`, `taskStore.ts`, and `vpnStore.ts`. Remove state and actions that are now handled by TanStack Query. If a store becomes empty or only holds minimal UI state, consider converting it to a smaller, focused UI store (e.g., `uiGlobalStore.ts`) or removing it if React Context/local state suffices."

### Task 8.2: Review `storeUtils.ts`
*   **Description:** Assess `handleStoreError` usage. TanStack Query's error objects might make it redundant.
*   **AI Prompt:** "Examine `frontend/src/utils/storeUtils.ts`. If `handleStoreError` is primarily used for API errors now handled by TanStack Query's `error` objects, refactor to use TanStack Query's error handling and consider deprecating `handleStoreError`. Keep `logDataFetch` if still useful."

### Task 8.3: TanStack Query Configuration Review
*   **Description:** Fine-tune `staleTime`, `cacheTime`, `refetchOnWindowFocus`, `refetchInterval`, `retry` for all queries.
*   **AI Prompt:** "Review all `useQuery` hooks. Define optimal `staleTime`, `cacheTime`, and `refetchOnWindowFocus` settings. Use `refetchInterval` for polling only if WebSockets are not a better fit. Adjust `retry` behavior as needed."

### Task 8.4: Testing
*   **Description:** Update existing tests and write new ones for components using TanStack Query.
*   **AI Prompt:** "Update Jest/React Testing Library tests for components now using TanStack Query. Ensure QueryClientProvider is in the test environment. Mock query/mutation responses to test various states."

### Task 8.5: Audit and Remove Redundant Zustand Stores
*   **Description:** Review remaining Zustand stores and confirm that server state is fully managed by TanStack Query hooks. Remove any store fully replaced by hooks, retaining only minimal UI-focused stores (<1KB) for purely client-side UI state.
*   **AI Prompt:** "Audit original stores (authStore, chatStore, dashboardStore, cardStore, taskStore, vpnStore). Verify no server logic remains. Delete empty or UI-only stores and ensure the application functions correctly with hooks and minimal UI stores."

### Phase 8.6: Audit and Refactor All Components for UI State

- **Description:** Remove all imports/usages of deleted Zustand stores from all components. Replace any remaining global UI state with minimal Zustand stores (`uiStore.ts`, `uiChatStore.ts`).
- **Files to update:**  
  - `HomeTabPage.tsx`, `EarnTab.tsx`, `CardTab.tsx`, `TaskTab.tsx`, `PremiumTab.tsx`, `FriendsTab.tsx`, `WalletTabPage.tsx`, `verse/VerseLayout.tsx`
  - All chat components: `chat/ChatDialog.tsx`, `chat/ChatPanel.tsx`, `chat/ConversationList.tsx`, `chat/ChatMessage.tsx`, `chat/MessageInput.tsx`, `chat/UserProfileDialog.tsx`, `chat/ChatErrorBoundary.tsx`, `chat/index.ts`
- **How:**  
  - Use TanStack Query hooks for all server state.
  - Use `uiStore.ts` for general UI state (theme, splash, etc.).
  - Use `uiChatStore.ts` for chat UI state (selected chat, chat view mode, etc.).
  - Remove all imports/usages of deleted Zustand stores for UI state.

#### UI State Store Refactor Checklist
- [x] Create/verify `src/stores/uiStore.ts` for general UI state.
- [x] Create/verify `src/stores/uiChatStore.ts` for chat UI state.
- [x] Replace all global UI state in components with these stores.
- [x] Remove all imports/usages of deleted Zustand stores for UI state.

| File                                    | Remove Store Imports | Use TanStack Query | Use UI Store(s) |
|------------------------------------------|:-------------------:|:------------------:|:---------------:|
| `HomeTabPage.tsx`                       |         ✅          |        ✅         |       ✅        |
| `EarnTab.tsx`                           |         ✅          |        ✅         |       ✅        |
| `CardTab.tsx`                           |         ✅          |        ✅         |       ✅        |
| `TaskTab.tsx`                           |         ✅          |        ✅         |       ✅        |
| `PremiumTab.tsx`                        |         ✅          |        ✅         |       ✅        |
| `FriendsTab.tsx`                        |         ✅          |        ✅         |       ✅        |
| `WalletTabPage.tsx`                     |         ✅          |        ✅         |       ✅        |
| `verse/VerseLayout.tsx`                 |         ✅          |        ✅         |       ✅        |
| `chat/ChatDialog.tsx`                   |         ✅          |        ✅         |       ✅        |
| `chat/ChatPanel.tsx`                    |         ✅          |        ✅         |       ✅        |
| `chat/ConversationList.tsx`             |         ✅          |        ✅         |       ✅        |
| `chat/ChatMessage.tsx`                  |         ✅          |        ✅         |       ✅        |
| `chat/MessageInput.tsx`                 |         ✅          |        ✅         |       ✅        |
| `chat/UserProfileDialog.tsx`            |         ✅          |        ✅         |       ✅        |
| `chat/ChatErrorBoundary.tsx`            |         ✅          |        ✅         |       ✅        |
| `chat/index.ts`                         |         ✅          |        ✅         |       ✅        |

- **Test:**
  - Run the app and ensure there are no import errors or runtime errors.
  - Run `pnpm run lint` and fix any issues.
  - Update this checklist as you complete each file.

---

## Conclusion

- This plan ensures **all server state is managed by React Query** and **all global UI state is managed by minimal, focused Zustand stores**.
- All files are explicitly listed for audit and update.
- The plan is actionable, testable, and easy to check off as you complete each file. 