<template>
  <div class="default-layout">
    <!-- Background Effects -->
    <div class="background-effects">
      <div class="bg-orb bg-orb-1"></div>
      <div class="bg-orb bg-orb-2"></div>
      <div class="bg-orb bg-orb-3"></div>
      <div class="bg-orb bg-orb-4"></div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
      <slot />
    </main>

    <!-- Global Loading Overlay -->
    <div v-if="globalLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">Loading...</p>
      </div>
    </div>

    <!-- Toast Notifications -->
    <div class="toast-container">
      <div
        v-for="toast in activeToasts"
        :key="toast.id"
        class="toast-item"
      >
        <UAlert
          :title="toast.title"
          :description="toast.message"
          :color="getToastColor(toast.type)"
          variant="solid"
          :close-button="{ icon: 'i-heroicons-x-mark-20-solid', color: 'white', variant: 'link' }"
          @close="removeToast(toast.id)"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const uiStore = useUiStore()

// Computed properties
const globalLoading = computed(() => uiStore.globalLoading)
const activeToasts = computed(() => uiStore.activeToasts)

// Methods
const removeToast = (id: string) => {
  uiStore.removeToast(id)
}

const getToastColor = (type: string) => {
  const colors: Record<string, string> = {
    success: 'green',
    error: 'red',
    warning: 'yellow',
    info: 'blue'
  }
  return colors[type] || 'blue'
}

// Initialize theme
onMounted(() => {
  uiStore.initializeTheme()
})
</script>

<style scoped>
.default-layout {
  min-height: 100vh;
  background-color: var(--color-atlas-black);
  color: var(--ui-text);
  position: relative;
  overflow-x: hidden;
}

/* Background Effects */
.background-effects {
  position: fixed;
  inset: 0;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.bg-orb {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.6;
}

.bg-orb-1 {
  top: 10%;
  left: 10%;
  width: 300px;
  height: 300px;
  background: radial-gradient(circle, var(--color-atlas-purple-500) 0%, transparent 70%);
  animation: float 8s ease-in-out infinite;
}

.bg-orb-2 {
  top: 60%;
  right: 10%;
  width: 400px;
  height: 400px;
  background: radial-gradient(circle, var(--color-atlas-cyan) 0%, transparent 70%);
  animation: float 10s ease-in-out infinite reverse;
}

.bg-orb-3 {
  bottom: 20%;
  left: 20%;
  width: 250px;
  height: 250px;
  background: radial-gradient(circle, var(--color-atlas-gold) 0%, transparent 70%);
  animation: float 12s ease-in-out infinite;
  animation-delay: -2s;
}

.bg-orb-4 {
  top: 40%;
  left: 50%;
  width: 200px;
  height: 200px;
  background: radial-gradient(circle, var(--color-atlas-emerald) 0%, transparent 70%);
  animation: float 9s ease-in-out infinite;
  animation-delay: -4s;
}

/* Main Content */
.main-content {
  position: relative;
  z-index: 1;
  min-height: 100vh;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(8px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  text-align: center;
  color: var(--ui-text);
}

.loading-spinner {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--spacing-4);
  border: 3px solid var(--color-atlas-darker);
  border-top: 3px solid var(--color-atlas-purple-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: var(--font-size-lg);
  font-weight: 500;
}

/* Toast Container */
.toast-container {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
  max-width: 400px;
}

.toast-item {
  width: 100%;
}

/* Responsive Adjustments */
@media (max-width: 639px) {
  .bg-orb {
    filter: blur(40px);
    opacity: 0.4;
  }

  .bg-orb-1,
  .bg-orb-2 {
    width: 200px;
    height: 200px;
  }

  .bg-orb-3,
  .bg-orb-4 {
    width: 150px;
    height: 150px;
  }

  .toast-container {
    top: var(--spacing-2);
    right: var(--spacing-2);
    left: var(--spacing-2);
    max-width: none;
  }
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
  }
  33% {
    transform: translateY(-20px) translateX(10px);
  }
  66% {
    transform: translateY(10px) translateX(-10px);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .bg-orb {
    animation: none;
  }

  .loading-spinner {
    animation: none;
    border-top-color: var(--color-atlas-purple-500);
  }
}
</style>
