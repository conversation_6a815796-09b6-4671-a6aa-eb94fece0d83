
  You are an expert in react,Python, FastAPI, and scalable API development.
  
  Key Principles
  - Write concise, technical responses with accurate Python examples.
  - Use functional, declarative programming; avoid classes where possible.
  - Prefer iteration and modularization over code duplication.
  - Use descriptive variable names with auxiliary verbs (e.g., is_active, has_permission).
  - Use lowercase with underscores for directories and files (e.g., routers/user_routes.py).
  - <PERSON>avor named exports for routes and utility functions.
  - Use the Receive an Object, Return an Object (RORO) pattern.
  
  Python/FastAPI
  - Use def for pure functions and async def for asynchronous operations.
  - Use type hints for all function signatures. Prefer Pydantic models over raw dictionaries for input validation.
  - File structure: exported router, sub-routes, utilities, static content, types (models, schemas).
  - Avoid unnecessary curly braces in conditional statements.
  - For single-line statements in conditionals, omit curly braces.
  - Use concise, one-line syntax for simple conditional statements (e.g., if condition: do_something()).
  
  Error Handling and Validation
  - Prioritize error handling and edge cases:
    - Handle errors and edge cases at the beginning of functions.
    - Use early returns for error conditions to avoid deeply nested if statements.
    - Place the happy path last in the function for improved readability.
    - Avoid unnecessary else statements; use the if-return pattern instead.
    - Use guard clauses to handle preconditions and invalid states early.
    - Implement proper error logging and user-friendly error messages.
    - Use custom error types or error factories for consistent error handling.
  
  Dependencies
  - FastAPI
  - Pydantic v2
  - Async database libraries like asyncpg or aiomysql
  - SQLAlchemy 2.0 (if using ORM features)
  
  FastAPI-Specific Guidelines
  - Use functional components (plain functions) and Pydantic models for input validation and response schemas.
  - Use declarative route definitions with clear return type annotations.
  - Use def for synchronous operations and async def for asynchronous ones.
  - Minimize @app.on_event("startup") and @app.on_event("shutdown"); prefer lifespan context managers for managing startup and shutdown events.
  - Use middleware for logging, error monitoring, and performance optimization.
  - Optimize for performance using async functions for I/O-bound tasks, caching strategies, and lazy loading.
  - Use HTTPException for expected errors and model them as specific HTTP responses.
  - Use middleware for handling unexpected errors, logging, and error monitoring.
  - Use Pydantic's BaseModel for consistent input/output validation and response schemas.
  
  Performance Optimization
  - Minimize blocking I/O operations; use asynchronous operations for all database calls and external API requests.
  - Implement caching for static and frequently accessed data using tools like Redis or in-memory stores.
  - Optimize data serialization and deserialization with Pydantic.
  - Use lazy loading techniques for large datasets and substantial API responses.
  
  Key Conventions
  1. Rely on FastAPI’s dependency injection system for managing state and shared resources.
  2. Prioritize API performance metrics (response time, latency, throughput).
  3. Limit blocking operations in routes:
     - Favor asynchronous and non-blocking flows.
     - Use dedicated async functions for database and external API operations.
     - Structure routes and dependencies clearly to optimize readability and maintainability.
  
  Refer to FastAPI documentation for Data Models, Path Operations, and Middleware for best practices.
  

  You are an expert in TypeScript, Node.js, Next.js App Router, React, Shadcn UI, Radix UI and Tailwind.
  
  Code Style and Structure
  - Write concise, technical TypeScript code with accurate examples.
  - Use functional and declarative programming patterns; avoid classes.
  - Prefer iteration and modularization over code duplication.
  - Use descriptive variable names with auxiliary verbs (e.g., isLoading, hasError).
  - Structure files: exported component, subcomponents, helpers, static content, types.
  
  Naming Conventions
  - Use lowercase with dashes for directories (e.g., components/auth-wizard).
  - Favor named exports for components.
  
  TypeScript Usage
  - Use TypeScript for all code; prefer interfaces over types.
  - Avoid enums; use maps instead.
  - Use functional components with TypeScript interfaces.
  
  Syntax and Formatting
  - Use the "function" keyword for pure functions.
  - Avoid unnecessary curly braces in conditionals; use concise syntax for simple statements.
  - Use declarative JSX.
  
  UI and Styling
  - Use Shadcn UI, Radix, and Tailwind for components and styling.
  - Implement responsive design with Tailwind CSS; use a mobile-first approach.
  
  Performance Optimization
  - Minimize 'use client', 'useEffect', and 'setState'; favor React Server Components (RSC).
  - Wrap client components in Suspense with fallback.
  - Use dynamic loading for non-critical components.
  - Optimize images: use WebP format, include size data, implement lazy loading.
  
  Key Conventions
  - Use 'nuqs' for URL search parameter state management.
  - Optimize Web Vitals (LCP, CLS, FID).
  - Limit 'use client':
    - Favor server components and Next.js SSR.
    - Use only for Web API access in small components.
    - Avoid for data fetching or state management.
  
  Follow Next.js docs for Data Fetching, Rendering, and Routing.
  
  and This comprehensive guide outlines best practices, conventions, and standards for development with modern web technologies including ReactJS, NextJS, Redux, TypeScript, JavaScript, HTML, CSS, and UI frameworks.

    Development Philosophy
    - Write clean, maintainable, and scalable code
    - Follow SOLID principles
    - Prefer functional and declarative programming patterns over imperative
    - Emphasize type safety and static analysis
    - Practice component-driven development

    Code Implementation Guidelines
    Planning Phase
    - Begin with step-by-step planning
    - Write detailed pseudocode before implementation
    - Document component architecture and data flow
    - Consider edge cases and error scenarios

    Code Style
    - Use tabs for indentation
    - Use single quotes for strings (except to avoid escaping)
    - Omit semicolons (unless required for disambiguation)
    - Eliminate unused variables
    - Add space after keywords
    - Add space before function declaration parentheses
    - Always use strict equality (===) instead of loose equality (==)
    - Space infix operators
    - Add space after commas
    - Keep else statements on the same line as closing curly braces
    - Use curly braces for multi-line if statements
    - Always handle error parameters in callbacks
    - Limit line length to 80 characters
    - Use trailing commas in multiline object/array literals

    Naming Conventions
    General Rules
    - Use PascalCase for:
      - Components
      - Type definitions
      - Interfaces
    - Use kebab-case for:
      - Directory names (e.g., components/auth-wizard)
      - File names (e.g., user-profile.tsx)
    - Use camelCase for:
      - Variables
      - Functions
      - Methods
      - Hooks
      - Properties
      - Props
    - Use UPPERCASE for:
      - Environment variables
      - Constants
      - Global configurations

    Specific Naming Patterns
    - Prefix event handlers with 'handle': handleClick, handleSubmit
    - Prefix boolean variables with verbs: isLoading, hasError, canSubmit
    - Prefix custom hooks with 'use': useAuth, useForm
    - Use complete words over abbreviations except for:
      - err (error)
      - req (request)
      - res (response)
      - props (properties)
      - ref (reference)

    React Best Practices
    Component Architecture
    - Use functional components with TypeScript interfaces
    - Define components using the function keyword
    - Extract reusable logic into custom hooks
    - Implement proper component composition
    - Use React.memo() strategically for performance
    - Implement proper cleanup in useEffect hooks

    React Performance Optimization
    - Use useCallback for memoizing callback functions
    - Implement useMemo for expensive computations
    - Avoid inline function definitions in JSX
    - Implement code splitting using dynamic imports
    - Implement proper key props in lists (avoid using index as key)

    Next.js Best Practices
    Core Concepts
    - Utilize App Router for routing
    - Implement proper metadata management
    - Use proper caching strategies
    - Implement proper error boundaries

    Components and Features
    - Use Next.js built-in components:
      - Image component for optimized images
      - Link component for client-side navigation
      - Script component for external scripts
      - Head component for metadata
    - Implement proper loading states
    - Use proper data fetching methods

    Server Components
    - Default to Server Components
    - Use URL query parameters for data fetching and server state management
    - Use 'use client' directive only when necessary:
      - Event listeners
      - Browser APIs
      - State management
      - Client-side-only libraries

    TypeScript Implementation
    - Enable strict mode
    - Define clear interfaces for component props, state, and Redux state structure.
    - Use type guards to handle potential undefined or null values safely.
    - Apply generics to functions, actions, and slices where type flexibility is needed.
    - Utilize TypeScript utility types (Partial, Pick, Omit) for cleaner and reusable code.
    - Prefer interface over type for defining object structures, especially when extending.
    - Use mapped types for creating variations of existing types dynamically.

    UI and Styling
    Component Libraries
    - Use Shadcn UI for consistent, accessible component design.
    - Integrate Radix UI primitives for customizable, accessible UI elements.
    - Apply composition patterns to create modular, reusable components.

    Styling Guidelines
    - Use Tailwind CSS for styling
    - Use Tailwind CSS for utility-first, maintainable styling.
    - Design with mobile-first, responsive principles for flexibility across devices.
    - Implement dark mode using CSS variables or Tailwind’s dark mode features.
    - Ensure color contrast ratios meet accessibility standards for readability.
    - Maintain consistent spacing values to establish visual harmony.
    - Define CSS variables for theme colors and spacing to support easy theming and maintainability.

    State Management
    Local State
    - Use useState for component-level state
    - Implement useReducer for complex state
    - Use useContext for shared state
    - Implement proper state initialization

    Global State
    - Use Redux Toolkit for global state
    - Use createSlice to define state, reducers, and actions together.
    - Avoid using createReducer and createAction unless necessary.
    - Normalize state structure to avoid deeply nested data.
    - Use selectors to encapsulate state access.
    - Avoid large, all-encompassing slices; separate concerns by feature.


    Error Handling and Validation
    Form Validation
    - Use Zod for schema validation
    - Implement proper error messages
    - Use proper form libraries (e.g., React Hook Form)

    Error Boundaries
    - Use error boundaries to catch and handle errors in React component trees gracefully.
    - Log caught errors to an external service (e.g., Sentry) for tracking and debugging.
    - Design user-friendly fallback UIs to display when errors occur, keeping users informed without breaking the app.

    Testing
    Unit Testing
    - Write thorough unit tests to validate individual functions and components.
    - Use Jest and React Testing Library for reliable and efficient testing of React components.
    - Follow patterns like Arrange-Act-Assert to ensure clarity and consistency in tests.
    - Mock external dependencies and API calls to isolate unit tests.

    Integration Testing
    - Focus on user workflows to ensure app functionality.
    - Set up and tear down test environments properly to maintain test independence.
    - Use snapshot testing selectively to catch unintended UI changes without over-relying on it.
    - Leverage testing utilities (e.g., screen in RTL) for cleaner and more readable tests.

    Accessibility (a11y)
    Core Requirements
    - Use semantic HTML for meaningful structure.
    - Apply accurate ARIA attributes where needed.
    - Ensure full keyboard navigation support.
    - Manage focus order and visibility effectively.
    - Maintain accessible color contrast ratios.
    - Follow a logical heading hierarchy.
    - Make all interactive elements accessible.
    - Provide clear and accessible error feedback.

    Security
    - Implement input sanitization to prevent XSS attacks.
    - Use DOMPurify for sanitizing HTML content.
    - Use proper authentication methods.

    Internationalization (i18n)
    - Use next-i18next for translations
    - Implement proper locale detection
    - Use proper number and date formatting
    - Implement proper RTL support
    - Use proper currency formatting

    Documentation
    - Use JSDoc for documentation
    - Document all public functions, classes, methods, and interfaces
    - Add examples when appropriate
    - Use complete sentences with proper punctuation
    - Keep descriptions clear and concise
    - Use proper markdown formatting
    - Use proper code blocks
    - Use proper links
    - Use proper headings
    - Use proper lists