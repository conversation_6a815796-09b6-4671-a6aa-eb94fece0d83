/**
 * Chat Store - Lightweight Zustand store for chat and WebSocket management
 * Migrated from original frontend
 */

import { create } from 'zustand';

interface ChatMessage {
  id: string;
  content: string;
  sender: string;
  timestamp: Date;
  type?: 'text' | 'system' | 'error';
  [key: string]: any;
}

interface ChatConversation {
  id: string;
  name: string;
  messages: ChatMessage[];
  lastActivity: Date;
  participants?: string[];
}

interface ChatState {
  // WebSocket connection
  wsUrl: string | null;
  setWsUrl: (url: string | null) => void;
  
  isConnected: boolean;
  setConnected: (connected: boolean) => void;
  
  // Chat data
  conversations: ChatConversation[];
  setConversations: (conversations: ChatConversation[]) => void;
  addConversation: (conversation: ChatConversation) => void;
  updateConversation: (conversationId: string, updates: Partial<ChatConversation>) => void;
  removeConversation: (conversationId: string) => void;
  
  // Messages
  addMessage: (conversationId: string, message: ChatMessage) => void;
  updateMessage: (conversationId: string, messageId: string, updates: Partial<ChatMessage>) => void;
  removeMessage: (conversationId: string, messageId: string) => void;
  
  // Current state
  activeConversationId: string | null;
  setActiveConversation: (conversationId: string | null) => void;
  
  // Loading and error states
  isLoading: boolean;
  setLoading: (loading: boolean) => void;
  
  error: string | null;
  setError: (error: string | null) => void;
  
  // WebSocket token management
  wsToken: string | null;
  setWsToken: (token: string | null) => void;
  
  // Utility functions
  getConversationById: (conversationId: string) => ChatConversation | undefined;
  getActiveConversation: () => ChatConversation | undefined;
  getMessagesByConversation: (conversationId: string) => ChatMessage[];
  
  // WebSocket URL construction
  buildWebSocketUrl: () => string | null;
  
  // Reset function
  resetChat: () => void;
}

const initialState = {
  wsUrl: null,
  isConnected: false,
  conversations: [],
  activeConversationId: null,
  isLoading: false,
  error: null,
  wsToken: null,
};

export const useChatStore = create<ChatState>()((set, get) => ({
  ...initialState,
  
  setWsUrl: (url: string | null) => set({ wsUrl: url }),
  
  setConnected: (connected: boolean) => set({ isConnected: connected }),
  
  setConversations: (conversations: ChatConversation[]) => 
    set({ conversations }),
  
  addConversation: (conversation: ChatConversation) => set((state) => ({
    conversations: [...state.conversations, conversation]
  })),
  
  updateConversation: (conversationId: string, updates: Partial<ChatConversation>) => 
    set((state) => ({
      conversations: state.conversations.map(conv => 
        conv.id === conversationId ? { ...conv, ...updates } : conv
      )
    })),
  
  removeConversation: (conversationId: string) => set((state) => ({
    conversations: state.conversations.filter(conv => conv.id !== conversationId)
  })),
  
  addMessage: (conversationId: string, message: ChatMessage) => set((state) => ({
    conversations: state.conversations.map(conv => 
      conv.id === conversationId 
        ? { 
            ...conv, 
            messages: [...conv.messages, message],
            lastActivity: new Date()
          }
        : conv
    )
  })),
  
  updateMessage: (conversationId: string, messageId: string, updates: Partial<ChatMessage>) => 
    set((state) => ({
      conversations: state.conversations.map(conv => 
        conv.id === conversationId 
          ? {
              ...conv,
              messages: conv.messages.map(msg => 
                msg.id === messageId ? { ...msg, ...updates } : msg
              )
            }
          : conv
      )
    })),
  
  removeMessage: (conversationId: string, messageId: string) => set((state) => ({
    conversations: state.conversations.map(conv => 
      conv.id === conversationId 
        ? {
            ...conv,
            messages: conv.messages.filter(msg => msg.id !== messageId)
          }
        : conv
    )
  })),
  
  setActiveConversation: (conversationId: string | null) => 
    set({ activeConversationId: conversationId }),
  
  setLoading: (loading: boolean) => set({ isLoading: loading }),
  
  setError: (error: string | null) => set({ error }),
  
  setWsToken: (token: string | null) => set({ wsToken: token }),
  
  getConversationById: (conversationId: string) => {
    const { conversations } = get();
    return conversations.find(conv => conv.id === conversationId);
  },
  
  getActiveConversation: () => {
    const { conversations, activeConversationId } = get();
    if (!activeConversationId) return undefined;
    return conversations.find(conv => conv.id === activeConversationId);
  },
  
  getMessagesByConversation: (conversationId: string) => {
    const conversation = get().getConversationById(conversationId);
    return conversation?.messages || [];
  },
  
  buildWebSocketUrl: () => {
    const { wsToken } = get();
    if (!wsToken) return null;
    
    // Build WebSocket URL based on environment
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    const wsPath = '/ws/chat';
    
    return `${protocol}//${host}${wsPath}?token=${wsToken}`;
  },
  
  resetChat: () => set(initialState),
}));

// Export store instance for direct access if needed
export default useChatStore;