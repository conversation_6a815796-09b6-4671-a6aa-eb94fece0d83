import React from 'react';
import QueryClientWrapper from './QueryClientWrapper';
import ToastProvider from './ToastProvider';
import LandingPageInteractive from './LandingPageInteractive';
import AuthStatus from '../AuthStatus';

const LandingPageWrapper: React.FC = () => {
  return (
    <QueryClientWrapper>
      <ToastProvider>
        <LandingPageInteractive />
        <AuthStatus />
      </ToastProvider>
    </QueryClientWrapper>
  );
};

export default LandingPageWrapper;
