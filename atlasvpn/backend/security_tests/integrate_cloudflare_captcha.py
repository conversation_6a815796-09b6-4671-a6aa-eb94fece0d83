#!/usr/bin/env python
"""
Cloudflare CAPTCHA Integration Script
-------------------------------------
This script demonstrates how to integrate Cloudflare Turnstile CAPTCHA with critical endpoints.
"""
import requests
import json
import logging
import sys
from typing import Dict, Any, Optional, List, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("cloudflare_captcha_integration.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Constants
BASE_URL = "http://localhost:8000"

# Cloudflare Turnstile config (these would be your actual Cloudflare keys in production)
CLOUDFLARE_SITE_KEY = "1x00000000000000000000AA"  # Example site key - replace with real one
CLOUDFLARE_SECRET_KEY = "1x0000000000000000000000000000000AA"  # Example secret key - replace with real one

# Critical routes that require CAPTCHA
CRITICAL_ROUTES = [
    {
        "name": "Login",
        "endpoint": "/auth/token",
        "method": "POST"
    },
    {
        "name": "Register",
        "endpoint": "/auth/register",
        "method": "POST"
    },
    {
        "name": "Admin Login",
        "endpoint": "/auth/admin/login",
        "method": "POST"
    }
]

def verify_turnstile_token(token: str, secret_key: str = CLOUDFLARE_SECRET_KEY) -> Dict[str, Any]:
    """Verify a Cloudflare Turnstile token"""
    try:
        response = requests.post(
            "https://challenges.cloudflare.com/turnstile/v0/siteverify",
            data={
                "secret": secret_key,
                "response": token,
                "remoteip": "127.0.0.1"  # In production, use the actual user's IP
            },
            timeout=10
        )
        
        return response.json()
    except Exception as e:
        logger.error(f"Error verifying Turnstile token: {e}")
        return {"success": False, "error": str(e)}

def create_frontend_captcha_html(route: Dict[str, str]) -> str:
    """Create HTML frontend example for Cloudflare Turnstile CAPTCHA integration"""
    # This is a simplified example showing how to integrate Cloudflare Turnstile on the frontend
    html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{route['name']} with CAPTCHA</title>
    <script src="https://challenges.cloudflare.com/turnstile/v0/api.js" async defer></script>
    <style>
        body {{
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }}
        .form-group {{
            margin-bottom: 15px;
        }}
        label {{
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }}
        input[type="text"], input[type="password"] {{
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }}
        button {{
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }}
        .captcha-container {{
            margin: 20px 0;
        }}
        .error {{
            color: red;
            margin-top: 10px;
        }}
    </style>
</head>
<body>
    <h1>{route['name']}</h1>
    <form id="secureForm" method="post" action="{route['endpoint']}">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        <div class="captcha-container">
            <div class="cf-turnstile" data-sitekey="{CLOUDFLARE_SITE_KEY}" data-callback="onCaptchaSuccess"></div>
        </div>
        <div id="error" class="error"></div>
        <button type="submit" id="submitButton" disabled>Submit</button>
    </form>

    <script>
        // Enable submit button when CAPTCHA is solved
        function onCaptchaSuccess(token) {{
            document.getElementById('submitButton').disabled = false;
        }}

        document.getElementById('secureForm').addEventListener('submit', async function(e) {{
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const token = document.querySelector('[name="cf-turnstile-response"]').value;
            
            try {{
                const response = await fetch('{route['endpoint']}', {{
                    method: '{route['method']}',
                    headers: {{
                        'Content-Type': 'application/json'
                    }},
                    body: JSON.stringify({{
                        username,
                        password,
                        'cf-turnstile-response': token
                    }})
                }});
                
                const result = await response.json();
                
                if (!response.ok) {{
                    document.getElementById('error').textContent = result.detail || 'An error occurred';
                    // Reset CAPTCHA on error
                    turnstile.reset();
                    document.getElementById('submitButton').disabled = true;
                }} else {{
                    // Successful authentication
                    window.location.href = '/dashboard';
                }}
            }} catch (error) {{
                document.getElementById('error').textContent = 'Network or server error';
                console.error('Error:', error);
            }}
        }});
    </script>
</body>
</html>
"""
    return html

def create_backend_middleware_code() -> str:
    """Create an example of how to implement the backend middleware for CAPTCHA verification"""
    code = """
from fastapi import Request, Response, status
from starlette.middleware.base import BaseHTTPMiddleware
import httpx
import json
import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)

# Critical endpoints that require CAPTCHA
CAPTCHA_REQUIRED_ENDPOINTS = [
    "/auth/token",
    "/auth/register", 
    "/auth/admin/login"
]

class CloudflareCaptchaMiddleware(BaseHTTPMiddleware):
    """Middleware for Cloudflare Turnstile CAPTCHA verification"""
    
    def __init__(self, app):
        super().__init__(app)
        self.secret_key = "YOUR_CLOUDFLARE_SECRET_KEY"  # Load from environment variable
    
    async def dispatch(self, request: Request, call_next):
        # Check if the endpoint requires CAPTCHA
        path = request.url.path
        
        # Skip CAPTCHA verification for non-critical endpoints
        if path not in CAPTCHA_REQUIRED_ENDPOINTS:
            return await call_next(request)
        
        # Only verify POST requests (form submissions)
        if request.method != "POST":
            return await call_next(request)
        
        try:
            # Read request body
            body = await request.body()
            body_str = body.decode()
            
            # Parse JSON body
            try:
                data = json.loads(body_str)
            except json.JSONDecodeError:
                # If not JSON, try to parse as form data
                form_data = await request.form()
                data = dict(form_data)
            
            # Check for CAPTCHA token
            token = data.get("cf-turnstile-response")
            
            if not token:
                # No CAPTCHA token provided
                return Response(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    content=json.dumps({
                        "detail": "CAPTCHA verification required",
                        "code": "CAPTCHA_REQUIRED"
                    }),
                    media_type="application/json"
                )
            
            # Verify token with Cloudflare
            async with httpx.AsyncClient() as client:
                verification_response = await client.post(
                    "https://challenges.cloudflare.com/turnstile/v0/siteverify",
                    data={
                        "secret": self.secret_key,
                        "response": token,
                        "remoteip": request.client.host
                    },
                    timeout=10
                )
                
                verification_data = verification_response.json()
                
                if not verification_data.get("success", False):
                    # CAPTCHA verification failed
                    return Response(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        content=json.dumps({
                            "detail": "CAPTCHA verification failed",
                            "code": "INVALID_CAPTCHA",
                            "errors": verification_data.get("error-codes", [])
                        }),
                        media_type="application/json"
                    )
            
            # Create new request with original body
            async def receive():
                return {"type": "http.request", "body": body}
            
            request._receive = receive
            
            # CAPTCHA verified, proceed with request
            return await call_next(request)
            
        except Exception as e:
            logger.error(f"CAPTCHA verification error: {str(e)}")
            return Response(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content=json.dumps({
                    "detail": "Error verifying CAPTCHA",
                    "code": "CAPTCHA_ERROR"
                }),
                media_type="application/json"
            )

# Add to main.py:
# app.add_middleware(CloudflareCaptchaMiddleware)
"""
    return code

def main() -> None:
    """Main function to demonstrate Cloudflare CAPTCHA integration"""
    print(f"Cloudflare Turnstile CAPTCHA Integration")
    print(f"=======================================\n")
    
    # 1. Generate frontend integration examples
    print(f"Generating frontend integration examples...")
    for route in CRITICAL_ROUTES:
        filename = f"cf_captcha_{route['name'].lower().replace(' ', '_')}.html"
        html = create_frontend_captcha_html(route)
        
        with open(filename, "w") as f:
            f.write(html)
        print(f"  - Created {filename}")
    
    # 2. Generate backend middleware code
    print(f"\nGenerating backend middleware code...")
    middleware_filename = "cloudflare_captcha_middleware.py"
    code = create_backend_middleware_code()
    
    with open(middleware_filename, "w") as f:
        f.write(code)
    print(f"  - Created {middleware_filename}")
    
    # 3. Adding middleware to main.py
    print(f"\nTo enable Cloudflare CAPTCHA verification, you need to:")
    print(f"  1. Add the CloudflareCaptchaMiddleware to your main.py file")
    print(f"  2. Set your Cloudflare Turnstile secret key in the middleware")
    print(f"  3. Update your frontend forms to include the Cloudflare widget")
    
    # 4. Instructions for testing
    print(f"\nOnce integrated, you can test it using:")
    print(f"  - The test_rate_limit_captcha.py script")
    print(f"  - Manual testing with a browser")

if __name__ == "__main__":
    main() 