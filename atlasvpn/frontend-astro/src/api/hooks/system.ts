/**
 * @file api/hooks/system.ts
 * @description System-related React Query hooks
 */

import { useQuery } from '@tanstack/react-query';
import type {
  QueryOptions
} from '../types';
import { systemService } from '../services';

// Query Keys
export const SYSTEM_QUERY_KEYS = {
  HEALTH_CHECK: ['system', 'health'],
  SYSTEM_STATUS: ['system', 'status']
} as const;

// Default query options
const DEFAULT_QUERY_OPTIONS: QueryOptions = {
  staleTime: 5 * 60 * 1000, // 5 minutes
  cacheTime: 10 * 60 * 1000, // 10 minutes
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  retry: false
};

// System Hooks
export function useHealthCheckQuery(options?: QueryOptions) {
  return useQuery({
    queryKey: SYSTEM_QUERY_KEYS.HEALTH_CHECK,
    queryFn: () => systemService.healthCheck(),
    ...DEFAULT_QUERY_OPTIONS,
    ...options
  });
}