/**
 * @file components/AuthStatus.css
 * @description Styles for the AuthStatus component
 */

.auth-status {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  max-width: 400px;
  width: 90%;
  background: var(--tg-theme-bg-color, #ffffff);
  border: 1px solid var(--tg-theme-hint-color, #e0e0e0);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 24px;
  text-align: center;
}

.auth-status--loading {
  background: var(--tg-theme-secondary-bg-color, #f8f9fa);
  border-color: var(--tg-theme-button-color, #007bff);
}

.auth-status--failed {
  background: var(--tg-theme-bg-color, #ffffff);
  border-color: var(--tg-theme-destructive-text-color, #dc3545);
}

.auth-status__content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.auth-status__title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--tg-theme-text-color, #000000);
}

.auth-status--failed .auth-status__title {
  color: var(--tg-theme-destructive-text-color, #dc3545);
}

.auth-status--loading .auth-status__title {
  color: var(--tg-theme-button-color, #007bff);
}

.auth-status__message {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
  color: var(--tg-theme-hint-color, #6c757d);
}

.auth-status__attempts {
  font-weight: 500;
  color: var(--tg-theme-accent-text-color, #007bff);
}

.auth-status__actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.auth-status__button {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.auth-status__button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.auth-status__button--primary {
  background: var(--tg-theme-button-color, #007bff);
  color: var(--tg-theme-button-text-color, #ffffff);
}

.auth-status__button--primary:hover:not(:disabled) {
  background: var(--tg-theme-button-color, #0056b3);
  transform: translateY(-1px);
}

.auth-status__button--secondary {
  background: transparent;
  color: var(--tg-theme-text-color, #000000);
  border: 1px solid var(--tg-theme-hint-color, #e0e0e0);
}

.auth-status__button--secondary:hover:not(:disabled) {
  background: var(--tg-theme-secondary-bg-color, #f8f9fa);
  transform: translateY(-1px);
}

/* Loading animation */
.auth-status--loading .auth-status__title::after {
  content: '';
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-left: 8px;
  border: 2px solid var(--tg-theme-button-color, #007bff);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: auth-status-spin 1s linear infinite;
}

@keyframes auth-status-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .auth-status {
    background: var(--tg-theme-bg-color, #1a1a1a);
    border-color: var(--tg-theme-hint-color, #404040);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
  
  .auth-status--loading {
    background: var(--tg-theme-secondary-bg-color, #2a2a2a);
  }
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .auth-status {
    width: 95%;
    padding: 20px;
  }
  
  .auth-status__actions {
    flex-direction: column;
  }
  
  .auth-status__button {
    width: 100%;
  }
}