# AtlasVPN Frontend Migration Plan: Core Functionality & Game Features
Based on the analysis of the existing React frontend and the new Astro setup, here's a comprehensive task-based migration plan focusing on core functionality and game features.

## Migration Overview
Current Status : ~15% complete (basic infrastructure) Focus : Core functionality, game mechanics, and main pages over UI components Target : Full SPA game experience with dashboard, wallet, friends, tasks, and verse

## Phase 1: Core Infrastructure & State Management (Priority: HIGH)
### Task 1.1: State Management Migration
- Migrate Zustand stores from `uiStore.ts`
  - Theme management
  - Welcome message states
  - Background visibility
  - Scroll position tracking
  - App lifecycle management
- Migrate chat store from `chatStore.ts`
  - WebSocket connection management
  - One-time token fetching
  - Connection state handling
  - Reconnection logic
- Create additional stores for:
  - User data management
  - VPN subscription states
  - Task progress tracking
  - Referral system
  - Card/NFT management
### Task 1.2: Telegram Integration
- Migrate Telegram utilities from `telegram.ts`
  - WebApp initialization
  - Theme change handling
  - Visibility management
  - Activation/deactivation events
  - State persistence
- Update BaseLayout to properly integrate Telegram WebApp
- Test Telegram WebApp functionality in Astro environment
### Task 1.3: API Client Enhancement
- Extend existing API client in `api.ts`
  - Add missing endpoints for tasks, cards, referrals
  - Implement proper error handling
  - Add retry mechanisms
  - CSRF token management
- Create API hooks for all major features
  - Task management hooks
  - Card/NFT hooks
  - Referral system hooks
  - User profile hooks
## Phase 2: Core Pages & Navigation (Priority: HIGH)
### Task 2.1: Dashboard Layout Migration
- Migrate DashboardLayout from `DashboardLayout.tsx`
  - Navigation system
  - Background effects
  - Tab management
  - Loading screens
- Create Astro/React hybrid components for:
  - Bottom navigation
  - Header component
  - Dashboard background effects
  - Welcome back splash
### Task 2.2: Main Dashboard Pages
- Home Tab Migration
  - User stats display
  - Quick actions
  - Recent activity
  - VPN status integration
- Wallet Tab Migration
  - Balance display
  - Transaction history
  - Payment methods
  - Withdrawal functionality
- Premium Tab Migration
  - Subscription status
  - Plan comparison
  - Upgrade flows
  - Feature access control
## Phase 3: Game Features & Earning System (Priority: HIGH)
### Task 3.1: Task System Migration
- Migrate TaskTab from `TaskTab.tsx`
  - Daily check-in system
  - Task filtering and display
  - Reward claiming
  - Task verification
  - Progress tracking
- Create task components :
  - Daily streak cards
  - Referral task cards
  - Social media tasks
  - VPN usage tasks
### Task 3.2: Earning & Card System
- Migrate EarnTab from `EarnTab.tsx`
  - Card management interface
  - Rarity system
  - Profit calculations
  - Card detail slides
- Implement card features :
  - Card collection display
  - Upgrade mechanisms
  - Trading functionality
  - Rarity-based rewards
### Task 3.3: Friends & Referral System
- Create Friends Tab
  - Referral link generation
  - Friend list display
  - Referral rewards tracking
  - Social sharing integration
- Implement referral mechanics :
  - Multi-level referral system
  - Bonus calculations
  - Achievement tracking
## Phase 4: 3D Verse Experience (Priority: MEDIUM)
### Task 4.1: 3D Infrastructure Setup
- Install 3D dependencies :
  ```
  npm install @react-three/fiber @react-three/
  drei three
  ```
- Create 3D component structure in Astro
- Set up lazy loading for heavy 3D components
### Task 4.2: Verse Page Migration
- Migrate VersePage from `VersePage.tsx`
  - 3D map interface
  - Land plot visualization
  - Network representation
- Migrate VerseLayout from `VerseLayout.tsx`
  - 3D scene setup
  - Interactive elements
  - Performance optimization
## Phase 5: Chat System (Priority: MEDIUM)
### Task 5.1: Chat Infrastructure
- Migrate ChatPanel from `ChatPanel.tsx`
  - Message display
  - User input handling
  - Scroll management
  - User profiles
- Implement WebSocket integration
  - Real-time messaging
  - Connection management
  - Message persistence
## Phase 6: Essential UI Components (Priority: LOW)
### Task 6.1: Core UI Components
- Create essential components only :
  - Loading spinners
  - Modal dialogs
  - Form inputs (basic)
  - Navigation elements
- Skip decorative UI (buttons, cards, etc.) for now
- Focus on functional components that support game mechanics
## Phase 7: Testing & Optimization (Priority: ONGOING)
### Task 7.1: Testing Strategy
- Extend existing tests in `connection.test.ts`
- Add component tests for migrated features
- Integration testing for game mechanics
- Telegram WebApp testing
### Task 7.2: Performance Optimization
- Implement code splitting for heavy components
- Optimize 3D rendering performance
- Bundle size optimization
- Loading state management
## Implementation Timeline
Week 1-2 : Phase 1 (Core Infrastructure) Week 3-4 : Phase 2 (Core Pages) Week 5-6 : Phase 3 (Game Features) Week 7-8 : Phase 4 (3D Verse) Week 9-10 : Phase 5 (Chat System) Week 11-12 : Phase 6 & 7 (UI & Testing)

## Success Criteria
- Full SPA functionality in Astro
- All game mechanics working
- Telegram WebApp integration complete
- 3D Verse experience functional
- Chat system operational
- Performance meets or exceeds old frontend
- All tests passing
## Notes
- Prioritize functionality over aesthetics during migration
- Test each phase thoroughly before moving to next
- Maintain backward compatibility with existing API
- Document any breaking changes or new requirements
- Focus on core user journey first (onboarding → dashboard → tasks → earning)
This plan focuses on migrating the core game functionality and main features while deferring UI component creation until the essential features are working.