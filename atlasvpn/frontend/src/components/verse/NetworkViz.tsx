/**
 * @file NetworkViz.tsx
 * @description R3F component to visualize connections between land plots with curves and animated flow.
 */

// @ts-nocheck
import React, { useMemo, useRef, useEffect } from 'react';
import * as THREE from 'three';
import { useFrame, extend } from '@react-three/fiber';
import { LandPlot } from '../../utils/mockVerseData';

// Extend Three.js objects for JSX usage
extend(THREE);

// Colors and constants
const baseLineColor = '#c4b5fd'; // Light purple
const flowGlowColor = '#ff9500'; // Orange
const NUM_DIAMONDS_PER_LINE = 2; // Fixed: 2 diamonds per line
const MOVE_SPEED = 0.03; // Consistent moderate speed

interface NetworkVizProps {
  lands: Array<LandPlot & { position: [number, number, number]; size: [number, number] }>;
  hoveredLandId: string | null;
  isPerformanceMode: boolean;
  isAppVisible: boolean;
}

function NetworkViz({
  lands,
  hoveredLandId,
  isPerformanceMode,
  isAppVisible,
}: NetworkVizProps): React.ReactElement {
  const meshRef = useRef<THREE.InstancedMesh>(null!);
  const dummy = useRef(new THREE.Object3D()).current;
  const wasAnimationPaused = useRef(false);
  const lastLogTime = useRef(Date.now());
  const timeRef = useRef(0);

  // Fixed positions for the diamonds (1/3 and 2/3 of the way)
  const diamondPositions = useMemo(() => {
    return Array.from(
      { length: NUM_DIAMONDS_PER_LINE },
      (_, i) => (i + 1) / (NUM_DIAMONDS_PER_LINE + 1)
    );
  }, []);

  // Connect each land to its nearest neighbors
  const lineData = useMemo(() => {
    const maxNeighbors = isPerformanceMode ? 1 : 2;
    const segments: { curve: THREE.CatmullRomCurve3; id: string }[] = [];

    // Use fewer lands in performance mode
    const landSubset = isPerformanceMode
      ? lands.filter((_, i) => i % 4 === 0) // 25% of lands
      : lands.filter((_, i) => i % 2 === 0); // 50% of lands

    for (const land of landSubset) {
      const startVec = new THREE.Vector3(...land.position);

      // Find nearest neighbors
      const neighbors = [...lands]
        .filter(l => l.id !== land.id)
        .map(l => ({
          land: l,
          dist: startVec.distanceTo(new THREE.Vector3(...l.position)),
        }))
        .sort((a, b) => a.dist - b.dist)
        .slice(0, maxNeighbors)
        .map(n => n.land);

      for (const neighbor of neighbors) {
        const endVec = new THREE.Vector3(...neighbor.position);
        const connectionId = `${land.id}-to-${neighbor.id}`;

        // Avoid duplicate connections
        const reverseId = `${neighbor.id}-to-${land.id}`;
        if (segments.some(s => s.id === reverseId)) continue;

        // Create a curved path
        const midPoint = startVec.clone().lerp(endVec, 0.5);
        midPoint.normalize();
        const planetRadius = startVec.length();
        midPoint.multiplyScalar(planetRadius);

        // Add some curvature
        const radialOffset = startVec.distanceTo(endVec) * 0.1;
        const ctrlPoint = midPoint.clone().multiplyScalar(1 + radialOffset / planetRadius);

        const curve = new THREE.CatmullRomCurve3([startVec, ctrlPoint, endVec]);
        curve.curveType = 'catmullrom';
        curve.tension = 0.5;

        segments.push({ curve, id: connectionId });
      }
    }
    return segments;
  }, [lands, isPerformanceMode]);

  // Create geometry for all lines
  const linesGeometry = useMemo(() => {
    const pointsPerCurve = isPerformanceMode ? 5 : 10;

    const points: THREE.Vector3[] = [];
    lineData.forEach(({ curve }) => {
      const curvePoints = curve.getPoints(pointsPerCurve);
      for (let i = 0; i < curvePoints.length - 1; i++) {
        points.push(curvePoints[i], curvePoints[i + 1]);
      }
    });
    return new THREE.BufferGeometry().setFromPoints(points);
  }, [lineData, isPerformanceMode]);

  // Calculate total diamonds
  const totalDiamonds = useMemo(() => lineData.length * NUM_DIAMONDS_PER_LINE, [lineData]);

  // Diamond shape geometry (slightly smaller)
  const diamondGeometry = useMemo(() => new THREE.TetrahedronGeometry(0.08, 0), []);

  // Create diamond positions
  const diamondData = useMemo(() => {
    if (isPerformanceMode) return [];

    const data: Array<{
      startPos: number;
      curve: THREE.CatmullRomCurve3;
    }> = [];

    lineData.forEach(({ curve }) => {
      diamondPositions.forEach(pos => {
        data.push({
          startPos: pos,
          curve,
        });
      });
    });

    return data;
  }, [lineData, diamondPositions, isPerformanceMode]);

  // Initialize InstancedMesh
  useEffect(() => {
    if (meshRef.current) {
      meshRef.current.count = totalDiamonds;
    }
  }, [totalDiamonds]);

  // Main animation frame
  useFrame((_, delta) => {
    // Skip if hidden or performance mode
    if (isPerformanceMode || !isAppVisible) {
      if (!wasAnimationPaused.current) {
        console.log(
          `[Task 2.1] NetworkViz animation paused (performanceMode: ${isPerformanceMode}, appVisible: ${isAppVisible})`
        );
        wasAnimationPaused.current = true;
      }
      return;
    }

    // Log resuming
    if (wasAnimationPaused.current) {
      console.log(
        `[Task 2.1] NetworkViz animation resumed (performanceMode: ${isPerformanceMode}, appVisible: ${isAppVisible})`
      );
      wasAnimationPaused.current = false;
    }

    // Periodic logging
    const now = Date.now();
    if (now - lastLogTime.current > 5000) {
      console.log(`[Task 2.1] NetworkViz animation running (${diamondData.length} diamonds)`);
      lastLogTime.current = now;
    }

    if (!meshRef.current) return;

    // Update time - use fixed increment to ensure consistent speed
    timeRef.current += delta * MOVE_SPEED;

    let instanceIdx = 0;

    // Animate diamonds with simple linear movement
    diamondData.forEach(diamond => {
      // Calculate position along curve (0-1), wrapping around
      const position = (timeRef.current + diamond.startPos) % 1;

      // Get point and orientation on curve
      const point = diamond.curve.getPointAt(position);
      const tangent = diamond.curve.getTangentAt(position).normalize();

      // Position and orient the diamond
      dummy.position.copy(point);
      dummy.lookAt(point.clone().add(tangent));
      dummy.rotateZ(timeRef.current * 2); // Slow rotation around axis

      dummy.updateMatrix();
      meshRef.current.setMatrixAt(instanceIdx++, dummy.matrix);
    });

    if (instanceIdx > 0) {
      meshRef.current.instanceMatrix.needsUpdate = true;
    }
  });

  return (
    <group name="network-viz-group">
      {/* Network lines */}
      <lineSegments geometry={linesGeometry}>
        <lineBasicMaterial color={baseLineColor} linewidth={2} opacity={0.4} transparent />
      </lineSegments>

      {/* Animated diamonds */}
      <instancedMesh
        ref={meshRef}
        args={[diamondGeometry, undefined, totalDiamonds]}
        frustumCulled={false}
        visible={!isPerformanceMode}
      >
        <meshStandardMaterial
          color={flowGlowColor}
          emissive={flowGlowColor}
          emissiveIntensity={1.2}
          transparent
          opacity={0.8}
          roughness={0.5}
          metalness={0.4}
        />
      </instancedMesh>
    </group>
  );
}

export default React.memo(NetworkViz);
