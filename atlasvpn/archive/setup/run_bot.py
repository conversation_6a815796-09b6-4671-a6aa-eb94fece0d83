#!/usr/bin/env python3
from telegram_bot import TelegramBot
import logging
import signal
import sys
import asyncio

# Configure logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# Global bot instance
bot = None

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info("🛑 Received shutdown signal")
    if bot and bot.is_running():
        asyncio.get_event_loop().run_until_complete(bot.stop())
    sys.exit(0)

async def main():
    global bot
    try:
        bot = TelegramBot()
        logger.info("✅ Bot initialized successfully")
        await bot.start()  # This will run until stopped
    except KeyboardInterrupt:
        logger.info("🛑 Keyboard interrupt received")
    except Exception as e:
        logger.error(f"❌ Error: {str(e)}", exc_info=True)
    finally:
        if bot and bot.is_running():
            await bot.stop()

if __name__ == "__main__":
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Run the bot in the event loop
    asyncio.run(main())