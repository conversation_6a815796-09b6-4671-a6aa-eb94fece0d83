/**
 * Telegram Mini App Optimization Plugin
 * Handles performance optimizations, offline support, and Telegram-specific features
 */

export default defineNuxtPlugin(() => {
  // Only run on client side
  if (process.server) return

  const { $router } = useNuxtApp()

  // Check if running in Telegram environment
  const isInTelegram = () => {
    return !!(window.Telegram?.WebApp && window.Telegram.WebApp.initData)
  }

  // Show "Open in Telegram" message for web users
  const showTelegramRedirect = () => {
    if (process.env.NODE_ENV === 'production' && !isInTelegram()) {
      document.body.innerHTML = `
        <div style="
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100vh;
          background: #000000;
          color: #ffffff;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          text-align: center;
          padding: 2rem;
        ">
          <div style="
            background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
            padding: 3rem 2rem;
            border-radius: 1rem;
            max-width: 400px;
            box-shadow: 0 0 30px rgba(124, 58, 237, 0.3);
          ">
            <h1 style="margin: 0 0 1rem 0; font-size: 1.5rem; font-weight: 600;">
              🚀 AtlasVPN Game
            </h1>
            <p style="margin: 0 0 2rem 0; opacity: 0.9; line-height: 1.6;">
              This app is designed to work within Telegram Mini Apps.
              Please open it through the Telegram bot for the best experience.
            </p>
            <p style="margin: 0; font-size: 0.9rem; opacity: 0.7;">
              Detected version: ${window.Telegram?.WebApp?.version || '6.0'}
            </p>
          </div>
        </div>
      `
    }
  }

  // Initialize Telegram WebApp optimizations
  const initTelegramOptimizations = () => {
    if (typeof window === 'undefined' || !window.Telegram?.WebApp) {
      console.warn('[TelegramOptimization] Telegram WebApp not available')
      showTelegramRedirect()
      return
    }

    const webapp = window.Telegram.WebApp

    try {
      // Configure WebApp for optimal performance
      webapp.ready()
      webapp.expand()
      webapp.enableClosingConfirmation()

      // Set ultra-deep black theme colors
      webapp.setHeaderColor('#000000') // Pure black
      webapp.setBackgroundColor('#000000') // Pure black

      // Configure viewport for better mobile experience
      if (webapp.viewportHeight) {
        document.documentElement.style.setProperty('--tg-viewport-height', `${webapp.viewportHeight}px`)
      }

      // Handle viewport changes
      webapp.onEvent('viewportChanged', () => {
        if (webapp.viewportHeight) {
          document.documentElement.style.setProperty('--tg-viewport-height', `${webapp.viewportHeight}px`)
        }
      })

      // Handle theme changes
      webapp.onEvent('themeChanged', () => {
        const themeParams = webapp.themeParams
        if (themeParams) {
          // Update CSS custom properties based on Telegram theme
          document.documentElement.style.setProperty('--tg-bg-color', themeParams.bg_color || '#111827')
          document.documentElement.style.setProperty('--tg-text-color', themeParams.text_color || '#ffffff')
          document.documentElement.style.setProperty('--tg-hint-color', themeParams.hint_color || '#6b7280')
          document.documentElement.style.setProperty('--tg-link-color', themeParams.link_color || '#3b82f6')
          document.documentElement.style.setProperty('--tg-button-color', themeParams.button_color || '#3b82f6')
          document.documentElement.style.setProperty('--tg-button-text-color', themeParams.button_text_color || '#ffffff')
        }
      })

      // Optimize for mobile performance
      optimizeForMobile()

      // Setup offline handling
      setupOfflineHandling()

      // Setup performance monitoring
      setupPerformanceMonitoring()

      console.log('[TelegramOptimization] Telegram WebApp optimizations initialized')
    } catch (error) {
      console.error('[TelegramOptimization] Failed to initialize optimizations:', error)
    }
  }

  // Mobile performance optimizations
  const optimizeForMobile = () => {
    // Disable text selection for better mobile UX
    document.body.style.userSelect = 'none'
    document.body.style.webkitUserSelect = 'none'

    // Prevent zoom on input focus
    const viewport = document.querySelector('meta[name="viewport"]')
    if (viewport) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no')
    }

    // Optimize touch events
    document.body.style.touchAction = 'manipulation'

    // Reduce animations on low-end devices
    if (navigator.hardwareConcurrency && navigator.hardwareConcurrency <= 2) {
      document.documentElement.classList.add('reduce-motion')
    }

    // Optimize scrolling
    document.body.style.overscrollBehavior = 'none'
    document.body.style.webkitOverflowScrolling = 'touch'
  }

  // Offline handling setup
  const setupOfflineHandling = () => {
    const updateOnlineStatus = () => {
      const isOnline = navigator.onLine
      document.documentElement.classList.toggle('offline', !isOnline)
      
      // Emit custom event for components to listen to
      window.dispatchEvent(new CustomEvent('connection-change', { 
        detail: { isOnline } 
      }))
    }

    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
    
    // Initial status
    updateOnlineStatus()

    // Service Worker registration for offline support
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then(registration => {
          console.log('[TelegramOptimization] Service Worker registered:', registration)
        })
        .catch(error => {
          console.error('[TelegramOptimization] Service Worker registration failed:', error)
        })
    }
  }

  // Performance monitoring
  const setupPerformanceMonitoring = () => {
    // Monitor Core Web Vitals
    if ('web-vital' in window) {
      // This would integrate with a web vitals library
      console.log('[TelegramOptimization] Performance monitoring enabled')
    }

    // Monitor memory usage
    if ('memory' in performance) {
      const checkMemory = () => {
        const memory = (performance as any).memory
        if (memory.usedJSHeapSize > memory.jsHeapSizeLimit * 0.9) {
          console.warn('[TelegramOptimization] High memory usage detected')
          // Trigger garbage collection if possible
          if ('gc' in window) {
            (window as any).gc()
          }
        }
      }

      // Check memory every 30 seconds
      setInterval(checkMemory, 30000)
    }

    // Monitor long tasks
    if ('PerformanceObserver' in window) {
      try {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) {
              console.warn('[TelegramOptimization] Long task detected:', entry.duration + 'ms')
            }
          }
        })
        observer.observe({ entryTypes: ['longtask'] })
      } catch (error) {
        // PerformanceObserver might not be supported
      }
    }
  }

  // Preload critical resources
  const preloadCriticalResources = () => {
    const criticalResources = [
      '/api/auth/verify-session',
      '/api/user/dashboard/stats',
      '/api/tasks/available'
    ]

    criticalResources.forEach(url => {
      // Preload API endpoints
      fetch(url, { method: 'HEAD' }).catch(() => {
        // Ignore errors, this is just for preloading
      })
    })
  }

  // Setup route-based optimizations
  const setupRouteOptimizations = () => {
    // Preload next likely routes based on current route
    $router.beforeEach((to, from) => {
      const routePreloadMap: Record<string, string[]> = {
        '/': ['/dashboard', '/auth/login'],
        '/dashboard': ['/dashboard/tasks', '/dashboard/cards', '/dashboard/vpn'],
        '/auth/login': ['/dashboard'],
        '/auth/new-user-setup': ['/dashboard']
      }

      const preloadRoutes = routePreloadMap[to.path]
      if (preloadRoutes) {
        preloadRoutes.forEach(route => {
          $router.prefetch(route).catch(() => {
            // Ignore prefetch errors
          })
        })
      }
    })
  }

  // Initialize optimizations using Nuxt hooks instead of onMounted
  nuxtApp.hook('app:mounted', () => {
    initTelegramOptimizations()
    preloadCriticalResources()
    setupRouteOptimizations()
  })

  // Provide utilities for components
  return {
    provide: {
      telegramOptimization: {
        isOnline: () => navigator.onLine,
        getViewportHeight: () => window.Telegram?.WebApp?.viewportHeight || window.innerHeight,
        getThemeParams: () => window.Telegram?.WebApp?.themeParams || {},
        triggerHaptic: (type: 'impact' | 'notification' | 'selection' = 'impact') => {
          if (window.Telegram?.WebApp?.HapticFeedback) {
            switch (type) {
              case 'impact':
                window.Telegram.WebApp.HapticFeedback.impactOccurred('medium')
                break
              case 'notification':
                window.Telegram.WebApp.HapticFeedback.notificationOccurred('success')
                break
              case 'selection':
                window.Telegram.WebApp.HapticFeedback.selectionChanged()
                break
            }
          }
        },
        showMainButton: (text: string, callback: () => void) => {
          if (window.Telegram?.WebApp?.MainButton) {
            const button = window.Telegram.WebApp.MainButton
            button.setText(text)
            button.onClick(callback)
            button.show()
          }
        },
        hideMainButton: () => {
          if (window.Telegram?.WebApp?.MainButton) {
            window.Telegram.WebApp.MainButton.hide()
          }
        }
      }
    }
  }
})

// CSS for offline state
if (process.client) {
  const style = document.createElement('style')
  style.textContent = `
    .offline {
      --offline-opacity: 0.7;
    }
    
    .offline * {
      opacity: var(--offline-opacity);
      pointer-events: none;
    }
    
    .offline .offline-indicator {
      opacity: 1;
      pointer-events: auto;
    }
    
    .reduce-motion * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
    
    /* Telegram theme integration - Ultra Deep Black */
    :root {
      --tg-viewport-height: 100vh;
      --tg-bg-color: #000000;
      --tg-text-color: #ffffff;
      --tg-hint-color: #9ca3af;
      --tg-link-color: #7c3aed;
      --tg-button-color: #7c3aed;
      --tg-button-text-color: #ffffff;
    }
    
    /* Mobile optimizations */
    body {
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
      -webkit-user-select: none;
      user-select: none;
    }
    
    input, textarea, select {
      -webkit-user-select: text;
      user-select: text;
    }
  `
  document.head.appendChild(style)
}
