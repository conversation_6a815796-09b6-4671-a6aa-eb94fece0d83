2025-04-02 19:43:13,906 - test_utils - INFO - [test_utils.py:295] - Environment Information:
2025-04-02 19:43:13,907 - test_utils - INFO - [test_utils.py:296] - Python Version: 3.10.12 (main, Jan 17 2025, 14:35:34) [GCC 11.4.0]
2025-04-02 19:43:13,907 - test_utils - INFO - [test_utils.py:297] - OS: posix posix.uname_result(sysname='Linux', nodename='ses', release='5.15.0-112-generic', version='#122-Ubuntu SMP Thu May 23 07:48:21 UTC 2024', machine='x86_64')
2025-04-02 19:43:13,908 - test_utils - INFO - [test_utils.py:298] - Working Directory: /root/project/backend/security_tests
2025-04-02 19:43:13,908 - test_utils - INFO - [test_utils.py:299] - Environment Variables:
2025-04-02 19:43:13,909 - test_utils - INFO - [test_utils.py:304] - SHELL: /bin/bash
2025-04-02 19:43:13,910 - test_utils - INFO - [test_utils.py:304] - TEST_MODE: true
2025-04-02 19:43:13,920 - test_utils - INFO - [test_utils.py:304] - COLORTERM: truecolor
2025-04-02 19:43:13,921 - test_utils - INFO - [test_utils.py:304] - TERM_PROGRAM_VERSION: 0.48.6
2025-04-02 19:43:13,924 - test_utils - INFO - [test_utils.py:304] - ENABLE_CLOUDFLARE_CAPTCHA: false
2025-04-02 19:43:13,925 - test_utils - INFO - [test_utils.py:304] - PWD: /root/project/backend/security_tests
2025-04-02 19:43:13,926 - test_utils - INFO - [test_utils.py:304] - LOGNAME: root
2025-04-02 19:43:13,927 - test_utils - INFO - [test_utils.py:304] - XDG_SESSION_TYPE: tty
2025-04-02 19:43:13,930 - test_utils - INFO - [test_utils.py:304] - MOTD_SHOWN: pam
2025-04-02 19:43:13,934 - test_utils - INFO - [test_utils.py:304] - HOME: /root
2025-04-02 19:43:13,935 - test_utils - INFO - [test_utils.py:304] - LANG: C.UTF-8
2025-04-02 19:43:13,936 - test_utils - INFO - [test_utils.py:304] - LS_COLORS: rs=0:di=01;34:ln=01;36:mh=00:pi=40;33:so=01;35:do=01;35:bd=40;33;01:cd=40;33;01:or=40;31;01:mi=00:su=37;41:sg=30;43:ca=30;41:tw=30;42:ow=34;42:st=37;44:ex=01;32:*.tar=01;31:*.tgz=01;31:*.arc=01;31:*.arj=01;31:*.taz=01;31:*.lha=01;31:*.lz4=01;31:*.lzh=01;31:*.lzma=01;31:*.tlz=01;31:*.txz=01;31:*.tzo=01;31:*.t7z=01;31:*.zip=01;31:*.z=01;31:*.dz=01;31:*.gz=01;31:*.lrz=01;31:*.lz=01;31:*.lzo=01;31:*.xz=01;31:*.zst=01;31:*.tzst=01;31:*.bz2=01;31:*.bz=01;31:*.tbz=01;31:*.tbz2=01;31:*.tz=01;31:*.deb=01;31:*.rpm=01;31:*.jar=01;31:*.war=01;31:*.ear=01;31:*.sar=01;31:*.rar=01;31:*.alz=01;31:*.ace=01;31:*.zoo=01;31:*.cpio=01;31:*.7z=01;31:*.rz=01;31:*.cab=01;31:*.wim=01;31:*.swm=01;31:*.dwm=01;31:*.esd=01;31:*.jpg=01;35:*.jpeg=01;35:*.mjpg=01;35:*.mjpeg=01;35:*.gif=01;35:*.bmp=01;35:*.pbm=01;35:*.pgm=01;35:*.ppm=01;35:*.tga=01;35:*.xbm=01;35:*.xpm=01;35:*.tif=01;35:*.tiff=01;35:*.png=01;35:*.svg=01;35:*.svgz=01;35:*.mng=01;35:*.pcx=01;35:*.mov=01;35:*.mpg=01;35:*.mpeg=01;35:*.m2v=01;35:*.mkv=01;35:*.webm=01;35:*.webp=01;35:*.ogm=01;35:*.mp4=01;35:*.m4v=01;35:*.mp4v=01;35:*.vob=01;35:*.qt=01;35:*.nuv=01;35:*.wmv=01;35:*.asf=01;35:*.rm=01;35:*.rmvb=01;35:*.flc=01;35:*.avi=01;35:*.fli=01;35:*.flv=01;35:*.gl=01;35:*.dl=01;35:*.xcf=01;35:*.xwd=01;35:*.yuv=01;35:*.cgm=01;35:*.emf=01;35:*.ogv=01;35:*.ogx=01;35:*.aac=00;36:*.au=00;36:*.flac=00;36:*.m4a=00;36:*.mid=00;36:*.midi=00;36:*.mka=00;36:*.mp3=00;36:*.mpc=00;36:*.ogg=00;36:*.ra=00;36:*.wav=00;36:*.oga=00;36:*.opus=00;36:*.spx=00;36:*.xspf=00;36:
2025-04-02 19:43:13,941 - test_utils - INFO - [test_utils.py:304] - VIRTUAL_ENV: /root/project/backend/venv
2025-04-02 19:43:13,948 - test_utils - INFO - [test_utils.py:304] - SSL_CERT_DIR: /usr/lib/ssl/certs
2025-04-02 19:43:13,954 - test_utils - INFO - [test_utils.py:304] - SSH_CONNECTION: ********** 53128 ************ 22
2025-04-02 19:43:13,961 - test_utils - INFO - [test_utils.py:304] - XDG_SESSION_CLASS: user
2025-04-02 19:43:14,465 - test_utils - INFO - [test_utils.py:304] - API_BASE_URL: http://localhost:8000
2025-04-02 19:43:14,466 - test_utils - INFO - [test_utils.py:304] - PYTHONPATH: ./../security_tests/..:
2025-04-02 19:43:14,466 - test_utils - INFO - [test_utils.py:304] - TERM: xterm-256color
2025-04-02 19:43:14,466 - test_utils - INFO - [test_utils.py:304] - USER: root
2025-04-02 19:43:14,466 - test_utils - INFO - [test_utils.py:304] - SHLVL: 1
2025-04-02 19:43:14,467 - test_utils - INFO - [test_utils.py:304] - XDG_SESSION_ID: 87
2025-04-02 19:43:14,467 - test_utils - INFO - [test_utils.py:304] - VIRTUAL_ENV_PROMPT: (venv) 
2025-04-02 19:43:14,467 - test_utils - INFO - [test_utils.py:304] - XDG_RUNTIME_DIR: /run/user/0
2025-04-02 19:43:14,467 - test_utils - INFO - [test_utils.py:304] - SSL_CERT_FILE: /usr/lib/ssl/certs/ca-certificates.crt
2025-04-02 19:43:14,467 - test_utils - INFO - [test_utils.py:304] - PS1: (venv) 
2025-04-02 19:43:14,467 - test_utils - INFO - [test_utils.py:304] - SSH_CLIENT: ********** 53128 22
2025-04-02 19:43:14,468 - test_utils - INFO - [test_utils.py:304] - LC_ALL: C.UTF-8
2025-04-02 19:43:14,468 - test_utils - INFO - [test_utils.py:304] - BROWSER: /root/.cursor-server/cli/servers/Stable-1649e229afdef8fd1d18ea173f063563f1e722e0/server/bin/helpers/browser.sh
2025-04-02 19:43:14,468 - test_utils - INFO - [test_utils.py:304] - PATH: /root/project/backend/venv/bin:/root/project/backend/venv/bin:/root/.cursor-server/cli/servers/Stable-1649e229afdef8fd1d18ea173f063563f1e722e0/server/bin/remote-cli:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin
2025-04-02 19:43:14,468 - test_utils - INFO - [test_utils.py:304] - DBUS_SESSION_BUS_ADDRESS: unix:path=/run/user/0/bus
2025-04-02 19:43:14,468 - test_utils - INFO - [test_utils.py:304] - OLDPWD: /root/project/backend/scripts
2025-04-02 19:43:14,488 - test_utils - INFO - [test_utils.py:304] - TERM_PROGRAM: vscode
2025-04-02 19:43:14,489 - test_utils - INFO - [test_utils.py:304] - VSCODE_IPC_HOOK_CLI: /run/user/0/vscode-ipc-86c0d3c4-fc5c-48fd-8b8f-3a64623cc5eb.sock
2025-04-02 19:43:14,489 - test_utils - INFO - [test_utils.py:304] - _: /root/project/backend/venv/bin/python3
2025-04-02 19:43:14,489 - test_utils - INFO - [test_utils.py:110] - Making GET request to http://localhost:8000/api/auth/token
2025-04-02 19:43:14,573 - test_utils - INFO - [test_utils.py:60] - Response Status: 404
2025-04-02 19:43:14,573 - test_utils - INFO - [test_utils.py:61] - Response Headers: {'date': 'Wed, 02 Apr 2025 19:43:13 GMT', 'server': 'uvicorn', 'content-length': '22', 'content-type': 'application/json', 'x-bot-detection': 'false', 'x-bot-confidence': '0.00', 'content-security-policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none'; form-action 'self'; base-uri 'self'", 'x-xss-protection': '1; mode=block', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'referrer-policy': 'strict-origin-when-cross-origin', 'permissions-policy': 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()', 'cache-control': 'no-store, max-age=0', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-ratelimit-limit': '60', 'x-ratelimit-remaining': '59', 'x-ratelimit-reset': '60', 'x-process-time': '0.028515338897705078', 'set-cookie': 'csrf_token=116926d35c00fb8064fa75c6194d31fd5fbd5194133130999bf961253d97ee95; HttpOnly; Path=/; SameSite=lax; Secure'}
2025-04-02 19:43:14,574 - test_utils - INFO - [test_utils.py:63] - Response Body: {'detail': 'Not Found'}
2025-04-02 19:43:14,574 - test_utils - INFO - [test_utils.py:229] - Verifying security headers...
2025-04-02 19:43:14,574 - test_utils - INFO - [test_utils.py:230] - Required headers: ['Content-Security-Policy', 'X-XSS-Protection', 'X-Content-Type-Options', 'X-Frame-Options', 'Referrer-Policy', 'Permissions-Policy', 'Cache-Control', 'Strict-Transport-Security']
2025-04-02 19:43:14,574 - test_utils - INFO - [test_utils.py:231] - Received headers: ['date', 'server', 'content-length', 'content-type', 'x-bot-detection', 'x-bot-confidence', 'content-security-policy', 'x-xss-protection', 'x-content-type-options', 'x-frame-options', 'referrer-policy', 'permissions-policy', 'cache-control', 'strict-transport-security', 'x-ratelimit-limit', 'x-ratelimit-remaining', 'x-ratelimit-reset', 'x-process-time', 'set-cookie']
2025-04-02 19:43:14,574 - test_utils - INFO - [test_utils.py:273] - All required security headers are present and valid
2025-04-02 19:43:14,575 - test_utils - INFO - [test_utils.py:287] - Test 'Security Headers - /auth/token' passed successfully
2025-04-02 19:43:14,577 - test_utils - INFO - [test_utils.py:110] - Making GET request to http://localhost:8000/api/auth/admin/login
2025-04-02 19:43:14,622 - test_utils - INFO - [test_utils.py:60] - Response Status: 404
2025-04-02 19:43:14,622 - test_utils - INFO - [test_utils.py:61] - Response Headers: {'date': 'Wed, 02 Apr 2025 19:43:13 GMT', 'server': 'uvicorn', 'content-length': '22', 'content-type': 'application/json', 'x-bot-detection': 'false', 'x-bot-confidence': '0.00', 'content-security-policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none'; form-action 'self'; base-uri 'self'", 'x-xss-protection': '1; mode=block', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'referrer-policy': 'strict-origin-when-cross-origin', 'permissions-policy': 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()', 'cache-control': 'no-store, max-age=0', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-ratelimit-limit': '60', 'x-ratelimit-remaining': '59', 'x-ratelimit-reset': '60', 'x-process-time': '0.009028196334838867', 'set-cookie': 'csrf_token=64522646e2356078dd7fecce254f448be91a002f0b1569210b7ebaa6f415fa73; HttpOnly; Path=/; SameSite=lax; Secure'}
2025-04-02 19:43:14,628 - test_utils - INFO - [test_utils.py:63] - Response Body: {'detail': 'Not Found'}
2025-04-02 19:43:14,628 - test_utils - INFO - [test_utils.py:229] - Verifying security headers...
2025-04-02 19:43:14,628 - test_utils - INFO - [test_utils.py:230] - Required headers: ['Content-Security-Policy', 'X-XSS-Protection', 'X-Content-Type-Options', 'X-Frame-Options', 'Referrer-Policy', 'Permissions-Policy', 'Cache-Control', 'Strict-Transport-Security']
2025-04-02 19:43:14,629 - test_utils - INFO - [test_utils.py:231] - Received headers: ['date', 'server', 'content-length', 'content-type', 'x-bot-detection', 'x-bot-confidence', 'content-security-policy', 'x-xss-protection', 'x-content-type-options', 'x-frame-options', 'referrer-policy', 'permissions-policy', 'cache-control', 'strict-transport-security', 'x-ratelimit-limit', 'x-ratelimit-remaining', 'x-ratelimit-reset', 'x-process-time', 'set-cookie']
2025-04-02 19:43:14,629 - test_utils - INFO - [test_utils.py:273] - All required security headers are present and valid
2025-04-02 19:43:14,629 - test_utils - INFO - [test_utils.py:287] - Test 'Security Headers - /auth/admin/login' passed successfully
2025-04-02 19:43:14,629 - test_utils - INFO - [test_utils.py:110] - Making GET request to http://localhost:8000/api/auth/register
2025-04-02 19:43:14,666 - test_utils - INFO - [test_utils.py:60] - Response Status: 404
2025-04-02 19:43:14,666 - test_utils - INFO - [test_utils.py:61] - Response Headers: {'date': 'Wed, 02 Apr 2025 19:43:13 GMT', 'server': 'uvicorn', 'content-length': '22', 'content-type': 'application/json', 'x-bot-detection': 'false', 'x-bot-confidence': '0.00', 'content-security-policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none'; form-action 'self'; base-uri 'self'", 'x-xss-protection': '1; mode=block', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'referrer-policy': 'strict-origin-when-cross-origin', 'permissions-policy': 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()', 'cache-control': 'no-store, max-age=0', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-ratelimit-limit': '60', 'x-ratelimit-remaining': '59', 'x-ratelimit-reset': '60', 'x-process-time': '0.011812448501586914', 'set-cookie': 'csrf_token=8b63d8761dfaa6aebe90f1c61585e05ffc08bd2e54f9779bbbd8390ef08c1326; HttpOnly; Path=/; SameSite=lax; Secure'}
2025-04-02 19:43:14,666 - test_utils - INFO - [test_utils.py:63] - Response Body: {'detail': 'Not Found'}
2025-04-02 19:43:14,666 - test_utils - INFO - [test_utils.py:229] - Verifying security headers...
2025-04-02 19:43:14,666 - test_utils - INFO - [test_utils.py:230] - Required headers: ['Content-Security-Policy', 'X-XSS-Protection', 'X-Content-Type-Options', 'X-Frame-Options', 'Referrer-Policy', 'Permissions-Policy', 'Cache-Control', 'Strict-Transport-Security']
2025-04-02 19:43:14,667 - test_utils - INFO - [test_utils.py:231] - Received headers: ['date', 'server', 'content-length', 'content-type', 'x-bot-detection', 'x-bot-confidence', 'content-security-policy', 'x-xss-protection', 'x-content-type-options', 'x-frame-options', 'referrer-policy', 'permissions-policy', 'cache-control', 'strict-transport-security', 'x-ratelimit-limit', 'x-ratelimit-remaining', 'x-ratelimit-reset', 'x-process-time', 'set-cookie']
2025-04-02 19:43:14,667 - test_utils - INFO - [test_utils.py:273] - All required security headers are present and valid
2025-04-02 19:43:14,667 - test_utils - INFO - [test_utils.py:287] - Test 'Security Headers - /auth/register' passed successfully
2025-04-02 19:43:14,667 - test_utils - INFO - [test_utils.py:110] - Making GET request to http://localhost:8000/api/payment/create
2025-04-02 19:43:14,694 - test_utils - INFO - [test_utils.py:60] - Response Status: 404
2025-04-02 19:43:14,694 - test_utils - INFO - [test_utils.py:61] - Response Headers: {'date': 'Wed, 02 Apr 2025 19:43:13 GMT', 'server': 'uvicorn', 'content-length': '22', 'content-type': 'application/json', 'x-bot-detection': 'false', 'x-bot-confidence': '0.00', 'content-security-policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none'; form-action 'self'; base-uri 'self'", 'x-xss-protection': '1; mode=block', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'referrer-policy': 'strict-origin-when-cross-origin', 'permissions-policy': 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()', 'cache-control': 'no-store, max-age=0', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-ratelimit-limit': '60', 'x-ratelimit-remaining': '59', 'x-ratelimit-reset': '60', 'x-process-time': '0.0032176971435546875', 'set-cookie': 'csrf_token=7269642b9cc39c32b8b06d3be2ee746dfc3fb2795cbb7850e0bbddeaa223d15f; HttpOnly; Path=/; SameSite=lax; Secure'}
2025-04-02 19:43:14,694 - test_utils - INFO - [test_utils.py:63] - Response Body: {'detail': 'Not Found'}
2025-04-02 19:43:14,694 - test_utils - INFO - [test_utils.py:229] - Verifying security headers...
2025-04-02 19:43:14,694 - test_utils - INFO - [test_utils.py:230] - Required headers: ['Content-Security-Policy', 'X-XSS-Protection', 'X-Content-Type-Options', 'X-Frame-Options', 'Referrer-Policy', 'Permissions-Policy', 'Cache-Control', 'Strict-Transport-Security']
2025-04-02 19:43:14,694 - test_utils - INFO - [test_utils.py:231] - Received headers: ['date', 'server', 'content-length', 'content-type', 'x-bot-detection', 'x-bot-confidence', 'content-security-policy', 'x-xss-protection', 'x-content-type-options', 'x-frame-options', 'referrer-policy', 'permissions-policy', 'cache-control', 'strict-transport-security', 'x-ratelimit-limit', 'x-ratelimit-remaining', 'x-ratelimit-reset', 'x-process-time', 'set-cookie']
2025-04-02 19:43:14,720 - test_utils - INFO - [test_utils.py:273] - All required security headers are present and valid
2025-04-02 19:43:14,720 - test_utils - INFO - [test_utils.py:287] - Test 'Security Headers - /payment/create' passed successfully
2025-04-02 19:43:14,720 - test_utils - INFO - [test_utils.py:110] - Making GET request to http://localhost:8000/api/payment/webhook
2025-04-02 19:43:14,749 - test_utils - INFO - [test_utils.py:60] - Response Status: 403
2025-04-02 19:43:14,749 - test_utils - INFO - [test_utils.py:61] - Response Headers: {'date': 'Wed, 02 Apr 2025 19:43:13 GMT', 'server': 'uvicorn', 'content-length': '26', 'content-type': 'application/json', 'content-security-policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none'; form-action 'self'; base-uri 'self'", 'x-xss-protection': '1; mode=block', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'referrer-policy': 'strict-origin-when-cross-origin', 'permissions-policy': 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()', 'cache-control': 'no-store, max-age=0', 'strict-transport-security': 'max-age=31536000; includeSubDomains'}
2025-04-02 19:43:14,749 - test_utils - INFO - [test_utils.py:63] - Response Body: {'detail': 'Access denied'}
2025-04-02 19:43:14,752 - test_utils - INFO - [test_utils.py:229] - Verifying security headers...
2025-04-02 19:43:14,752 - test_utils - INFO - [test_utils.py:230] - Required headers: ['Content-Security-Policy', 'X-XSS-Protection', 'X-Content-Type-Options', 'X-Frame-Options', 'Referrer-Policy', 'Permissions-Policy', 'Cache-Control', 'Strict-Transport-Security']
2025-04-02 19:43:14,752 - test_utils - INFO - [test_utils.py:231] - Received headers: ['date', 'server', 'content-length', 'content-type', 'content-security-policy', 'x-xss-protection', 'x-content-type-options', 'x-frame-options', 'referrer-policy', 'permissions-policy', 'cache-control', 'strict-transport-security']
2025-04-02 19:43:14,752 - test_utils - INFO - [test_utils.py:273] - All required security headers are present and valid
2025-04-02 19:43:14,753 - test_utils - INFO - [test_utils.py:287] - Test 'Security Headers - /payment/webhook' passed successfully
2025-04-02 19:43:14,756 - test_utils - INFO - [test_utils.py:110] - Making GET request to http://localhost:8000/api/user/profile/update
2025-04-02 19:43:14,826 - test_utils - INFO - [test_utils.py:60] - Response Status: 405
2025-04-02 19:43:14,826 - test_utils - INFO - [test_utils.py:61] - Response Headers: {'date': 'Wed, 02 Apr 2025 19:43:13 GMT', 'server': 'uvicorn', 'allow': 'PUT', 'content-length': '31', 'content-type': 'application/json', 'x-bot-detection': 'false', 'x-bot-confidence': '0.00', 'content-security-policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none'; form-action 'self'; base-uri 'self'", 'x-xss-protection': '1; mode=block', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'referrer-policy': 'strict-origin-when-cross-origin', 'permissions-policy': 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()', 'cache-control': 'no-store, max-age=0', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-ratelimit-limit': '60', 'x-ratelimit-remaining': '59', 'x-ratelimit-reset': '60', 'x-process-time': '0.014903068542480469', 'set-cookie': 'csrf_token=fd125abe0afac50932ccfccfab645bd831e5c522a96adcf2f06aa5ab98aa0106; HttpOnly; Path=/; SameSite=lax; Secure'}
2025-04-02 19:43:14,826 - test_utils - INFO - [test_utils.py:63] - Response Body: {'detail': 'Method Not Allowed'}
2025-04-02 19:43:14,826 - test_utils - INFO - [test_utils.py:229] - Verifying security headers...
2025-04-02 19:43:14,826 - test_utils - INFO - [test_utils.py:230] - Required headers: ['Content-Security-Policy', 'X-XSS-Protection', 'X-Content-Type-Options', 'X-Frame-Options', 'Referrer-Policy', 'Permissions-Policy', 'Cache-Control', 'Strict-Transport-Security']
2025-04-02 19:43:14,826 - test_utils - INFO - [test_utils.py:231] - Received headers: ['date', 'server', 'allow', 'content-length', 'content-type', 'x-bot-detection', 'x-bot-confidence', 'content-security-policy', 'x-xss-protection', 'x-content-type-options', 'x-frame-options', 'referrer-policy', 'permissions-policy', 'cache-control', 'strict-transport-security', 'x-ratelimit-limit', 'x-ratelimit-remaining', 'x-ratelimit-reset', 'x-process-time', 'set-cookie']
2025-04-02 19:43:14,832 - test_utils - INFO - [test_utils.py:273] - All required security headers are present and valid
2025-04-02 19:43:14,832 - test_utils - INFO - [test_utils.py:287] - Test 'Security Headers - /user/profile/update' passed successfully
2025-04-02 19:43:14,832 - test_utils - INFO - [test_utils.py:110] - Making GET request to http://localhost:8000/api/user/transactions
2025-04-02 19:43:14,902 - test_utils - INFO - [test_utils.py:60] - Response Status: 401
2025-04-02 19:43:14,902 - test_utils - INFO - [test_utils.py:61] - Response Headers: {'date': 'Wed, 02 Apr 2025 19:43:13 GMT', 'server': 'uvicorn', 'content-length': '30', 'content-type': 'application/json', 'x-bot-detection': 'false', 'x-bot-confidence': '0.00', 'content-security-policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none'; form-action 'self'; base-uri 'self'", 'x-xss-protection': '1; mode=block', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'referrer-policy': 'strict-origin-when-cross-origin', 'permissions-policy': 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()', 'cache-control': 'no-store, max-age=0', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-ratelimit-limit': '60', 'x-ratelimit-remaining': '59', 'x-ratelimit-reset': '60', 'x-process-time': '0.05988025665283203'}
2025-04-02 19:43:14,902 - test_utils - INFO - [test_utils.py:63] - Response Body: {'detail': 'Not authenticated'}
2025-04-02 19:43:14,903 - test_utils - INFO - [test_utils.py:229] - Verifying security headers...
2025-04-02 19:43:14,903 - test_utils - INFO - [test_utils.py:230] - Required headers: ['Content-Security-Policy', 'X-XSS-Protection', 'X-Content-Type-Options', 'X-Frame-Options', 'Referrer-Policy', 'Permissions-Policy', 'Cache-Control', 'Strict-Transport-Security']
2025-04-02 19:43:14,908 - test_utils - INFO - [test_utils.py:231] - Received headers: ['date', 'server', 'content-length', 'content-type', 'x-bot-detection', 'x-bot-confidence', 'content-security-policy', 'x-xss-protection', 'x-content-type-options', 'x-frame-options', 'referrer-policy', 'permissions-policy', 'cache-control', 'strict-transport-security', 'x-ratelimit-limit', 'x-ratelimit-remaining', 'x-ratelimit-reset', 'x-process-time']
2025-04-02 19:43:14,908 - test_utils - INFO - [test_utils.py:273] - All required security headers are present and valid
2025-04-02 19:43:14,908 - test_utils - INFO - [test_utils.py:287] - Test 'Security Headers - /user/transactions' passed successfully
2025-04-02 19:43:14,909 - test_utils - INFO - [test_utils.py:110] - Making GET request to http://localhost:8000/api/admin/*
2025-04-02 19:43:14,923 - test_utils - INFO - [test_utils.py:60] - Response Status: 404
2025-04-02 19:43:14,923 - test_utils - INFO - [test_utils.py:61] - Response Headers: {'date': 'Wed, 02 Apr 2025 19:43:13 GMT', 'server': 'uvicorn', 'content-length': '22', 'content-type': 'application/json', 'x-bot-detection': 'false', 'x-bot-confidence': '0.00', 'content-security-policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'none'; form-action 'self'; base-uri 'self'", 'x-xss-protection': '1; mode=block', 'x-content-type-options': 'nosniff', 'x-frame-options': 'DENY', 'referrer-policy': 'strict-origin-when-cross-origin', 'permissions-policy': 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()', 'cache-control': 'no-store, max-age=0', 'strict-transport-security': 'max-age=31536000; includeSubDomains', 'x-ratelimit-limit': '60', 'x-ratelimit-remaining': '59', 'x-ratelimit-reset': '60', 'x-process-time': '0.0044193267822265625'}
2025-04-02 19:43:14,924 - test_utils - INFO - [test_utils.py:63] - Response Body: {'detail': 'Not Found'}
2025-04-02 19:43:14,924 - test_utils - INFO - [test_utils.py:229] - Verifying security headers...
2025-04-02 19:43:14,924 - test_utils - INFO - [test_utils.py:230] - Required headers: ['Content-Security-Policy', 'X-XSS-Protection', 'X-Content-Type-Options', 'X-Frame-Options', 'Referrer-Policy', 'Permissions-Policy', 'Cache-Control', 'Strict-Transport-Security']
2025-04-02 19:43:14,924 - test_utils - INFO - [test_utils.py:231] - Received headers: ['date', 'server', 'content-length', 'content-type', 'x-bot-detection', 'x-bot-confidence', 'content-security-policy', 'x-xss-protection', 'x-content-type-options', 'x-frame-options', 'referrer-policy', 'permissions-policy', 'cache-control', 'strict-transport-security', 'x-ratelimit-limit', 'x-ratelimit-remaining', 'x-ratelimit-reset', 'x-process-time']
2025-04-02 19:43:14,935 - test_utils - INFO - [test_utils.py:273] - All required security headers are present and valid
2025-04-02 19:43:14,936 - test_utils - INFO - [test_utils.py:287] - Test 'Security Headers - /admin/*' passed successfully
