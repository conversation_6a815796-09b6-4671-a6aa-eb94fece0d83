<template>
  <div class="p-6 space-y-6">
    <h2 class="text-2xl font-bold">Modern Data Fetching Patterns Demo</h2>
    
    <!-- User Authentication Example -->
    <div class="border rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-3">1. Modern Authentication with useFetch</h3>
      <div v-if="auth.isLoading.value" class="text-blue-600">Loading user...</div>
      <div v-else-if="auth.user.value" class="text-green-600">
        Welcome, {{ auth.user.value.username }}!
      </div>
      <div v-else class="text-gray-600">Not authenticated</div>
      <button 
        @click="auth.refresh()" 
        class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Refresh User Data
      </button>
    </div>

    <!-- Dashboard Stats Example -->
    <div class="border rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-3">2. Dashboard Stats with Caching</h3>
      <div v-if="dashboard.isLoadingStats.value" class="text-blue-600">Loading stats...</div>
      <div v-else-if="dashboard.stats.value" class="space-y-2">
        <p>Balance: {{ dashboard.formattedBalance.value }}</p>
        <p>Active Subscriptions: {{ dashboard.stats.value.active_subscriptions }}</p>
        <p>Data Usage: {{ dashboard.dataUsagePercentage.value.toFixed(1) }}%</p>
      </div>
      <div v-if="dashboard.statsError.value" class="text-red-600">
        Error: {{ dashboard.statsError.value }}
      </div>
      <button 
        @click="dashboard.refreshAll()" 
        class="mt-2 px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
      >
        Refresh Dashboard
      </button>
    </div>

    <!-- Tasks Example with Optimistic Updates -->
    <div class="border rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-3">3. Tasks with Optimistic Updates</h3>
      <div v-if="tasks.isLoading.value" class="text-blue-600">Loading tasks...</div>
      <div v-else-if="tasks.tasks.value?.length" class="space-y-2">
        <div 
          v-for="task in tasks.tasks.value" 
          :key="task.id"
          class="flex justify-between items-center p-2 border rounded"
        >
          <span>{{ task.title }}</span>
          <button 
            @click="handleCompleteTask(task.id)"
            :disabled="task.status === 'completed'"
            class="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
          >
            {{ task.status === 'completed' ? 'Completed' : 'Complete' }}
          </button>
        </div>
      </div>
      <div v-else class="text-gray-600">No tasks available</div>
    </div>

    <!-- VPN Packages with Reactive Queries -->
    <div class="border rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-3">4. VPN Packages with Reactive Filtering</h3>
      
      <!-- Filters -->
      <div class="mb-4 space-y-2">
        <input 
          v-model="vpn.packagesQuery.category" 
          placeholder="Filter by category"
          class="px-3 py-2 border rounded w-full"
        >
        <div class="flex space-x-2">
          <input 
            v-model.number="vpn.packagesQuery.minPrice" 
            type="number" 
            placeholder="Min Price"
            class="px-3 py-2 border rounded flex-1"
          >
          <input 
            v-model.number="vpn.packagesQuery.maxPrice" 
            type="number" 
            placeholder="Max Price"
            class="px-3 py-2 border rounded flex-1"
          >
        </div>
      </div>

      <div v-if="vpn.isLoading.value" class="text-blue-600">Loading packages...</div>
      <div v-else-if="vpn.packages.value?.length" class="space-y-2">
        <div 
          v-for="pkg in vpn.packages.value" 
          :key="pkg.id"
          class="flex justify-between items-center p-2 border rounded"
        >
          <div>
            <span class="font-medium">{{ pkg.name }}</span>
            <span class="text-gray-600 ml-2">${{ pkg.price }}</span>
          </div>
          <button 
            @click="handlePurchase(pkg.id)"
            class="px-3 py-1 bg-orange-500 text-white rounded hover:bg-orange-600"
          >
            Purchase
          </button>
        </div>
      </div>
      <div v-else class="text-gray-600">No packages found</div>
    </div>

    <!-- Custom Fetch Example -->
    <div class="border rounded-lg p-4">
      <h3 class="text-lg font-semibold mb-3">5. Custom Endpoint with useModernFetch</h3>
      <div v-if="customData.pending.value" class="text-blue-600">Loading custom data...</div>
      <div v-else-if="customData.data.value" class="text-green-600">
        Custom data loaded: {{ JSON.stringify(customData.data.value, null, 2) }}
      </div>
      <div v-if="customData.error.value" class="text-red-600">
        Error: {{ customData.error.value }}
      </div>
      <button 
        @click="customData.refresh()" 
        class="mt-2 px-4 py-2 bg-indigo-500 text-white rounded hover:bg-indigo-600"
      >
        Refresh Custom Data
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
// Import modern composables
import { 
  useModernAuth, 
  useModernDashboard, 
  useModernTasks, 
  useModernVPN,
  useModernFetch 
} from '~/composables/useModernApi'

// Initialize composables
const auth = useModernAuth()
const dashboard = useModernDashboard()
const tasks = useModernTasks()
const vpn = useModernVPN()

// Custom fetch example
const customData = useModernFetch('/custom/endpoint', {
  key: 'custom-data',
  default: () => ({ message: 'No data' })
})

// Handle task completion with error handling
const handleCompleteTask = async (taskId: number) => {
  try {
    await tasks.completeTask(taskId)
    // Show success toast
    console.log('Task completed successfully!')
  } catch (error) {
    console.error('Failed to complete task:', error)
    // Show error toast
  }
}

// Handle package purchase
const handlePurchase = async (packageId: number) => {
  try {
    await vpn.purchasePackage(packageId, 1) // Assuming panel ID 1
    console.log('Package purchased successfully!')
  } catch (error) {
    console.error('Failed to purchase package:', error)
  }
}

// Auto-refresh data on mount
onMounted(() => {
  // Data will be fetched automatically by useFetch
  console.log('Modern data fetching demo mounted')
})
</script>

<style scoped>
/* Component-specific styles */
</style>
