<template>
  <div class="daily-streak-card relative overflow-hidden rounded-xl border border-yellow-500/20 bg-gradient-to-br from-yellow-900/20 to-orange-900/20 backdrop-blur-sm shadow-lg shadow-yellow-500/15">
    <!-- Glow Effect -->
    <div class="absolute top-0 right-0 w-32 h-32 bg-yellow-500/10 rounded-full blur-3xl -translate-y-16 translate-x-16"></div>
    
    <!-- Corner Accents -->
    <div class="absolute top-0 left-0 w-8 h-8 opacity-70">
      <svg width="32" height="32" viewBox="0 0 32 32" fill="none">
        <path d="M 0 12 V 0 H 12" stroke="#f59e0b" stroke-width="1" />
        <path d="M 0 8 V 0 H 8" stroke="#f59e0b" stroke-width="2" />
      </svg>
    </div>

    <div class="relative p-4">
      <!-- Header -->
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center space-x-3">
          <div class="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-500 to-orange-500 flex items-center justify-center animate-float">
            <Icon name="i-heroicons-fire" class="w-6 h-6 text-white" />
          </div>
          <div>
            <h3 class="text-lg font-semibold text-white">Daily Streak</h3>
            <p class="text-yellow-300 text-sm">
              {{ currentStreak }} day{{ currentStreak !== 1 ? 's' : '' }} in a row!
            </p>
          </div>
        </div>
        
        <UButton
          @click="handleCheckIn"
          :disabled="!canCheckIn || isCheckingIn"
          :loading="isCheckingIn"
          color="yellow"
          size="sm"
          class="min-w-[100px]"
        >
          {{ canCheckIn ? 'Check In' : 'Checked In' }}
        </UButton>
      </div>

      <!-- Streak Progress -->
      <div class="mb-4">
        <div class="flex justify-between text-sm mb-2">
          <span class="text-yellow-300">Progress to next reward</span>
          <span class="text-white">
            Day {{ currentStreak }} / {{ nextRewardDay }}
          </span>
        </div>
        <div class="w-full bg-gray-700 rounded-full h-2">
          <div 
            class="bg-gradient-to-r from-yellow-500 to-orange-500 h-2 rounded-full transition-all duration-500"
            :style="{ width: `${streakProgress}%` }"
          ></div>
        </div>
      </div>

      <!-- Streak Calendar -->
      <div class="mb-4">
        <div class="text-sm text-yellow-300 mb-2">Last 7 days</div>
        <div class="flex space-x-1">
          <div
            v-for="(day, index) in last7Days"
            :key="index"
            class="w-8 h-8 rounded-lg flex items-center justify-center text-xs font-medium transition-all duration-200"
            :class="getDayClass(day)"
          >
            {{ day.day }}
          </div>
        </div>
      </div>

      <!-- Next Reward -->
      <div v-if="nextRewardAmount > 0" class="bg-yellow-500/10 rounded-lg p-3">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <Icon name="i-heroicons-gift" class="w-5 h-5 text-yellow-400" />
            <span class="text-yellow-300 text-sm">Next reward</span>
          </div>
          <div class="text-white font-semibold">
            ${{ nextRewardAmount.toFixed(2) }}
          </div>
        </div>
        <div class="text-xs text-yellow-300/70 mt-1">
          {{ daysUntilNextReward }} more day{{ daysUntilNextReward !== 1 ? 's' : '' }} to go
        </div>
      </div>

      <!-- Streak Stats -->
      <div class="grid grid-cols-2 gap-3 mt-4">
        <div class="text-center">
          <div class="text-lg font-bold text-white">{{ longestStreak }}</div>
          <div class="text-xs text-yellow-300/70">Best Streak</div>
        </div>
        <div class="text-center">
          <div class="text-lg font-bold text-white">{{ totalCheckIns }}</div>
          <div class="text-xs text-yellow-300/70">Total Check-ins</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { DailyTaskStreak } from '~/types'

interface Props {
  streak?: DailyTaskStreak | null
  isCheckingIn?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  streak: null,
  isCheckingIn: false
})

const emit = defineEmits<{
  checkIn: []
}>()

// Computed properties
const currentStreak = computed(() => props.streak?.current_streak || 0)
const longestStreak = computed(() => props.streak?.longest_streak || 0)
const totalCheckIns = computed(() => props.streak?.total_checkins || 0)
const nextRewardDay = computed(() => props.streak?.next_reward_day || 1)
const nextRewardAmount = computed(() => props.streak?.next_reward_amount || 0)

const canCheckIn = computed(() => {
  if (!props.streak) return false
  const lastCheckIn = props.streak.last_checkin_date
  if (!lastCheckIn) return true
  
  const today = new Date().toDateString()
  const lastCheckInDate = new Date(lastCheckIn).toDateString()
  return today !== lastCheckInDate
})

const streakProgress = computed(() => {
  if (nextRewardDay.value <= 1) return 100
  return Math.min((currentStreak.value / nextRewardDay.value) * 100, 100)
})

const daysUntilNextReward = computed(() => {
  return Math.max(nextRewardDay.value - currentStreak.value, 0)
})

// Generate last 7 days for calendar
const last7Days = computed(() => {
  const days = []
  const today = new Date()
  
  for (let i = 6; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    
    const dayNumber = date.getDate()
    const isToday = i === 0
    const isCheckedIn = isToday ? canCheckIn.value === false : i < currentStreak.value
    
    days.push({
      day: dayNumber,
      isToday,
      isCheckedIn
    })
  }
  
  return days
})

const getDayClass = (day: any) => {
  if (day.isToday && canCheckIn.value === false) {
    return 'bg-yellow-500 text-white'
  } else if (day.isCheckedIn) {
    return 'bg-yellow-500/30 text-yellow-300'
  } else if (day.isToday) {
    return 'bg-gray-700 text-white border-2 border-yellow-500'
  } else {
    return 'bg-gray-800 text-gray-400'
  }
}

const handleCheckIn = () => {
  if (canCheckIn.value && !props.isCheckingIn) {
    emit('checkIn')
  }
}
</script>

<style scoped>
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.daily-streak-card {
  background-image: 
    radial-gradient(circle at 20% 80%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(251, 146, 60, 0.05) 0%, transparent 50%);
}
</style>
