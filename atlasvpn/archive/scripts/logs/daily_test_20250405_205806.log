Daily Task Test Configuration
API Base URL: http://localhost:8000
Admin Username: admin_test
User Username: user
Test Mode: true


Starting Daily Task Tests...
INFO:httpx:HTTP Request: POST http://localhost:8000/auth/token "HTTP/1.1 403 Forbidden"
ERROR:__main__:Admin login failed: {"detail":"CSRF token missing or invalid"}
ERROR:__main__:Admin login failed, cannot continue tests

Test setup failed, cannot continue
