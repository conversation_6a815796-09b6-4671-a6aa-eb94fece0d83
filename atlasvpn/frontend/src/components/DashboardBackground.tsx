import React, { useEffect, useRef, useMemo } from 'react';

interface Point {
  x: number;
  y: number;
}

interface MatrixSymbol {
  x: number;
  y: number;
  speed: number;
  char: string;
  opacity: number;
}

interface Snake {
  points: Point[];
  velocity: Point;
  length: number;
  width: number;
  speed: number;
  turnSpeed: number;
  target?: Point;
  isFollowing: boolean;
  brightness: number;
  color: string;
}

interface DashboardBackgroundProps {
  matrixColor?: string;
  trailColor?: string;
  particleCount?: number;
  activeTabElement?: HTMLElement | null;
}

const DashboardBackground: React.FC<DashboardBackgroundProps> = ({
  matrixColor = 'rgba(43, 75, 2, 0.25)',
  trailColor = 'rgba(13, 101, 112, 0.55)',
  particleCount = 50,
  activeTabElement,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const pointerRef = useRef<Point>({ x: 0, y: 0 });
  const snakesRef = useRef<Snake[]>([]);
  const matrixSymbolsRef = useRef<MatrixSymbol[]>([]);
  const frameRef = useRef<number>(0);
  const isPointerActiveRef = useRef(false);
  const lastActiveTabRef = useRef<Point | null>(null);
  const lightSourceRef = useRef<Point>({
    x: Math.random() * window.innerWidth,
    y: Math.random() * window.innerHeight,
  });
  const lightVelocityRef = useRef<Point>({
    x: (Math.random() - 0.5) * 0.5,
    y: (Math.random() - 0.5) * 0.5,
  });

  const matrixChars = useMemo(
    () =>
      'アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲンATLAS777777'.split(
        ''
      ),
    []
  );

  const getRandomColor = () => {
    const colors = [
      'rgba(147, 51, 234, 0.2)',
      'rgba(59, 130, 246, 0.2)',
      'rgba(236, 72, 153, 0.2)',
      'rgba(16, 185, 129, 0.2)',
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  const createMatrixSymbol = (canvas: HTMLCanvasElement): MatrixSymbol => ({
    x: Math.random() * canvas.width,
    y: Math.random() * canvas.height,
    speed: 0.4 + Math.random() * 0.4,
    char: matrixChars[Math.floor(Math.random() * matrixChars.length)],
    opacity: 0.05 + Math.random() * 0.7,
  });

  const createSnake = useMemo(
    () =>
      (canvas: HTMLCanvasElement, isFollowing: boolean): Snake => ({
        points: [{ x: Math.random() * canvas.width, y: Math.random() * canvas.height }],
        velocity: { x: 0, y: 0 },
        length: isFollowing ? 25 + Math.random() * 15 : 15 + Math.random() * 10,
        width: isFollowing ? 2 + Math.random() : 1 + Math.random(),
        speed: isFollowing ? 1 + Math.random() : 0.5 + Math.random(),
        turnSpeed: isFollowing ? 0.08 : 0.04,
        isFollowing,
        brightness: 0.4,
        color: getRandomColor(),
      }),
    []
  );

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d', { alpha: true });
    if (!ctx) return;

    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;

      // Reinitialize on resize
      snakesRef.current = [
        ...Array(4)
          .fill(null)
          .map(() => createSnake(canvas, true)),
        ...Array(6)
          .fill(null)
          .map(() => createSnake(canvas, false)),
      ];
      matrixSymbolsRef.current = Array(particleCount)
        .fill(null)
        .map(() => createMatrixSymbol(canvas));
    };

    const handlePointerMove = (e: TouchEvent | MouseEvent) => {
      const rect = canvas.getBoundingClientRect();
      let x, y;

      if ('touches' in e) {
        const touch = e.touches[0];
        x = touch.clientX;
        y = touch.clientY;

        // Randomly make some snakes follow this touch point
        snakesRef.current.forEach(snake => {
          if (Math.random() < 0.3) {
            // 30% chance for each snake
            snake.isFollowing = true;
          }
        });
      } else {
        x = (e as MouseEvent).clientX;
        y = (e as MouseEvent).clientY;
      }

      pointerRef.current = {
        x: x - rect.left,
        y: y - rect.top,
      };
      isPointerActiveRef.current = true;
    };

    const handlePointerEnd = () => {
      isPointerActiveRef.current = false;
      // Reset snake following state
      snakesRef.current.forEach(snake => {
        snake.isFollowing = Math.random() < 0.3; // 30% chance to keep following
      });
    };

    const updateMatrixSymbol = (symbol: MatrixSymbol) => {
      symbol.y += symbol.speed;
      if (symbol.y > canvas.height) {
        symbol.y = 0;
        symbol.x = Math.random() * canvas.width;
        symbol.char = matrixChars[Math.floor(Math.random() * matrixChars.length)];
      }
      return symbol;
    };

    const updateSnake = (snake: Snake) => {
      let targetPoint: Point | null = null;

      if (isPointerActiveRef.current) {
        targetPoint = pointerRef.current;
      } else if (activeTabElement && !lastActiveTabRef.current) {
        const rect = activeTabElement.getBoundingClientRect();
        targetPoint = {
          x: rect.left + rect.width / 2,
          y: rect.top + rect.height / 2,
        };
        lastActiveTabRef.current = targetPoint;
      } else if (lastActiveTabRef.current) {
        targetPoint = lastActiveTabRef.current;
      }

      if (targetPoint && snake.isFollowing) {
        const dx = targetPoint.x - snake.points[0].x;
        const dy = targetPoint.y - snake.points[0].y;
        const angle = Math.atan2(dy, dx);
        const distance = Math.sqrt(dx * dx + dy * dy);

        const maxDistance = 200;
        snake.brightness = Math.max(0.5, 0.9 - distance / maxDistance);

        snake.velocity.x += Math.cos(angle) * snake.turnSpeed;
        snake.velocity.y += Math.sin(angle) * snake.turnSpeed;
      } else {
        snake.velocity.x += (Math.random() - 0.5) * 0.1;
        snake.velocity.y += (Math.random() - 0.5) * 0.1;
        snake.brightness = 0.4;
      }

      // Normalize velocity
      const speed = Math.sqrt(snake.velocity.x ** 2 + snake.velocity.y ** 2);
      snake.velocity.x = (snake.velocity.x / speed) * snake.speed;
      snake.velocity.y = (snake.velocity.y / speed) * snake.speed;

      // Update head position
      const newHead = {
        x: snake.points[0].x + snake.velocity.x,
        y: snake.points[0].y + snake.velocity.y,
      };

      // Wrap around edges
      if (newHead.x < 0) newHead.x = canvas.width;
      if (newHead.x > canvas.width) newHead.x = 0;
      if (newHead.y < 0) newHead.y = canvas.height;
      if (newHead.y > canvas.height) newHead.y = 0;

      snake.points.unshift(newHead);
      if (snake.points.length > snake.length) {
        snake.points.pop();
      }

      return snake;
    };

    const draw = () => {
      // Clear canvas completely each frame
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // --- Draw Moving Light Source (Enhanced) ---
      const light = lightSourceRef.current;
      const lightVel = lightVelocityRef.current;

      // Update light position
      light.x += lightVel.x;
      light.y += lightVel.y;

      // Bounce off edges
      if (light.x < 0 || light.x > canvas.width) lightVel.x *= -1;
      if (light.y < 0 || light.y > canvas.height) lightVel.y *= -1;

      // Create radial gradient - brighter
      const lightRadius = Math.min(canvas.width, canvas.height) * 0.6; // Increased radius
      const gradientLight = ctx.createRadialGradient(
        light.x,
        light.y,
        0,
        light.x,
        light.y,
        lightRadius
      );
      // Darker light effect
      gradientLight.addColorStop(0, 'rgba(147, 51, 234, 0.05)'); // Dimmer center
      gradientLight.addColorStop(0.3, 'rgba(147, 51, 234, 0.02)'); // Dimmer mid
      gradientLight.addColorStop(1, 'rgba(0, 0, 0, 0)'); // Fade to transparent

      // Fill background with the light gradient
      ctx.fillStyle = gradientLight;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // --- End Moving Light Source ---

      // Draw matrix rain - brighter
      ctx.font = '14px monospace';
      matrixSymbolsRef.current.forEach(symbol => {
        updateMatrixSymbol(symbol);
        // Brighter matrix symbols
        ctx.fillStyle = `rgba(147, 51, 234, ${symbol.opacity * 0.2})`; // Increased from 0.4 to 0.7
        ctx.fillText(symbol.char, symbol.x, symbol.y);
      });

      // Draw snakes with enhanced visibility
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      snakesRef.current.forEach(snake => {
        updateSnake(snake);
        if (snake.points.length < 2) return;

        ctx.beginPath();
        ctx.moveTo(snake.points[0].x, snake.points[0].y);

        for (let i = 1; i < snake.points.length - 1; i++) {
          const xc = (snake.points[i].x + snake.points[i + 1].x) / 2;
          const yc = (snake.points[i].y + snake.points[i + 1].y) / 2;
          ctx.quadraticCurveTo(snake.points[i].x, snake.points[i].y, xc, yc);
        }

        const gradient = ctx.createLinearGradient(
          snake.points[0].x,
          snake.points[0].y,
          snake.points[snake.points.length - 1].x,
          snake.points[snake.points.length - 1].y
        );

        const baseColor = snake.color.replace(/,[\d\.\s]+\)$/, ''); // Get base color
        // Brighter snake effect
        const headAlpha = snake.isFollowing ? 0.4 * snake.brightness : 0.25; // Increased from 0.25/0.15 to 0.4/0.25
        const tailAlpha = 0;

        gradient.addColorStop(0, baseColor + ',' + headAlpha + ')'); // Brighter head
        gradient.addColorStop(1, baseColor + ',' + tailAlpha + ')'); // Fade to transparent

        ctx.strokeStyle = gradient;
        ctx.lineWidth = snake.width;
        ctx.shadowBlur = snake.isFollowing ? 15 : 8; // Increased blur from 10/5 to 15/8
        ctx.shadowColor = snake.color.replace(/,[\d\.\s]+\)$/, ',0.7)'); // Increased opacity from 0.5 to 0.7
        ctx.stroke();
      });

      frameRef.current = requestAnimationFrame(draw);
    };

    // Initialize
    handleResize();
    window.addEventListener('resize', handleResize);
    canvas.addEventListener('mousemove', handlePointerMove);
    canvas.addEventListener('touchmove', handlePointerMove);
    canvas.addEventListener('touchend', handlePointerEnd);
    canvas.addEventListener('mouseleave', handlePointerEnd);
    draw();

    return () => {
      cancelAnimationFrame(frameRef.current);
      window.removeEventListener('resize', handleResize);
      canvas.removeEventListener('mousemove', handlePointerMove);
      canvas.removeEventListener('touchmove', handlePointerMove);
      canvas.removeEventListener('touchend', handlePointerEnd);
      canvas.removeEventListener('mouseleave', handlePointerEnd);
    };
  }, [createSnake, particleCount, matrixChars, activeTabElement]);

  return (
    <canvas
      ref={canvasRef}
      className="fixed inset-0 w-full h-full opacity-90"
      style={{ zIndex: 0 }}
    />
  );
};

export default DashboardBackground;
