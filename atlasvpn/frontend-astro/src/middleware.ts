import { defineMiddleware } from 'astro:middleware';

function getApiBaseUrl(): string {
  if (typeof window === 'undefined') {
    return process.env.API_URL || 'https://app.atlasvip.cloud';
  }
  return import.meta.env.VITE_API_URL || 'https://app.atlasvip.cloud';
}

export const onRequest = defineMiddleware(async (context, next) => {
  const { url, request, cookies, redirect, locals } = context;

  // For development/testing - temporarily disable auth for dashboard routes
  // TODO: Re-enable authentication once auth system is migrated

  // Set mock user data for testing (commented out due to TypeScript issues)
  // locals.user = {
  //   id: 1,
  //   username: 'Test User',
  //   email: '<EMAIL>',
  //   telegram_photo_url: 'test-avatar',
  //   wallet_balance: 100.50
  // };
  // locals.isAuthenticated = true;

  return next();
});
