import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { fetchData, mutateData } from '../utils/apiClient';
import type { UnifiedCardResponse, UserCard, BuyCardResult, ClaimAllResponse } from '../types/cardTypes';
import { toast } from 'react-hot-toast';

export function useUnifiedCardsQuery(options?: { staleTime?: number; enabled?: boolean }) {
  return useQuery<UnifiedCardResponse[], Error>({
    queryKey: ['unifiedCards', { endpoint: '/api/cards/all' }],
    queryFn: async (context) => {
      try {
        return await fetchData<UnifiedCardResponse[]>(context);
      } catch (error: any) {
        // Handle specific errors gracefully
        if (error?.response?.status === 404) {
          // Return empty array if no cards found
          return [];
        }
        throw error;
      }
    },
    staleTime: options?.staleTime ?? 5 * 60 * 1000,
    enabled: options?.enabled,
    // Use global retry configuration instead of custom one
  });
}

// Mutations from cardStore

/**
 * Claim all card profits.
 */
export function useClaimAllProfitsMutation() {
  const queryClient = useQueryClient();
  return useMutation<ClaimAllResponse, Error, void>({
    mutationFn: () => mutateData<ClaimAllResponse>({ endpoint: '/api/cards/claim-all' }),
    onSuccess: response => {
      // Only invalidate card-related queries, not auth state
      queryClient.invalidateQueries({ queryKey: ['unifiedCards'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
      if (response?.total_claimed !== undefined) {
        const amount = response.total_claimed;
        toast.success(`Claimed ${amount} profit!`);
      } else {
        toast.success('Profits claimed successfully!');
      }
    },
    onError: error => {
      toast.error(error.message || 'Failed to claim profits');
    },
  });
}

/**
 * Upgrade a single card by userCardId.
 */
export function useUpgradeCardMutation() {
  const queryClient = useQueryClient();
  return useMutation<UserCard, Error, number>({
    mutationFn: (userCardId: number) =>
      mutateData<UserCard>({ endpoint: `/api/cards/${userCardId}/upgrade` }),
    onSuccess: () => {
      // Only invalidate card-related queries, not auth state
      queryClient.invalidateQueries({ queryKey: ['unifiedCards'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
    },
    onError: (error: any) => {
      // Try to show detail/details if present, else fallback to error.message
      const detail = error?.detail || error?.details || error?.message;
      toast.error(detail || 'Failed to upgrade card');
    },
  });
}

/**
 * Purchase a card by catalog ID.
 */
export function useBuyCardMutation() {
  const queryClient = useQueryClient();
  return useMutation<BuyCardResult, Error, number>({
    mutationFn: (cardCatalogId: number) =>
      mutateData<BuyCardResult>({ endpoint: `/api/cards/buy/${cardCatalogId}` }),
    onSuccess: () => {
      // Only invalidate card-related queries, not auth state
      queryClient.invalidateQueries({ queryKey: ['unifiedCards'] });
      queryClient.invalidateQueries({ queryKey: ['dashboardStats'] });
    },
    onError: (error: any) => {
      const detail = error?.detail || error?.details || error?.message;
      toast.error(detail || 'Failed to buy card');
    },
  });
}
