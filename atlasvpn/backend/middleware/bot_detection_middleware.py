from fastapi import Request, Response, status, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from services.bot_detection_service import BotDetectionService
from database import get_db
from config.redis_config import RedisClient
import logging
import json
from typing import Optional, Dict, Any, List
import os
import time
import re
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)

class BotDetectionMiddleware(BaseHTTPMiddleware):
    """Middleware for bot detection and CAPTCHA challenges"""
    
    def __init__(self, app):
        super().__init__(app)
        self.redis_client = RedisClient()
        self.is_test = os.getenv("TEST_ENV", "false").lower() == "true"
        self._initialized = False
        self.bot_detection_service = None
        self.test_mode = os.getenv("TEST_MODE", "false").lower() == "true"
        self.disable_bot_detection = os.getenv("DISABLE_BOT_DETECTION", "false").lower() == "true"
        
        # Paths that should be excluded from bot detection
        self.excluded_paths = [
            r"^/static/.*",
            r"^/api/health$",
            r"^/api/security/verify-captcha$",
            r"^/favicon\.ico$",
            r"^/robots\.txt$",
            r"^/sitemap\.xml$",
            r"^/health$",
            r"^/metrics$",
            r"^/docs.*$",  # Match docs and all paths under it
            r"^/redoc.*$",  # Match redoc and all paths under it
            r"^/openapi\.json$"  # Match openapi.json exactly
        ]
        
        # Configure detection thresholds
        self.high_confidence_threshold = float(os.getenv("BOT_HIGH_CONFIDENCE", "0.8"))
        self.medium_confidence_threshold = float(os.getenv("BOT_MEDIUM_CONFIDENCE", "0.6"))
        self.request_threshold = int(os.getenv("BOT_REQUEST_THRESHOLD", "20"))
        self.test_request_threshold = int(os.getenv("BOT_TEST_REQUEST_THRESHOLD", "10"))
        
        # Define legitimate search engine bot patterns
        self.legitimate_bots = [
            r"Googlebot",
            r"bingbot",
            r"Baiduspider",
            r"YandexBot",
            r"DuckDuckBot",
            r"Applebot",
            r"facebookexternalhit",
            r"Twitterbot"
        ]
        
        # Define automation tool patterns that should require CAPTCHA
        self.automation_tools = [
            r"python-requests",
            r"Wget",
            r"curl",
            r"Postman",
            r"HttpClient",
            r"Go-http-client",
            r"Apache-HttpClient"
        ]

    async def initialize(self):
        """Initialize Redis client"""
        if not self._initialized:
            await self.redis_client.initialize()
            self._initialized = True
            logger.info("Bot detection middleware Redis client initialized")
            
    def _should_skip_path(self, path: str) -> bool:
        """Check if path should be excluded from bot detection"""
        for pattern in self.excluded_paths:
            if re.match(pattern, path):
                return True
        return False
        
    def _is_legitimate_bot(self, user_agent: str) -> bool:
        """Check if user agent is a legitimate search engine bot"""
        if not user_agent:
            return False
            
        for bot_pattern in self.legitimate_bots:
            if re.search(bot_pattern, user_agent, re.IGNORECASE):
                logger.info(f"Legitimate bot detected: {user_agent}")
                return True
                
        return False
        
    def _is_automation_tool(self, user_agent: str) -> bool:
        """Check if user agent is an automation tool that should require CAPTCHA"""
        if not user_agent:
            return False
            
        for tool_pattern in self.automation_tools:
            if re.search(tool_pattern, user_agent, re.IGNORECASE):
                logger.info(f"Automation tool detected: {user_agent}")
                return True
                
        return False
        
    async def dispatch(self, request: Request, call_next):
        """Handle request with bot detection"""
        try:
            # Skip OPTIONS requests (CORS preflight)
            if request.method == "OPTIONS":
                return await call_next(request)
                
            # Get bot detection service from app state
            bot_detection_service = request.app.state.bot_detection_service
            if not bot_detection_service:
                logger.warning("Bot detection service not initialized")
                return await call_next(request)

            # Skip bot detection if disabled
            if self.disable_bot_detection:
                return await call_next(request)

            # Check path against excluded paths
            path = request.url.path
            if self._should_skip_path(path):
                logger.debug(f"Skipping bot detection for excluded path: {path}")
                return await call_next(request)

            # Check if it's a legitimate bot (search engine crawler)
            user_agent = request.headers.get("user-agent", "")
            if self._is_legitimate_bot(user_agent):
                logger.info(f"Legitimate bot detected for {path}: {user_agent}")
                return await call_next(request)

            # Check if it's a known automation tool
            if self._is_automation_tool(user_agent):
                if "/api/payment/webhook" in path or path.endswith("/webhook"):
                    # Allow webhooks to use automation tools
                    logger.info(f"Allowing automation tool for webhook: {path}")
                    return await call_next(request)
                    
                logger.warning(f"Blocking automation tool for {path}: {user_agent}")
                return JSONResponse(
                    status_code=403,
                    content={"detail": "Access denied"},
                    headers={"X-Bot-Detection": "true", "X-Bot-Confidence": "0.95"}
                )

            # Enhanced bot detection
            is_bot, confidence, details = await bot_detection_service.is_bot(
                request=request,
                check_distributed=True,
                sensitivity=0.7
            )

            # First, handle critical paths with strict bot detection
            critical_paths = [
                "/auth/login", 
                "/auth/register", 
                "/auth/token",
                "/payment/create", 
                "/payment/webhook",
                "/user/profile",
                "/admin/"
            ]
            
            is_critical_path = any(path.endswith(cpath) for cpath in critical_paths)
            
            # For testing agents, check the user agent
            is_test_agent = "SecurityTestAgent" in user_agent
            
            # Apply stricter rules for critical paths
            if is_critical_path and is_bot and confidence > 0.6 and not is_test_agent:
                logger.warning(f"Bot activity detected on critical path {path}: {confidence:.2f}")
                # Add bot detection headers
                return JSONResponse(
                    status_code=403,
                    content={"detail": "Access denied"},
                    headers={
                        "X-Bot-Detection": "true",
                        "X-Bot-Confidence": f"{confidence:.2f}"
                    }
                )

            # For non-critical paths, use normal threshold
            if is_bot and confidence > 0.8 and not is_test_agent:
                logger.warning(f"High confidence bot detection: {confidence:.2f}")
                return JSONResponse(
                    status_code=403,
                    content={"detail": "Access denied"},
                    headers={
                        "X-Bot-Detection": "true",
                        "X-Bot-Confidence": f"{confidence:.2f}"
                    }
                )

            # Process the request normally
            response = await call_next(request)

            # Add bot detection headers to response
            response.headers["X-Bot-Detection"] = str(is_bot).lower()
            response.headers["X-Bot-Confidence"] = f"{confidence:.2f}"

            return response

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in bot detection middleware: {str(e)}")
            return await call_next(request)
            
    def _check_suspicious_headers(self, request: Request) -> List[str]:
        """Check for suspicious patterns in request headers"""
        suspicious = []
        
        # Check for inconsistent or missing headers
        user_agent = request.headers.get("user-agent", "").lower()
        accept = request.headers.get("accept", "")
        accept_language = request.headers.get("accept-language", "")
        accept_encoding = request.headers.get("accept-encoding", "")
        
        # Missing user agent
        if not user_agent:
            suspicious.append("missing_user_agent")
            
        # Browser inconsistencies
        if user_agent:
            if "chrome" in user_agent and "mozilla" not in user_agent:
                suspicious.append("inconsistent_browser_headers")
                
            if ("chrome" in user_agent or "firefox" in user_agent or "safari" in user_agent):
                # Browser should have these headers
                if not accept:
                    suspicious.append("missing_accept")
                if not accept_language:
                    suspicious.append("missing_accept_language")
                if not accept_encoding:
                    suspicious.append("missing_accept_encoding")
                    
        # Check for suspicious connection headers
        connection = request.headers.get("connection", "").lower()
        if connection == "close" and ("chrome" in user_agent or "firefox" in user_agent):
            suspicious.append("suspicious_connection_header")
            
        return suspicious
            
    async def _track_request_volume(self, client_ip: str, is_test_attack: bool) -> Optional[Response]:
        """Track request volume to detect distributed attacks"""
        try:
            # Get Redis client
            redis = await self.redis_client.get_client()
            
            # Set keys
            request_key = f"bot:request_count:{client_ip}"
            
            # Increment request count and set expiry
            await redis.incr(request_key)
            await redis.expire(request_key, 60)  # 1 minute window
            
            # Get current count
            count_str = await redis.get(request_key)
            count = int(count_str) if count_str else 1
            
            # Block if too many requests from one IP
            # Use a lower threshold for test attacks
            request_threshold = self.test_request_threshold if is_test_attack else self.request_threshold
            
            if count > request_threshold:
                logger.warning(f"Distributed attack detected: {client_ip} - {count} requests/min")
                return Response(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    content=json.dumps({
                        "detail": "Too many requests - possible distributed attack",
                        "code": "DISTRIBUTED_ATTACK",
                        "retry_after": 60
                    }),
                    media_type="application/json"
                )
                
            return None
            
        except Exception as e:
            logger.error(f"Redis error in request volume tracking: {str(e)}")
            return None 