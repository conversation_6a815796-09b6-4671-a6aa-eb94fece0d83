# AI Assistant Reference Guide - AtlasVPN Frontend Application

## 🎯 **CURRENT APPLICATION STATE** (July 30, 2025)

### ✅ **WORKING STATUS**
- **Framework**: Nuxt 4.0.2 (Latest Stable)
- **State Management**: Pinia with persistence (WORKING)
- **Build System**: Vite (NO IMPORT ERRORS)
- **Container**: Docker with proper volume mounting (RESOLVED)
- **Environment**: Production-ready Telegram Mini App

### 🏗️ **ARCHITECTURE OVERVIEW**

```
AtlasVPN Frontend (Nuxt 4.0.2)
├── 🎨 UI Framework: @nuxt/ui + Tailwind CSS
├── 🗄️ State: Pinia + pinia-plugin-persistedstate@4.4.1
├── 🔌 API: Modern useFetch/useAsyncData patterns
├── 📱 Platform: Telegram Mini App optimized
├── 🐳 Deployment: Docker container
└── 🎮 Features: VPN management + Gaming elements
```

## 🔧 **CRITICAL FIXES APPLIED**

### 1. Persistence Error Resolution ✅
**Issue**: `ReferenceError: persistedState is not defined`
**Root Cause**: Docker container had cached files with legacy persistence syntax
**Solution**: 
- Updated all stores to use correct `localStorage` syntax
- Created proper client-side plugin
- Manual file synchronization to Docker container

**Files Fixed**:
- `/stores/ui.ts` - Fixed persist configuration
- `/stores/user.ts` - Fixed persist configuration  
- `/stores/vpn.ts` - Fixed persist configuration
- `/plugins/pinia-persistedstate.client.ts` - Created proper plugin

### 2. Docker Volume Mounting Issues ✅
**Issue**: Local file changes not reflected in Docker container
**Root Cause**: Build-time file conflicts and caching issues
**Solution**: 
- Manual file copying to container
- Enhanced rebuild script with verification
- Package.json synchronization process

**Prevention Script**: `/rebuild-clean.sh`

### 3. Vite Import Resolution Error ✅
**Issue**: `Failed to resolve import "pinia-plugin-persistedstate"`
**Root Cause**: Docker container had outdated package.json missing dependencies
**Solution**:
- Synchronized package.json and pnpm-lock.yaml
- Installed missing dependencies in container
- Verified import resolution

**Dependencies Updated**:
- `nuxt`: 3.18.0 → 4.0.2
- Added `pinia-plugin-persistedstate@4.4.1`
- Updated all related packages

## 📋 **CURRENT WORKING CONFIGURATION**

### Package.json Dependencies (VERIFIED WORKING)
```json
{
  "dependencies": {
    "nuxt": "^4.0.2",
    "@nuxt/ui": "^3.0.0",
    "@pinia/nuxt": "^0.11.2",
    "pinia": "^3.0.3",
    "pinia-plugin-persistedstate": "^4.4.1",
    "vue": "^3.5.18",
    "@vueuse/nuxt": "^13.6.0",
    "@telegram-apps/sdk": "^3.11.4"
  }
}
```

### Nuxt Configuration (nuxt.config.ts)
```typescript
export default defineNuxtConfig({
  ssr: false, // SPA mode for Telegram Mini App
  modules: [
    '@nuxt/ui',
    '@pinia/nuxt'
  ],
  pinia: {
    storesDirs: ['./stores/**']
  }
})
```

### Store Configuration Pattern (WORKING)
```typescript
export const useExampleStore = defineStore('example', {
  state: () => ({
    // state properties
  }),
  persist: {
    key: 'atlas-example-store',
    storage: typeof window !== 'undefined' ? localStorage : undefined,
    pick: ['property1', 'property2'] // specify what to persist
  }
})
```

## 🚨 **CRITICAL WARNINGS FOR FUTURE AI ASSISTANTS**

### ❌ **DO NOT USE THESE (BROKEN/DEPRECATED)**
1. `@pinia-plugin-persistedstate/nuxt` - Deprecated, incompatible with Nuxt 4
2. `persistedState.localStorage` syntax - Causes ReferenceError
3. `paths` in persist config - Use `pick` instead
4. Direct package.json editing without container sync

### ✅ **ALWAYS DO THESE**
1. Use `./rebuild-clean.sh` for dependency changes
2. Verify file sync between local and Docker container
3. Test imports with `docker exec atlasvpn-frontend node -e "require('package-name')"`
4. Check build logs for import resolution errors

## 🛠️ **TROUBLESHOOTING GUIDE**

### Common Issues and Solutions

#### 1. "persistedState is not defined" Error
```bash
# Check if stores have correct syntax
grep -r "persistedState.localStorage" stores/
# Should return no results

# Verify plugin exists
ls -la plugins/pinia-persistedstate.client.ts

# Check Docker container has correct files
docker exec atlasvpn-frontend cat /app/stores/ui.ts | grep "storage:"
# Should show: storage: typeof window !== 'undefined' ? localStorage : undefined
```

#### 2. Vite Import Resolution Errors
```bash
# Check if package exists in container
docker exec atlasvpn-frontend ls /app/node_modules | grep persistedstate

# Verify package.json sync
diff <(cat package.json) <(docker exec atlasvpn-frontend cat /app/package.json)

# Fix if different
docker cp package.json atlasvpn-frontend:/app/package.json
docker exec atlasvpn-frontend pnpm install
```

#### 3. Docker Volume Mount Issues
```bash
# Use the rebuild script
./rebuild-clean.sh

# Manual verification
ls -la stores/ui.ts
docker exec atlasvpn-frontend ls -la /app/stores/ui.ts
# Timestamps should match
```

## 📁 **FILE STRUCTURE REFERENCE**

```
/opt/atlasvpn/frontend-nuxt/
├── 📄 nuxt.config.ts (Nuxt 4 config)
├── 📄 package.json (Dependencies)
├── 📄 pnpm-lock.yaml (Lock file)
├── 🗂️ stores/
│   ├── ui.ts (UI state + persistence)
│   ├── user.ts (User data + persistence)
│   └── vpn.ts (VPN state + persistence)
├── 🗂️ plugins/
│   └── pinia-persistedstate.client.ts (Persistence plugin)
├── 🗂️ composables/ (Modern data fetching)
│   ├── useDashboard.ts
│   ├── useTasks.ts
│   ├── useCards.ts
│   └── [other composables]
├── 🗂️ pages/ (Nuxt pages)
├── 🗂️ components/ (Vue components)
└── 📄 rebuild-clean.sh (Docker sync script)
```

## 🧪 **TESTING AND VERIFICATION**

### Browser Test Script
```bash
# Run comprehensive test
node test-browser.cjs

# Expected output:
# ✅ Page loaded successfully!
# ✅ LocalStorage test: PASSED
# ✅ No persistence errors
```

### Manual Verification Checklist
- [ ] Application loads without console errors
- [ ] Persistence working (localStorage data saved)
- [ ] All imports resolve correctly
- [ ] Docker container has latest files
- [ ] Build completes without errors

## 🔄 **DEVELOPMENT WORKFLOW**

### For Code Changes
1. Make changes to local files
2. Run `./rebuild-clean.sh` if changing dependencies/stores
3. Test with `node test-browser.cjs`
4. Verify no console errors

### For Dependency Changes
1. Update package.json locally
2. Run `./rebuild-clean.sh` (handles Docker sync)
3. Verify imports work: `docker exec atlasvpn-frontend node -e "require('new-package')"`

### For Store/Persistence Changes
1. Update store files locally
2. Run `./rebuild-clean.sh`
3. Test persistence functionality
4. Check for "persistedState" errors

## 📞 **EMERGENCY RECOVERY**

If application is completely broken:

```bash
# Nuclear option - complete rebuild
docker stop atlasvpn-frontend && docker rm atlasvpn-frontend
docker system prune -f
rm -rf .nuxt .output node_modules/.cache
cd /opt/atlasvpn && ./start-frontend.sh

# Wait for build, then sync files if needed
./rebuild-clean.sh
```

## 📚 **DOCUMENTATION FILES**

- `COMPREHENSIVE_MIGRATION_GUIDE.md` - Complete migration history
- `PERSISTENCE_ERROR_FIX.md` - Persistence error resolution
- `VITE_IMPORT_RESOLUTION_FIX.md` - Import error resolution
- `AI_ASSISTANT_REFERENCE_GUIDE.md` - This file

**Last Updated**: July 30, 2025  
**Status**: ✅ Production Ready  
**Next AI Assistant**: Follow this guide to understand current state before making changes
